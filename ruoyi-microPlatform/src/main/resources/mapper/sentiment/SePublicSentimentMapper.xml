<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SePublicSentimentMapper">

    <resultMap type="SePublicSentiment" id="SePublicSentimentResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="feedbackIssue" column="feedback_issue"/>
        <result property="otherIssue" column="other_issue"/>
        <result property="isBackPhone" column="is_back_phone"/>
        <result property="descDetail" column="desc_detail"/>
        <result property="accessory" column="accessory"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="phone" column="phone"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="addrCounty" column="addr_county"/>
        <result property="addrCountry" column="addr_country"/>
        <result property="addr" column="addr"/>
        <result property="status" column="status"/>
        <result property="handleResult" column="handle_result"/>
        <result property="createTime" column="create_time"/>
        <result property="handleCount" column="handle_count"/>
        <result property="userGridId" column="user_grid_id"/>
        <result property="duration" column="duration"/>
    </resultMap>

    <sql id="selectSePublicSentimentVo">
        select id, dept_id, county, country, town, grid_name, grid_id, feedback_issue, other_issue, is_back_phone, desc_detail, accessory,duration,
        user_id, nick_name, phone, lat, lng, province, city, addr_county, addr_country, addr, status, handle_result, create_time from se_public_sentiment
    </sql>

    <select id="selectSePublicSentimentList" parameterType="SePublicSentiment" resultMap="SePublicSentimentResult">
        select ps.id, ps.dept_id, ps.county, ps.country, ps.grid_id, ps.town, ps.grid_name, ps.feedback_issue, ps.other_issue, ps.duration, ps.is_back_phone, ps.desc_detail, ps.accessory,
        ps.user_id, ps.nick_name, ps.phone, ps.lat, ps.lng, ps.province, ps.city, ps.addr_county, ps.addr_country, ps.addr, ps.status, ps.handle_result, ps.create_time,
        COALESCE(ssi.handle_count, 0) AS handle_count
        from se_public_sentiment ps
        LEFT JOIN (
            SELECT form_id, COUNT(*) AS handle_count
            FROM se_subsequent_info
            WHERE form_type = '居民吹哨'
            GROUP BY form_id
        ) ssi ON ps.id = ssi.form_id
        <where>
            <if test="deptId != null ">and (ps.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = ps.dept_id))</if>
            <if test="county != null  and county != ''">and ps.county = #{county}</if>
            <if test="country != null  and country != ''">and ps.country = #{country}</if>
            <if test="town != null  and town != ''">and ps.town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and ps.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="feedbackIssue != null  and feedbackIssue != ''">and ps.feedback_issue = #{feedbackIssue}</if>
            <if test="otherIssue != null  and otherIssue != ''">and ps.other_issue = #{otherIssue}</if>
            <if test="isBackPhone != null ">and ps.is_back_phone = #{isBackPhone}</if>
            <if test="descDetail != null  and descDetail != ''">and ps.desc_detail = #{descDetail}</if>
            <if test="accessory != null  and accessory != ''">and ps.accessory = #{accessory}</if>
            <if test="userId != null ">and ps.user_id = #{userId}</if>
            <if test="gridId != null ">and ps.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and ps.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="nickName != null  and nickName != ''">and ps.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''">and ps.phone = #{phone}</if>
            <if test="lat != null  and lat != ''">and ps.lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and ps.lng = #{lng}</if>
            <if test="province != null  and province != ''">and ps.province = #{province}</if>
            <if test="city != null  and city != ''">and ps.city = #{city}</if>
            <if test="addrCounty != null  and addrCounty != ''">and ps.addr_county = #{addrCounty}</if>
            <if test="addrCountry != null  and addrCountry != ''">and ps.addr_country = #{addrCountry}</if>
            <if test="addr != null  and addr != ''">and ps.addr = #{addr}</if>
            <if test="status != null ">and ps.status = #{status}</if>
            <if test="gridIdNull != null ">and ps.grid_id is null</if>
            <if test="handleResult != null  and handleResult != ''">and ps.handle_result = #{handleResult}</if>

            <!--<if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
-->
            <if test="startTime != null and startTime != null">
                AND ps.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(startTime == null or startTime == null) and time != null">
                <choose>
                    <when test="time == 'day'">
                        AND DATE(ps.create_time) = CURDATE()
                    </when>
                    <when test="time == 'week'">
                        AND YEARWEEK(ps.create_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="time == 'month'">
                        AND DATE_FORMAT(ps.create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    </when>
                </choose>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectSePublicSentimentById" parameterType="Long" resultMap="SePublicSentimentResult">
        <include refid="selectSePublicSentimentVo"/>
        where id = #{id}
    </select>

    <select id="selectSePublicSentimentById2" parameterType="Long" resultMap="SePublicSentimentResult">
        select s.id, s.dept_id, s.grid_id, s.user_id, s.nick_name, s.phone, u.job_id as user_grid_id, s.town, s.is_back_phone from se_public_sentiment s
        left join sys_user u on s.user_id = u.user_id
        where id = #{id}
    </select>

    <select id="checkSePublicSentimentUnique" parameterType="SePublicSentiment" resultMap="SePublicSentimentResult">
        select id from se_public_sentiment where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSePublicSentiment" parameterType="SePublicSentiment" useGeneratedKeys="true" keyProperty="id">
        insert into se_public_sentiment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="feedbackIssue != null">feedback_issue,</if>
            <if test="otherIssue != null">other_issue,</if>
            <if test="isBackPhone != null">is_back_phone,</if>
            <if test="descDetail != null">desc_detail,</if>
            <if test="accessory != null">accessory,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="phone != null">phone,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="addrCounty != null">addr_county,</if>
            <if test="addrCountry != null">addr_country,</if>
            <if test="addr != null">addr,</if>
            <if test="status != null">status,</if>
            <if test="handleResult != null">handle_result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="duration != null">duration,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="feedbackIssue != null">#{feedbackIssue},</if>
            <if test="otherIssue != null">#{otherIssue},</if>
            <if test="isBackPhone != null">#{isBackPhone},</if>
            <if test="descDetail != null">#{descDetail},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="addrCounty != null">#{addrCounty},</if>
            <if test="addrCountry != null">#{addrCountry},</if>
            <if test="addr != null">#{addr},</if>
            <if test="status != null">#{status},</if>
            <if test="handleResult != null">#{handleResult},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="duration != null">#{duration},</if>
        </trim>
    </insert>

    <update id="updateSePublicSentiment" parameterType="SePublicSentiment">
        update se_public_sentiment
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="feedbackIssue != null">feedback_issue = #{feedbackIssue},</if>
            <if test="otherIssue != null">other_issue = #{otherIssue},</if>
            <if test="isBackPhone != null">is_back_phone = #{isBackPhone},</if>
            <if test="descDetail != null">desc_detail = #{descDetail},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="addrCounty != null">addr_county = #{addrCounty},</if>
            <if test="addrCountry != null">addr_country = #{addrCountry},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handleResult != null">handle_result = #{handleResult},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="duration != null">duration = #{duration},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSePublicSentimentById" parameterType="Long">
        delete from se_public_sentiment where id = #{id}
    </delete>

    <delete id="deleteSePublicSentimentByIds" parameterType="String">
        delete from se_public_sentiment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSubmitCount" parameterType="SePublicSentiment" resultType="map">
        select count(*) as submitCount, max(create_time) as submitTime, sum(IFNULL(is_back_phone, 0)) as msgCount from se_public_sentiment
        where user_id = #{userId}
    </select>

    <select id="selectSubmitCountByHome" resultType="int">
        SELECT COUNT(*) AS count
        FROM se_public_sentiment
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_public_sentiment.dept_id))</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(startTime == null or startTime == null) and time != null">
                <choose>
                    <when test="time == 'day'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="time == 'week'">
                        AND YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="time == 'month'">
                        AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    </when>
                    <otherwise>
                        AND DATE(create_time) = CURDATE()
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <select id="getResidentWhistleCount" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) AS total
        FROM se_public_sentiment
        <where>
            <if test="params.startDate != null and params.startDate != '' and params.endDate != null and params.endDate != ''">
                and create_time BETWEEN CONCAT(#{params.startDate}, ' 00:00:00') AND CONCAT(#{params.endDate}, ' 23:59:59')
            </if>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_public_sentiment.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>


            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
    </select>
</mapper>