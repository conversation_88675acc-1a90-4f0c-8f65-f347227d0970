package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 导出请求审核过程对象 tb_request_audit
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public class TbRequestAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 请求Id */
    @Excel(name = "请求Id")
    private Long requestId;

    private Long auditDeptId;

    /** 审核人id */
    @Excel(name = "审核人id")
    private Long auditUserId;

    /** 审核人名称 */
    @Excel(name = "审核人名称")
    private String auditNickName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核部门名称 */
    @Excel(name = "审核部门名称")
    private String auditDeptName;

    /** 审核结果（0不通过 1通过） */
    @Excel(name = "审核结果", readConverterExp = "0=不通过,1=通过")
    private Integer auditResult;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditReason;

    public Long getAuditDeptId() {
        return auditDeptId;
    }

    public void setAuditDeptId(Long auditDeptId) {
        this.auditDeptId = auditDeptId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRequestId(Long requestId) 
    {
        this.requestId = requestId;
    }

    public Long getRequestId() 
    {
        return requestId;
    }
    public void setAuditUserId(Long auditUserId) 
    {
        this.auditUserId = auditUserId;
    }

    public Long getAuditUserId() 
    {
        return auditUserId;
    }
    public void setAuditNickName(String auditNickName) 
    {
        this.auditNickName = auditNickName;
    }

    public String getAuditNickName() 
    {
        return auditNickName;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditDeptName(String auditDeptName) 
    {
        this.auditDeptName = auditDeptName;
    }

    public String getAuditDeptName() 
    {
        return auditDeptName;
    }
    public void setAuditResult(Integer auditResult) 
    {
        this.auditResult = auditResult;
    }

    public Integer getAuditResult() 
    {
        return auditResult;
    }
    public void setAuditReason(String auditReason) 
    {
        this.auditReason = auditReason;
    }

    public String getAuditReason() 
    {
        return auditReason;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("requestId", getRequestId())
            .append("auditUserId", getAuditUserId())
            .append("auditNickName", getAuditNickName())
            .append("auditTime", getAuditTime())
            .append("auditDeptName", getAuditDeptName())
            .append("auditResult", getAuditResult())
            .append("auditReason", getAuditReason())
            .toString();
    }
}
