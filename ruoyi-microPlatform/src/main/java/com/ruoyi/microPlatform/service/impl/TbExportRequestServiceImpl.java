package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.microPlatform.mapper.TbRequestAuditMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.microPlatform.domain.TbRequestAudit;
import com.ruoyi.microPlatform.mapper.TbExportRequestMapper;
import com.ruoyi.microPlatform.domain.TbExportRequest;
import com.ruoyi.microPlatform.service.ITbExportRequestService;

/**
 * 数据导出审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
@Service
public class TbExportRequestServiceImpl implements ITbExportRequestService {
    @Autowired
    private TbExportRequestMapper tbExportRequestMapper;
    @Autowired
    private TbRequestAuditMapper requestAuditMapper;
    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询数据导出审核
     *
     * @param id 数据导出审核主键
     * @return 数据导出审核
     */
    @Override
    public TbExportRequest selectTbExportRequestById(Long id) {
        return tbExportRequestMapper.selectTbExportRequestById(id);
    }

    /**
     * 校验数据导出审核是否存在
     *
     * @param tbExportRequest
     * @return boolean
     */
    @Override
    public boolean checkTbExportRequest(TbExportRequest tbExportRequest) {
        TbExportRequest old = tbExportRequestMapper.checkTbExportRequestUnique(tbExportRequest);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询数据导出审核列表
     *
     * @param tbExportRequest 数据导出审核
     * @return 数据导出审核
     */
    @Override
    public List<TbExportRequest> selectTbExportRequestList(TbExportRequest tbExportRequest) {
        return tbExportRequestMapper.selectTbExportRequestList(tbExportRequest);
    }

    /**
     * 导入数据导出审核
     *
     * @param infos           数据导出审核列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbExportRequest(List<TbExportRequest> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入数据导出审核数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbExportRequest info : infos) {
            try {
                // 验证是否存在这个数据
                TbExportRequest old = tbExportRequestMapper.checkTbExportRequestUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbExportRequestMapper.insertTbExportRequest(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbExportRequestMapper.updateTbExportRequest(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增数据导出审核
     *
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTbExportRequest(TbExportRequest tbExportRequest) {
        int rows = tbExportRequestMapper.insertTbExportRequest(tbExportRequest);
        return rows;
    }

    /**
     * 修改数据导出审核
     *
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTbExportRequest(TbExportRequest tbExportRequest, LoginUser loginUser) {
        if (ObjectUtil.isNotEmpty(tbExportRequest.getAuditResult()) && tbExportRequest.getAuditResult().intValue() == 3) {
            // 上级部门上报审核
            if (loginUser.getUser().getDept().getDeptLevel() == 4) {
                throw new ServiceException("您已是审批部门最高级别，无法再向上申请！");
            }
            SysDept dept = loginUser.getUser().getDept();
            tbExportRequest.setAuditDeptId(dept.getParentId());
            tbExportRequest.setAuditLevel(dept.getDeptLevel() - 1);
            tbExportRequest.setAuditReason(null);
            tbExportRequest.setStatus(0);// 待审核
            TbRequestAudit audit = new TbRequestAudit();
            audit.setRequestId(tbExportRequest.getId());
            audit.setAuditResult(tbExportRequest.getAuditResult());
            audit.setAuditDeptName(dept.getDeptName());
            audit.setAuditReason(tbExportRequest.getAuditReason());
            audit.setAuditTime(new Date());
            audit.setAuditDeptId(loginUser.getDeptId());
            audit.setAuditUserId(loginUser.getUserId());
            audit.setAuditNickName(loginUser.getUser().getNickName());
            requestAuditMapper.insertTbRequestAudit(audit);
        } else if (ObjectUtil.isNotEmpty(tbExportRequest.getAuditResult())) {
            tbExportRequest.setStatus(1);
        }
        return tbExportRequestMapper.updateTbExportRequest(tbExportRequest);
    }

    @Override
    public int dataExport(TbExportRequest tbExportRequest, LoginUser loginUser) {
        tbExportRequest.setExportStatus(1);
        tbExportRequest.setExportTime(new Date());
        return tbExportRequestMapper.updateTbExportRequest(tbExportRequest);
    }

    /**
     * 批量删除数据导出审核
     *
     * @param ids 需要删除的数据导出审核主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbExportRequestByIds(Long[] ids) {
        tbExportRequestMapper.deleteTbRequestAuditByRequestIds(ids);
        return tbExportRequestMapper.deleteTbExportRequestByIds(ids);
    }

    /**
     * 删除数据导出审核信息
     *
     * @param id 数据导出审核主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbExportRequestById(Long id) {
        tbExportRequestMapper.deleteTbRequestAuditByRequestId(id);
        return tbExportRequestMapper.deleteTbExportRequestById(id);
    }

    /**
     * 新增导出请求审核过程信息
     *
     * @param tbExportRequest 数据导出审核对象
     */
    public void insertTbRequestAudit(TbExportRequest tbExportRequest) {
        List<TbRequestAudit> tbRequestAuditList = tbExportRequest.getTbRequestAuditList();
        Long id = tbExportRequest.getId();
        if (ObjectUtil.isNotEmpty(tbRequestAuditList)) {
            List<TbRequestAudit> list = new ArrayList<TbRequestAudit>();
            for (TbRequestAudit tbRequestAudit : tbRequestAuditList) {
                tbRequestAudit.setRequestId(id);
                list.add(tbRequestAudit);
            }
            if (list.size() > 0) {
                tbExportRequestMapper.batchTbRequestAudit(list);
            }
        }
    }
}
