<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbIssueInfoMapper">

    <resultMap type="TbIssueInfo" id="TbIssueInfoResult">
        <result property="id" column="id"/>
        <result property="dataFrom" column="data_from"/>
        <result property="formId" column="form_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="mdjfzj" column="mdjfzj"/>
        <result property="mdjfmc" column="mdjfmc"/>
        <result property="mdjflbdm" column="mdjflbdm"/>
        <result property="mdjflbmc" column="mdjflbmc"/>
        <result property="fssj" column="fssj"/>
        <result property="fsdz" column="fsdz"/>
        <result property="mdjflydm" column="mdjflydm"/>
        <result property="mdjflymc" column="mdjflymc"/>
        <result property="fxdjdm" column="fxdjdm"/>
        <result property="fxdjmc" column="fxdjmc"/>
        <result property="mdjfxq" column="mdjfxq"/>
        <result property="mdjfztdm" column="mdjfztdm"/>
        <result property="mdjfztmc" column="mdjfztmc"/>
        <result property="mdjfdjr" column="mdjfdjr"/>
        <result property="mdjfdjsj" column="mdjfdjsj"/>
        <result property="mdjfdjrhm" column="mdjfdjrhm"/>
        <result property="mdjfzzgxddm" column="mdjfzzgxddm"/>
        <result property="mdjfzzgxdmc" column="mdjfzzgxdmc"/>
        <result property="mdjfpcsdm" column="mdjfpcsdm"/>
        <result property="mdjfpcsmc" column="mdjfpcsmc"/>
        <result property="tbTszt" column="tb_tszt"/>
        <result property="personNum" column="person_num"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="accessory" column="accessory"/>
    </resultMap>

    <resultMap id="TbIssueInfoTbMainPartyResult" type="TbIssueInfo" extends="TbIssueInfoResult">
        <collection property="tbMainPartyList" notNullColumn="sub_id" javaType="java.util.List"
                    resultMap="TbMainPartyResult"/>
    </resultMap>

    <resultMap type="TbMainParty" id="TbMainPartyResult">
        <result property="id" column="sub_id"/>
        <result property="issueId" column="sub_issue_id"/>
        <result property="mdjfdsrzj" column="sub_mdjfdsrzj"/>
        <result property="mdjfzj" column="sub_mdjfzj"/>
        <result property="dsrzjzldm" column="sub_dsrzjzldm"/>
        <result property="dsrzjhm" column="sub_dsrzjhm"/>
        <result property="dsrzjzlmc" column="sub_dsrzjzlmc"/>
        <result property="dsrxm" column="sub_dsrxm"/>
        <result property="lxdh" column="sub_lxdh"/>
        <result property="xbdm" column="sub_xbdm"/>
        <result property="xb" column="sub_xb"/>
        <result property="hyzkdm" column="sub_hyzkdm"/>
        <result property="hyzk" column="sub_hyzk"/>
        <result property="whcddm" column="sub_whcddm"/>
        <result property="whcd" column="sub_whcd"/>
        <result property="mzdm" column="sub_mzdm"/>
        <result property="mz" column="sub_mz"/>
        <result property="dsrhjdz" column="sub_dsrhjdz"/>
        <result property="dsrxzz" column="sub_dsrxzz"/>
        <result property="nl" column="sub_nl"/>
        <result property="csrq" column="sub_csrq"/>
        <result property="gzdw" column="sub_gzdw"/>
        <result property="createTime" column="sub_create_time"/>
        <result property="createBy" column="sub_create_by"/>
        <result property="updateTime" column="sub_update_time"/>
        <result property="updateBy" column="sub_update_by"/>
    </resultMap>

    <sql id="selectTbIssueInfoVo">
        select id, data_from, form_id, dept_id, county, country, town, grid_id, grid_name, mdjfzj, mdjfmc, mdjflbdm, mdjflbmc, fssj, fsdz, mdjflydm, mdjflymc, fxdjdm, fxdjmc, mdjfxq, mdjfztdm, mdjfztmc, mdjfdjr, mdjfdjsj, mdjfdjrhm, mdjfzzgxddm, mdjfzzgxdmc, mdjfpcsdm, mdjfpcsmc, tb_tszt, person_num, create_time, create_by, update_time, update_by, accessory from tb_issue_info
    </sql>

    <select id="selectTbIssueInfoList" parameterType="TbIssueInfo" resultMap="TbIssueInfoResult">
        <include refid="selectTbIssueInfoVo"/>
        <where>
            <if test="dataFrom != null  and dataFrom != ''">and data_from = #{dataFrom}</if>
            <if test="formId != null ">and form_id = #{formId}</if>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_issue_info.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="mdjfzj != null  and mdjfzj != ''">and mdjfzj = #{mdjfzj}</if>
            <if test="mdjfmc != null  and mdjfmc != ''">and mdjfmc like concat(#{mdjfmc}, '%')</if>
            <if test="mdjflbdm != null  and mdjflbdm != ''">and mdjflbdm like concat(#{mdjflbdm}, '%')</if>
            <if test="mdjflbmc != null  and mdjflbmc != ''">and mdjflbmc = #{mdjflbmc}</if>
            <if test="fssj != null ">and fssj = #{fssj}</if>
            <if test="fsdz != null  and fsdz != ''">and fsdz = #{fsdz}</if>
            <if test="mdjflydm != null  and mdjflydm != ''">and mdjflydm = #{mdjflydm}</if>
            <if test="mdjflymc != null  and mdjflymc != ''">and mdjflymc = #{mdjflymc}</if>
            <if test="fxdjdm != null  and fxdjdm != ''">and fxdjdm = #{fxdjdm}</if>
            <if test="fxdjmc != null  and fxdjmc != ''">and fxdjmc = #{fxdjmc}</if>
            <if test="mdjfxq != null  and mdjfxq != ''">and mdjfxq = #{mdjfxq}</if>
            <if test="mdjfztdm != null  and mdjfztdm != ''">and mdjfztdm = #{mdjfztdm}</if>
            <if test="mdjfztmc != null  and mdjfztmc != ''">
                <choose>
                    <when test="mdjfztmc == '已化解'">
                        AND mdjfztmc IN ('公安已化解', '综治已化解')
                    </when>
                    <when test="mdjfztmc == '未化解'">
                        AND (mdjfztmc NOT IN ('公安已化解', '综治已化解') OR mdjfztmc IS NULL)
                    </when>
                </choose>
            </if>
            <if test="mdjfdjr != null  and mdjfdjr != ''">and mdjfdjr = #{mdjfdjr}</if>

            <if test="mdjfdjrhm != null  and mdjfdjrhm != ''">and mdjfdjrhm = #{mdjfdjrhm}</if>
            <if test="mdjfzzgxddm != null  and mdjfzzgxddm != ''">and mdjfzzgxddm = #{mdjfzzgxddm}</if>
            <if test="mdjfzzgxdmc != null  and mdjfzzgxdmc != ''">and mdjfzzgxdmc = #{mdjfzzgxdmc}</if>
            <if test="mdjfpcsdm != null  and mdjfpcsdm != ''">and mdjfpcsdm = #{mdjfpcsdm}</if>
            <if test="mdjfpcsmc != null  and mdjfpcsmc != ''">and mdjfpcsmc = #{mdjfpcsmc}</if>
            <if test="tbTszt != null ">and tb_tszt = #{tbTszt}</if>
            <if test="personNum != null ">and person_num = #{personNum}</if>
            <if test="accessory != null  and accessory != ''">and accessory = #{accessory}</if>
            <if test="name != null  and name != ''">and id in (select issue_id from tb_main_party where dsrxm like
                concat('%', #{name}, '%'))
            </if>
            <if test="phone != null  and phone != ''">and id in (select issue_id from tb_main_party where lxdh =
                #{phone})
            </if>
            <if test="idCard != null  and idCard != ''">and id in (select issue_id from tb_main_party where dsrzjhm =
                #{idCard})
            </if>
            <if test="params.beginFssj != null and params.beginFssj != '' and params.endFssj != null and params.endFssj != '' ">
                and fssj between #{params.beginFssj} and #{params.endFssj}
            </if>
            <if test="params.beginDjsj != null and params.beginDjsj != '' and params.endDjsj != null and params.endDjsj != '' ">
                and mdjfdjsj between #{params.beginDjsj} and #{params.endDjsj}
            </if>
        </where>
        order by mdjfdjsj desc
    </select>

    <select id="selectTbIssueInfoById" parameterType="Long" resultMap="TbIssueInfoTbMainPartyResult">
            select a.id, a.data_from, a.form_id, a.dept_id, a.county, a.country, a.town, a.grid_id, a.grid_name, a.mdjfzj, a.mdjfmc, a.mdjflbdm, a.mdjflbmc, a.fssj, a.fsdz, a.mdjflydm, a.mdjflymc, a.fxdjdm, a.fxdjmc, a.mdjfxq, a.mdjfztdm, a.mdjfztmc, a.mdjfdjr, a.mdjfdjsj, a.mdjfdjrhm, a.mdjfzzgxddm, a.mdjfzzgxdmc, a.mdjfpcsdm, a.mdjfpcsmc, a.tb_tszt, a.person_num, a.create_time, a.create_by, a.update_time, a.update_by, a.accessory,
 b.id as sub_id, b.issue_id as sub_issue_id, b.mdjfdsrzj as sub_mdjfdsrzj, b.mdjfzj as sub_mdjfzj, b.dsrzjzldm as sub_dsrzjzldm, b.dsrzjhm as sub_dsrzjhm, b.dsrzjzlmc as sub_dsrzjzlmc, b.dsrxm as sub_dsrxm, b.lxdh as sub_lxdh, b.xbdm as sub_xbdm, b.xb as sub_xb, b.hyzkdm as sub_hyzkdm, b.hyzk as sub_hyzk, b.whcddm as sub_whcddm, b.whcd as sub_whcd, b.mzdm as sub_mzdm, b.mz as sub_mz, b.dsrhjdz as sub_dsrhjdz, b.dsrxzz as sub_dsrxzz, b.nl as sub_nl, b.csrq as sub_csrq, b.gzdw as sub_gzdw, b.create_time as sub_create_time, b.create_by as sub_create_by, b.update_time as sub_update_time, b.update_by as sub_update_by
            from tb_issue_info a
            left join tb_main_party b on b.issue_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkTbIssueInfoUnique" parameterType="TbIssueInfo" resultMap="TbIssueInfoResult">
        select id from tb_issue_info where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbIssueInfo" parameterType="TbIssueInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_issue_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataFrom != null">data_from,</if>
            <if test="formId != null">form_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="mdjfzj != null">mdjfzj,</if>
            <if test="mdjfmc != null and mdjfmc != ''">mdjfmc,</if>
            <if test="mdjflbdm != null and mdjflbdm != ''">mdjflbdm,</if>
            <if test="mdjflbmc != null">mdjflbmc,</if>
            <if test="fssj != null">fssj,</if>
            <if test="fsdz != null and fsdz != ''">fsdz,</if>
            <if test="mdjflydm != null and mdjflydm != ''">mdjflydm,</if>
            <if test="mdjflymc != null">mdjflymc,</if>
            <if test="fxdjdm != null">fxdjdm,</if>
            <if test="fxdjmc != null">fxdjmc,</if>
            <if test="mdjfxq != null and mdjfxq != ''">mdjfxq,</if>
            <if test="mdjfztdm != null">mdjfztdm,</if>
            <if test="mdjfztmc != null">mdjfztmc,</if>
            <if test="mdjfdjr != null and mdjfdjr != ''">mdjfdjr,</if>
            <if test="mdjfdjsj != null">mdjfdjsj,</if>
            <if test="mdjfdjrhm != null">mdjfdjrhm,</if>
            <if test="mdjfzzgxddm != null">mdjfzzgxddm,</if>
            <if test="mdjfzzgxdmc != null">mdjfzzgxdmc,</if>
            <if test="mdjfpcsdm != null">mdjfpcsdm,</if>
            <if test="mdjfpcsmc != null">mdjfpcsmc,</if>
            <if test="tbTszt != null">tb_tszt,</if>
            <if test="personNum != null">person_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="accessory != null">accessory,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataFrom != null">#{dataFrom},</if>
            <if test="formId != null">#{formId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="mdjfzj != null">#{mdjfzj},</if>
            <if test="mdjfmc != null and mdjfmc != ''">#{mdjfmc},</if>
            <if test="mdjflbdm != null and mdjflbdm != ''">#{mdjflbdm},</if>
            <if test="mdjflbmc != null">#{mdjflbmc},</if>
            <if test="fssj != null">#{fssj},</if>
            <if test="fsdz != null and fsdz != ''">#{fsdz},</if>
            <if test="mdjflydm != null and mdjflydm != ''">#{mdjflydm},</if>
            <if test="mdjflymc != null">#{mdjflymc},</if>
            <if test="fxdjdm != null">#{fxdjdm},</if>
            <if test="fxdjmc != null">#{fxdjmc},</if>
            <if test="mdjfxq != null and mdjfxq != ''">#{mdjfxq},</if>
            <if test="mdjfztdm != null">#{mdjfztdm},</if>
            <if test="mdjfztmc != null">#{mdjfztmc},</if>
            <if test="mdjfdjr != null and mdjfdjr != ''">#{mdjfdjr},</if>
            <if test="mdjfdjsj != null">#{mdjfdjsj},</if>
            <if test="mdjfdjrhm != null">#{mdjfdjrhm},</if>
            <if test="mdjfzzgxddm != null">#{mdjfzzgxddm},</if>
            <if test="mdjfzzgxdmc != null">#{mdjfzzgxdmc},</if>
            <if test="mdjfpcsdm != null">#{mdjfpcsdm},</if>
            <if test="mdjfpcsmc != null">#{mdjfpcsmc},</if>
            <if test="tbTszt != null">#{tbTszt},</if>
            <if test="personNum != null">#{personNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="accessory != null">#{accessory},</if>
        </trim>
    </insert>

    <update id="updateTbIssueInfo" parameterType="TbIssueInfo">
        update tb_issue_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataFrom != null">data_from = #{dataFrom},</if>
            <if test="formId != null">form_id = #{formId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="mdjfzj != null">mdjfzj = #{mdjfzj},</if>
            <if test="mdjfmc != null and mdjfmc != ''">mdjfmc = #{mdjfmc},</if>
            <if test="mdjflbdm != null and mdjflbdm != ''">mdjflbdm = #{mdjflbdm},</if>
            <if test="mdjflbmc != null">mdjflbmc = #{mdjflbmc},</if>
            <if test="fssj != null">fssj = #{fssj},</if>
            <if test="fsdz != null and fsdz != ''">fsdz = #{fsdz},</if>
            <if test="mdjflydm != null and mdjflydm != ''">mdjflydm = #{mdjflydm},</if>
            <if test="mdjflymc != null">mdjflymc = #{mdjflymc},</if>
            <if test="fxdjdm != null">fxdjdm = #{fxdjdm},</if>
            <if test="fxdjmc != null">fxdjmc = #{fxdjmc},</if>
            <if test="mdjfxq != null and mdjfxq != ''">mdjfxq = #{mdjfxq},</if>
            <if test="mdjfztdm != null">mdjfztdm = #{mdjfztdm},</if>
            <if test="mdjfztmc != null">mdjfztmc = #{mdjfztmc},</if>
            <if test="mdjfdjr != null and mdjfdjr != ''">mdjfdjr = #{mdjfdjr},</if>
            <if test="mdjfdjsj != null">mdjfdjsj = #{mdjfdjsj},</if>
            <if test="mdjfdjrhm != null">mdjfdjrhm = #{mdjfdjrhm},</if>
            <if test="mdjfzzgxddm != null">mdjfzzgxddm = #{mdjfzzgxddm},</if>
            <if test="mdjfzzgxdmc != null">mdjfzzgxdmc = #{mdjfzzgxdmc},</if>
            <if test="mdjfpcsdm != null">mdjfpcsdm = #{mdjfpcsdm},</if>
            <if test="mdjfpcsmc != null">mdjfpcsmc = #{mdjfpcsmc},</if>
            <if test="tbTszt != null">tb_tszt = #{tbTszt},</if>
            <if test="personNum != null">person_num = #{personNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbIssueInfoById" parameterType="Long">
        delete from tb_issue_info where id = #{id}
    </delete>

    <delete id="deleteTbIssueInfoByIds" parameterType="String">
        delete from tb_issue_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbMainPartyByIssueIds" parameterType="String">
        delete from tb_main_party where issue_id in
        <foreach item="issueId" collection="array" open="(" separator="," close=")">
            #{issueId}
        </foreach>
    </delete>

    <delete id="deleteTbMainPartyByIssueId" parameterType="Long">
        delete from tb_main_party where issue_id = #{issueId}
    </delete>

    <insert id="batchTbMainParty">
        insert into tb_main_party( id, issue_id, mdjfdsrzj, mdjfzj, dsrzjzldm, dsrzjhm, dsrzjzlmc, dsrxm, lxdh, xbdm,
        xb, hyzkdm, hyzk, whcddm, whcd, mzdm, mz, dsrhjdz, dsrxzz, nl, csrq, gzdw, create_time, create_by, update_time,
        update_by) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.issueId}, #{item.mdjfdsrzj}, #{item.mdjfzj}, #{item.dsrzjzldm}, #{item.dsrzjhm},
            #{item.dsrzjzlmc}, #{item.dsrxm}, #{item.lxdh}, #{item.xbdm}, #{item.xb}, #{item.hyzkdm}, #{item.hyzk},
            #{item.whcddm}, #{item.whcd}, #{item.mzdm}, #{item.mz}, #{item.dsrhjdz}, #{item.dsrxzz}, #{item.nl},
            #{item.csrq}, #{item.gzdw}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="getIssueCountByMdjfztdm" resultType="java.util.Map">
        SELECT
            SUM(CASE
                WHEN mdjfztmc IN ('公安已化解', '综治已化解') THEN 1
                ELSE 0
                END) AS resolved_count,
            SUM(CASE
                WHEN mdjfztmc NOT IN ('公安已化解', '综治已化解') OR mdjfztmc IS NULL THEN 1
                ELSE 0
                END) AS unresolved_count,
            COUNT(*) AS total_count
        FROM tb_issue_info
        <where>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
        </where>
    </select>

    <select id="getHandleStepInfo" resultType="com.ruoyi.microPlatform.domain.HandleStepVO">
        select
            id,
            issue_id,
            main_id,
            mdjftczj,
            mdjfzj,
            mdjftccs,
            DATE_FORMAT(STR_TO_DATE(mdjftcsj, '%Y%m%d%H%i%s'), '%Y-%m-%d %H:%i:%s') AS mdjftcsj,
            mdjftcdw,
            mdjftcry,
            mdjftcdd,
            mdjfdsr,
            mdjftcxs,
            mdjftcgc,
            mdjfdsrxm,
            mdjftcddlx,
            mdjftcjg,
            create_time,
            create_by,
            accessory
        from tb_handle_step
        where
            issue_id = #{issueId}
    </select>

    <select id="getTransferInfo" resultType="com.ruoyi.microPlatform.domain.TransferIssueInfoVO">
        select
            id,
            form_id,
            mdjfzj,
            mdjfmc,
            mdjflbdm,
            mdjflbmc,
            fssj,
            fsdz,
            mdjflydm,
            mdjflymc,
            fxdjdm,
            fxdjmc,
            mdjfxq,
            mdjfztdm,
            mdjfztmc,
            mdjfdjr,
            mdjfdjsj,
            mdjfzzgxddm,
            mdjfzzgxdmc,
            mdjfczjy,
            mdjfdjpcs,
            mdjfssjws,
            mdjfyjsj,
            flag
        from
            tb_issue2
        where
            form_id = #{issueId}
    </select>
    <select id="statusCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select mdjfztmc lable ,count(*) count from tb_issue_info
        group by mdjfztmc
    </select>
</mapper>