package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 社区干部关联网格对象 tb_dept_user_grid
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbDeptUserGrid extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 社区干部ID */
    @Excel(name = "社区干部ID")
    private Long deptUserId;

    /** 管理网格ID */
    @Excel(name = "管理网格ID")
    private Long gridId;

    private List<Long> idList;

    private TbDeptUser deptUser;

    private TbGridInfo grid;

    public TbDeptUser getDeptUser() {
        return deptUser;
    }

    public void setDeptUser(TbDeptUser deptUser) {
        this.deptUser = deptUser;
    }

    public TbGridInfo getGrid() {
        return grid;
    }

    public void setGrid(TbGridInfo grid) {
        this.grid = grid;
    }

    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptUserId(Long deptUserId) 
    {
        this.deptUserId = deptUserId;
    }

    public Long getDeptUserId() 
    {
        return deptUserId;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptUserId", getDeptUserId())
            .append("gridId", getGridId())
            .toString();
    }
}
