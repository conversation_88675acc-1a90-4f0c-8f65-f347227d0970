package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.microPlatform.domain.CountyStatics;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 小区住户居民信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbCommunityResidentService 
{
    /**
     * 查询小区住户居民信息
     * 
     * @param id 小区住户居民信息主键
     * @return 小区住户居民信息
     */
    public TbCommunityResident selectTbCommunityResidentById(Long id);

    /**
     * 校验小区住户居民信息是否存在
     *
     * @param tbCommunityResident 小区住户居民信息
     * @return 小区住户居民信息
     */
    public boolean checkTbCommunityResident(TbCommunityResident tbCommunityResident);

    /**
     * 查询小区住户居民信息列表
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 小区住户居民信息集合
     */
    public TableDataInfo selectTbCommunityResidentList(TbCommunityResident tbCommunityResident,Integer pageNum,Integer pageSize);

    /**
     * 导入小区住户居民信息
     *
     * @param infos       小区住户居民信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbCommunityResident(List<TbCommunityResident> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增小区住户居民信息
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    public int insertTbCommunityResident(TbCommunityResident tbCommunityResident);

    /**
     * 修改小区住户居民信息
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    public int updateTbCommunityResident(TbCommunityResident tbCommunityResident);

    /**
     * 批量删除小区住户居民信息
     * 
     * @param ids 需要删除的小区住户居民信息主键集合
     * @return 结果
     */
    public int deleteTbCommunityResidentByIds(Long[] ids);

    /**
     * 删除小区住户居民信息信息
     * 
     * @param id 小区住户居民信息主键
     * @return 结果
     */
    public int deleteTbCommunityResidentById(Long id);

    /**
     * 查询小区住户居民信息和对应的居民住户成员信息  1对多
     * @param communityResident
     * @return
     */
    List<TbCommunityResident> selectTbCommunityResidentAndMemberList(TbCommunityResident communityResident);

    public Integer selectTbCommunityResidentCount(TbCommunityResident communityResident);

    public List<CountyStatics> selectTypeList(Integer deptLevelType, String county, String country, Integer dataType);
}
