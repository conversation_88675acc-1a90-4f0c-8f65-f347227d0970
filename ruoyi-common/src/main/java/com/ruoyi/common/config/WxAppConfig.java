package com.ruoyi.common.config;

import com.ruoyi.common.constant.Constants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wx-app")
public class WxAppConfig
{
    /** appId */
    private String appId;

    /** 密钥 */
    private String appSecret;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }
}
