package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.SePublicSentiment;

/**
 * 民情收集Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SePublicSentimentMapper 
{
    /**
     * 查询民情收集
     * 
     * @param id 民情收集主键
     * @return 民情收集
     */
    public SePublicSentiment selectSePublicSentimentById(Long id);

    public SePublicSentiment selectSePublicSentimentById2(Long id);

    /**
     * 校验民情收集是否唯一
     *
     * @param sePublicSentiment
     * @return 民情收集
     */
    public SePublicSentiment checkSePublicSentimentUnique(SePublicSentiment sePublicSentiment);

    /**
     * 查询民情收集列表
     * 
     * @param sePublicSentiment 民情收集
     * @return 民情收集集合
     */
    public List<SePublicSentiment> selectSePublicSentimentList(SePublicSentiment sePublicSentiment);

    /**
     * 新增民情收集
     * 
     * @param sePublicSentiment 民情收集
     * @return 结果
     */
    public int insertSePublicSentiment(SePublicSentiment sePublicSentiment);

    /**
     * 修改民情收集
     * 
     * @param sePublicSentiment 民情收集
     * @return 结果
     */
    public int updateSePublicSentiment(SePublicSentiment sePublicSentiment);

    /**
     * 删除民情收集
     * 
     * @param id 民情收集主键
     * @return 结果
     */
    public int deleteSePublicSentimentById(Long id);

    /**
     * 批量删除民情收集
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSePublicSentimentByIds(Long[] ids);

    public Map<String,Object> selectSubmitCount(SePublicSentiment sePublicSentiment);

    int selectSubmitCountByHome(HomePage homePage);

    Integer getResidentWhistleCount(LargeScreenInfo largeScreenInfo);

}
