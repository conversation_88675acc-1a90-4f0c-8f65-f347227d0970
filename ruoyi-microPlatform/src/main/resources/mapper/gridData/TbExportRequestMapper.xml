<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbExportRequestMapper">

    <resultMap type="TbExportRequest" id="TbExportRequestResult">
        <result property="id" column="id"/>
        <result property="dataType" column="data_type"/>
        <result property="paramsStr" column="params_str"/>
        <result property="dataCount" column="data_count"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptLevel" column="dept_level"/>
        <result property="requestUnit" column="request_unit"/>
        <result property="status" column="status"/>
        <result property="auditResult" column="audit_result"/>
        <result property="auditReason" column="audit_reason"/>
        <result property="auditUserId" column="audit_user_id"/>
        <result property="auditNickName" column="audit_nick_name"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditDeptId" column="audit_dept_id"/>
        <result property="auditLevel" column="audit_level"/>
        <result property="exportStatus" column="export_status"/>
        <result property="exportTime" column="export_time"/>
        <result property="reason" column="reason"/>
        <result property="type" column="type"/>
    </resultMap>

    <resultMap id="TbExportRequestTbRequestAuditResult" type="TbExportRequest" extends="TbExportRequestResult">
        <collection property="tbRequestAuditList" notNullColumn="sub_id" javaType="java.util.List"
                    resultMap="TbRequestAuditResult"/>
    </resultMap>

    <resultMap type="TbRequestAudit" id="TbRequestAuditResult">
        <result property="id" column="sub_id"/>
        <result property="requestId" column="sub_request_id"/>
        <result property="auditUserId" column="sub_audit_user_id"/>
        <result property="auditNickName" column="sub_audit_nick_name"/>
        <result property="auditTime" column="sub_audit_time"/>
        <result property="auditDeptId" column="sub_audit_dept_id"/>
        <result property="auditDeptName" column="sub_audit_dept_name"/>
        <result property="auditResult" column="sub_audit_result"/>
        <result property="auditReason" column="sub_audit_reason"/>
    </resultMap>

    <sql id="selectTbExportRequestVo">
        select id, data_type, params_str, data_count, user_id, nick_name, dept_id, dept_level, request_unit, type, reason, status, audit_result, audit_reason, audit_user_id, audit_nick_name, audit_time, audit_dept_id, audit_level, export_status, export_time from tb_export_request
    </sql>

    <select id="selectTbExportRequestList" parameterType="TbExportRequest" resultMap="TbExportRequestResult">
        <include refid="selectTbExportRequestVo"/>
        <where>
            <if test="dataType != null  and dataType != ''">and data_type = #{dataType}</if>
            <if test="paramsStr != null  and paramsStr != ''">and params_str = #{paramsStr}</if>
            <if test="dataCount != null ">and data_count = #{dataCount}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="deptLevel != null ">and dept_level = #{deptLevel}</if>
            <if test="requestUnit != null  and requestUnit != ''">and request_unit = #{requestUnit}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="auditResult != null ">and audit_result = #{auditResult}</if>
            <if test="auditReason != null  and auditReason != ''">and audit_reason = #{auditReason}</if>
            <if test="auditUserId != null ">and audit_user_id = #{auditUserId}</if>
            <if test="auditNickName != null  and auditNickName != ''">and audit_nick_name like concat('%',
                #{auditNickName}, '%')
            </if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="auditDeptId != null ">and audit_dept_id = #{auditDeptId}</if>
            <if test="auditDoneDeptId != null ">and ((audit_dept_id = #{auditDoneDeptId} and status = 1) or id in (select request_id from tb_request_audit where audit_dept_id = #{auditDoneDeptId}) )</if>
            <if test="auditLevel != null ">and audit_level = #{auditLevel}</if>
            <if test="exportStatus != null ">and export_status = #{exportStatus}</if>
            <if test="exportTime != null ">and export_time = #{exportTime}</if>
            <if test="type != null ">and type = #{type}</if>
        </where>
        order by status asc, id desc
    </select>

    <select id="selectTbExportRequestById" parameterType="Long" resultMap="TbExportRequestTbRequestAuditResult">
            select a.id, a.data_type, a.params_str, a.data_count, a.user_id, a.type, a.reason, a.nick_name, a.dept_id, a.dept_level, a.request_unit, a.status, a.audit_result, a.audit_reason, a.audit_user_id, a.audit_nick_name, a.audit_time, a.audit_dept_id, a.audit_level, a.export_status, a.export_time,
 b.id as sub_id, b.request_id as sub_request_id, b.audit_user_id as sub_audit_user_id, b.audit_nick_name as sub_audit_nick_name, b.audit_time as sub_audit_time, b.audit_dept_name as sub_audit_dept_name, b.audit_result as sub_audit_result, b.audit_reason as sub_audit_reason
            from tb_export_request a
            left join tb_request_audit b on b.request_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkTbExportRequestUnique" parameterType="TbExportRequest" resultMap="TbExportRequestResult">
        select id from tb_export_request where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbExportRequest" parameterType="TbExportRequest" useGeneratedKeys="true" keyProperty="id">
        insert into tb_export_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataType != null">data_type,</if>
            <if test="paramsStr != null">params_str,</if>
            <if test="dataCount != null">data_count,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="deptLevel != null">dept_level,</if>
            <if test="requestUnit != null">request_unit,</if>
            <if test="status != null">status,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="auditReason != null">audit_reason,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditNickName != null">audit_nick_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditDeptId != null">audit_dept_id,</if>
            <if test="auditLevel != null">audit_level,</if>
            <if test="exportStatus != null">export_status,</if>
            <if test="exportTime != null">export_time,</if>
            <if test="type != null">type,</if>
            <if test="reason != null">reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataType != null">#{dataType},</if>
            <if test="paramsStr != null">#{paramsStr},</if>
            <if test="dataCount != null">#{dataCount},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="deptLevel != null">#{deptLevel},</if>
            <if test="requestUnit != null">#{requestUnit},</if>
            <if test="status != null">#{status},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="auditReason != null">#{auditReason},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditNickName != null">#{auditNickName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditDeptId != null">#{auditDeptId},</if>
            <if test="auditLevel != null">#{auditLevel},</if>
            <if test="exportStatus != null">#{exportStatus},</if>
            <if test="exportTime != null">#{exportTime},</if>
            <if test="type != null">#{type},</if>
            <if test="reason != null">#{reason},</if>
        </trim>
    </insert>

    <update id="updateTbExportRequest" parameterType="TbExportRequest">
        update tb_export_request
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="paramsStr != null">params_str = #{paramsStr},</if>
            <if test="dataCount != null">data_count = #{dataCount},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="deptLevel != null">dept_level = #{deptLevel},</if>
            <if test="requestUnit != null">request_unit = #{requestUnit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditNickName != null">audit_nick_name = #{auditNickName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditDeptId != null">audit_dept_id = #{auditDeptId},</if>
            <if test="auditLevel != null">audit_level = #{auditLevel},</if>
            <if test="exportStatus != null">export_status = #{exportStatus},</if>
            <if test="exportTime != null">export_time = #{exportTime},</if>
            <if test="type != null">type = #{type},</if>
            <if test="reason != null">reason = #{reason},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbExportRequestById" parameterType="Long">
        delete from tb_export_request where id = #{id}
    </delete>

    <delete id="deleteTbExportRequestByIds" parameterType="String">
        delete from tb_export_request where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbRequestAuditByRequestIds" parameterType="String">
        delete from tb_request_audit where request_id in
        <foreach item="requestId" collection="array" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </delete>

    <delete id="deleteTbRequestAuditByRequestId" parameterType="Long">
        delete from tb_request_audit where request_id = #{requestId}
    </delete>

    <insert id="batchTbRequestAudit">
        insert into tb_request_audit( id, request_id, audit_user_id, audit_nick_name, audit_time, audit_dept_name,
        audit_result, audit_reason) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.requestId}, #{item.auditUserId}, #{item.auditNickName}, #{item.auditTime},
            #{item.auditDeptName}, #{item.auditResult}, #{item.auditReason})
        </foreach>
    </insert>
</mapper>