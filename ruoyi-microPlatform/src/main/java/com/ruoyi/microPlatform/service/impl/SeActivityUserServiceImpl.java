package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.microPlatform.domain.SeCommunityActivity;
import com.ruoyi.microPlatform.mapper.SeCommunityActivityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeActivityUserMapper;
import com.ruoyi.microPlatform.domain.SeActivityUser;
import com.ruoyi.microPlatform.service.ISeActivityUserService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报名者信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class SeActivityUserServiceImpl implements ISeActivityUserService {
    @Autowired
    private SeActivityUserMapper seActivityUserMapper;

    @Autowired
    private SeCommunityActivityMapper seCommunityActivityMapper;

    /**
     * 查询报名者信息
     *
     * @param id 报名者信息主键
     * @return 报名者信息
     */
    @Override
    public SeActivityUser selectSeActivityUserById(Long id) {
        return seActivityUserMapper.selectSeActivityUserById(id);
    }

    /**
     * 校验报名者信息是否存在
     *
     * @param seActivityUser
     * @return boolean
     */
    @Override
    public boolean checkSeActivityUser(SeActivityUser seActivityUser) {
        SeActivityUser old = seActivityUserMapper.checkSeActivityUserUnique(seActivityUser);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询报名者信息列表
     *
     * @param seActivityUser 报名者信息
     * @return 报名者信息
     */
    @Override
    public List<SeActivityUser> selectSeActivityUserList(SeActivityUser seActivityUser) {
        return seActivityUserMapper.selectSeActivityUserList(seActivityUser);
    }

    /**
     * 导入报名者信息
     *
     * @param infos           报名者信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeActivityUser(List<SeActivityUser> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入报名者信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeActivityUser info : infos) {
            try {
                // 验证是否存在这个数据
                SeActivityUser old = seActivityUserMapper.checkSeActivityUserUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seActivityUserMapper.insertSeActivityUser(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seActivityUserMapper.updateSeActivityUser(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增报名者信息
     *
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSeActivityUser(SeActivityUser seActivityUser) {
        int user = 0;
        //新增报名用户之前要先查看当前 要报名的活动 报名人数是否已满.
        SeCommunityActivity activity = seCommunityActivityMapper.selectSeCommunityActivityById(seActivityUser.getActivityId());
        if (activity.getSignNum() < activity.getNeedNum()) {
            //代表报名人数未满 可以继续报名
            //新增报名用户
            user = seActivityUserMapper.insertSeActivityUser(seActivityUser);
            //同时新增活动已经报名的人数
            SeCommunityActivity seCommunityActivity = new SeCommunityActivity();
            seCommunityActivity.setId(seActivityUser.getActivityId());
            seCommunityActivity.setInsetUserCount(true); //代表当前活动有人报名，报名人数要+1；
            seCommunityActivityMapper.updateSeCommunityActivity(seCommunityActivity);
        } else {
            throw new ServiceException("报名人数已达上限！");
        }
        return user;
    }

    /**
     * 修改报名者信息
     *
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    @Override
    public int updateSeActivityUser(SeActivityUser seActivityUser) {
        return seActivityUserMapper.updateSeActivityUser(seActivityUser);
    }

    /**
     * 批量删除报名者信息
     *
     * @param ids 需要删除的报名者信息主键
     * @return 结果
     */
    @Override
    public int deleteSeActivityUserByIds(Long[] ids) {
        return seActivityUserMapper.deleteSeActivityUserByIds(ids);
    }

    /**
     * 删除报名者信息信息
     *
     * @param id 报名者信息主键
     * @return 结果
     */
    @Override
    public int deleteSeActivityUserById(Long id) {
        return seActivityUserMapper.deleteSeActivityUserById(id);
    }
}
