<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbTypeInfoMapper">

    <resultMap type="TbTypeInfo" id="TbTypeInfoResult">
        <result property="id" column="id"/>
        <result property="oneCode" column="one_code"/>
        <result property="oneName" column="one_name"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentName" column="parent_name"/>
        <result property="text" column="text"/>
        <result property="value" column="value"/>
    </resultMap>

    <sql id="selectTbTypeInfoVo">
        select id, one_code, one_name, parent_code, parent_name from tb_type_info
    </sql>

    <select id="selectTbTypeInfoList" parameterType="TbTypeInfo" resultMap="TbTypeInfoResult">
        <include refid="selectTbTypeInfoVo"/>
        <where>
            <if test="oneCode != null  and oneCode != ''">and one_code = #{oneCode}</if>
            <if test="oneName != null  and oneName != ''">and one_name like concat('%', #{oneName}, '%')</if>
            <if test="parentCode != null  and parentCode != ''">and parent_code = #{parentCode}</if>
            <if test="parentName != null  and parentName != ''">and parent_name like concat('%', #{parentName}, '%')
            </if>
        </where>
    </select>

    <select id="selectTbTypeInfoList2" parameterType="TbTypeInfo" resultMap="TbTypeInfoResult">
        SELECT
            CONCAT(parent.one_name, '-', child.one_name) AS text,
            child.one_code AS value
        FROM
            tb_type_info child
        LEFT JOIN
            tb_type_info parent ON child.parent_code = parent.one_code
        WHERE
            child.parent_code != 0
        ORDER BY
            parent.one_code
    </select>

    <select id="selectTbTypeInfoById" parameterType="Integer" resultMap="TbTypeInfoResult">
        <include refid="selectTbTypeInfoVo"/>
        where id = #{id}
    </select>

    <select id="checkTbTypeInfoUnique" parameterType="TbTypeInfo" resultMap="TbTypeInfoResult">
        select id from tb_type_info where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbTypeInfo" parameterType="TbTypeInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_type_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oneCode != null">one_code,</if>
            <if test="oneName != null">one_name,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="parentName != null">parent_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oneCode != null">#{oneCode},</if>
            <if test="oneName != null">#{oneName},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="parentName != null">#{parentName},</if>
        </trim>
    </insert>

    <update id="updateTbTypeInfo" parameterType="TbTypeInfo">
        update tb_type_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="oneCode != null">one_code = #{oneCode},</if>
            <if test="oneName != null">one_name = #{oneName},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="parentName != null">parent_name = #{parentName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbTypeInfoById" parameterType="Integer">
        delete from tb_type_info where id = #{id}
    </delete>

    <delete id="deleteTbTypeInfoByIds" parameterType="String">
        delete from tb_type_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>