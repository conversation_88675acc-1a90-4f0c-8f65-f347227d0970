package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;

/**
 * 门店巡查基础检查项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbMerchantInspectDetailService 
{
    /**
     * 查询门店巡查基础检查项目
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 门店巡查基础检查项目
     */
    public TbMerchantInspectDetail selectTbMerchantInspectDetailById(Integer id);

    /**
     * 校验门店巡查基础检查项目是否存在
     *
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 门店巡查基础检查项目
     */
    public boolean checkTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 查询门店巡查基础检查项目列表
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 门店巡查基础检查项目集合
     */
    public List<TbMerchantInspectDetail> selectTbMerchantInspectDetailList(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 导入门店巡查基础检查项目
     *
     * @param infos       门店巡查基础检查项目列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantInspectDetail(List<TbMerchantInspectDetail> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    public int insertTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 修改门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    public int updateTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 批量删除门店巡查基础检查项目
     * 
     * @param ids 需要删除的门店巡查基础检查项目主键集合
     * @return 结果
     */
    public int deleteTbMerchantInspectDetailByIds(Integer[] ids);

    /**
     * 删除门店巡查基础检查项目信息
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 结果
     */
    public int deleteTbMerchantInspectDetailById(Integer id);



}
