package com.ruoyi.dataview.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbPetitionInfo;
import com.ruoyi.dataview.domain.vo.PetitionInfoStatisticsVO;
import com.ruoyi.dataview.domain.vo.PetitionInfoStatisticsVO;
import com.ruoyi.dataview.mapper.DbPetitionInfoMapper;
import com.ruoyi.dataview.service.IDbPetitionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 市信访局-上访信息数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbPetitionInfoServiceImpl implements IDbPetitionInfoService {
    @Resource
    private DbPetitionInfoMapper dbPetitionInfoMapper;

    /**
     * 查询市信访局-上访信息数据
     *
     * @param id 市信访局-上访信息数据主键
     * @return 市信访局-上访信息数据
     */
    @Override
    public DbPetitionInfo selectDbPetitionInfoById(Long id) {
        return dbPetitionInfoMapper.selectDbPetitionInfoById(id);
    }

    /**
     * 校验市信访局-上访信息数据是否存在
     *
     * @param dbPetitionInfo
     * @return boolean
     */
    @Override
    public boolean checkDbPetitionInfo(DbPetitionInfo dbPetitionInfo) {
        DbPetitionInfo old = dbPetitionInfoMapper.checkDbPetitionInfoUnique(dbPetitionInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询市信访局-上访信息数据列表
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 市信访局-上访信息数据
     */
    @Override
    public List<DbPetitionInfo> selectDbPetitionInfoList(DbPetitionInfo dbPetitionInfo) {
        return dbPetitionInfoMapper.selectDbPetitionInfoList(dbPetitionInfo);
    }

    /**
     * 导入市信访局-上访信息数据
     *
     * @param infos           市信访局-上访信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbPetitionInfo(List<DbPetitionInfo> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入市信访局-上访信息数据数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbPetitionInfo info : infos) {
            try {
                // 验证是否存在这个数据
                DbPetitionInfo old = dbPetitionInfoMapper.checkDbPetitionInfoUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbPetitionInfoMapper.insertDbPetitionInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbPetitionInfoMapper.updateDbPetitionInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    @Override
    public int insertDbPetitionInfo(DbPetitionInfo dbPetitionInfo) {
        dbPetitionInfo.setCreateTime(DateUtils.getNowDate());
        return dbPetitionInfoMapper.insertDbPetitionInfo(dbPetitionInfo);
    }

    /**
     * 修改市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    @Override
    public int updateDbPetitionInfo(DbPetitionInfo dbPetitionInfo) {
        dbPetitionInfo.setUpdateTime(DateUtils.getNowDate());
        return dbPetitionInfoMapper.updateDbPetitionInfo(dbPetitionInfo);
    }

    /**
     * 批量删除市信访局-上访信息数据
     *
     * @param ids 需要删除的市信访局-上访信息数据主键
     * @return 结果
     */
    @Override
    public int deleteDbPetitionInfoByIds(Long[] ids) {
        return dbPetitionInfoMapper.deleteDbPetitionInfoByIds(ids);
    }

    /**
     * 删除市信访局-上访信息数据信息
     *
     * @param id 市信访局-上访信息数据主键
     * @return 结果
     */
    @Override
    public int deleteDbPetitionInfoById(Long id) {
        return dbPetitionInfoMapper.deleteDbPetitionInfoById(id);
    }

    @Override
    public PetitionInfoStatisticsVO getStatistics() {
        // 创建返回对象
        PetitionInfoStatisticsVO vo = new PetitionInfoStatisticsVO();

        // 1. 获取信访总数
        Long total = dbPetitionInfoMapper.selectTotalCount();
        vo.setTotal(total != null ? total : 0L);

        // 2. 获取本月信访数
        Long monthTotal = dbPetitionInfoMapper.selectCurrentMonthCount();
        vo.setMonthTotal(monthTotal != null ? monthTotal : 0L);

        // 3. 获取按内容分类的统计信息
        List<CommonBaseCount> categoryStats = dbPetitionInfoMapper.selectCategoryStatistics();
        vo.setTypeCount(categoryStats);
        return vo;
    }

    /**
     * Date → LocalDateTime
     */
    private static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
