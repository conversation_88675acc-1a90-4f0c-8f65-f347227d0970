package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeCommunityActivity;
import com.ruoyi.microPlatform.domain.SeActivityUser;
import org.apache.ibatis.annotations.Param;

/**
 * 社区活动Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface SeCommunityActivityMapper 
{
    /**
     * 查询社区活动
     * 
     * @param id 社区活动主键
     * @return 社区活动
     */
    public SeCommunityActivity selectSeCommunityActivityById(Long id);

    /**
     * 校验社区活动是否唯一
     *
     * @param seCommunityActivity
     * @return 社区活动
     */
    public SeCommunityActivity checkSeCommunityActivityUnique(SeCommunityActivity seCommunityActivity);

    /**
     * 查询社区活动列表
     * 
     * @param seCommunityActivity 社区活动
     * @return 社区活动集合
     */
    public List<SeCommunityActivity> selectSeCommunityActivityList(SeCommunityActivity seCommunityActivity);

    /**
     * 新增社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    public int insertSeCommunityActivity(SeCommunityActivity seCommunityActivity);

    /**
     * 修改社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    public int  updateSeCommunityActivity(SeCommunityActivity seCommunityActivity);

    /**
     * 删除社区活动
     * 
     * @param id 社区活动主键
     * @return 结果
     */
    public int deleteSeCommunityActivityById(Long id);

    /**
     * 批量删除社区活动
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeCommunityActivityByIds(Long[] ids);

    /**
     * 批量删除报名者信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeActivityUserByActivityIds(Long[] ids);
    
    /**
     * 批量新增报名者信息
     * 
     * @param seActivityUserList 报名者信息列表
     * @return 结果
     */
    public int batchSeActivityUser(List<SeActivityUser> seActivityUserList);
    

    /**
     * 通过社区活动主键删除报名者信息信息
     * 
     * @param id 社区活动ID
     * @return 结果
     */
    public int deleteSeActivityUserByActivityId(Long id);

    List<SeCommunityActivity> selectSeCommunityActivityListByUserId(SeCommunityActivity seCommunityActivity);

    //查询表数据数量
    Map<String, Integer> selectSeCommunityActivityListSize(SeCommunityActivity seCommunityActivity);

    List<Map<String, Object>> getActivityStatistics(@Param("deptLevel") Integer deptLevel,@Param("county") String county,@Param("country") String country,@Param("taskStatusType") Integer taskStatusType);

    Integer getActivityTotal(@Param("deptLevel") Integer deptLevel,@Param("county") String county,@Param("country") String country,@Param("taskStatusType") Integer taskStatusType);
}
