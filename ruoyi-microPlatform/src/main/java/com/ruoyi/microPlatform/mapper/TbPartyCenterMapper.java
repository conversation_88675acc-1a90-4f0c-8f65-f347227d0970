package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbPartyCenter;

/**
 * 党群服务中心报到情况Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbPartyCenterMapper 
{
    /**
     * 查询党群服务中心报到情况
     * 
     * @param id 党群服务中心报到情况主键
     * @return 党群服务中心报到情况
     */
    public TbPartyCenter selectTbPartyCenterById(Long id);

    /**
     * 校验党群服务中心报到情况是否唯一
     *
     * @param tbPartyCenter
     * @return 党群服务中心报到情况
     */
    public TbPartyCenter checkTbPartyCenterUnique(TbPartyCenter tbPartyCenter);

    /**
     * 查询党群服务中心报到情况列表
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 党群服务中心报到情况集合
     */
    public List<TbPartyCenter> selectTbPartyCenterList(TbPartyCenter tbPartyCenter);

    /**
     * 新增党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    public int insertTbPartyCenter(TbPartyCenter tbPartyCenter);

    /**
     * 修改党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    public int updateTbPartyCenter(TbPartyCenter tbPartyCenter);

    /**
     * 删除党群服务中心报到情况
     * 
     * @param id 党群服务中心报到情况主键
     * @return 结果
     */
    public int deleteTbPartyCenterById(Long id);

    /**
     * 批量删除党群服务中心报到情况
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbPartyCenterByIds(Long[] ids);
}
