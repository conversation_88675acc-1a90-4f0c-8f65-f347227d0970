package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbBpmNodeJumpConvert;
import com.ruoyi.dataview.domain.dto.DbBpmNodeJumpFormDTO;
import com.ruoyi.dataview.domain.dto.DbBpmNodeJumpQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;
import com.ruoyi.dataview.service.IDbBpmNodeJumpService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运管服案件流程流转记录Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/fiveLevel/bpmNodeJump")
@RequiredArgsConstructor
public class DbBpmNodeJumpController extends BaseController
{
    private final IDbBpmNodeJumpService dbBpmNodeJumpService;

    private final DbBpmNodeJumpConvert bpmNodeJumpConvert;

    /**
     * 查询运管服案件流程流转记录列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbBpmNodeJumpQueryDTO bpmNodeJumpQueryDTO)
    {
        DbBpmNodeJump dbBpmNodeJump = bpmNodeJumpConvert.toDO(bpmNodeJumpQueryDTO);
        startPage();
        return getDataTable(bpmNodeJumpConvert.toVOList(dbBpmNodeJumpService.selectDbBpmNodeJumpList(dbBpmNodeJump)));
    }

    /**
     * 导出运管服案件流程流转记录列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:export')")
    @Log(title = "运管服案件流程流转记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbBpmNodeJumpQueryDTO bpmNodeJumpQueryDTO)
    {
        DbBpmNodeJump dbBpmNodeJump = bpmNodeJumpConvert.toDO(bpmNodeJumpQueryDTO);
        List<DbBpmNodeJump> list = dbBpmNodeJumpService.selectDbBpmNodeJumpList(dbBpmNodeJump);
        ExcelUtil<DbBpmNodeJump> util = new ExcelUtil<DbBpmNodeJump>(DbBpmNodeJump.class);
        util.exportExcel(response, list, "运管服案件流程流转记录数据");
    }

    @Log(title = "运管服案件流程流转记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbBpmNodeJump> util = new ExcelUtil<DbBpmNodeJump>(DbBpmNodeJump.class);
        List<DbBpmNodeJump> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbBpmNodeJumpService.importDbBpmNodeJump(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbBpmNodeJump> util = new ExcelUtil<DbBpmNodeJump>(DbBpmNodeJump.class);
        util.exportTemplateExcel(response, "运管服案件流程流转记录数据");
    }

    /**
     * 获取运管服案件流程流转记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(bpmNodeJumpConvert.toVO(dbBpmNodeJumpService.selectDbBpmNodeJumpById(id)));
    }

    /**
     * 新增运管服案件流程流转记录
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:add')")
    @Log(title = "运管服案件流程流转记录", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbBpmNodeJumpFormDTO bpmNodeJumpFormDTO)
    {
        DbBpmNodeJump dbBpmNodeJump = bpmNodeJumpConvert.toDO(bpmNodeJumpFormDTO);
        dbBpmNodeJump.setCreateBy(getUsername());
        /*if (dbBpmNodeJumpService.checkDbBpmNodeJump(dbBpmNodeJump)){
            return error("数据已存在！");
        }*/
        return toAjax(dbBpmNodeJumpService.insertDbBpmNodeJump(dbBpmNodeJump));
    }

    /**
     * 修改运管服案件流程流转记录
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:edit')")
    @Log(title = "运管服案件流程流转记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbBpmNodeJumpFormDTO bpmNodeJumpFormDTO)
    {
        DbBpmNodeJump dbBpmNodeJump = bpmNodeJumpConvert.toDO(bpmNodeJumpFormDTO);
        dbBpmNodeJump.setUpdateBy(getUsername());
        /*if (dbBpmNodeJumpService.checkDbBpmNodeJump(dbBpmNodeJump)){
            return error("数据已存在！");
        }*/
        return toAjax(dbBpmNodeJumpService.updateDbBpmNodeJump(dbBpmNodeJump));
    }

    /**
     * 删除运管服案件流程流转记录
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:bpmNodeJump:remove')")
    @Log(title = "运管服案件流程流转记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbBpmNodeJumpService.deleteDbBpmNodeJumpByIds(ids));
    }
}
