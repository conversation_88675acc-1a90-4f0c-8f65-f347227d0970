package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.SeHeartPromise;

/**
 * 心灵有约Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeHeartPromiseService 
{
    /**
     * 查询心灵有约
     * 
     * @param id 心灵有约主键
     * @return 心灵有约
     */
    public SeHeartPromise selectSeHeartPromiseById(Long id);

    /**
     * 校验心灵有约是否存在
     *
     * @param seHeartPromise 心灵有约
     * @return 心灵有约
     */
    public boolean checkSeHeartPromise(SeHeartPromise seHeartPromise);

    /**
     * 查询心灵有约列表
     * 
     * @param seHeartPromise 心灵有约
     * @return 心灵有约集合
     */
    public List<SeHeartPromise> selectSeHeartPromiseList(SeHeartPromise seHeartPromise);

    /**
     * 导入心灵有约
     *
     * @param infos       心灵有约列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeHeartPromise(List<SeHeartPromise> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    public int insertSeHeartPromise(SeHeartPromise seHeartPromise);

    /**
     * 修改心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    public int updateSeHeartPromise(SeHeartPromise seHeartPromise);

    /**
     * 批量删除心灵有约
     * 
     * @param ids 需要删除的心灵有约主键集合
     * @return 结果
     */
    public int deleteSeHeartPromiseByIds(Long[] ids);

    /**
     * 删除心灵有约信息
     * 
     * @param id 心灵有约主键
     * @return 结果
     */
    public int deleteSeHeartPromiseById(Long id);
}
