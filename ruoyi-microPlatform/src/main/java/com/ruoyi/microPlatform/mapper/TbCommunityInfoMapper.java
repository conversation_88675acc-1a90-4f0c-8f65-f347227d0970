package com.ruoyi.microPlatform.mapper;

import java.util.List;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbCommunityInfo;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;

/**
 * 小区信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbCommunityInfoMapper 
{
    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    public TbCommunityInfo selectTbCommunityInfoById(Long id);

    /**
     * 校验小区信息是否唯一
     *
     * @param tbCommunityInfo
     * @return 小区信息
     */
    public TbCommunityInfo checkTbCommunityInfoUnique(TbCommunityInfo tbCommunityInfo);

    /**
     * 查询小区信息列表
     * 
     * @param tbCommunityInfo 小区信息
     * @return 小区信息集合
     */
    public List<TbCommunityInfo> selectTbCommunityInfoList(TbCommunityInfo tbCommunityInfo);

    public List<Long> selectTbCommunityInfoCount(TbCommunityInfo tbCommunityInfo);

    /**
     * 新增小区信息
     * 
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    public int insertTbCommunityInfo(TbCommunityInfo tbCommunityInfo);

    /**
     * 修改小区信息
     * 
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    public int updateTbCommunityInfo(TbCommunityInfo tbCommunityInfo);

    /**
     * 删除小区信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    public int deleteTbCommunityInfoById(Long id);

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbCommunityInfoByIds(Long[] ids);


    List<TbCommunityInfo> selectTbCommunityInfoSpaceList(LargeScreenInfo largeScreenInfo);

    TbCommunityInfo getCommunityInfo(LargeScreenInfo largeScreenInfo);
}
