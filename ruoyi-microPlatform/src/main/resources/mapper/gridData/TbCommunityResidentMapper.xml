<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbCommunityResidentMapper">

    <resultMap type="TbCommunityResident" id="TbCommunityResidentResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="communityId" column="community_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="communityName" column="community_name"/>
        <result property="buildingNum" column="building_num"/>
        <result property="unitNum" column="unit_num"/>
        <result property="floorNum" column="floor_num"/>
        <result property="roomNum" column="room_num"/>
        <result property="residenceArea" column="residence_area"/>
        <result property="populationNum" column="population_num"/>
        <result property="phone" column="phone"/>
        <result property="householder" column="householder"/>
        <result property="residentialSituation" column="residential_situation"/>
        <result property="familySituation" column="family_situation"/>
        <result property="difficulty" column="difficulty"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="sort" column="sort"/>
        <result property="geometry" column="geometry"/>
    </resultMap>

    <resultMap id="TbCommunityResidentMemberResult" type="TbCommunityResident" extends="TbCommunityResidentResult">
        <collection property="list"  javaType="java.util.List" resultMap="TbResidentMemberResult" />
    </resultMap>

    <resultMap type="TbResidentMember" id="TbResidentMemberResult">
        <result property="id" column="me_id"/>
        <result property="residentId" column="resident_id"/>
        <result property="householdRelation" column="household_relation"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="nation" column="nation"/>
        <result property="birthday" column="birthday" jdbcType="DATE"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="educationBackground" column="education_background"/>
        <result property="registerResidence" column="register_residence"/>
        <result property="maritalStatus" column="marital_status"/>
        <result property="idCard" column="id_card"/>
        <result property="jobUnit" column="job_unit"/>
        <result property="partyOrganizationLocation" column="party_organization_location"/>
        <result property="talent" column="talent"/>
        <result property="phone" column="me_phone"/>
        <result property="oldPhone" column="me_phone"/>
        <result property="remark" column="me_remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="personLabel" column="person_label"/>
        <result property="age" column="age"/>
        <collection property="personTypes" javaType="java.util.List" ofType="TbPersonType" resultMap="TypeResult"/>
    </resultMap>

    <resultMap id="TypeResult" type="TbPersonType">
        <id property="id" column="pt_id"/>
        <result property="label" column="pt_label"/>
        <result property="value" column="pt_id"/>
        <result property="text" column="pt_label"/>
    </resultMap>


    <sql id="selectTbCommunityResidentVo">
        select id, dept_id, community_id, county, country, town, grid_name, grid_id, community_name,phone, building_num, unit_num, floor_num, room_num, residence_area, population_num, householder, residential_situation, family_situation, difficulty, remark, create_by, create_time, update_by, update_time, status from tb_community_resident
    </sql>

    <sql id="selectTbCommunityResidentAndResidentMember">
        select co.id, co.dept_id, co.community_id, co.county, co.country, co.town, co.grid_name,
               co.grid_id, co.community_name,co.phone, co.building_num, co.unit_num,
               co.floor_num, co.room_num, co.residence_area, co.population_num, co.householder,
               co.residential_situation, co.family_situation, co.difficulty, co.remark,
               co.create_by, co.create_time, co.update_by, co.update_time, co.status,
               me.id as me_id,me.resident_id,
               me.household_relation,me.name,me.sex,me.nation,me.birthday,me.political_status,
               me.education_background,me.register_residence,me.marital_status,me.id_card,
               me.job_unit,me.party_organization_location,me.talent,me.phone as me_phone ,me.remark as me_remark,
               me.person_label,
               IFNULL(TIMESTAMPDIFF(YEAR, me.birthday, CURDATE()), null) AS age
        from tb_community_resident co left join tb_resident_member me
        on co.id = me.resident_id
    </sql>
<!--    <sql id="selectTbCommunityResidentAndResidentMember">-->
<!--        select co.id, co.dept_id, co.community_id, co.county, co.country, co.town, co.grid_name,-->
<!--               co.grid_id, co.community_name,co.phone, co.building_num, co.unit_num,-->
<!--               co.floor_num, co.room_num, co.residence_area, co.population_num, co.householder,-->
<!--               co.residential_situation, co.family_situation, co.difficulty, co.remark,-->
<!--               co.create_by, co.create_time, co.update_by, co.update_time, co.status,-->
<!--               me.id as me_id,me.resident_id,-->
<!--               me.household_relation,me.name,me.sex,me.nation,me.birthday,me.political_status,-->
<!--               me.education_background,me.register_residence,me.marital_status,me.id_card,-->
<!--               me.job_unit,me.party_organization_location,me.talent,me.phone as me_phone ,me.remark as me_remark,-->
<!--               me.person_label-->
<!--        from tb_community_resident co left join tb_resident_member me-->
<!--        LEFT JOIN (-->
<!--            SELECT-->
<!--                rmt.member_id,-->
<!--                GROUP_CONCAT(DISTINCT pt.label SEPARATOR ',') AS person_type-->
<!--            FROM-->
<!--                tb_resident_member_type rmt-->
<!--            JOIN-->
<!--                tb_person_type pt ON rmt.type_id = pt.id-->
<!--            GROUP BY-->
<!--                rmt.member_id-->
<!--        ) agg ON me.id = agg.member_id-->
<!--        on co.id = me.resident_id-->
<!--    </sql>-->

    <sql id="queryTbCommunityResidentVo">
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_community_resident.dept_id))</if>
            <if test="communityId != null ">and community_id = #{communityId}</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="communityName != null  and communityName != ''">and community_name like concat('%',
                #{communityName}, '%')
            </if>
            <if test="buildingNum != null  and buildingNum != ''">and building_num = #{buildingNum}</if>
            <if test="unitNum != null  and unitNum != ''">and unit_num = #{unitNum}</if>
            <if test="floorNum != null  and floorNum != ''">and floor_num = #{floorNum}</if>
            <if test="roomNum != null  and roomNum != ''">and room_num = #{roomNum}</if>
            <if test="residenceArea != null  and residenceArea != ''">and residence_area = #{residenceArea}</if>
            <if test="populationNum != null ">and population_num = #{populationNum}</if>
            <if test="householder != null  and householder != ''">and householder = #{householder}</if>
            <if test="residentialSituation != null  and residentialSituation != ''">and residential_situation =
                #{residentialSituation}
            </if>
            <if test="familySituation != null  and familySituation != ''">and family_situation = #{familySituation}</if>
            <if test="difficulty != null ">and difficulty = #{difficulty}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="name != null  and name != ''">and id in (select distinct resident_id from tb_resident_member where name = #{name})</if>
            <if test="phoneUser != null  and phoneUser != ''">and id in (select distinct resident_id from tb_resident_member where phone = #{phoneUser})</if>
            <if test="idCard != null  and idCard != ''">and id in (select distinct resident_id from tb_resident_member where id_card = #{idCard})</if>
            <if test="registerResidence != null  and registerResidence != ''">and id in (select distinct resident_id from tb_resident_member where register_residence = #{registerResidence})</if>
        </where>
    </sql>
    <select id="selectTbCommunityResidentList" parameterType="TbCommunityResident"
            resultMap="TbCommunityResidentResult">
        SELECT r1.id, r1.dept_id, r1.community_id, r1.county, r1.country, r1.town, r1.grid_name, r1.grid_id, r1.community_name,
        r1.phone, r1.building_num, r1.unit_num, r1.floor_num, r1.room_num, r1.residence_area, r1.population_num, r1.householder,
        r1.residential_situation, r1.family_situation, r1.difficulty, r1.remark, r1.create_by, r1.create_time, r1.update_by, r1.update_time,
        r1.status
        FROM tb_community_resident r1
        JOIN (
            SELECT r.id
            FROM tb_community_resident r
            <if test="country == null">
                USE INDEX (idx_county_sort_id)
            </if>
            <if test="name != null">
                INNER JOIN tb_resident_member m_name ON m_name.resident_id = r.id AND m_name.name = #{name}
            </if>
            <if test="phoneUser != null">
                INNER JOIN tb_resident_member m_phone ON m_phone.resident_id = r.id AND m_phone.phone = #{phoneUser}
            </if>
            <if test="idCard != null">
                INNER JOIN tb_resident_member m_idcard ON m_idcard.resident_id = r.id AND m_idcard.id_card = #{idCard}
            </if>
            <where>
                <if test="communityId != null ">AND r.community_id = #{communityId}</if>
                <if test="county != null and county != ''">AND r.county = #{county}</if>
                <if test="country != null and country != ''">AND r.country = #{country}</if>
                <if test="town != null and town != ''">AND r.town = #{town}</if>
                <if test="gridName != null and gridName != ''">AND r.grid_name LIKE CONCAT('%', #{gridName}, '%')</if>
                <if test="gridId != null ">AND r.grid_id = #{gridId}</if>
                <if test="gridArr != null and gridArr != ''">
                    AND r.grid_id IN
                    <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="communityName != null and communityName != ''">AND r.community_name LIKE CONCAT('%', #{communityName}, '%')</if>
                <if test="buildingNum != null and buildingNum != ''">AND r.building_num = #{buildingNum}</if>
                <if test="unitNum != null and unitNum != ''">AND r.unit_num = #{unitNum}</if>
                <if test="floorNum != null and floorNum != ''">AND r.floor_num = #{floorNum}</if>
                <if test="roomNum != null and roomNum != ''">AND r.room_num = #{roomNum}</if>
                <if test="residenceArea != null and residenceArea != ''">AND r.residence_area = #{residenceArea}</if>
                <if test="populationNum != null">AND r.population_num = #{populationNum}</if>
                <if test="householder != null and householder != ''">AND r.householder LIKE CONCAT('%', #{householder}, '%')</if>
                <if test="residentialSituation != null and residentialSituation != ''">AND r.residential_situation = #{residentialSituation}</if>
                <if test="familySituation != null and familySituation != ''">AND r.family_situation = #{familySituation}</if>
                <if test="difficulty != null">AND r.difficulty = #{difficulty}</if>
                <if test="status != null and status != ''">AND r.status = #{status}</if>
            </where>
            ORDER BY r.sort ASC, r.id DESC
            LIMIT #{offset}, #{pageSize}
        ) page_ids ON r1.id = page_ids.id
    </select>

    <select id="selectTbCommunityResidentCount" parameterType="TbCommunityResident"
            resultType="long">
        select id from tb_community_resident
        <include refid="queryTbCommunityResidentVo"/>
    </select>

    <select id="selectTbCommunityResidentById" parameterType="Long" resultMap="TbCommunityResidentMemberResult">
        select co.id, co.dept_id, co.community_id, co.county, co.country, co.town, co.grid_name,
               co.grid_id, co.community_name,co.phone, co.building_num, co.unit_num,
               co.floor_num, co.room_num, co.residence_area, co.population_num, co.householder,
               co.residential_situation, co.family_situation, co.difficulty, co.remark,
               co.create_by, co.create_time, co.update_by, co.update_time, co.status,
               me.id as me_id,me.resident_id,
               me.household_relation,me.name,me.sex,me.nation,me.birthday,me.political_status,
               me.education_background,me.register_residence,me.marital_status,me.id_card,
               me.job_unit,me.party_organization_location,me.talent,me.phone as me_phone ,me.remark as me_remark,
               pt.id as pt_id, pt.label as pt_label,
               IFNULL(TIMESTAMPDIFF(YEAR, me.birthday, CURDATE()), null) AS age
        from tb_community_resident co
            left join tb_resident_member me on co.id = me.resident_id
            left join tb_resident_member_type rmt on me.id = rmt.member_id
		    left join tb_person_type pt on pt.id = rmt.type_id
        where co.id = #{id}
    </select>

    <select id="selectTbCommunityResidentById2" parameterType="Long" resultMap="TbCommunityResidentMemberResult">
        select co.id, co.dept_id, co.community_id, co.county, co.country, co.town, co.grid_name,
               co.grid_id, co.community_name,co.phone, co.building_num, co.unit_num,
               co.floor_num, co.room_num, co.residence_area, co.population_num, co.householder,
               co.residential_situation, co.family_situation, co.difficulty, co.remark,
               co.create_by, co.create_time, co.update_by, co.update_time, co.status
        from tb_community_resident co
        where co.id = #{id}
    </select>

    <select id="checkTbCommunityResidentUnique" parameterType="TbCommunityResident"
            resultMap="TbCommunityResidentResult">
        select id from tb_community_resident where dept_id = #{deptId} and grid_id = #{gridId} and community_id = #{communityId} and room_num = #{roomNum}
        <if test="buildingNum != null">
            and building_num = #{buildingNum}
        </if>
        <if test="floorNum != null">
            and floor_num = #{floorNum}
        </if>
        <if test="unitNum != null">
            and unit_num = #{unitNum}
        </if>
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <insert id="insertTbCommunityResident" parameterType="TbCommunityResident" useGeneratedKeys="true" keyProperty="id">
        insert into tb_community_resident
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="communityName != null">community_name,</if>
            <if test="buildingNum != null">building_num,</if>
            <if test="unitNum != null">unit_num,</if>
            <if test="floorNum != null">floor_num,</if>
            <if test="roomNum != null">room_num,</if>
            <if test="residenceArea != null">residence_area,</if>
            <if test="populationNum != null">population_num,</if>
            <if test="householder != null">householder,</if>
            <if test="residentialSituation != null">residential_situation,</if>
            <if test="familySituation != null">family_situation,</if>
            <if test="difficulty != null">difficulty,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="phone != null">phone,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="buildingNum != null">#{buildingNum},</if>
            <if test="unitNum != null">#{unitNum},</if>
            <if test="floorNum != null">#{floorNum},</if>
            <if test="roomNum != null">#{roomNum},</if>
            <if test="residenceArea != null">#{residenceArea},</if>
            <if test="populationNum != null">#{populationNum},</if>
            <if test="householder != null">#{householder},</if>
            <if test="residentialSituation != null">#{residentialSituation},</if>
            <if test="familySituation != null">#{familySituation},</if>
            <if test="difficulty != null">#{difficulty},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="phone != null">#{phone},</if>
            <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateTbCommunityResident" parameterType="TbCommunityResident">
        update tb_community_resident
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="buildingNum != null">building_num = #{buildingNum},</if>
            <if test="unitNum != null">unit_num = #{unitNum},</if>
            <if test="floorNum != null">floor_num = #{floorNum},</if>
            <if test="roomNum != null">room_num = #{roomNum},</if>
            <if test="residenceArea != null">residence_area = #{residenceArea},</if>
            <if test="populationNum != null">population_num = #{populationNum},</if>
            <if test="householder != null">householder = #{householder},</if>
            <if test="residentialSituation != null">residential_situation = #{residentialSituation},</if>
            <if test="familySituation != null">family_situation = #{familySituation},</if>
            <if test="difficulty != null">difficulty = #{difficulty},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbCommunityResidentById" parameterType="Long">
        delete from tb_community_resident where id = #{id}
    </delete>

    <delete id="deleteTbCommunityResidentByIds" parameterType="String">
        delete from tb_community_resident where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPersonCount" parameterType="String" resultType="integer">
        select count(*) from tb_community_resident where community_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectTbCommunityResidentAndMemberList" resultMap="TbCommunityResidentMemberResult">
        <include refid="selectTbCommunityResidentAndResidentMember"/>
        <where>
            <if test="deptId != null ">and (co.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = co.dept_id))</if>
            <if test="communityId != null ">and co.community_id = #{communityId}</if>
            <if test="county != null  and county != ''">and co.county = #{county}</if>
            <if test="country != null  and country != ''">and co.country = #{country}</if>
            <if test="town != null  and town != ''">and co.town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and co.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and co.grid_id = #{gridId}</if>
            <if test="communityName != null  and communityName != ''">and co.community_name like concat('%',
                #{communityName}, '%')
            </if>
            <if test="buildingNum != null  and buildingNum != ''">and co.building_num = #{buildingNum}</if>
            <if test="unitNum != null  and unitNum != ''">and co.unit_num = #{unitNum}</if>
            <if test="floorNum != null  and floorNum != ''">and co.floor_num = #{floorNum}</if>
            <if test="roomNum != null  and roomNum != ''">and co.room_num = #{roomNum}</if>
            <if test="residenceArea != null  and residenceArea != ''">and co.residence_area = #{residenceArea}</if>
            <if test="populationNum != null ">and co.population_num = #{populationNum}</if>
            <if test="householder != null  and householder != ''">and co.householder = #{householder}</if>
            <if test="residentialSituation != null  and residentialSituation != ''">and co.residential_situation =
                #{residentialSituation}
            </if>
            <if test="familySituation != null  and familySituation != ''">and co.family_situation = #{familySituation}</if>
            <if test="difficulty != null  and difficulty != ''">and co.difficulty = #{difficulty}</if>
            <if test="status != null  and status != ''">and co.status = #{status}</if>
            <if test="name != null  and name != ''">and co.id in (select distinct resident_id from tb_resident_member where name = #{name})</if>
            <if test="phoneUser != null  and phoneUser != ''">and co.id in (select distinct resident_id from tb_resident_member where phone = #{phoneUser})</if>
            <if test="idCard != null  and idCard != ''">and co.id in (select distinct resident_id from tb_resident_member where id_card = #{idCard})</if>
        </where>
    </select>

    <select id="selectTbCommunityResidentSpaceList" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbCommunityResidentResult">
        select co.id, co.dept_id, co.community_id, co.county, co.country, co.town, co.grid_name,
               co.grid_id, co.community_name,co.phone, co.building_num, co.unit_num,
               co.floor_num, co.room_num, co.residence_area, co.population_num, co.householder,
               co.residential_situation, co.family_situation, co.difficulty, co.remark,
               co.create_by, co.create_time, co.update_by, co.update_time, co.status,
               ci.geometry
        from tb_community_resident co
        left join tb_community_info ci
        on co.community_id = ci.id
        <where>
            ci.geometry IS NOT NULL and ci.geometry != ''
            <if test="deptId != null ">and (co.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = co.dept_id))</if>
        </where>
    </select>

    <select id="selectTypeList" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_community_resident
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_community_resident
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_community_resident where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList2" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_resident_member
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_resident_member
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_resident_member where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList3" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_merchant_info
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_merchant_info
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_merchant_info where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList4" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    m.county AS label,
                    COUNT(m.county) AS count
                FROM
                    tb_merchant_assistant ma left join tb_merchant_info m on ma.merchant_id = m.id
                GROUP BY m.county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                SELECT
                    m.country AS label,
                    COUNT(m.country) AS count
                FROM
                    tb_merchant_assistant ma left join tb_merchant_info m on ma.merchant_id = m.id
                WHERE m.county = #{county}
                GROUP BY m.country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT m.town as label, count(m.town) as count, m.dept_id as deptId from tb_merchant_assistant ma
            left join tb_merchant_info m on ma.merchant_id = m.id
            where m.county = #{county} and m.country = #{country} group by m.town, m.dept_id order by m.dept_id
        </if>
    </select>
    <select id="selectTypeList5" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_grid_info
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_grid_info
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_grid_info where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList6" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_grid_inspect
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_grid_inspect
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_grid_inspect where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList7" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_merchant_inspect
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_merchant_inspect
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_merchant_inspect where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList8" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_resident_contact
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_resident_contact
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_resident_contact where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList9" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    se_public_sentiment
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    se_public_sentiment
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from se_public_sentiment where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList10" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_issue_info
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_issue_info
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_issue_info where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList11" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    se_function_reservation
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    se_function_reservation
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from se_function_reservation where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList12" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    tb_community_info
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    tb_community_info
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from tb_community_info where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList13" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(DISTINCT phone) AS count
                FROM
                    tb_grid_user
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(DISTINCT phone) AS count
                FROM
                    tb_grid_user
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(DISTINCT phone) as count, dept_id as deptId from tb_grid_user where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList15" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    se_community_function
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    se_community_function
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from se_community_function where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList16" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    se_convenient_phone
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    se_convenient_phone
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from se_convenient_phone where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList17" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    county AS label,
                    COUNT(county) AS count
                FROM
                    se_volunteer_register
                GROUP BY county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    country AS label,
                    COUNT(country) AS count
                FROM
                    se_volunteer_register
                WHERE county = #{county}
                GROUP BY country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT town as label, count(town) as count, dept_id as deptId from se_volunteer_register where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>
    <select id="selectTypeList14" resultType="CountyStatics">
        <if test="deptLevelType == 4">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
            FROM (
                -- 先计算每个 county 的数量
                SELECT
                    sg.county AS label,
                    COUNT(DISTINCT sgu.phone) AS count
                FROM
                    tb_sideline_grid_info sg left join tb_sideline_grid_user sgu on sgu.id = sg.side_user_id
                GROUP BY sg.county
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name AND d.dept_level = 5
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 5">
            SELECT
            t.label,
            t.count,
            d.dept_id as deptId
                FROM (
                    SELECT
                    sg.country AS label,
                    COUNT(DISTINCT sgu.phone) AS count
                FROM
                    tb_sideline_grid_info sg left join tb_sideline_grid_user sgu on sgu.id = sg.side_user_id
                WHERE sg.county = #{county}
                GROUP BY sg.country
            ) t
            LEFT JOIN sys_dept d ON t.label = d.dept_name and d.county = #{county} AND d.dept_level = 6
            ORDER BY d.dept_id
        </if>
        <if test="deptLevelType == 6">
            SELECT sg.town as label, count(DISTINCT sgu.phone) as count, dept_id as deptId from tb_grid_user where county = #{county} and country = #{country} group by town, dept_id order by dept_id
        </if>
    </select>

    <select id="countResidentList" resultType="int">
        SELECT COUNT(DISTINCT r.id)
        FROM tb_community_resident r
        <if test="name != null">
            INNER JOIN tb_resident_member m_name ON m_name.resident_id = r.id AND m_name.name = #{name}
        </if>
        <if test="phoneUser != null">
            INNER JOIN tb_resident_member m_phone ON m_phone.resident_id = r.id AND m_phone.phone = #{phoneUser}
        </if>
        <if test="idCard != null">
            INNER JOIN tb_resident_member m_idcard ON m_idcard.resident_id = r.id AND m_idcard.id_card = #{idCard}
        </if>
        <where>
            <if test="communityId != null ">AND r.community_id = #{communityId}</if>
            <if test="county != null and county != ''">AND r.county = #{county}</if>
            <if test="country != null and country != ''">AND r.country = #{country}</if>
            <if test="town != null and town != ''">AND r.town = #{town}</if>
            <if test="gridName != null and gridName != ''">AND r.grid_name LIKE CONCAT('%', #{gridName}, '%')</if>
            <if test="gridId != null ">AND r.grid_id = #{gridId}</if>
            <if test="gridArr != null and gridArr != ''">
                AND r.grid_id IN
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="communityName != null and communityName != ''">AND r.community_name LIKE CONCAT('%', #{communityName}, '%')</if>
            <if test="buildingNum != null and buildingNum != ''">AND r.building_num = #{buildingNum}</if>
            <if test="unitNum != null and unitNum != ''">AND r.unit_num = #{unitNum}</if>
            <if test="floorNum != null and floorNum != ''">AND r.floor_num = #{floorNum}</if>
            <if test="roomNum != null and roomNum != ''">AND r.room_num = #{roomNum}</if>
            <if test="residenceArea != null and residenceArea != ''">AND r.residence_area = #{residenceArea}</if>
            <if test="populationNum != null">AND r.population_num = #{populationNum}</if>
            <if test="householder != null and householder != ''">AND r.householder LIKE CONCAT('%', #{householder}, '%')</if>
            <if test="residentialSituation != null and residentialSituation != ''">AND r.residential_situation = #{residentialSituation}</if>
            <if test="familySituation != null and familySituation != ''">AND r.family_situation = #{familySituation}</if>
            <if test="difficulty != null">AND r.difficulty = #{difficulty}</if>
            <if test="status != null and status != ''">AND r.status = #{status}</if>
        </where>
    </select>
</mapper>