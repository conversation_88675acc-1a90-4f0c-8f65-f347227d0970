package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbDeptUserGrid;
import com.ruoyi.microPlatform.service.ITbDeptUserGridService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 社区干部关联网格Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/deptUserGrid")
public class TbDeptUserGridController extends BaseController
{
    @Autowired
    private ITbDeptUserGridService tbDeptUserGridService;

    /**
     * 查询社区干部关联网格列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbDeptUserGrid tbDeptUserGrid)
    {
        startPage();
        List<TbDeptUserGrid> list = tbDeptUserGridService.selectTbDeptUserGridList(tbDeptUserGrid);
        return getDataTable(list);
    }

    /**
     * 导出社区干部关联网格列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUserGrid:export')")
    @Log(title = "社区干部关联网格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbDeptUserGrid tbDeptUserGrid)
    {
        List<TbDeptUserGrid> list = tbDeptUserGridService.selectTbDeptUserGridList(tbDeptUserGrid);
        ExcelUtil<TbDeptUserGrid> util = new ExcelUtil<TbDeptUserGrid>(TbDeptUserGrid.class);
        util.exportExcel(response, list, "社区干部关联网格数据");
    }

    @Log(title = "社区干部关联网格", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:deptUserGrid:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbDeptUserGrid> util = new ExcelUtil<TbDeptUserGrid>(TbDeptUserGrid.class);
        List<TbDeptUserGrid> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbDeptUserGridService.importTbDeptUserGrid(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbDeptUserGrid> util = new ExcelUtil<TbDeptUserGrid>(TbDeptUserGrid.class);
        util.exportTemplateExcel(response, "社区干部关联网格数据");
    }

    /**
     * 获取社区干部关联网格详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUserGrid:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbDeptUserGridService.selectTbDeptUserGridById(id));
    }

    /**
     * 新增社区干部关联网格
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUserGrid:add')")
    @Log(title = "社区干部关联网格", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbDeptUserGrid tbDeptUserGrid)
    {
        tbDeptUserGrid.setCreateBy(getUsername());
        if (tbDeptUserGridService.checkTbDeptUserGrid(tbDeptUserGrid)){
            return error("社区干部已关联该网格已存在！");
        }
        return toAjax(tbDeptUserGridService.insertTbDeptUserGrid(tbDeptUserGrid));
    }

    @Log(title = "社区干部关联网格", businessType = BusinessType.INSERT)
    @PostMapping("addList")
    @RepeatSubmit
    public AjaxResult addList(@RequestBody TbDeptUserGrid tbDeptUserGrid)
    {
        if (ObjectUtil.isEmpty(tbDeptUserGrid.getIdList()) || tbDeptUserGrid.getIdList().size() == 0){
            return error("不可为空！");
        }
        if (ObjectUtil.isEmpty(tbDeptUserGrid.getGridId())){
            return error("网格ID不可为空！");
        }
        for (int i = 0; i < tbDeptUserGrid.getIdList().size(); i++) {
            tbDeptUserGrid.setDeptUserId(tbDeptUserGrid.getIdList().get(i));
            tbDeptUserGrid.setCreateBy(getUsername());
            if (tbDeptUserGridService.checkTbDeptUserGrid(tbDeptUserGrid)){
                throw new ServiceException("社区干部已关联该网格，不可重复添加！");
            }
            tbDeptUserGridService.insertTbDeptUserGrid(tbDeptUserGrid);
        }
        return toAjax(1);
    }

    /**
     * 修改社区干部关联网格
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUserGrid:edit')")
    @Log(title = "社区干部关联网格", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbDeptUserGrid tbDeptUserGrid)
    {
        tbDeptUserGrid.setUpdateBy(getUsername());
        if (tbDeptUserGridService.checkTbDeptUserGrid(tbDeptUserGrid)){
            return error("数据已存在！");
        }
        return toAjax(tbDeptUserGridService.updateTbDeptUserGrid(tbDeptUserGrid));
    }

    /**
     * 删除社区干部关联网格
     */
    @Log(title = "社区干部关联网格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbDeptUserGridService.deleteTbDeptUserGridByIds(ids));
    }
}
