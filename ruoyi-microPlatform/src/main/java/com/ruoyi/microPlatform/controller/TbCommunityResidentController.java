package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.CountyStatics;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.importListener.ResidentImportListener;
import com.ruoyi.microPlatform.util.ExcelMainUtil;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import com.ruoyi.microPlatform.service.ITbCommunityResidentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区住户家庭信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/communityResident")
public class TbCommunityResidentController extends BaseController
{
    @Autowired
    private ITbCommunityResidentService tbCommunityResidentService;
    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysDeptService sysDeptService;

    /**
     * 查询小区住户家庭信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbCommunityResident tbCommunityResident ,@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize)
    {
        handle(tbCommunityResident);
        //先查询当前部门id和子部门id
//        if (ObjectUtil.isNotEmpty(tbCommunityResident.getDeptId())){
//            List<Long> deptIds = sysDeptService.selectChildDeptIds(tbCommunityResident.getDeptId());
//            tbCommunityResident.setDeptIdList(deptIds);
//        }
        // 设置分页参数（手动传入到 service）
  /*      tbCommunityResident.setPageNum(pageNum);
        tbCommunityResident.setPageSize(pageSize);*/
        //startPage();
        //List<TbCommunityResident> list = tbCommunityResidentService.selectTbCommunityResidentList(tbCommunityResident);

        // 2. 查询分页 id 列表（偏移量计算：从第几条开始）
        int offset = (pageNum - 1) * pageSize;
        tbCommunityResident.setOffset(offset);
        tbCommunityResident.setPageSize(pageSize);

        TableDataInfo pageData = tbCommunityResidentService.selectTbCommunityResidentList(tbCommunityResident,pageNum,pageSize);
        return pageData;
    }

    public void handle(TbCommunityResident tbCommunityResident) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbCommunityResident.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // // 只能查看相应部门下的住户家庭数据
                if (ObjectUtil.isEmpty(tbCommunityResident.getDeptId()) && ObjectUtil.isEmpty(tbCommunityResident.getGridId())) {
                    tbCommunityResident.setDeptId(getDataDeptId());
                }
            }
        }
        if (ObjectUtil.isNotEmpty(tbCommunityResident.getDeptId())){
            getDeptInfo1(tbCommunityResident);
            tbCommunityResident.setDeptId(null);
        }
    }

    public void getDeptInfo1(TbCommunityResident tbCommunityResident) {
        SysDept dept = getDept(tbCommunityResident.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            tbCommunityResident.setCounty(split[1]);
            if (split.length >= 3){
                tbCommunityResident.setCountry(split[2]);
                if (split.length >= 4){
                    tbCommunityResident.setTown(split[3]);
                    if (split.length >= 5){
                        tbCommunityResident.setGridName(split[4]);
                    }
                }
            }
        }
        System.err.println("查询辣" + tbCommunityResident.getCounty() + tbCommunityResident.getCountry() + tbCommunityResident.getTown() + tbCommunityResident.getGridName());
    }


    /**
     * 导出小区住户家庭信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:export')")
    @Log(title = "小区住户家庭信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbCommunityResident tbCommunityResident)
    {
        handle(tbCommunityResident);
        if (ObjectUtil.isNotEmpty(tbCommunityResident.getIsExportAll()) && tbCommunityResident.getIsExportAll() == 0){
            //startPage(pageNum, pageSize);
            // 2. 查询分页 id 列表（偏移量计算：从第几条开始）
            int offset = (pageNum - 1) * pageSize;
            tbCommunityResident.setOffset(offset);
            tbCommunityResident.setPageSize(pageSize);
        }
        List<TbCommunityResident> list = tbCommunityResidentService.selectTbCommunityResidentAndMemberList(tbCommunityResident);
        ExcelUtil<TbCommunityResident> util = new ExcelUtil<TbCommunityResident>(TbCommunityResident.class);
        util.exportExcel(response, list, "住户家庭及成员信息数据","住户家庭及成员信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbCommunityResident tbCommunityResident)
    {
        handle(tbCommunityResident);
        if (ObjectUtil.isNotEmpty(tbCommunityResident.getIsExportAll()) && tbCommunityResident.getIsExportAll() == 0){
            startPage();
        }
        Integer integer = tbCommunityResidentService.selectTbCommunityResidentCount(tbCommunityResident);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer,exportData, tbCommunityResident);
    }

    @Log(title = "小区住户家庭信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelMainUtil<TbCommunityResident, TbResidentMember> util = new ExcelMainUtil<TbCommunityResident,TbResidentMember>(TbCommunityResident.class, TbResidentMember.class);
        List<TbCommunityResident> infos = util.importExcel(file.getInputStream(), "居民住户成员信息");

        String message = tbCommunityResidentService.importTbCommunityResident(infos, updateSupport, getDeptId() + "-" + getNickName(), getDept(getDataDeptId()));
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbCommunityResident> util = new ExcelUtil<TbCommunityResident>(TbCommunityResident.class);
        util.exportTemplateExcel(response, "小区住户家庭信息数据");
    }

    /**
     * 获取小区住户家庭信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbCommunityResidentService.selectTbCommunityResidentById(id));
    }

    public void getDeptInfo(TbCommunityResident tbCommunityResident) {
        if (ObjectUtil.isEmpty(tbCommunityResident.getDeptId())){
            tbCommunityResident.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbCommunityResident.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            tbCommunityResident.setCounty(split[1]);
            if (split.length >= 3){
                tbCommunityResident.setCountry(split[2]);
                if (split.length >= 4){
                    tbCommunityResident.setTown(split[3]);
                    if (split.length >= 5){
                        tbCommunityResident.setGridName(split[4]);
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(tbCommunityResident.getGridName()) && NumberUtil.isNumber(tbCommunityResident.getGridName())){
            tbCommunityResident.setSort(Integer.parseInt(tbCommunityResident.getGridName()));
        }
    }

    /**
     * 新增小区住户家庭信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:add')")
    @Log(title = "小区住户家庭信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbCommunityResident tbCommunityResident)
    {
        tbCommunityResident.setCreateBy(getDeptId() + "-" + (StringUtils.isNotBlank(tbCommunityResident.getCreateBy()) ? tbCommunityResident.getCreateBy() : "") + getUsername());
        if (ObjectUtil.isEmpty(tbCommunityResident.getGridId())){
            return error("所属网格不可为空！");
        }
        if (ObjectUtil.isEmpty(tbCommunityResident.getCommunityId())){
            return error("所属小区不可为空！");
        }
        getDeptInfo(tbCommunityResident);
        Long gridId = SecurityUtils.getGridId();
        // 网格员账号
        if (ObjectUtil.isNotEmpty(gridId)){
            if (StringUtils.isBlank(tbCommunityResident.getGridName()) && gridId.longValue() == tbCommunityResident.getGridId().longValue()){
                tbCommunityResident.setGridName(SecurityUtils.getLoginUser().getUser().getDept().getDeptName());
            }
        }
//        if (tbCommunityResidentService.checkTbCommunityResident(tbCommunityResident)){
//            return error("数据已存在！");
//        }
        return toAjax(tbCommunityResidentService.insertTbCommunityResident(tbCommunityResident));
    }

    /**
     * 修改小区住户家庭信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:edit')")
    @Log(title = "小区住户家庭信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbCommunityResident tbCommunityResident)
    {
        tbCommunityResident.setUpdateBy(getDeptId() + "-" + (StringUtils.isNotBlank(tbCommunityResident.getUpdateBy()) ? tbCommunityResident.getUpdateBy() : "") + getUsername());
        getDeptInfo(tbCommunityResident);
//        if (tbCommunityResidentService.checkTbCommunityResident(tbCommunityResident)){
//            return error("数据已存在！");
//        }
        return toAjax(tbCommunityResidentService.updateTbCommunityResident(tbCommunityResident));
    }

    /**
     * 删除小区住户家庭信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityResident:remove')")
    @Log(title = "小区住户家庭信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbCommunityResidentService.deleteTbCommunityResidentByIds(ids));
    }

    @GetMapping("/selectCountList")
    public AjaxResult selectCountList(Integer dataType)
    {
        Integer deptLevel = SecurityUtils.getLoginUser().getUser().getDept().getDeptLevel();
        if (deptLevel > 6){
            return success();
        }
        String county = null;
        String country = null;
        if (deptLevel == 5){
            county = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        } else if (deptLevel == 6){
            TbCommunityResident resident = new TbCommunityResident();
            getDeptInfo(resident);
            county = resident.getCounty();
            country = resident.getCountry();
        }
        List<CountyStatics> maps = tbCommunityResidentService.selectTypeList(deptLevel, county, country, dataType);
        AjaxResult success = success(maps);

        int totalSum = maps.stream()
                .mapToInt(CountyStatics::getCount)
                .sum();
        success.put("total",totalSum);
        return success;
    }
}
