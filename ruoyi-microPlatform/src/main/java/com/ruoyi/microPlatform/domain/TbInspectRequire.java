package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * 门店基础检查项目对象 tb_inspect_require
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbInspectRequire extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Integer id;

    /** 门店基础检查项目 */
    @Excel(name = "门店基础检查项目")
    private String inspectType;

    /** 检查要求 */
    @Excel(name = "检查要求")
    private String inspectDesc;

    /** 类别 */
    @Excel(name = "类别")
    private String type;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    private String accessory;

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setInspectType(String inspectType) 
    {
        this.inspectType = inspectType;
    }

    public String getInspectType() 
    {
        return inspectType;
    }
    public void setInspectDesc(String inspectDesc) 
    {
        this.inspectDesc = inspectDesc;
    }

    public String getInspectDesc() 
    {
        return inspectDesc;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("inspectType", getInspectType())
            .append("inspectDesc", getInspectDesc())
            .append("type", getType())
            .append("status", getStatus())
            .toString();
    }
}
