<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbAssociateInfoMapper">

    <resultMap type="TbAssociateInfo" id="TbAssociateInfoResult">
            <result property="id"    column="id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="county"    column="county"    />
            <result property="country"    column="country"    />
            <result property="town"    column="town"    />
            <result property="level"    column="level"    />
            <result property="name"    column="name"    />
            <result property="address"    column="address"    />
            <result property="director"    column="director"    />
            <result property="directorId"    column="director_id"    />
            <result property="partyAttachment"    column="party_attachment"    />
            <result property="partyName"    column="party_name"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTbAssociateInfoVo">
        select id, dept_id, county, country, town, level, name, address, director, director_id, party_attachment, party_name, create_time, create_by, update_time, update_by from tb_associate_info
    </sql>

    <select id="selectTbAssociateInfoList" parameterType="TbAssociateInfo" resultMap="TbAssociateInfoResult">
        <include refid="selectTbAssociateInfoVo"/>
        <where>
                        <if test="deptId != null "> and dept_id = #{deptId}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="country != null  and country != ''"> and country = #{country}</if>
                        <if test="town != null  and town != ''"> and town = #{town}</if>
                        <if test="level != null  and level != ''"> and level = #{level}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="director != null  and director != ''"> and director = #{director}</if>
                        <if test="directorId != null "> and director_id = #{directorId}</if>
                        <if test="partyAttachment != null  and partyAttachment != ''"> and party_attachment = #{partyAttachment}</if>
                        <if test="partyName != null  and partyName != ''"> and party_name like concat('%', #{partyName}, '%')</if>
        </where>
    </select>

    <select id="selectTbAssociateInfoById" parameterType="Long" resultMap="TbAssociateInfoResult">
            <include refid="selectTbAssociateInfoVo"/>
            where id = #{id}
    </select>

    <select id="checkTbAssociateInfoUnique" parameterType="TbAssociateInfo" resultMap="TbAssociateInfoResult">
        select id from tb_associate_info where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbAssociateInfo" parameterType="TbAssociateInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_associate_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">dept_id,</if>
                    <if test="county != null">county,</if>
                    <if test="country != null">country,</if>
                    <if test="town != null">town,</if>
                    <if test="level != null">level,</if>
                    <if test="name != null">name,</if>
                    <if test="address != null">address,</if>
                    <if test="director != null">director,</if>
                    <if test="directorId != null">director_id,</if>
                    <if test="partyAttachment != null">party_attachment,</if>
                    <if test="partyName != null">party_name,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">#{deptId},</if>
                    <if test="county != null">#{county},</if>
                    <if test="country != null">#{country},</if>
                    <if test="town != null">#{town},</if>
                    <if test="level != null">#{level},</if>
                    <if test="name != null">#{name},</if>
                    <if test="address != null">#{address},</if>
                    <if test="director != null">#{director},</if>
                    <if test="directorId != null">#{directorId},</if>
                    <if test="partyAttachment != null">#{partyAttachment},</if>
                    <if test="partyName != null">#{partyName},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTbAssociateInfo" parameterType="TbAssociateInfo">
        update tb_associate_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="level != null">level = #{level},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="director != null">director = #{director},</if>
                    <if test="directorId != null">director_id = #{directorId},</if>
                    <if test="partyAttachment != null">party_attachment = #{partyAttachment},</if>
                    <if test="partyName != null">party_name = #{partyName},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbAssociateInfoById" parameterType="Long">
        delete from tb_associate_info where id = #{id}
    </delete>

    <delete id="deleteTbAssociateInfoByIds" parameterType="String">
        delete from tb_associate_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>