package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbResidentContact;

import java.util.List;
import java.util.Map;

/**
 * 居民住户联络沟通信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbResidentContactMapper 
{
    /**
     * 查询居民住户联络沟通信息
     * 
     * @param id 居民住户联络沟通信息主键
     * @return 居民住户联络沟通信息
     */
    public TbResidentContact selectTbResidentContactById(Long id);

    /**
     * 校验居民住户联络沟通信息是否唯一
     *
     * @param tbResidentContact
     * @return 居民住户联络沟通信息
     */
    public TbResidentContact checkTbResidentContactUnique(TbResidentContact tbResidentContact);

    /**
     * 查询居民住户联络沟通信息列表
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 居民住户联络沟通信息集合
     */
    public List<TbResidentContact> selectTbResidentContactList(TbResidentContact tbResidentContact);

    /**
     * 新增居民住户联络沟通信息
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 结果
     */
    public int insertTbResidentContact(TbResidentContact tbResidentContact);

    /**
     * 修改居民住户联络沟通信息
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 结果
     */
    public int updateTbResidentContact(TbResidentContact tbResidentContact);

    /**
     * 删除居民住户联络沟通信息
     * 
     * @param id 居民住户联络沟通信息主键
     * @return 结果
     */
    public int deleteTbResidentContactById(Long id);

    /**
     * 批量删除居民住户联络沟通信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbResidentContactByIds(Long[] ids);

    //查询当前 resident_id 下的所有数据count
    int selectTbResidentContactCountById(TbResidentContact tbResidentContact);

    //查询本月 create_time  和 resident_id下的所有数据
    int selectTbResidentContactCountByIdTime(TbResidentContact tbResidentContact);
    /**
     * 查询居民住户联络沟通信息 根据创建者id 查询提交的总次数  和 最新提交的数据日期
     */
    Map<String, Object> selectSubmitCount(TbResidentContact tbResidentContact);

    List<Map<String, Object>> selectMonthlyStats(LargeScreenInfo largeScreenInfo);

    int selectCount(LargeScreenInfo largeScreenInfo);

}
