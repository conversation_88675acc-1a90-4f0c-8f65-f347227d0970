package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbSidelineGridUser;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;

/**
 * 兼职网格员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface TbSidelineGridUserMapper 
{
    /**
     * 查询兼职网格员信息
     * 
     * @param id 兼职网格员信息主键
     * @return 兼职网格员信息
     */
    public TbSidelineGridUser selectTbSidelineGridUserById(Long id);

    /**
     * 校验兼职网格员信息是否唯一
     *
     * @param tbSidelineGridUser
     * @return 兼职网格员信息
     */
    public TbSidelineGridUser checkTbSidelineGridUserUnique(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 查询兼职网格员信息列表
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 兼职网格员信息集合
     */
    public List<TbSidelineGridUser> selectTbSidelineGridUserList(TbSidelineGridUser tbSidelineGridUser);

    public List<Long> selectTbSidelineGridUserCount(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 新增兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    public int insertTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 修改兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    public int updateTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser);

    /**
     * 删除兼职网格员信息
     * 
     * @param id 兼职网格员信息主键
     * @return 结果
     */
    public int deleteTbSidelineGridUserById(Long id);

    /**
     * 批量删除兼职网格员信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbSidelineGridUserByIds(Long[] ids);

    /**
     * 批量删除兼职网格员所属网格信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbSidelineGridInfoBySideUserIds(Long[] ids);
    
    /**
     * 批量新增兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfoList 兼职网格员所属网格信息列表
     * @return 结果
     */
    public int batchTbSidelineGridInfo(List<TbSidelineGridInfo> tbSidelineGridInfoList);
    

    /**
     * 通过兼职网格员信息主键删除兼职网格员所属网格信息信息
     * 
     * @param tbSidelineGridInfo 兼职网格员信息ID
     * @return 结果
     */
    public int deleteTbSidelineGridInfoBySideUserId(TbSidelineGridInfo tbSidelineGridInfo);

    List<TbSidelineGridUser> selectTbSidelineGridUserList2(TbSidelineGridUser tbSidelineGridUser);


}
