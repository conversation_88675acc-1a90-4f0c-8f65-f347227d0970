package com.ruoyi.microPlatform.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.SeFunctionReservation;
import com.ruoyi.microPlatform.domain.SePublicSentiment;
import com.ruoyi.microPlatform.domain.TbCommunityInfo;
import com.ruoyi.microPlatform.service.IHomePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 首页Controller
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/homePage")
public class HomePageController extends BaseController {

    @Autowired
    private IHomePageService homePageService;

    /**
     * 首页：志愿报名、开放预约 居民吹哨数  （当日、本周、当月、当年、时间范围）
     */
    //@PreAuthorize("@ss.hasPermi('sentiment:homePage:list')")
    @GetMapping("/count")
    public AjaxResult count(HomePage homePage) {
        handle(homePage);
        Map<String, Integer> map = homePageService.selectCount(homePage);
        return success(map);
    }

    public void handle(HomePage homePage) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (SecurityUtils.hasRole("common")) {
                // 普通用户
                homePage.setUserId(getUserId());
            } else {
                // 非普通用户
                if (ObjectUtil.isEmpty(homePage.getDeptId())) {
                    homePage.setDeptId(getDataDeptId());
                }
            }
        }
    }

   /* public void handle(HomePage homePage){

        if (!SecurityUtils.isAdmin(getUserId())){
            // 只能查看相应部门下的数据
            if (ObjectUtil.isEmpty(homePage.getDeptId())){
                homePage.setDeptId(getDataDeptId());
            }
        }
    }
*/

}
