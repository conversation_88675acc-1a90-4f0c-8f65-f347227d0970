<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbMayorMailboxMapper">

    <resultMap type="DbMayorMailbox" id="DbMayorMailboxResult">
            <result property="id"    column="id"    />
            <result property="mailGuid"    column="mail_guid"    />
            <result property="mailNo"    column="mail_no"    />
            <result property="mailTitle"    column="mail_title"    />
            <result property="mailContent"    column="mail_content"    />
            <result property="category"    column="category"    />
            <result property="categoryType"    column="category_type"    />
            <result property="status"    column="status"    />
            <result property="isHot"    column="is_hot"    />
            <result property="isPublic"    column="is_public"    />
            <result property="allowPublish"    column="allow_publish"    />
            <result property="postUserName"    column="post_user_name"    />
            <result property="postUserGender"    column="post_user_gender"    />
            <result property="postDate"    column="post_date"    />
            <result property="postTel"    column="post_tel"    />
            <result property="postEmail"    column="post_email"    />
            <result property="postIp"    column="post_ip"    />
            <result property="postAddress"    column="post_address"    />
            <result property="consultPwd"    column="consult_pwd"    />
            <result property="urgencyLevel"    column="urgency_level"    />
            <result property="timeLimit"    column="time_limit"    />
            <result property="handleTimeLimit"    column="handle_time_limit"    />
            <result property="undertakeUnit"    column="undertake_unit"    />
            <result property="undertakeResult"    column="undertake_result"    />
            <result property="replyContent"    column="reply_content"    />
            <result property="replyDate"    column="reply_date"    />
            <result property="operateDate"    column="operate_date"    />
            <result property="handleDate"    column="handle_date"    />
            <result property="completeStatus"    column="complete_status"    />
            <result property="publishDate"    column="publish_date"    />
            <result property="pushFlag"    column="push_flag"    />
            <result property="pushDate"    column="push_date"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDbMayorMailboxVo">
        select id, mail_guid, mail_no, mail_title, mail_content, category, category_type, status, is_hot, is_public, allow_publish, post_user_name, post_user_gender, post_date, post_tel, post_email, post_ip, post_address, consult_pwd, urgency_level, time_limit, handle_time_limit, undertake_unit, undertake_result, reply_content, reply_date, operate_date, handle_date, complete_status, publish_date, push_flag, push_date, create_time, create_by, update_time, update_by from db_mayor_mailbox
    </sql>

    <select id="selectDbMayorMailboxList" parameterType="DbMayorMailbox" resultMap="DbMayorMailboxResult">
        <include refid="selectDbMayorMailboxVo"/>
        <where>
                        <if test="mailGuid != null  and mailGuid != ''"> and mail_guid = #{mailGuid}</if>
                        <if test="mailNo != null  and mailNo != ''"> and mail_no = #{mailNo}</if>
                        <if test="mailTitle != null  and mailTitle != ''"> and mail_title = #{mailTitle}</if>
                        <if test="mailContent != null  and mailContent != ''"> and mail_content = #{mailContent}</if>
                        <if test="category != null  and category != ''"> and category = #{category}</if>
                        <if test="categoryType != null  and categoryType != ''"> and category_type = #{categoryType}</if>
                        <if test="status != null  and status != ''"> and status = #{status}</if>
                        <if test="isHot != null "> and is_hot = #{isHot}</if>
                        <if test="isPublic != null "> and is_public = #{isPublic}</if>
                        <if test="allowPublish != null "> and allow_publish = #{allowPublish}</if>
                        <if test="postUserName != null  and postUserName != ''"> and post_user_name like concat('%', #{postUserName}, '%')</if>
                        <if test="postUserGender != null "> and post_user_gender = #{postUserGender}</if>
                        <if test="postDate != null "> and post_date = #{postDate}</if>
                        <if test="postTel != null  and postTel != ''"> and post_tel = #{postTel}</if>
                        <if test="postEmail != null  and postEmail != ''"> and post_email = #{postEmail}</if>
                        <if test="postIp != null  and postIp != ''"> and post_ip = #{postIp}</if>
                        <if test="postAddress != null  and postAddress != ''"> and post_address = #{postAddress}</if>
                        <if test="consultPwd != null  and consultPwd != ''"> and consult_pwd = #{consultPwd}</if>
                        <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
                        <if test="timeLimit != null "> and time_limit = #{timeLimit}</if>
                        <if test="handleTimeLimit != null "> and handle_time_limit = #{handleTimeLimit}</if>
                        <if test="undertakeUnit != null  and undertakeUnit != ''"> and undertake_unit = #{undertakeUnit}</if>
                        <if test="undertakeResult != null  and undertakeResult != ''"> and undertake_result = #{undertakeResult}</if>
                        <if test="replyContent != null  and replyContent != ''"> and reply_content = #{replyContent}</if>
                        <if test="replyDate != null "> and reply_date = #{replyDate}</if>
                        <if test="operateDate != null "> and operate_date = #{operateDate}</if>
                        <if test="handleDate != null "> and handle_date = #{handleDate}</if>
                        <if test="completeStatus != null "> and complete_status = #{completeStatus}</if>
                        <if test="publishDate != null "> and publish_date = #{publishDate}</if>
                        <if test="pushFlag != null "> and push_flag = #{pushFlag}</if>
                        <if test="pushDate != null "> and push_date = #{pushDate}</if>
        </where>
    </select>

    <select id="selectDbMayorMailboxById" parameterType="Long" resultMap="DbMayorMailboxResult">
            <include refid="selectDbMayorMailboxVo"/>
            where id = #{id}
    </select>

    <select id="checkDbMayorMailboxUnique" parameterType="DbMayorMailbox" resultMap="DbMayorMailboxResult">
        select id from db_mayor_mailbox where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="dbMayorMailboxAllCount" resultType="java.lang.Integer">
        select count(*)
        from db_mayor_mailbox
    </select>
    <select id="dbMayorMailboxCountGroupByCategory"
            resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
                select category as lable,
                       count(*) as count
                from db_mayor_mailbox
                group by category
    </select>

    <insert id="insertDbMayorMailbox" parameterType="DbMayorMailbox" useGeneratedKeys="true" keyProperty="id">
        insert into db_mayor_mailbox
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="mailGuid != null and mailGuid != ''">mail_guid,</if>
                    <if test="mailNo != null and mailNo != ''">mail_no,</if>
                    <if test="mailTitle != null">mail_title,</if>
                    <if test="mailContent != null">mail_content,</if>
                    <if test="category != null">category,</if>
                    <if test="categoryType != null">category_type,</if>
                    <if test="status != null">status,</if>
                    <if test="isHot != null">is_hot,</if>
                    <if test="isPublic != null">is_public,</if>
                    <if test="allowPublish != null">allow_publish,</if>
                    <if test="postUserName != null">post_user_name,</if>
                    <if test="postUserGender != null">post_user_gender,</if>
                    <if test="postDate != null">post_date,</if>
                    <if test="postTel != null">post_tel,</if>
                    <if test="postEmail != null">post_email,</if>
                    <if test="postIp != null">post_ip,</if>
                    <if test="postAddress != null">post_address,</if>
                    <if test="consultPwd != null">consult_pwd,</if>
                    <if test="urgencyLevel != null">urgency_level,</if>
                    <if test="timeLimit != null">time_limit,</if>
                    <if test="handleTimeLimit != null">handle_time_limit,</if>
                    <if test="undertakeUnit != null">undertake_unit,</if>
                    <if test="undertakeResult != null">undertake_result,</if>
                    <if test="replyContent != null">reply_content,</if>
                    <if test="replyDate != null">reply_date,</if>
                    <if test="operateDate != null">operate_date,</if>
                    <if test="handleDate != null">handle_date,</if>
                    <if test="completeStatus != null">complete_status,</if>
                    <if test="publishDate != null">publish_date,</if>
                    <if test="pushFlag != null">push_flag,</if>
                    <if test="pushDate != null">push_date,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="mailGuid != null and mailGuid != ''">#{mailGuid},</if>
                    <if test="mailNo != null and mailNo != ''">#{mailNo},</if>
                    <if test="mailTitle != null">#{mailTitle},</if>
                    <if test="mailContent != null">#{mailContent},</if>
                    <if test="category != null">#{category},</if>
                    <if test="categoryType != null">#{categoryType},</if>
                    <if test="status != null">#{status},</if>
                    <if test="isHot != null">#{isHot},</if>
                    <if test="isPublic != null">#{isPublic},</if>
                    <if test="allowPublish != null">#{allowPublish},</if>
                    <if test="postUserName != null">#{postUserName},</if>
                    <if test="postUserGender != null">#{postUserGender},</if>
                    <if test="postDate != null">#{postDate},</if>
                    <if test="postTel != null">#{postTel},</if>
                    <if test="postEmail != null">#{postEmail},</if>
                    <if test="postIp != null">#{postIp},</if>
                    <if test="postAddress != null">#{postAddress},</if>
                    <if test="consultPwd != null">#{consultPwd},</if>
                    <if test="urgencyLevel != null">#{urgencyLevel},</if>
                    <if test="timeLimit != null">#{timeLimit},</if>
                    <if test="handleTimeLimit != null">#{handleTimeLimit},</if>
                    <if test="undertakeUnit != null">#{undertakeUnit},</if>
                    <if test="undertakeResult != null">#{undertakeResult},</if>
                    <if test="replyContent != null">#{replyContent},</if>
                    <if test="replyDate != null">#{replyDate},</if>
                    <if test="operateDate != null">#{operateDate},</if>
                    <if test="handleDate != null">#{handleDate},</if>
                    <if test="completeStatus != null">#{completeStatus},</if>
                    <if test="publishDate != null">#{publishDate},</if>
                    <if test="pushFlag != null">#{pushFlag},</if>
                    <if test="pushDate != null">#{pushDate},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateDbMayorMailbox" parameterType="DbMayorMailbox">
        update db_mayor_mailbox
        <trim prefix="SET" suffixOverrides=",">
                    <if test="mailGuid != null and mailGuid != ''">mail_guid = #{mailGuid},</if>
                    <if test="mailNo != null and mailNo != ''">mail_no = #{mailNo},</if>
                    <if test="mailTitle != null">mail_title = #{mailTitle},</if>
                    <if test="mailContent != null">mail_content = #{mailContent},</if>
                    <if test="category != null">category = #{category},</if>
                    <if test="categoryType != null">category_type = #{categoryType},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="isHot != null">is_hot = #{isHot},</if>
                    <if test="isPublic != null">is_public = #{isPublic},</if>
                    <if test="allowPublish != null">allow_publish = #{allowPublish},</if>
                    <if test="postUserName != null">post_user_name = #{postUserName},</if>
                    <if test="postUserGender != null">post_user_gender = #{postUserGender},</if>
                    <if test="postDate != null">post_date = #{postDate},</if>
                    <if test="postTel != null">post_tel = #{postTel},</if>
                    <if test="postEmail != null">post_email = #{postEmail},</if>
                    <if test="postIp != null">post_ip = #{postIp},</if>
                    <if test="postAddress != null">post_address = #{postAddress},</if>
                    <if test="consultPwd != null">consult_pwd = #{consultPwd},</if>
                    <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
                    <if test="timeLimit != null">time_limit = #{timeLimit},</if>
                    <if test="handleTimeLimit != null">handle_time_limit = #{handleTimeLimit},</if>
                    <if test="undertakeUnit != null">undertake_unit = #{undertakeUnit},</if>
                    <if test="undertakeResult != null">undertake_result = #{undertakeResult},</if>
                    <if test="replyContent != null">reply_content = #{replyContent},</if>
                    <if test="replyDate != null">reply_date = #{replyDate},</if>
                    <if test="operateDate != null">operate_date = #{operateDate},</if>
                    <if test="handleDate != null">handle_date = #{handleDate},</if>
                    <if test="completeStatus != null">complete_status = #{completeStatus},</if>
                    <if test="publishDate != null">publish_date = #{publishDate},</if>
                    <if test="pushFlag != null">push_flag = #{pushFlag},</if>
                    <if test="pushDate != null">push_date = #{pushDate},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbMayorMailboxById" parameterType="Long">
        delete from db_mayor_mailbox where id = #{id}
    </delete>

    <delete id="deleteDbMayorMailboxByIds" parameterType="String">
        delete from db_mayor_mailbox where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>