package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 门店巡查基础检查项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbMerchantInspectDetailMapper 
{
    /**
     * 查询门店巡查基础检查项目
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 门店巡查基础检查项目
     */
    public TbMerchantInspectDetail selectTbMerchantInspectDetailById(Integer id);

    /**
     * 校验门店巡查基础检查项目是否唯一
     *
     * @param tbMerchantInspectDetail
     * @return 门店巡查基础检查项目
     */
    public TbMerchantInspectDetail checkTbMerchantInspectDetailUnique(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 查询门店巡查基础检查项目列表
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 门店巡查基础检查项目集合
     */
    public List<TbMerchantInspectDetail> selectTbMerchantInspectDetailList(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 新增门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    public int insertTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 修改门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    public int updateTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail);

    /**
     * 删除门店巡查基础检查项目
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 结果
     */
    public int deleteTbMerchantInspectDetailById(Integer id);

    /**
     * 批量删除门店巡查基础检查项目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMerchantInspectDetailByIds(Integer[] ids);

    int  batchInsertTbMerchantInspectDetail(@Param("inspectId")Long inspectId, @Param("list") List<TbMerchantInspectDetail> details);



}
