package com.ruoyi.microPlatform.controller.bigdata;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.BizDailySubmission;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.IBizDailySubmissionService;
import com.ruoyi.microPlatform.service.ILargeScreenService;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页 大数据相关api
 */
@RestController
@RequestMapping("/bigData/homePage")
public class BigDataHomePageController extends BaseController {



    /**
     * 事项统计
     * 这些是所有的数据来源
     * 事项总数 和分类（综合事项（biz_comprehensive_event） + db_um_event（运管服） + db_hotline_detail（12345热线） + db_enterprise_appeal（万人助企） + db_emergency_event（应急局事项） + 市长信箱 +db_petition_info（信访）+ + major_opinion_events（舆情事项））
     *
     * db_um_event（运管服） + db_hotline_detail（12345热线） + db_enterprise_appeal（万人助企） + db_emergency_event（应急局事项） + 市长信箱 +db_petition_info（信访）
     * @DataSource(DataSourceType.SLAVE)
     *
     * 统计： 当日新增事项数 、待办事项数、 累计办理事项数 、办结率 、满意度
     */
    @GetMapping("/getEventStatistics")
    public AjaxResult getEventStatistics(BigdataParam bigdataParam) {
        Map<String, Object> result = new HashMap<>();

        return success(result);
    }

    /**
     * 每日要情
     */
    @Resource
    private IBizDailySubmissionService bizDailySubmissionService;


    /**
     * 每日要请分页列表
     */
    @GetMapping("/tb/bizDailySubmissionListPage")
    public TableDataInfo bizDailySubmissionListPage(BizDailySubmission bizDailySubmission) {
        startPage();
        List<BizDailySubmission> list = bizDailySubmissionService.selectBizDailySubmissionList(bizDailySubmission);
        return getDataTable(list);
    }

    @Resource
    private ITbResidentMemberService tbResidentMemberService;

    /**
     * 重点人群总数
     */
    @GetMapping("/tb/keyPopulationsCount")
    public AjaxResult keyPopulationsCount(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCount(tbResidentMember));
    }

    /**
     * 重点人群分类数量
     */
    @GetMapping("/tb/keyPopulationsCountGroupByType")
    public AjaxResult keyPopulationsCountGroupByType(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCountGroupByType(tbResidentMember));
    }


    @Resource
    private ILargeScreenService largeScreenService;

    /**
     * 干部岗位类型数量
     */
    @GetMapping("/getDeptUserPostType")
    public AjaxResult getDeptUserPostType(LargeScreenInfo largeScreenInfo) {
//        handle(largeScreenInfo);
        Map<String, Integer> map = largeScreenService.getDeptUserPostType(largeScreenInfo);
        return success(map);
    }

}
