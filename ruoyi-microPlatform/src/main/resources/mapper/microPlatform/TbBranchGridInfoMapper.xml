<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbBranchGridInfoMapper">

    <resultMap type="TbBranchGridInfo" id="TbBranchGridInfoResult">
            <result property="id"    column="id"    />
            <result property="branchId"    column="branch_id"    />
            <result property="gridId"    column="grid_id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="county"    column="county"    />
            <result property="country"    column="country"    />
            <result property="town"    column="town"    />
            <result property="gridName"    column="grid_name"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="gridRange"    column="grid_range"    />
    </resultMap>

    <sql id="selectTbBranchGridInfoVo">
        select id, branch_id, grid_id, dept_id, county, country, town, grid_name, create_by, create_time, update_by, update_time from tb_branch_grid_info
    </sql>

    <select id="selectTbBranchGridInfoList" parameterType="TbBranchGridInfo" resultMap="TbBranchGridInfoResult">
        select a.id, a.branch_id, a.grid_id, a.dept_id, a.county, a.country, a.town, a.grid_name, a.create_by, a.create_time, a.update_by, a.update_time,
        b.administrative_plan as grid_range
        from tb_branch_grid_info a
        left join
        tb_grid_info b
        on a.grid_id = b.id
        <where>
                        <if test="branchId != null "> and a.branch_id = #{branchId}</if>
                        <if test="gridId != null "> and a.grid_id = #{gridId}</if>
                        <if test="deptId != null "> and a.dept_id = #{deptId}</if>
                        <if test="county != null  and county != ''"> and a.county = #{county}</if>
                        <if test="country != null  and country != ''"> and a.country = #{country}</if>
                        <if test="town != null  and town != ''"> and a.town = #{town}</if>
                        <if test="gridName != null  and gridName != ''"> and a.grid_name like concat('%', #{gridName}, '%')</if>
        </where>
    </select>

    <select id="selectTbBranchGridInfoById" parameterType="Long" resultMap="TbBranchGridInfoResult">
        select a.id, a.branch_id, a.grid_id, a.dept_id, a.county, a.country, a.town, a.grid_name, a.create_by, a.create_time, a.update_by, a.update_time,
               b.administrative_plan as grid_range
        from tb_branch_grid_info a
                 left join
             tb_grid_info b on a.grid_id = b.id
            where a.id = #{id}
    </select>

    <select id="checkTbBranchGridInfoUnique" parameterType="TbBranchGridInfo" resultMap="TbBranchGridInfoResult">
        select id from tb_branch_grid_info
        <where>
            <if test="id != null">
                and id != #{id}
            </if>
            <if test="branchId != null">
                and branch_id = #{branchId}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
        </where>

    </select>

    <insert id="insertTbBranchGridInfo" parameterType="TbBranchGridInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_branch_grid_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="branchId != null">branch_id,</if>
                    <if test="gridId != null">grid_id,</if>
                    <if test="deptId != null">dept_id,</if>
                    <if test="county != null">county,</if>
                    <if test="country != null">country,</if>
                    <if test="town != null">town,</if>
                    <if test="gridName != null">grid_name,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="branchId != null">#{branchId},</if>
                    <if test="gridId != null">#{gridId},</if>
                    <if test="deptId != null">#{deptId},</if>
                    <if test="county != null">#{county},</if>
                    <if test="country != null">#{country},</if>
                    <if test="town != null">#{town},</if>
                    <if test="gridName != null">#{gridName},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbBranchGridInfo" parameterType="TbBranchGridInfo">
        update tb_branch_grid_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="branchId != null">branch_id = #{branchId},</if>
                    <if test="gridId != null">grid_id = #{gridId},</if>
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="gridName != null">grid_name = #{gridName},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbBranchGridInfoById" parameterType="Long">
        delete from tb_branch_grid_info where id = #{id}
    </delete>

    <delete id="deleteTbBranchGridInfoByIds" parameterType="String">
        delete from tb_branch_grid_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>