package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.domain.TbGridInfo;

/**
 * 网格信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbGridInfoService 
{
    /**
     * 查询网格信息
     * 
     * @param id 网格信息主键
     * @return 网格信息
     */
    public TbGridInfo selectTbGridInfoById(Long id);

    public TbGridInfo selectGridInfoById(Long id);

    /**
     * 校验网格信息是否存在
     *
     * @param tbGridInfo 网格信息
     * @return 网格信息
     */
    public boolean checkTbGridInfo(TbGridInfo tbGridInfo);

    /**
     * 查询网格信息列表
     * 
     * @param tbGridInfo 网格信息
     * @return 网格信息集合
     */
    public List<TbGridInfo> selectTbGridInfoList(TbGridInfo tbGridInfo);

    public List<TbGridInfo> getWorkGrid(TbGridInfo tbGridInfo);

    public Integer selectExportGridInfoCount(TbGridInfo tbGridInfo);

    /**
     * 导入网格信息
     *
     * @param infos       网格信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridInfo(List<TbGridInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增网格信息
     * 
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    public int insertTbGridInfo(TbGridInfo tbGridInfo);

    /**
     * 修改网格信息
     * 
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    public int updateTbGridInfo(TbGridInfo tbGridInfo);

    /**
     * 批量删除网格信息
     * 
     * @param ids 需要删除的网格信息主键集合
     * @return 结果
     */
    public int deleteTbGridInfoByIds(Long[] ids);

    /**
     * 删除网格信息信息
     * 
     * @param id 网格信息主键
     * @return 结果
     */
    public int deleteTbGridInfoById(Long id);
}
