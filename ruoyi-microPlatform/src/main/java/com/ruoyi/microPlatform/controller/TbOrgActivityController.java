package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbOrgActivity;
import com.ruoyi.microPlatform.service.ITbOrgActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 组织日常活动开展情况Controller
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/gridData/orgActivity")
public class TbOrgActivityController extends BaseController
{
    @Autowired
    private ITbOrgActivityService tbOrgActivityService;

    /**
     * 查询组织日常活动开展情况列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbOrgActivity tbOrgActivity)
    {
        startPage();
        List<TbOrgActivity> list = tbOrgActivityService.selectTbOrgActivityList(tbOrgActivity);
        return getDataTable(list);
    }

    /**
     * 导出组织日常活动开展情况列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:export')")
    @Log(title = "组织日常活动开展情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbOrgActivity tbOrgActivity)
    {
        List<TbOrgActivity> list = tbOrgActivityService.selectTbOrgActivityList(tbOrgActivity);
        ExcelUtil<TbOrgActivity> util = new ExcelUtil<TbOrgActivity>(TbOrgActivity.class);
        util.exportExcel(response, list, "组织日常活动开展情况数据");
    }

    @Log(title = "组织日常活动开展情况", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbOrgActivity> util = new ExcelUtil<TbOrgActivity>(TbOrgActivity.class);
        List<TbOrgActivity> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbOrgActivityService.importTbOrgActivity(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbOrgActivity> util = new ExcelUtil<TbOrgActivity>(TbOrgActivity.class);
        util.exportTemplateExcel(response, "组织日常活动开展情况数据");
    }

    /**
     * 获取组织日常活动开展情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbOrgActivityService.selectTbOrgActivityById(id));
    }

    /**
     * 新增组织日常活动开展情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:add')")
    @Log(title = "组织日常活动开展情况", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbOrgActivity tbOrgActivity)
    {
        tbOrgActivity.setCreateBy(getUsername());
        if (tbOrgActivityService.checkTbOrgActivity(tbOrgActivity)){
            return error("数据已存在！");
        }
        return toAjax(tbOrgActivityService.insertTbOrgActivity(tbOrgActivity));
    }

    /**
     * 修改组织日常活动开展情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:edit')")
    @Log(title = "组织日常活动开展情况", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbOrgActivity tbOrgActivity)
    {
        tbOrgActivity.setUpdateBy(getUsername());
        if (tbOrgActivityService.checkTbOrgActivity(tbOrgActivity)){
            return error("数据已存在！");
        }
        return toAjax(tbOrgActivityService.updateTbOrgActivity(tbOrgActivity));
    }

    /**
     * 删除组织日常活动开展情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgActivity:remove')")
    @Log(title = "组织日常活动开展情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbOrgActivityService.deleteTbOrgActivityByIds(ids));
    }
}
