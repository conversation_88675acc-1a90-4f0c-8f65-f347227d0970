<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbCommunityInfoMapper">

    <resultMap type="TbCommunityInfo" id="TbCommunityInfoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="communityName" column="community_name"/>
        <result property="communitySort" column="community_sort"/>
        <result property="geometry" column="geometry"/>
        <result property="area" column="area"/>
        <result property="merchantNum" column="merchant_num"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbCommunityInfoVo">
        select id, dept_id, county, country, town, grid_id, grid_name, community_name, community_sort, geometry, area,
        merchant_num, remark, create_by, create_time, update_by, update_time from tb_community_info
    </sql>

    <sql id="queryCommunityInfoVo">
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_community_info.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="communityName != null  and communityName != ''">and community_name like concat('%',
                #{communityName}, '%')
            </if>
            <if test="communitySort != null  and communitySort != ''">and community_sort = #{communitySort}</if>
            <if test="geometry != null  and geometry != ''">and geometry = #{geometry}</if>
            <if test="area != null  and area != ''">and area = #{area}</if>
            <if test="merchantNum != null ">and merchant_num = #{merchantNum}</if>

            <if test="notGridUserId != null ">and id not in (select community_id from tb_grid_user_community where grid_user_id = #{notGridUserId})</if>
        </where>
    </sql>

    <select id="selectTbCommunityInfoList" parameterType="TbCommunityInfo" resultMap="TbCommunityInfoResult">
        <include refid="selectTbCommunityInfoVo"/>
        <include refid="queryCommunityInfoVo"/>
        order by community_sort asc, id desc
    </select>

    <select id="selectTbCommunityInfoCount" parameterType="TbCommunityInfo" resultType="Long">
        select id from tb_community_info
        <include refid="queryCommunityInfoVo"/>
    </select>

    <select id="selectTbCommunityInfoById" parameterType="Long" resultMap="TbCommunityInfoResult">
        <include refid="selectTbCommunityInfoVo"/>
        where id = #{id}
    </select>

    <select id="checkTbCommunityInfoUnique" parameterType="TbCommunityInfo" resultMap="TbCommunityInfoResult">
        select id from tb_community_info where community_name = #{communityName} and grid_id = #{gridId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbCommunityInfo" parameterType="TbCommunityInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_community_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="communityName != null">community_name,</if>
            <if test="communitySort != null">community_sort,</if>
            <if test="geometry != null">geometry,</if>
            <if test="area != null">area,</if>
            <if test="merchantNum != null">merchant_num,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="communitySort != null">#{communitySort},</if>
            <if test="geometry != null">#{geometry},</if>
            <if test="area != null">#{area},</if>
            <if test="merchantNum != null">#{merchantNum},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbCommunityInfo" parameterType="TbCommunityInfo">
        update tb_community_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="communitySort != null">community_sort = #{communitySort},</if>
            <if test="geometry != null">geometry = #{geometry},</if>
            <if test="area != null">area = #{area},</if>
            <if test="merchantNum != null">merchant_num = #{merchantNum},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbCommunityInfoById" parameterType="Long">
        delete from tb_community_info where id = #{id}
    </delete>

    <delete id="deleteTbCommunityInfoByIds" parameterType="String">
        delete from tb_community_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbCommunityInfoSpaceList" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbCommunityInfoResult">
        <include refid="selectTbCommunityInfoVo"/>
        <where>
            geometry IS NOT NULL and geometry != ''
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_community_info.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
        </where>
        order by community_sort asc, id desc
    </select>

    <select id="getCommunityInfo" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbCommunityInfoResult">
        select id,community_name,grid_name,area
        from
            tb_community_info
        where id = #{communityId}

    </select>
</mapper>