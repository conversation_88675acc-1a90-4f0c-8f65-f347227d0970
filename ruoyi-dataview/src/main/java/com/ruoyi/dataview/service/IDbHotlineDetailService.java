package com.ruoyi.dataview.service;

import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbHotlineDetail;

import java.util.List;

/**
 * 12345热线中心综合明细Service接口
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface IDbHotlineDetailService {
    /**
     * 查询12345热线中心综合明细
     *
     * @param id 12345热线中心综合明细主键
     * @return 12345热线中心综合明细
     */
    public DbHotlineDetail selectDbHotlineDetailById(Long id);

    /**
     * 校验12345热线中心综合明细是否存在
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 12345热线中心综合明细
     */
    public boolean checkDbHotlineDetail(DbHotlineDetail dbHotlineDetail);

    /**
     * 查询12345热线中心综合明细列表
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 12345热线中心综合明细集合
     */
    public List<DbHotlineDetail> selectDbHotlineDetailList(DbHotlineDetail dbHotlineDetail);

    /**
     *
     */
    int selectDbHotlineDetailCount(DbHotlineDetail dbHotlineDetail);

    /**
     * 导入12345热线中心综合明细
     *
     * @param infos           12345热线中心综合明细列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbHotlineDetail(List<DbHotlineDetail> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    public int insertDbHotlineDetail(DbHotlineDetail dbHotlineDetail);

    /**
     * 修改12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    public int updateDbHotlineDetail(DbHotlineDetail dbHotlineDetail);

    /**
     * 批量删除12345热线中心综合明细
     *
     * @param ids 需要删除的12345热线中心综合明细主键集合
     * @return 结果
     */
    public int deleteDbHotlineDetailByIds(Long[] ids);

    /**
     * 删除12345热线中心综合明细信息
     *
     * @param id 12345热线中心综合明细主键
     * @return 结果
     */
    public int deleteDbHotlineDetailById(Long id);

    List<CommonBaseCount> hotlineDetailGroupRec(DbBigdataParam bigdataParam);
}
