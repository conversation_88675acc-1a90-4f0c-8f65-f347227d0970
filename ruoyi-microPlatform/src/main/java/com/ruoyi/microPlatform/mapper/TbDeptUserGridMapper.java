package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbDeptUserGrid;

/**
 * 社区干部关联网格Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbDeptUserGridMapper 
{
    /**
     * 查询社区干部关联网格
     * 
     * @param id 社区干部关联网格主键
     * @return 社区干部关联网格
     */
    public TbDeptUserGrid selectTbDeptUserGridById(Long id);

    /**
     * 校验社区干部关联网格是否唯一
     *
     * @param tbDeptUserGrid
     * @return 社区干部关联网格
     */
    public TbDeptUserGrid checkTbDeptUserGridUnique(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 查询社区干部关联网格列表
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 社区干部关联网格集合
     */
    public List<TbDeptUserGrid> selectTbDeptUserGridList(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 新增社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    public int insertTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 修改社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    public int updateTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 删除社区干部关联网格
     * 
     * @param id 社区干部关联网格主键
     * @return 结果
     */
    public int deleteTbDeptUserGridById(Long id);

    /**
     * 批量删除社区干部关联网格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbDeptUserGridByIds(Long[] ids);
}
