package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbOrgEmployeeMapper;
import com.ruoyi.microPlatform.domain.TbOrgEmployee;
import com.ruoyi.microPlatform.service.ITbOrgEmployeeService;

/**
 * 组织情况员工信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class TbOrgEmployeeServiceImpl implements ITbOrgEmployeeService 
{
    @Autowired
    private TbOrgEmployeeMapper tbOrgEmployeeMapper;

    /**
     * 查询组织情况员工信息
     * 
     * @param id 组织情况员工信息主键
     * @return 组织情况员工信息
     */
    @Override
    public TbOrgEmployee selectTbOrgEmployeeById(Long id)
    {
        return tbOrgEmployeeMapper.selectTbOrgEmployeeById(id);
    }

    /**
     * 校验组织情况员工信息是否存在
     *
     * @param tbOrgEmployee
     * @return boolean
     */
    @Override
    public boolean checkTbOrgEmployee(TbOrgEmployee tbOrgEmployee){
        TbOrgEmployee old = tbOrgEmployeeMapper.checkTbOrgEmployeeUnique(tbOrgEmployee);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询组织情况员工信息列表
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 组织情况员工信息
     */
    @Override
    public List<TbOrgEmployee> selectTbOrgEmployeeList(TbOrgEmployee tbOrgEmployee)
    {
        return tbOrgEmployeeMapper.selectTbOrgEmployeeList(tbOrgEmployee);
    }

    /**
     * 导入组织情况员工信息
     *
     * @param infos       组织情况员工信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbOrgEmployee(List<TbOrgEmployee> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入组织情况员工信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbOrgEmployee info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbOrgEmployee old = tbOrgEmployeeMapper.checkTbOrgEmployeeUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbOrgEmployeeMapper.insertTbOrgEmployee(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbOrgEmployeeMapper.updateTbOrgEmployee(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    @Override
    public int insertTbOrgEmployee(TbOrgEmployee tbOrgEmployee)
    {
        return tbOrgEmployeeMapper.insertTbOrgEmployee(tbOrgEmployee);
    }

    /**
     * 修改组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    @Override
    public int updateTbOrgEmployee(TbOrgEmployee tbOrgEmployee)
    {
        return tbOrgEmployeeMapper.updateTbOrgEmployee(tbOrgEmployee);
    }

    /**
     * 批量删除组织情况员工信息
     * 
     * @param ids 需要删除的组织情况员工信息主键
     * @return 结果
     */
    @Override
    public int deleteTbOrgEmployeeByIds(Long[] ids)
    {
        return tbOrgEmployeeMapper.deleteTbOrgEmployeeByIds(ids);
    }

    /**
     * 删除组织情况员工信息信息
     * 
     * @param id 组织情况员工信息主键
     * @return 结果
     */
    @Override
    public int deleteTbOrgEmployeeById(Long id)
    {
        return tbOrgEmployeeMapper.deleteTbOrgEmployeeById(id);
    }
}
