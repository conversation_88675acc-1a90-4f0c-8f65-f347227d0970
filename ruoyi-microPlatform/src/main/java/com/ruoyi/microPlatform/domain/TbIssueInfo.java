package com.ruoyi.microPlatform.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 矛盾纠纷对象 tb_issue_info
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public class TbIssueInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 数据来源 */
    @Excel(name = "数据来源")
    private String dataFrom;

    /** 数据id */
    @Excel(name = "数据id")
    private Long formId;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 所在县市区 */
    @Excel(name = "所在县市区")
    private String county;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    private String country;

    /** 村/社区 */
    @Excel(name = "村/社区")
    private String town;

    /** 网格Id */
    @Excel(name = "网格Id")
    private Long gridId;

    /** 网格名称 */
    @Excel(name = "网格名称")
    private String gridName;

    /** 矛盾纠纷唯一标识(来源于警综平台) */
    @Excel(name = "矛盾纠纷唯一标识(来源于警综平台)")
    private String mdjfzj;

    /** 矛盾纠纷名称 */
    @Excel(name = "矛盾纠纷名称")
    private String mdjfmc;

    /** 矛盾纠纷类别代码 */
    @Excel(name = "矛盾纠纷类别代码")
    private String mdjflbdm;

    /** 矛盾纠纷类别名称 */
    @Excel(name = "矛盾纠纷类别名称")
    private String mdjflbmc;

    /** 发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date fssj;

    /** 矛盾纠纷发生详细地址 */
    @Excel(name = "矛盾纠纷发生详细地址")
    private String fsdz;

    /** 矛盾纠纷来源代码(来源代码05 综治平台 01警综平台 06许邻e家)字典 */
    @Excel(name = "矛盾纠纷来源代码(来源代码05 综治平台 01警综平台 06许邻e家)字典")
    private String mdjflydm;

    /** 矛盾纠纷来源名称 */
    @Excel(name = "矛盾纠纷来源名称")
    private String mdjflymc;

    /** 风险等级（字典） */
    @Excel(name = "风险等级", readConverterExp = "字=典")
    private String fxdjdm;

    /** 风险等级名称 */
    @Excel(name = "风险等级名称")
    private String fxdjmc;

    /** 矛盾纠纷详情 */
    @Excel(name = "矛盾纠纷详情")
    private String mdjfxq;

    /** 矛盾纠纷状态（字典） */
    @Excel(name = "矛盾纠纷状态", readConverterExp = "字=典")
    private String mdjfztdm;

    /** 矛盾纠纷名称 */
    @Excel(name = "矛盾纠纷名称")
    private String mdjfztmc;

    /** 登记人姓名 */
    @Excel(name = "登记人姓名")
    private String mdjfdjr;

    /** 登记时间  20240314121230 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date mdjfdjsj;

    /** 登记人号码 */
    @Excel(name = "登记人号码")
    private String mdjfdjrhm;

    /** 矛盾纠纷综治管辖地代码 */
    @Excel(name = "矛盾纠纷综治管辖地代码")
    private String mdjfzzgxddm;

    /** 矛盾纠纷综治管辖地名称 */
    @Excel(name = "矛盾纠纷综治管辖地名称")
    private String mdjfzzgxdmc;

    /** 矛盾纠纷派出所代码 */
    @Excel(name = "矛盾纠纷派出所代码")
    private String mdjfpcsdm;

    /** 矛盾纠纷派出所名称 */
    @Excel(name = "矛盾纠纷派出所名称")
    private String mdjfpcsmc;

    /** 推送警综平台状态（0待推送 1已推送）*/
    @Excel(name = "推送警综平台状态（0待推送 1已推送）")
    private Integer tbTszt;

    /** 涉及人数 */
    @Excel(name = "涉及人数")
    private Integer personNum;

    /** 附件 */
    @Excel(name = "附件")
    private String accessory;
    /**
     * 电话
     */
    private String phone;
    /**
     * 当事人
     */
    private String name;
    /**
     * 证件号码
     */
    private String idCard;

    /** 矛盾纠纷当事人信息信息 */
    private List<TbMainParty> tbMainPartyList;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDataFrom(String dataFrom) 
    {
        this.dataFrom = dataFrom;
    }

    public String getDataFrom() 
    {
        return dataFrom;
    }
    public void setFormId(Long formId) 
    {
        this.formId = formId;
    }

    public Long getFormId() 
    {
        return formId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setMdjfzj(String mdjfzj) 
    {
        this.mdjfzj = mdjfzj;
    }

    public String getMdjfzj() 
    {
        return mdjfzj;
    }
    public void setMdjfmc(String mdjfmc) 
    {
        this.mdjfmc = mdjfmc;
    }

    public String getMdjfmc() 
    {
        return mdjfmc;
    }
    public void setMdjflbdm(String mdjflbdm) 
    {
        this.mdjflbdm = mdjflbdm;
    }

    public String getMdjflbdm() 
    {
        return mdjflbdm;
    }
    public void setMdjflbmc(String mdjflbmc) 
    {
        this.mdjflbmc = mdjflbmc;
    }

    public String getMdjflbmc() 
    {
        return mdjflbmc;
    }
    public void setFssj(Date fssj) 
    {
        this.fssj = fssj;
    }

    public Date getFssj() 
    {
        return fssj;
    }
    public void setFsdz(String fsdz) 
    {
        this.fsdz = fsdz;
    }

    public String getFsdz() 
    {
        return fsdz;
    }
    public void setMdjflydm(String mdjflydm) 
    {
        this.mdjflydm = mdjflydm;
    }

    public String getMdjflydm() 
    {
        return mdjflydm;
    }
    public void setMdjflymc(String mdjflymc) 
    {
        this.mdjflymc = mdjflymc;
    }

    public String getMdjflymc() 
    {
        return mdjflymc;
    }
    public void setFxdjdm(String fxdjdm) 
    {
        this.fxdjdm = fxdjdm;
    }

    public String getFxdjdm() 
    {
        return fxdjdm;
    }
    public void setFxdjmc(String fxdjmc) 
    {
        this.fxdjmc = fxdjmc;
    }

    public String getFxdjmc() 
    {
        return fxdjmc;
    }
    public void setMdjfxq(String mdjfxq) 
    {
        this.mdjfxq = mdjfxq;
    }

    public String getMdjfxq() 
    {
        return mdjfxq;
    }
    public void setMdjfztdm(String mdjfztdm) 
    {
        this.mdjfztdm = mdjfztdm;
    }

    public String getMdjfztdm() 
    {
        return mdjfztdm;
    }
    public void setMdjfztmc(String mdjfztmc) 
    {
        this.mdjfztmc = mdjfztmc;
    }

    public String getMdjfztmc() 
    {
        return mdjfztmc;
    }
    public void setMdjfdjr(String mdjfdjr) 
    {
        this.mdjfdjr = mdjfdjr;
    }

    public String getMdjfdjr() 
    {
        return mdjfdjr;
    }
    public void setMdjfdjsj(Date mdjfdjsj) 
    {
        this.mdjfdjsj = mdjfdjsj;
    }

    public Date getMdjfdjsj() 
    {
        return mdjfdjsj;
    }
    public void setMdjfdjrhm(String mdjfdjrhm) 
    {
        this.mdjfdjrhm = mdjfdjrhm;
    }

    public String getMdjfdjrhm() 
    {
        return mdjfdjrhm;
    }
    public void setMdjfzzgxddm(String mdjfzzgxddm) 
    {
        this.mdjfzzgxddm = mdjfzzgxddm;
    }

    public String getMdjfzzgxddm() 
    {
        return mdjfzzgxddm;
    }
    public void setMdjfzzgxdmc(String mdjfzzgxdmc) 
    {
        this.mdjfzzgxdmc = mdjfzzgxdmc;
    }

    public String getMdjfzzgxdmc() 
    {
        return mdjfzzgxdmc;
    }
    public void setMdjfpcsdm(String mdjfpcsdm) 
    {
        this.mdjfpcsdm = mdjfpcsdm;
    }

    public String getMdjfpcsdm() 
    {
        return mdjfpcsdm;
    }
    public void setMdjfpcsmc(String mdjfpcsmc) 
    {
        this.mdjfpcsmc = mdjfpcsmc;
    }

    public String getMdjfpcsmc() 
    {
        return mdjfpcsmc;
    }
    public void setTbTszt(Integer tbTszt) 
    {
        this.tbTszt = tbTszt;
    }

    public Integer getTbTszt() 
    {
        return tbTszt;
    }
    public void setPersonNum(Integer personNum) 
    {
        this.personNum = personNum;
    }

    public Integer getPersonNum() 
    {
        return personNum;
    }

    public List<TbMainParty> getTbMainPartyList()
    {
        return tbMainPartyList;
    }

    public void setTbMainPartyList(List<TbMainParty> tbMainPartyList)
    {
        this.tbMainPartyList = tbMainPartyList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataFrom", getDataFrom())
            .append("formId", getFormId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .append("mdjfzj", getMdjfzj())
            .append("mdjfmc", getMdjfmc())
            .append("mdjflbdm", getMdjflbdm())
            .append("mdjflbmc", getMdjflbmc())
            .append("fssj", getFssj())
            .append("fsdz", getFsdz())
            .append("mdjflydm", getMdjflydm())
            .append("mdjflymc", getMdjflymc())
            .append("fxdjdm", getFxdjdm())
            .append("fxdjmc", getFxdjmc())
            .append("mdjfxq", getMdjfxq())
            .append("mdjfztdm", getMdjfztdm())
            .append("mdjfztmc", getMdjfztmc())
            .append("mdjfdjr", getMdjfdjr())
            .append("mdjfdjsj", getMdjfdjsj())
            .append("mdjfdjrhm", getMdjfdjrhm())
            .append("mdjfzzgxddm", getMdjfzzgxddm())
            .append("mdjfzzgxdmc", getMdjfzzgxdmc())
            .append("mdjfpcsdm", getMdjfpcsdm())
            .append("mdjfpcsmc", getMdjfpcsmc())
            .append("tbTszt", getTbTszt())
            .append("personNum", getPersonNum())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("accessory", getAccessory())
            .append("tbMainPartyList", getTbMainPartyList())
            .toString();
    }
}
