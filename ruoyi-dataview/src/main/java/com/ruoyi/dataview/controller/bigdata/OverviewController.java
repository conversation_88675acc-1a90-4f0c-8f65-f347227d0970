package com.ruoyi.dataview.controller.bigdata;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import com.ruoyi.dataview.domain.po.DbCityComponent;
import com.ruoyi.dataview.domain.po.DbHotlineDetail;
import com.ruoyi.dataview.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 常态事项
 */
@RestController
@RequestMapping("/bigData/Overview")
public class OverviewController extends BaseController {

    /**
     * 12345 热线
     */
    @Resource
    private IDbHotlineDetailService dbHotlineDetailService;

    /**
     * 运管服
     */
    @Resource
    private IDbCityComponentService dbCityComponentService;


    /**
     * 12345数据总览 上
     */
    @GetMapping("/homePage/dbCount")
    public AjaxResult dbCount(DbBigdataParam dbBigdataParam) {
//        if (!SecurityUtils.isEmptyAdmin(getUserId())) {
//            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
//                dbBigdataParam.setDeptId(getDataDeptId());
//            }
//        }
//        DbCityComponent seCommunityActivity = new DbCityComponent();
//        DbHotlineDetail dbHotlineDetail = new DbHotlineDetail();
//        HashMap<String, Integer> res = new HashMap<>();
//        res.put("dbHotlineDetail", dbHotlineDetailService.selectDbHotlineDetailCount(dbHotlineDetail));
//        res.put("dbCityComponent", dbCityComponentService.selectDbCityComponentCount(seCommunityActivity));
//        return success(res);

        Map<String, String> res = new HashMap<>();
        List<SysDictData> marketOverview = DictUtils.getDictCache("hotlineDetailCount");
        if (marketOverview == null) {
            marketOverview = new ArrayList<>();
        }
        Map<String, String> collect = marketOverview.stream().collect(Collectors
                .toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (k1, k2) -> k1));
        res.put("numberOfProblemsHandled", collect.getOrDefault("numberOfProblemsHandled", "1229"));// 问题办理数
        res.put("directReplyNumber", collect.getOrDefault("directReplyNumber", "234"));// 直接答复数
        res.put("turnover", collect.getOrDefault("turnover", "89"));// 办理率
        res.put("satisfaction", collect.getOrDefault("satisfaction", "90"));// 满意度
        return success(res);
    }

    /**
     * 12345数据总览 下
     */
    @GetMapping("/homePage/hotlineDetailGroupRec")
    public AjaxResult hotlineDetailGroupRec(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        if (dbBigdataParam.getYear() == null) {
            dbBigdataParam.setYear(DateUtil.thisYear());
        }
        return success(dbHotlineDetailService.hotlineDetailGroupRec(dbBigdataParam));
    }

    @Resource
    private IDbEnterpriseAppealService dbEnterpriseAppealService;

    /**
     * 万人助企
     */
    @GetMapping("/homePage/enterpriseAppealGroup")
    public AjaxResult enterpriseAppealGroup(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        HashMap<String, Integer> res = new HashMap<>();
        return success(res);
    }

    /**
     * 城市部件
     */
    @GetMapping("/homePage/urbanComponents")
    public AjaxResult urbanComponents(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(dbCityComponentService.selectCountGroupByMajorCode());
    }

    @Resource
    private IDbUmEventService dbUmEventService;


    /**
     * 城市运行事件
     */
    @GetMapping("/homePage/dbUmEventBigCount")
    public AjaxResult dbUmEventBigCount(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(dbUmEventService.dbUmEventBigCount("事件"));
    }

    /**
     * 城市运行部件
     */
    @GetMapping("/homePage/dbUmEventComponentBigCount")
    public AjaxResult dbUmEventComponentBigCount(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(dbUmEventService.dbUmEventBigCount("部件"));
    }

    @Resource
    private IDbMayorMailboxService dbMayorMailboxService;


    /**
     * 市长信箱统计
     */
    @GetMapping("/homePage/dbMayorMailboxCount")
    public AjaxResult dbMayorMailboxCount(DbBigdataParam dbBigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(dbBigdataParam.getDeptId())) {
                dbBigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(dbMayorMailboxService.dbMayorMailboxCount());
    }


    @Autowired
    private IDbPetitionInfoService dbPetitionInfoService;

    /**
     *  信访统计
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics() {
        return AjaxResult.success(dbPetitionInfoService.getStatistics());
    }
}
