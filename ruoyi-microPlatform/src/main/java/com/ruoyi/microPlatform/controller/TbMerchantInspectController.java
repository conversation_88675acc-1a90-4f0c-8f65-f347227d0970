package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbMerchantInspect;
import com.ruoyi.microPlatform.service.ITbMerchantInspectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 门店巡查Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridManage/merchantInspect")
public class TbMerchantInspectController extends BaseController {
    @Autowired
    private ITbMerchantInspectService tbMerchantInspectService;

    /**
     * 查询门店巡查列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbMerchantInspect tbMerchantInspect) {
        startPage();
        handle(tbMerchantInspect);
        List<TbMerchantInspect> list = tbMerchantInspectService.selectTbMerchantInspectList(tbMerchantInspect);
        return getDataTable(list);
    }

    public void handle(TbMerchantInspect tbMerchantInspect) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isNotEmpty(tbMerchantInspect.getQueryType())) {
                if (tbMerchantInspect.getQueryType() == 2) {
                    // 查询个人提交的
                    tbMerchantInspect.setPersonId(getUserId());
                    return;
                } else if (tbMerchantInspect.getQueryType() == 1) {
                    // 查询全部
                    tbMerchantInspect.setPersonId(null);
                }
            }
            if (ObjectUtil.isEmpty(tbMerchantInspect.getGridId())) {
                // 网格为空， 查询个人权限内
                Long gridId = SecurityUtils.getGridId();
                if (ObjectUtil.isNotEmpty(gridId)) {
                    tbMerchantInspect.setGridArr(getLoginUser().getUser().getDrillNo());
                } else {
                    // // 只能查看相应部门下的门店巡查数据
                    if (ObjectUtil.isEmpty(tbMerchantInspect.getDeptId()) && ObjectUtil.isEmpty(tbMerchantInspect.getMerchantId())) {
                        tbMerchantInspect.setDeptId(getDataDeptId());
                    }
                }
            }
        }
    }

    /**
     * 导出门店巡查列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:export')")
    @Log(title = "门店巡查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbMerchantInspect tbMerchantInspect) {
        handle(tbMerchantInspect);
        List<TbMerchantInspect> list = tbMerchantInspectService.selectTbMerchantInspectList(tbMerchantInspect);
        ExcelUtil<TbMerchantInspect> util = new ExcelUtil<TbMerchantInspect>(TbMerchantInspect.class);
        util.exportExcel(response, list, "门店巡查数据");
    }

    @Log(title = "门店巡查", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbMerchantInspect> util = new ExcelUtil<TbMerchantInspect>(TbMerchantInspect.class);
        List<TbMerchantInspect> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbMerchantInspectService.importTbMerchantInspect(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbMerchantInspect> util = new ExcelUtil<TbMerchantInspect>(TbMerchantInspect.class);
        util.exportTemplateExcel(response, "门店巡查数据");
    }

    /**
     * 获取门店巡查详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbMerchantInspectService.selectTbMerchantInspectById(id));
    }


    /**
     * 新增门店巡查 同时新增 门店巡查基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:add')")
    @Log(title = "门店巡查", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbMerchantInspect tbMerchantInspect) {
        tbMerchantInspect.setCreateBy(getNickName());
        if (ObjectUtil.isEmpty(tbMerchantInspect.getMerchantId())) {
            return error("所属门店ID不可为空！");
        }
        tbMerchantInspect.setPersonId(getUserId());
        tbMerchantInspect.setPersonName(getNickName());
        Long Id = tbMerchantInspectService.insertTbMerchantInspect(tbMerchantInspect);
        AjaxResult ajaxResult = toAjax(1);
        ajaxResult.put("formId",Id);
        System.err.println("添加万恶了");
        return ajaxResult;
    }

    /**
     * 修改门店巡查
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:edit')")
    @Log(title = "门店巡查", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbMerchantInspect tbMerchantInspect) {
        tbMerchantInspect.setUpdateBy(getNickName());
        return toAjax(tbMerchantInspectService.updateTbMerchantInspect(tbMerchantInspect));
    }

    /**
     * 删除门店巡查
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspect:remove')")
    @Log(title = "门店巡查", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbMerchantInspectService.deleteTbMerchantInspectByIds(ids));
    }


    /**
     * 获取门店巡查 表累计次数和巡查记录表本月次数
     */
    @GetMapping("/listCount")
    public AjaxResult listCount(TbMerchantInspect tbMerchantInspect) {
        Map<String, Integer> map = tbMerchantInspectService.selectTbMerchantInspectListCount(tbMerchantInspect);
        return success(map);
    }

    @GetMapping("/submitCount")
    public AjaxResult submitCount(TbMerchantInspect tbMerchantInspect) {
        tbMerchantInspect.setPersonId(getUserId());
        return success(tbMerchantInspectService.selectSubmitCount(tbMerchantInspect));
    }


}
