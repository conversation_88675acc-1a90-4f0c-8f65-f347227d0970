package com.ruoyi.microPlatform.controller.bigdata.gov.req;

import com.ruoyi.microPlatform.controller.bigdata.gov.GovBaseReq;
import lombok.Data;

@Data
public class GovEditFaceReq extends GovBaseReq {

    // 人脸id 编辑的时候需要
    private Integer faceId;

    private String name;
    // 人脸base64图片
    // 这个名字没有意义 只是为了转换
    private byte[] faceImg_byteToByteArrayInputStream;
    // 手机号
    private String phoneNumber;
    // 身份证号
    private String identityCard;
}
