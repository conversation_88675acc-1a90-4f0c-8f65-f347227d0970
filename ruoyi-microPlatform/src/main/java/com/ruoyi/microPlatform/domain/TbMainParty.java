package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 矛盾纠纷当事人信息对象 tb_main_party
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public class TbMainParty extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 矛盾纠纷id */
    @Excel(name = "矛盾纠纷id")
    private Long issueId;

    /** 矛盾纠纷当事人主键 */
    @Excel(name = "矛盾纠纷当事人主键")
    private String mdjfdsrzj;

    /** 矛盾纠纷主键 */
    @Excel(name = "矛盾纠纷主键")
    private String mdjfzj;

    /** 当事人证件种类（字典） */
    @Excel(name = "当事人证件种类", readConverterExp = "字=典")
    private String dsrzjzldm;

    /** 当事人证件号码 */
    @Excel(name = "当事人证件号码")
    private String dsrzjhm;

    /** 当事人证件种类名称 */
    @Excel(name = "当事人证件种类名称")
    private String dsrzjzlmc;

    /** 当事人姓名 */
    @Excel(name = "当事人姓名")
    private String dsrxm;

    /** 当事人联系电话 */
    @Excel(name = "当事人联系电话")
    private String lxdh;

    /** 当事人性别（字典） */
    @Excel(name = "当事人性别", readConverterExp = "字=典")
    private String xbdm;

    /** 当事人性别 */
    @Excel(name = "当事人性别")
    private String xb;

    /** 当事人婚姻状况（字典） */
    @Excel(name = "当事人婚姻状况", readConverterExp = "字=典")
    private String hyzkdm;

    /**  */
    @Excel(name = "")
    private String hyzk;

    /** 当事人文化程度（字典） */
    @Excel(name = "当事人文化程度", readConverterExp = "字=典")
    private String whcddm;

    /**  */
    @Excel(name = "")
    private String whcd;

    /** 当事人民族（字典） */
    @Excel(name = "当事人民族", readConverterExp = "字=典")
    private String mzdm;

    /**  */
    @Excel(name = "")
    private String mz;

    /** 当事人户籍地址 */
    @Excel(name = "当事人户籍地址")
    private String dsrhjdz;

    /** 当事人现住址 */
    @Excel(name = "当事人现住址")
    private String dsrxzz;

    /** 当事人年龄 */
    @Excel(name = "当事人年龄")
    private String nl;

    /** 当事人出生日期 */
    @Excel(name = "当事人出生日期")
    private String csrq;

    /** 当事人工作单位 */
    @Excel(name = "当事人工作单位")
    private String gzdw;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setIssueId(Long issueId) 
    {
        this.issueId = issueId;
    }

    public Long getIssueId() 
    {
        return issueId;
    }
    public void setMdjfdsrzj(String mdjfdsrzj) 
    {
        this.mdjfdsrzj = mdjfdsrzj;
    }

    public String getMdjfdsrzj() 
    {
        return mdjfdsrzj;
    }
    public void setMdjfzj(String mdjfzj) 
    {
        this.mdjfzj = mdjfzj;
    }

    public String getMdjfzj() 
    {
        return mdjfzj;
    }
    public void setDsrzjzldm(String dsrzjzldm) 
    {
        this.dsrzjzldm = dsrzjzldm;
    }

    public String getDsrzjzldm() 
    {
        return dsrzjzldm;
    }
    public void setDsrzjhm(String dsrzjhm) 
    {
        this.dsrzjhm = dsrzjhm;
    }

    public String getDsrzjhm() 
    {
        return dsrzjhm;
    }
    public void setDsrzjzlmc(String dsrzjzlmc) 
    {
        this.dsrzjzlmc = dsrzjzlmc;
    }

    public String getDsrzjzlmc() 
    {
        return dsrzjzlmc;
    }
    public void setDsrxm(String dsrxm) 
    {
        this.dsrxm = dsrxm;
    }

    public String getDsrxm() 
    {
        return dsrxm;
    }
    public void setLxdh(String lxdh) 
    {
        this.lxdh = lxdh;
    }

    public String getLxdh() 
    {
        return lxdh;
    }
    public void setXbdm(String xbdm) 
    {
        this.xbdm = xbdm;
    }

    public String getXbdm() 
    {
        return xbdm;
    }
    public void setXb(String xb) 
    {
        this.xb = xb;
    }

    public String getXb() 
    {
        return xb;
    }
    public void setHyzkdm(String hyzkdm) 
    {
        this.hyzkdm = hyzkdm;
    }

    public String getHyzkdm() 
    {
        return hyzkdm;
    }
    public void setHyzk(String hyzk) 
    {
        this.hyzk = hyzk;
    }

    public String getHyzk() 
    {
        return hyzk;
    }
    public void setWhcddm(String whcddm) 
    {
        this.whcddm = whcddm;
    }

    public String getWhcddm() 
    {
        return whcddm;
    }
    public void setWhcd(String whcd) 
    {
        this.whcd = whcd;
    }

    public String getWhcd() 
    {
        return whcd;
    }
    public void setMzdm(String mzdm) 
    {
        this.mzdm = mzdm;
    }

    public String getMzdm() 
    {
        return mzdm;
    }
    public void setMz(String mz) 
    {
        this.mz = mz;
    }

    public String getMz() 
    {
        return mz;
    }
    public void setDsrhjdz(String dsrhjdz) 
    {
        this.dsrhjdz = dsrhjdz;
    }

    public String getDsrhjdz() 
    {
        return dsrhjdz;
    }
    public void setDsrxzz(String dsrxzz) 
    {
        this.dsrxzz = dsrxzz;
    }

    public String getDsrxzz() 
    {
        return dsrxzz;
    }
    public void setNl(String nl) 
    {
        this.nl = nl;
    }

    public String getNl() 
    {
        return nl;
    }
    public void setCsrq(String csrq) 
    {
        this.csrq = csrq;
    }

    public String getCsrq() 
    {
        return csrq;
    }
    public void setGzdw(String gzdw) 
    {
        this.gzdw = gzdw;
    }

    public String getGzdw() 
    {
        return gzdw;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("issueId", getIssueId())
            .append("mdjfdsrzj", getMdjfdsrzj())
            .append("mdjfzj", getMdjfzj())
            .append("dsrzjzldm", getDsrzjzldm())
            .append("dsrzjhm", getDsrzjhm())
            .append("dsrzjzlmc", getDsrzjzlmc())
            .append("dsrxm", getDsrxm())
            .append("lxdh", getLxdh())
            .append("xbdm", getXbdm())
            .append("xb", getXb())
            .append("hyzkdm", getHyzkdm())
            .append("hyzk", getHyzk())
            .append("whcddm", getWhcddm())
            .append("whcd", getWhcd())
            .append("mzdm", getMzdm())
            .append("mz", getMz())
            .append("dsrhjdz", getDsrhjdz())
            .append("dsrxzz", getDsrxzz())
            .append("nl", getNl())
            .append("csrq", getCsrq())
            .append("gzdw", getGzdw())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
