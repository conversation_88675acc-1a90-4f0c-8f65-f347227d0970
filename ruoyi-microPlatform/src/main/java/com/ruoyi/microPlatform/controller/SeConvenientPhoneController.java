package com.ruoyi.microPlatform.controller;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeConvenientPhone;
import com.ruoyi.microPlatform.service.ISeConvenientPhoneService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 便民电话Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/convenientPhone")
public class SeConvenientPhoneController extends BaseController {
    @Autowired
    private ISeConvenientPhoneService seConvenientPhoneService;

    /**
     * 查询便民电话列表
     */
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(SeConvenientPhone seConvenientPhone) {
        startPage();
        try {
            handle(seConvenientPhone);
        } catch (Exception e) {
            throw new ServiceException("请登录1", HttpStatus.UNAUTHORIZED);
        }
        List<SeConvenientPhone> list = seConvenientPhoneService.selectSeConvenientPhoneList(seConvenientPhone);
        return getDataTable(list);
    }


    public void handle(SeConvenientPhone seConvenientPhone) {
        if (ObjectUtil.isNotEmpty(seConvenientPhone.getQueryType())) {
            if (seConvenientPhone.getQueryType() == 1) {
                // 查询已经审核通过的
                seConvenientPhone.setStatus(1);
            } else if (seConvenientPhone.getQueryType() == 2) {
                // 个人提交的
                seConvenientPhone.setCreateId(getUserId());
            }
        }
        if (ObjectUtil.isEmpty(seConvenientPhone.getDeptId())) {
            if (!SecurityUtils.isAdmin(getUserId())) {
                seConvenientPhone.setDeptId(getDataDeptId());
            }
        }
    }

    @GetMapping("/submitCount")
    public AjaxResult submitCount(SeConvenientPhone seConvenientPhone) {
        seConvenientPhone.setCreateId(getUserId());
        return success(seConvenientPhoneService.selectSubmitCount(seConvenientPhone));
    }

    /**
     * 导出便民电话列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:export')")
    @Log(title = "便民电话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeConvenientPhone seConvenientPhone) {
        try {
            handle(seConvenientPhone);
        } catch (Exception e) {
            throw new ServiceException("请登录1", HttpStatus.UNAUTHORIZED);
        }
        List<SeConvenientPhone> list = seConvenientPhoneService.selectSeConvenientPhoneList(seConvenientPhone);
        ExcelUtil<SeConvenientPhone> util = new ExcelUtil<SeConvenientPhone>(SeConvenientPhone.class);
        util.exportExcel(response, list, "便民电话数据");
    }

    @Log(title = "便民电话", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SeConvenientPhone> util = new ExcelUtil<SeConvenientPhone>(SeConvenientPhone.class);
        List<SeConvenientPhone> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seConvenientPhoneService.importSeConvenientPhone(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SeConvenientPhone> util = new ExcelUtil<SeConvenientPhone>(SeConvenientPhone.class);
        util.exportTemplateExcel(response, "便民电话数据");
    }

    /**
     * 获取便民电话详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(seConvenientPhoneService.selectSeConvenientPhoneById(id));
    }

    public void getDeptInfo(SeConvenientPhone seConvenientPhone) {
        if (ObjectUtil.isEmpty(seConvenientPhone.getDeptId())) {
            seConvenientPhone.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(seConvenientPhone.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            seConvenientPhone.setCounty(split[1]);
            if (split.length >= 3) {
                seConvenientPhone.setCountry(split[2]);
                if (split.length >= 4) {
                    seConvenientPhone.setTown(split[3]);
                }
            }
        }
    }

    /**
     * 新增便民电话
     */
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:add')")
    @Log(title = "便民电话", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeConvenientPhone seConvenientPhone) {
        seConvenientPhone.setCreateBy(getUsername());
        getDeptInfo(seConvenientPhone);
        if (ObjectUtil.isEmpty(seConvenientPhone.getStatus()) || seConvenientPhone.getStatus() != 1) {
            seConvenientPhone.setCreateId(getUserId());
        }
        return toAjax(seConvenientPhoneService.insertSeConvenientPhone(seConvenientPhone));
    }

    /**
     * 修改便民电话
     */
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:edit')")
    @Log(title = "便民电话", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeConvenientPhone seConvenientPhone) {
        seConvenientPhone.setUpdateBy(getUsername());
        getDeptInfo(seConvenientPhone);
        return toAjax(seConvenientPhoneService.updateSeConvenientPhone(seConvenientPhone));
    }

    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:audit')")
    @Log(title = "便民电话(审核)", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @RepeatSubmit
    public AjaxResult audit(@RequestBody SeConvenientPhone seConvenientPhone) {
        seConvenientPhone.setUpdateBy(getUsername());
        if (ObjectUtil.isEmpty(seConvenientPhone.getAuditResult())) {
            return error("审核结果不可为空！");
        }
        if (seConvenientPhone.getAuditResult() == 0) {
            seConvenientPhone.setStatus(3);// 审核驳回
        } else {
            seConvenientPhone.setStatus(1);
        }
        return toAjax(seConvenientPhoneService.updateSeConvenientPhone(seConvenientPhone));
    }

    /**
     * 删除便民电话
     */
    @PreAuthorize("@ss.hasPermi('sentiment:convenientPhone:remove')")
    @Log(title = "便民电话", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(seConvenientPhoneService.deleteSeConvenientPhoneByIds(ids));
    }
}
