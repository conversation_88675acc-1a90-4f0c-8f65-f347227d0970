package com.ruoyi.dataview.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 水利局涵洞监测站点对象 db_wm_site
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbWmSiteVO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 站点编号 */
    @Excel(name = "站点编号")
    private String stCode;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String stName;

    /** 设备状态(0正常 1离线 2异常) */
    @Excel(name = "设备状态",readConverterExp = "0=正常,1=离线,2=异常")
    private Long status;

    /** 最新观测时间(ttTime) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新观测时间(ttTime)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastObsTime;

    /** 最新存储时间(sttTime) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新存储时间(sttTime)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastStoreTime;

}
