package com.ruoyi.dataview.service.impl;

import java.util.Date;
import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbBpmNodeJumpMapper;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;
import com.ruoyi.dataview.service.IDbBpmNodeJumpService;

/**
 * 运管服案件流程流转记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbBpmNodeJumpServiceImpl implements IDbBpmNodeJumpService
{
    @Autowired
    private DbBpmNodeJumpMapper dbBpmNodeJumpMapper;

    /**
     * 查询运管服案件流程流转记录
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 运管服案件流程流转记录
     */
    @Override
    public DbBpmNodeJump selectDbBpmNodeJumpById(Long id)
    {
        return dbBpmNodeJumpMapper.selectDbBpmNodeJumpById(id);
    }

    /**
     * 校验运管服案件流程流转记录是否存在
     *
     * @param dbBpmNodeJump
     * @return boolean
     */
    @Override
    public boolean checkDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump){
        DbBpmNodeJump old = dbBpmNodeJumpMapper.checkDbBpmNodeJumpUnique(dbBpmNodeJump);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询运管服案件流程流转记录列表
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 运管服案件流程流转记录
     */
    @Override
    public List<DbBpmNodeJump> selectDbBpmNodeJumpList(DbBpmNodeJump dbBpmNodeJump)
    {
        return dbBpmNodeJumpMapper.selectDbBpmNodeJumpList(dbBpmNodeJump);
    }

    /**
     * 导入运管服案件流程流转记录
     *
     * @param infos       运管服案件流程流转记录列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbBpmNodeJump(List<DbBpmNodeJump> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入运管服案件流程流转记录数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbBpmNodeJump info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                DbBpmNodeJump old = dbBpmNodeJumpMapper.checkDbBpmNodeJumpUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbBpmNodeJumpMapper.insertDbBpmNodeJump(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbBpmNodeJumpMapper.updateDbBpmNodeJump(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    @Override
    public int insertDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump)
    {
        dbBpmNodeJump.setCreateTime(DateUtils.getNowDate());
        return dbBpmNodeJumpMapper.insertDbBpmNodeJump(dbBpmNodeJump);
    }

    /**
     * 修改运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    @Override
    public int updateDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump)
    {
        dbBpmNodeJump.setUpdateTime(DateUtils.getNowDate());
        return dbBpmNodeJumpMapper.updateDbBpmNodeJump(dbBpmNodeJump);
    }

    /**
     * 批量删除运管服案件流程流转记录
     * 
     * @param ids 需要删除的运管服案件流程流转记录主键
     * @return 结果
     */
    @Override
    public int deleteDbBpmNodeJumpByIds(Long[] ids)
    {
        return dbBpmNodeJumpMapper.deleteDbBpmNodeJumpByIds(ids);
    }

    /**
     * 删除运管服案件流程流转记录信息
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 结果
     */
    @Override
    public int deleteDbBpmNodeJumpById(Long id)
    {
        return dbBpmNodeJumpMapper.deleteDbBpmNodeJumpById(id);
    }
}
