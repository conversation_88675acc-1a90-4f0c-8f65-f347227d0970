<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbHotlineDetailMapper">

    <resultMap type="DbHotlineDetail" id="DbHotlineDetailResult">
            <result property="id"    column="id"    />
            <result property="recId"    column="rec_id"    />
            <result property="createTime"    column="create_time"    />
            <result property="address"    column="address"    />
            <result property="eventDesc"    column="event_desc"    />
            <result property="eventSrcName"    column="event_src_name"    />
            <result property="recTypeName"    column="rec_type_name"    />
            <result property="eventTypeName"    column="event_type_name"    />
            <result property="mainTypeName"    column="main_type_name"    />
            <result property="subTypeName"    column="sub_type_name"    />
            <result property="districtName"    column="district_name"    />
            <result property="streetName"    column="street_name"    />
            <result property="firstUnitName"    column="first_unit_name"    />
            <result property="thirdTypeName"    column="third_type_name"    />
            <result property="caseGoal"    column="case_goal"    />
            <result property="urgencyLevel"    column="urgency_level"    />
            <result property="eventStatus"    column="event_status"    />
            <result property="handleStartTime"    column="handle_start_time"    />
            <result property="handleDeadline"    column="handle_deadline"    />
            <result property="thirdPartyDeadline"    column="third_party_deadline"    />
            <result property="appealerName"    column="appealer_name"    />
            <result property="appealerContact"    column="appealer_contact"    />
            <result property="isReturnVisit"    column="is_return_visit"    />
            <result property="handleOpinion"    column="handle_opinion"    />
            <result property="finishOpinion"    column="finish_opinion"    />
            <result property="returnVisitScore"    column="return_visit_score"    />
            <result property="returnVisitContent"    column="return_visit_content"    />
            <result property="operatorName"    column="operator_name"    />
            <result property="handleEndTime"    column="handle_end_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDbHotlineDetailVo">
        select id, rec_id, create_time, address, event_desc, event_src_name, rec_type_name, event_type_name, main_type_name, sub_type_name, district_name, street_name, first_unit_name, third_type_name, case_goal, urgency_level, event_status, handle_start_time, handle_deadline, third_party_deadline, appealer_name, appealer_contact, is_return_visit, handle_opinion, finish_opinion, return_visit_score, return_visit_content, operator_name, handle_end_time, create_by, update_time, update_by from db_hotline_detail
    </sql>

    <select id="selectDbHotlineDetailList" parameterType="DbHotlineDetail" resultMap="DbHotlineDetailResult">
        <include refid="selectDbHotlineDetailVo"/>
        <where>
                        <if test="recId != null  and recId != ''"> and rec_id = #{recId}</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="eventDesc != null  and eventDesc != ''"> and event_desc = #{eventDesc}</if>
                        <if test="eventSrcName != null  and eventSrcName != ''"> and event_src_name like concat('%', #{eventSrcName}, '%')</if>
                        <if test="recTypeName != null  and recTypeName != ''"> and rec_type_name like concat('%', #{recTypeName}, '%')</if>
                        <if test="eventTypeName != null  and eventTypeName != ''"> and event_type_name like concat('%', #{eventTypeName}, '%')</if>
                        <if test="mainTypeName != null  and mainTypeName != ''"> and main_type_name like concat('%', #{mainTypeName}, '%')</if>
                        <if test="subTypeName != null  and subTypeName != ''"> and sub_type_name like concat('%', #{subTypeName}, '%')</if>
                        <if test="districtName != null  and districtName != ''"> and district_name like concat('%', #{districtName}, '%')</if>
                        <if test="streetName != null  and streetName != ''"> and street_name like concat('%', #{streetName}, '%')</if>
                        <if test="firstUnitName != null  and firstUnitName != ''"> and first_unit_name like concat('%', #{firstUnitName}, '%')</if>
                        <if test="thirdTypeName != null  and thirdTypeName != ''"> and third_type_name like concat('%', #{thirdTypeName}, '%')</if>
                        <if test="caseGoal != null  and caseGoal != ''"> and case_goal = #{caseGoal}</if>
                        <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
                        <if test="eventStatus != null  and eventStatus != ''"> and event_status = #{eventStatus}</if>
                        <if test="handleStartTime != null "> and handle_start_time = #{handleStartTime}</if>
                        <if test="handleDeadline != null "> and handle_deadline = #{handleDeadline}</if>
                        <if test="thirdPartyDeadline != null "> and third_party_deadline = #{thirdPartyDeadline}</if>
                        <if test="appealerName != null  and appealerName != ''"> and appealer_name like concat('%', #{appealerName}, '%')</if>
                        <if test="appealerContact != null  and appealerContact != ''"> and appealer_contact = #{appealerContact}</if>
                        <if test="isReturnVisit != null "> and is_return_visit = #{isReturnVisit}</if>
                        <if test="handleOpinion != null  and handleOpinion != ''"> and handle_opinion = #{handleOpinion}</if>
                        <if test="finishOpinion != null  and finishOpinion != ''"> and finish_opinion = #{finishOpinion}</if>
                        <if test="returnVisitScore != null  and returnVisitScore != ''"> and return_visit_score = #{returnVisitScore}</if>
                        <if test="returnVisitContent != null  and returnVisitContent != ''"> and return_visit_content = #{returnVisitContent}</if>
                        <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
                        <if test="handleEndTime != null "> and handle_end_time = #{handleEndTime}</if>
        </where>
    </select>

    <select id="selectDbHotlineDetailCount" parameterType="DbHotlineDetail" resultType="int">
        select count(*) from db_hotline_detail
        <where>
            <if test="recId != null  and recId != ''"> and rec_id = #{recId}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="eventDesc != null  and eventDesc != ''"> and event_desc = #{eventDesc}</if>
            <if test="eventSrcName != null  and eventSrcName != ''"> and event_src_name like concat('%', #{eventSrcName}, '%')</if>
            <if test="recTypeName != null  and recTypeName != ''"> and rec_type_name like concat('%', #{recTypeName}, '%')</if>
            <if test="eventTypeName != null  and eventTypeName != ''"> and event_type_name like concat('%', #{eventTypeName}, '%')</if>
            <if test="mainTypeName != null  and mainTypeName != ''"> and main_type_name like concat('%', #{mainTypeName}, '%')</if>
            <if test="subTypeName != null  and subTypeName != ''"> and sub_type_name like concat('%', #{subTypeName}, '%')</if>
            <if test="districtName != null  and districtName != ''"> and district_name like concat('%', #{districtName}, '%')</if>
            <if test="streetName != null  and streetName != ''"> and street_name like concat('%', #{streetName}, '%')</if>
            <if test="firstUnitName != null  and firstUnitName != ''"> and first_unit_name like concat('%', #{firstUnitName}, '%')</if>
            <if test="thirdTypeName != null  and thirdTypeName != ''"> and third_type_name like concat('%', #{thirdTypeName}, '%')</if>
            <if test="caseGoal != null  and caseGoal != ''"> and case_goal = #{caseGoal}</if>
            <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="eventStatus != null  and eventStatus != ''"> and event_status = #{eventStatus}</if>
            <if test="handleStartTime != null "> and handle_start_time = #{handleStartTime}</if>
            <if test="handleDeadline != null "> and handle_deadline = #{handleDeadline}</if>
            <if test="thirdPartyDeadline != null "> and third_party_deadline = #{thirdPartyDeadline}</if>
            <if test="appealerName != null  and appealerName != ''"> and appealer_name like concat('%', #{appealerName}, '%')</if>
            <if test="appealerContact != null  and appealerContact != ''"> and appealer_contact = #{appealerContact}</if>
            <if test="isReturnVisit != null "> and is_return_visit = #{isReturnVisit}</if>
            <if test="handleOpinion != null  and handleOpinion != ''"> and handle_opinion = #{handleOpinion}</if>
            <if test="finishOpinion != null  and finishOpinion != ''"> and finish_opinion = #{finishOpinion}</if>
            <if test="returnVisitScore != null  and returnVisitScore != ''"> and return_visit_score = #{returnVisitScore}</if>
            <if test="returnVisitContent != null  and returnVisitContent != ''"> and return_visit_content = #{returnVisitContent}</if>
            <if test="operatorName != null  and operatorName != ''"> and operator_name like concat('%', #{operatorName}, '%')</if>
            <if test="handleEndTime != null "> and handle_end_time = #{handleEndTime}</if>
        </where>
    </select>

    <select id="selectDbHotlineDetailById" parameterType="Long" resultMap="DbHotlineDetailResult">
            <include refid="selectDbHotlineDetailVo"/>
            where id = #{id}
    </select>

    <select id="checkDbHotlineDetailUnique" parameterType="DbHotlineDetail" resultMap="DbHotlineDetailResult">
        select id from db_hotline_detail where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="hotlineDetailGroupRec" resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        select rec_type_name as lable,
        count(1) as count
        from db_hotline_detail
        where 1=1
        <if test="bigdataParam.year != null">
            and YEAR(create_time) = #{bigdataParam.year}
        </if>
        group by rec_type_name
    </select>

    <insert id="insertDbHotlineDetail" parameterType="DbHotlineDetail" useGeneratedKeys="true" keyProperty="id">
        insert into db_hotline_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="recId != null and recId != ''">rec_id,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="address != null">address,</if>
                    <if test="eventDesc != null">event_desc,</if>
                    <if test="eventSrcName != null">event_src_name,</if>
                    <if test="recTypeName != null">rec_type_name,</if>
                    <if test="eventTypeName != null">event_type_name,</if>
                    <if test="mainTypeName != null">main_type_name,</if>
                    <if test="subTypeName != null">sub_type_name,</if>
                    <if test="districtName != null">district_name,</if>
                    <if test="streetName != null">street_name,</if>
                    <if test="firstUnitName != null">first_unit_name,</if>
                    <if test="thirdTypeName != null">third_type_name,</if>
                    <if test="caseGoal != null">case_goal,</if>
                    <if test="urgencyLevel != null">urgency_level,</if>
                    <if test="eventStatus != null">event_status,</if>
                    <if test="handleStartTime != null">handle_start_time,</if>
                    <if test="handleDeadline != null">handle_deadline,</if>
                    <if test="thirdPartyDeadline != null">third_party_deadline,</if>
                    <if test="appealerName != null">appealer_name,</if>
                    <if test="appealerContact != null">appealer_contact,</if>
                    <if test="isReturnVisit != null">is_return_visit,</if>
                    <if test="handleOpinion != null">handle_opinion,</if>
                    <if test="finishOpinion != null">finish_opinion,</if>
                    <if test="returnVisitScore != null">return_visit_score,</if>
                    <if test="returnVisitContent != null">return_visit_content,</if>
                    <if test="operatorName != null">operator_name,</if>
                    <if test="handleEndTime != null">handle_end_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="recId != null and recId != ''">#{recId},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="address != null">#{address},</if>
                    <if test="eventDesc != null">#{eventDesc},</if>
                    <if test="eventSrcName != null">#{eventSrcName},</if>
                    <if test="recTypeName != null">#{recTypeName},</if>
                    <if test="eventTypeName != null">#{eventTypeName},</if>
                    <if test="mainTypeName != null">#{mainTypeName},</if>
                    <if test="subTypeName != null">#{subTypeName},</if>
                    <if test="districtName != null">#{districtName},</if>
                    <if test="streetName != null">#{streetName},</if>
                    <if test="firstUnitName != null">#{firstUnitName},</if>
                    <if test="thirdTypeName != null">#{thirdTypeName},</if>
                    <if test="caseGoal != null">#{caseGoal},</if>
                    <if test="urgencyLevel != null">#{urgencyLevel},</if>
                    <if test="eventStatus != null">#{eventStatus},</if>
                    <if test="handleStartTime != null">#{handleStartTime},</if>
                    <if test="handleDeadline != null">#{handleDeadline},</if>
                    <if test="thirdPartyDeadline != null">#{thirdPartyDeadline},</if>
                    <if test="appealerName != null">#{appealerName},</if>
                    <if test="appealerContact != null">#{appealerContact},</if>
                    <if test="isReturnVisit != null">#{isReturnVisit},</if>
                    <if test="handleOpinion != null">#{handleOpinion},</if>
                    <if test="finishOpinion != null">#{finishOpinion},</if>
                    <if test="returnVisitScore != null">#{returnVisitScore},</if>
                    <if test="returnVisitContent != null">#{returnVisitContent},</if>
                    <if test="operatorName != null">#{operatorName},</if>
                    <if test="handleEndTime != null">#{handleEndTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateDbHotlineDetail" parameterType="DbHotlineDetail">
        update db_hotline_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="recId != null and recId != ''">rec_id = #{recId},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="eventDesc != null">event_desc = #{eventDesc},</if>
                    <if test="eventSrcName != null">event_src_name = #{eventSrcName},</if>
                    <if test="recTypeName != null">rec_type_name = #{recTypeName},</if>
                    <if test="eventTypeName != null">event_type_name = #{eventTypeName},</if>
                    <if test="mainTypeName != null">main_type_name = #{mainTypeName},</if>
                    <if test="subTypeName != null">sub_type_name = #{subTypeName},</if>
                    <if test="districtName != null">district_name = #{districtName},</if>
                    <if test="streetName != null">street_name = #{streetName},</if>
                    <if test="firstUnitName != null">first_unit_name = #{firstUnitName},</if>
                    <if test="thirdTypeName != null">third_type_name = #{thirdTypeName},</if>
                    <if test="caseGoal != null">case_goal = #{caseGoal},</if>
                    <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
                    <if test="eventStatus != null">event_status = #{eventStatus},</if>
                    <if test="handleStartTime != null">handle_start_time = #{handleStartTime},</if>
                    <if test="handleDeadline != null">handle_deadline = #{handleDeadline},</if>
                    <if test="thirdPartyDeadline != null">third_party_deadline = #{thirdPartyDeadline},</if>
                    <if test="appealerName != null">appealer_name = #{appealerName},</if>
                    <if test="appealerContact != null">appealer_contact = #{appealerContact},</if>
                    <if test="isReturnVisit != null">is_return_visit = #{isReturnVisit},</if>
                    <if test="handleOpinion != null">handle_opinion = #{handleOpinion},</if>
                    <if test="finishOpinion != null">finish_opinion = #{finishOpinion},</if>
                    <if test="returnVisitScore != null">return_visit_score = #{returnVisitScore},</if>
                    <if test="returnVisitContent != null">return_visit_content = #{returnVisitContent},</if>
                    <if test="operatorName != null">operator_name = #{operatorName},</if>
                    <if test="handleEndTime != null">handle_end_time = #{handleEndTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbHotlineDetailById" parameterType="Long">
        delete from db_hotline_detail where id = #{id}
    </delete>

    <delete id="deleteDbHotlineDetailByIds" parameterType="String">
        delete from db_hotline_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>