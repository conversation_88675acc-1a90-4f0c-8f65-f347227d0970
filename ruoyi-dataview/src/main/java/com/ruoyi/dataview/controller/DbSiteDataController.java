package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbSiteDataConvert;
import com.ruoyi.dataview.domain.dto.DbSiteDataFormDTO;
import com.ruoyi.dataview.domain.dto.DbSiteDataQueryDTO;
import com.ruoyi.dataview.domain.dto.SiteParamsQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbSiteData;
import com.ruoyi.dataview.service.IDbSiteDataService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 水利局涵洞监测数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/fiveLevel/siteData")
@RequiredArgsConstructor
public class DbSiteDataController extends BaseController
{
    @Autowired
    private final IDbSiteDataService dbSiteDataService;

    @Autowired
    private final DbSiteDataConvert siteDataConvert;


    /**
     * 查询水利局涵洞监测数据列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbSiteDataQueryDTO siteDataQueryDTO)
    {
        DbSiteData dbSiteData = siteDataConvert.toDO(siteDataQueryDTO);
        startPage();
        return getDataTable(siteDataConvert.toVOList(dbSiteDataService.selectDbSiteDataList(dbSiteData)));
    }

    /**
     * 导出水利局涵洞监测数据列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:export')")
    @Log(title = "水利局涵洞监测数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbSiteDataQueryDTO siteDataQueryDTO)
    {
        DbSiteData dbSiteData = siteDataConvert.toDO(siteDataQueryDTO);
        List<DbSiteData> list = dbSiteDataService.selectDbSiteDataList(dbSiteData);
        ExcelUtil<DbSiteData> util = new ExcelUtil<DbSiteData>(DbSiteData.class);
        util.exportExcel(response, list, "水利局涵洞监测数据数据");
    }

    @Log(title = "水利局涵洞监测数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbSiteData> util = new ExcelUtil<DbSiteData>(DbSiteData.class);
        List<DbSiteData> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbSiteDataService.importDbSiteData(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbSiteData> util = new ExcelUtil<DbSiteData>(DbSiteData.class);
        util.exportTemplateExcel(response, "水利局涵洞监测数据数据");
    }

    /**
     * 获取水利局涵洞监测数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(siteDataConvert.toVO(dbSiteDataService.selectDbSiteDataById(id)));
    }

    /**
     * 新增水利局涵洞监测数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:add')")
    @Log(title = "水利局涵洞监测数据", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbSiteDataFormDTO siteDataFormDTO)
    {
        DbSiteData dbSiteData = siteDataConvert.toDO(siteDataFormDTO);
        dbSiteData.setCreateBy(getUsername());
        return toAjax(dbSiteDataService.insertDbSiteData(dbSiteData));
    }

    /**
     * 修改水利局涵洞监测数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:edit')")
    @Log(title = "水利局涵洞监测数据", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbSiteDataFormDTO siteDataFormDTO)
    {
        DbSiteData dbSiteData = siteDataConvert.toDO(siteDataFormDTO);
        dbSiteData.setUpdateBy(getUsername());
        if (dbSiteDataService.checkDbSiteData(dbSiteData)){
            return error("数据已存在！");
        }
        return toAjax(dbSiteDataService.updateDbSiteData(dbSiteData));
    }

    /**
     * 删除水利局涵洞监测数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:siteData:remove')")
    @Log(title = "水利局涵洞监测数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(dbSiteDataService.deleteDbSiteDataByIds(ids));
    }

    @PostMapping("/site-login")
    public AjaxResult siteLogin(){
        String login = "xczsd";
        String pwd = "xczsd666";
        return success(dbSiteDataService.siteSysLogin(login,pwd));
    }

    @GetMapping("/site-groups")
    public AjaxResult getSiteNoByGroupsNo(String NFSESSIONID){
        return success(dbSiteDataService.proxyRequestGetSiteGroups(NFSESSIONID));
    }

    @GetMapping("/site-details")
    public AjaxResult getSiteDetailsByCode(String stCode,String NFSESSIONID){
        return success(dbSiteDataService.proxyRequestgGetSiteDetailsByCode(stCode,NFSESSIONID));
    }

    @GetMapping("/site-history")
    public TableDataInfo getSiteHistoryDateList(SiteParamsQueryDTO queryDTO, String NFSESSIONID){
        return  dbSiteDataService.proxyRequestGetSiteHistoryDateList(queryDTO, NFSESSIONID);
    }
    @GetMapping("/site-echart")
    public AjaxResult getSiteEchart(SiteParamsQueryDTO queryDTO, String NFSESSIONID){
        return  success(dbSiteDataService.proxyRequestGetSiteEchart(queryDTO, NFSESSIONID));
    }

    @GetMapping("/site-statistics")
    public AjaxResult getSiteStatistics(SiteParamsQueryDTO queryDTO, String NFSESSIONID){
        return  success(dbSiteDataService.proxyRequestGetSiteStatistics(queryDTO, NFSESSIONID));
    }



}
