<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbPetitionInfoMapper">

    <resultMap type="com.ruoyi.dataview.domain.po.DbPetitionInfo" id="DbPetitionInfoResult">
            <result property="id"    column="id"    />
            <result property="petitionDate"    column="petition_date"    />
            <result property="isReviewed"    column="is_reviewed"    />
            <result property="overview"    column="overview"    />
            <result property="registerTime"    column="register_time"    />
            <result property="petitionForm"    column="petition_form"    />
            <result property="name"    column="name"    />
            <result property="address"    column="address"    />
            <result property="petitionPurpose"    column="petition_purpose"    />
            <result property="contentCategory"    column="content_category"    />
            <result property="problemLocation"    column="problem_location"    />
            <result property="responsibleUnit"    column="responsible_unit"    />
            <result property="petitionStatus"    column="petition_status"    />
            <result property="registerOrg"    column="register_org"    />
            <result property="complaintMethod"    column="complaint_method"    />
            <result property="repeatCount"    column="repeat_count"    />
            <result property="receptionLeader"    column="reception_leader"    />
            <result property="registerDepartment"    column="register_department"    />
            <result property="handleMethod"    column="handle_method"    />
            <result property="handleOpinion"    column="handle_opinion"    />
            <result property="inSatisfactionEval"    column="in_satisfaction_eval"    />
            <result property="petitionNo"    column="petition_no"    />
            <result property="isFinalized"    column="is_finalized"    />
            <result property="peopleCount"    column="people_count"    />
            <result property="handlingSituation"    column="handling_situation"    />
            <result property="handlingTime"    column="handling_time"    />
            <result property="householdReg"    column="household_reg"    />
            <result property="ethnicity"    column="ethnicity"    />
            <result property="hotIssue"    column="hot_issue"    />
            <result property="remainingDays"    column="remaining_days"    />
            <result property="isClosed"    column="is_closed"    />
            <result property="isAccumulatedCase"    column="is_accumulated_case"    />
            <result property="isJoint"    column="is_joint"    />
            <result property="isAnonymous"    column="is_anonymous"    />
            <result property="isThreeCrossThreeSplit"    column="is_three_cross_three_split"    />
            <result property="isInvolvingMacau"    column="is_involving_macau"    />
            <result property="isInvolvingLaw"    column="is_involving_law"    />
            <result property="isInvolvingHk"    column="is_involving_hk"    />
            <result property="isInvolvingOverseasChinese"    column="is_involving_overseas_chinese"    />
            <result property="isInvolvingTaiwan"    column="is_involving_taiwan"    />
            <result property="isThreatening"    column="is_threatening"    />
            <result property="isRepeatPetition"    column="is_repeat_petition"    />
            <result property="phone"    column="phone"    />
            <result property="firstPetitionOrg"    column="first_petition_org"    />
            <result property="firstPetitionDate"    column="first_petition_date"    />
            <result property="firstComplaintMethod"    column="first_complaint_method"    />
            <result property="complaintContent"    column="complaint_content"    />
            <result property="petitionReason"    column="petition_reason"    />
            <result property="gender"    column="gender"    />
            <result property="politicalStatus"    column="political_status"    />
            <result property="idNumber"    column="id_number"    />
            <result property="idType"    column="id_type"    />
            <result property="occupation"    column="occupation"    />
            <result property="keywords"    column="keywords"    />
            <result property="lastPetitionDate"    column="last_petition_date"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDbPetitionInfoVo">
        select id, petition_date, is_reviewed, overview, register_time, petition_form, name, address, petition_purpose, content_category, problem_location, responsible_unit, petition_status, register_org, complaint_method, repeat_count, reception_leader, register_department, handle_method, handle_opinion, in_satisfaction_eval, petition_no, is_finalized, people_count, handling_situation, handling_time, household_reg, ethnicity, hot_issue, remaining_days, is_closed, is_accumulated_case, is_joint, is_anonymous, is_three_cross_three_split, is_involving_macau, is_involving_law, is_involving_hk, is_involving_overseas_chinese, is_involving_taiwan, is_threatening, is_repeat_petition, phone, first_petition_org, first_petition_date, first_complaint_method, complaint_content, petition_reason, gender, political_status, id_number, id_type, occupation, keywords, last_petition_date, create_time, create_by, update_time, update_by from db_petition_info
    </sql>

    <select id="selectDbPetitionInfoList" parameterType="com.ruoyi.dataview.domain.po.DbPetitionInfo" resultMap="DbPetitionInfoResult">
        <include refid="selectDbPetitionInfoVo"/>
        <where>
                        <if test="petitionDate != null "> and petition_date = #{petitionDate}</if>
                        <if test="isReviewed != null "> and is_reviewed = #{isReviewed}</if>
                        <if test="overview != null  and overview != ''"> and overview = #{overview}</if>
                        <if test="registerTime != null "> and register_time = #{registerTime}</if>
                        <if test="petitionForm != null  and petitionForm != ''"> and petition_form = #{petitionForm}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="petitionPurpose != null  and petitionPurpose != ''"> and petition_purpose = #{petitionPurpose}</if>
                        <if test="contentCategory != null  and contentCategory != ''"> and content_category = #{contentCategory}</if>
                        <if test="problemLocation != null  and problemLocation != ''"> and problem_location = #{problemLocation}</if>
                        <if test="responsibleUnit != null  and responsibleUnit != ''"> and responsible_unit = #{responsibleUnit}</if>
                        <if test="petitionStatus != null  and petitionStatus != ''"> and petition_status = #{petitionStatus}</if>
                        <if test="registerOrg != null  and registerOrg != ''"> and register_org = #{registerOrg}</if>
                        <if test="complaintMethod != null  and complaintMethod != ''"> and complaint_method = #{complaintMethod}</if>
                        <if test="repeatCount != null "> and repeat_count = #{repeatCount}</if>
                        <if test="receptionLeader != null  and receptionLeader != ''"> and reception_leader = #{receptionLeader}</if>
                        <if test="registerDepartment != null  and registerDepartment != ''"> and register_department = #{registerDepartment}</if>
                        <if test="handleMethod != null  and handleMethod != ''"> and handle_method = #{handleMethod}</if>
                        <if test="handleOpinion != null  and handleOpinion != ''"> and handle_opinion = #{handleOpinion}</if>
                        <if test="inSatisfactionEval != null "> and in_satisfaction_eval = #{inSatisfactionEval}</if>
                        <if test="petitionNo != null  and petitionNo != ''"> and petition_no = #{petitionNo}</if>
                        <if test="isFinalized != null "> and is_finalized = #{isFinalized}</if>
                        <if test="peopleCount != null "> and people_count = #{peopleCount}</if>
                        <if test="handlingSituation != null  and handlingSituation != ''"> and handling_situation = #{handlingSituation}</if>
                        <if test="handlingTime != null "> and handling_time = #{handlingTime}</if>
                        <if test="householdReg != null  and householdReg != ''"> and household_reg = #{householdReg}</if>
                        <if test="ethnicity != null  and ethnicity != ''"> and ethnicity = #{ethnicity}</if>
                        <if test="hotIssue != null  and hotIssue != ''"> and hot_issue = #{hotIssue}</if>
                        <if test="remainingDays != null "> and remaining_days = #{remainingDays}</if>
                        <if test="isClosed != null "> and is_closed = #{isClosed}</if>
                        <if test="isAccumulatedCase != null "> and is_accumulated_case = #{isAccumulatedCase}</if>
                        <if test="isJoint != null "> and is_joint = #{isJoint}</if>
                        <if test="isAnonymous != null "> and is_anonymous = #{isAnonymous}</if>
                        <if test="isThreeCrossThreeSplit != null "> and is_three_cross_three_split = #{isThreeCrossThreeSplit}</if>
                        <if test="isInvolvingMacau != null "> and is_involving_macau = #{isInvolvingMacau}</if>
                        <if test="isInvolvingLaw != null "> and is_involving_law = #{isInvolvingLaw}</if>
                        <if test="isInvolvingHk != null "> and is_involving_hk = #{isInvolvingHk}</if>
                        <if test="isInvolvingOverseasChinese != null "> and is_involving_overseas_chinese = #{isInvolvingOverseasChinese}</if>
                        <if test="isInvolvingTaiwan != null "> and is_involving_taiwan = #{isInvolvingTaiwan}</if>
                        <if test="isThreatening != null "> and is_threatening = #{isThreatening}</if>
                        <if test="isRepeatPetition != null "> and is_repeat_petition = #{isRepeatPetition}</if>
                        <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
                        <if test="firstPetitionOrg != null  and firstPetitionOrg != ''"> and first_petition_org = #{firstPetitionOrg}</if>
                        <if test="firstPetitionDate != null "> and first_petition_date = #{firstPetitionDate}</if>
                        <if test="firstComplaintMethod != null  and firstComplaintMethod != ''"> and first_complaint_method = #{firstComplaintMethod}</if>
                        <if test="complaintContent != null  and complaintContent != ''"> and complaint_content = #{complaintContent}</if>
                        <if test="petitionReason != null  and petitionReason != ''"> and petition_reason = #{petitionReason}</if>
                        <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
                        <if test="politicalStatus != null  and politicalStatus != ''"> and political_status = #{politicalStatus}</if>
                        <if test="idNumber != null  and idNumber != ''"> and id_number = #{idNumber}</if>
                        <if test="idType != null  and idType != ''"> and id_type = #{idType}</if>
                        <if test="occupation != null  and occupation != ''"> and occupation = #{occupation}</if>
                        <if test="keywords != null  and keywords != ''"> and keywords = #{keywords}</if>
                        <if test="lastPetitionDate != null "> and last_petition_date = #{lastPetitionDate}</if>
        </where>
    </select>

    <select id="selectDbPetitionInfoById" parameterType="Long" resultMap="DbPetitionInfoResult">
            <include refid="selectDbPetitionInfoVo"/>
            where id = #{id}
    </select>

    <select id="checkDbPetitionInfoUnique" parameterType="com.ruoyi.dataview.domain.po.DbPetitionInfo" resultMap="DbPetitionInfoResult">
        select id from db_petition_info where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertDbPetitionInfo" parameterType="com.ruoyi.dataview.domain.po.DbPetitionInfo" useGeneratedKeys="true" keyProperty="id">
        insert into db_petition_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="petitionDate != null">petition_date,</if>
                    <if test="isReviewed != null">is_reviewed,</if>
                    <if test="overview != null">overview,</if>
                    <if test="registerTime != null">register_time,</if>
                    <if test="petitionForm != null">petition_form,</if>
                    <if test="name != null">name,</if>
                    <if test="address != null">address,</if>
                    <if test="petitionPurpose != null">petition_purpose,</if>
                    <if test="contentCategory != null">content_category,</if>
                    <if test="problemLocation != null">problem_location,</if>
                    <if test="responsibleUnit != null">responsible_unit,</if>
                    <if test="petitionStatus != null">petition_status,</if>
                    <if test="registerOrg != null">register_org,</if>
                    <if test="complaintMethod != null">complaint_method,</if>
                    <if test="repeatCount != null">repeat_count,</if>
                    <if test="receptionLeader != null">reception_leader,</if>
                    <if test="registerDepartment != null">register_department,</if>
                    <if test="handleMethod != null">handle_method,</if>
                    <if test="handleOpinion != null">handle_opinion,</if>
                    <if test="inSatisfactionEval != null">in_satisfaction_eval,</if>
                    <if test="petitionNo != null">petition_no,</if>
                    <if test="isFinalized != null">is_finalized,</if>
                    <if test="peopleCount != null">people_count,</if>
                    <if test="handlingSituation != null">handling_situation,</if>
                    <if test="handlingTime != null">handling_time,</if>
                    <if test="householdReg != null">household_reg,</if>
                    <if test="ethnicity != null">ethnicity,</if>
                    <if test="hotIssue != null">hot_issue,</if>
                    <if test="remainingDays != null">remaining_days,</if>
                    <if test="isClosed != null">is_closed,</if>
                    <if test="isAccumulatedCase != null">is_accumulated_case,</if>
                    <if test="isJoint != null">is_joint,</if>
                    <if test="isAnonymous != null">is_anonymous,</if>
                    <if test="isThreeCrossThreeSplit != null">is_three_cross_three_split,</if>
                    <if test="isInvolvingMacau != null">is_involving_macau,</if>
                    <if test="isInvolvingLaw != null">is_involving_law,</if>
                    <if test="isInvolvingHk != null">is_involving_hk,</if>
                    <if test="isInvolvingOverseasChinese != null">is_involving_overseas_chinese,</if>
                    <if test="isInvolvingTaiwan != null">is_involving_taiwan,</if>
                    <if test="isThreatening != null">is_threatening,</if>
                    <if test="isRepeatPetition != null">is_repeat_petition,</if>
                    <if test="phone != null">phone,</if>
                    <if test="firstPetitionOrg != null">first_petition_org,</if>
                    <if test="firstPetitionDate != null">first_petition_date,</if>
                    <if test="firstComplaintMethod != null">first_complaint_method,</if>
                    <if test="complaintContent != null">complaint_content,</if>
                    <if test="petitionReason != null">petition_reason,</if>
                    <if test="gender != null">gender,</if>
                    <if test="politicalStatus != null">political_status,</if>
                    <if test="idNumber != null">id_number,</if>
                    <if test="idType != null">id_type,</if>
                    <if test="occupation != null">occupation,</if>
                    <if test="keywords != null">keywords,</if>
                    <if test="lastPetitionDate != null">last_petition_date,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="petitionDate != null">#{petitionDate},</if>
                    <if test="isReviewed != null">#{isReviewed},</if>
                    <if test="overview != null">#{overview},</if>
                    <if test="registerTime != null">#{registerTime},</if>
                    <if test="petitionForm != null">#{petitionForm},</if>
                    <if test="name != null">#{name},</if>
                    <if test="address != null">#{address},</if>
                    <if test="petitionPurpose != null">#{petitionPurpose},</if>
                    <if test="contentCategory != null">#{contentCategory},</if>
                    <if test="problemLocation != null">#{problemLocation},</if>
                    <if test="responsibleUnit != null">#{responsibleUnit},</if>
                    <if test="petitionStatus != null">#{petitionStatus},</if>
                    <if test="registerOrg != null">#{registerOrg},</if>
                    <if test="complaintMethod != null">#{complaintMethod},</if>
                    <if test="repeatCount != null">#{repeatCount},</if>
                    <if test="receptionLeader != null">#{receptionLeader},</if>
                    <if test="registerDepartment != null">#{registerDepartment},</if>
                    <if test="handleMethod != null">#{handleMethod},</if>
                    <if test="handleOpinion != null">#{handleOpinion},</if>
                    <if test="inSatisfactionEval != null">#{inSatisfactionEval},</if>
                    <if test="petitionNo != null">#{petitionNo},</if>
                    <if test="isFinalized != null">#{isFinalized},</if>
                    <if test="peopleCount != null">#{peopleCount},</if>
                    <if test="handlingSituation != null">#{handlingSituation},</if>
                    <if test="handlingTime != null">#{handlingTime},</if>
                    <if test="householdReg != null">#{householdReg},</if>
                    <if test="ethnicity != null">#{ethnicity},</if>
                    <if test="hotIssue != null">#{hotIssue},</if>
                    <if test="remainingDays != null">#{remainingDays},</if>
                    <if test="isClosed != null">#{isClosed},</if>
                    <if test="isAccumulatedCase != null">#{isAccumulatedCase},</if>
                    <if test="isJoint != null">#{isJoint},</if>
                    <if test="isAnonymous != null">#{isAnonymous},</if>
                    <if test="isThreeCrossThreeSplit != null">#{isThreeCrossThreeSplit},</if>
                    <if test="isInvolvingMacau != null">#{isInvolvingMacau},</if>
                    <if test="isInvolvingLaw != null">#{isInvolvingLaw},</if>
                    <if test="isInvolvingHk != null">#{isInvolvingHk},</if>
                    <if test="isInvolvingOverseasChinese != null">#{isInvolvingOverseasChinese},</if>
                    <if test="isInvolvingTaiwan != null">#{isInvolvingTaiwan},</if>
                    <if test="isThreatening != null">#{isThreatening},</if>
                    <if test="isRepeatPetition != null">#{isRepeatPetition},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="firstPetitionOrg != null">#{firstPetitionOrg},</if>
                    <if test="firstPetitionDate != null">#{firstPetitionDate},</if>
                    <if test="firstComplaintMethod != null">#{firstComplaintMethod},</if>
                    <if test="complaintContent != null">#{complaintContent},</if>
                    <if test="petitionReason != null">#{petitionReason},</if>
                    <if test="gender != null">#{gender},</if>
                    <if test="politicalStatus != null">#{politicalStatus},</if>
                    <if test="idNumber != null">#{idNumber},</if>
                    <if test="idType != null">#{idType},</if>
                    <if test="occupation != null">#{occupation},</if>
                    <if test="keywords != null">#{keywords},</if>
                    <if test="lastPetitionDate != null">#{lastPetitionDate},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateDbPetitionInfo" parameterType="com.ruoyi.dataview.domain.po.DbPetitionInfo">
        update db_petition_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="petitionDate != null">petition_date = #{petitionDate},</if>
                    <if test="isReviewed != null">is_reviewed = #{isReviewed},</if>
                    <if test="overview != null">overview = #{overview},</if>
                    <if test="registerTime != null">register_time = #{registerTime},</if>
                    <if test="petitionForm != null">petition_form = #{petitionForm},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="petitionPurpose != null">petition_purpose = #{petitionPurpose},</if>
                    <if test="contentCategory != null">content_category = #{contentCategory},</if>
                    <if test="problemLocation != null">problem_location = #{problemLocation},</if>
                    <if test="responsibleUnit != null">responsible_unit = #{responsibleUnit},</if>
                    <if test="petitionStatus != null">petition_status = #{petitionStatus},</if>
                    <if test="registerOrg != null">register_org = #{registerOrg},</if>
                    <if test="complaintMethod != null">complaint_method = #{complaintMethod},</if>
                    <if test="repeatCount != null">repeat_count = #{repeatCount},</if>
                    <if test="receptionLeader != null">reception_leader = #{receptionLeader},</if>
                    <if test="registerDepartment != null">register_department = #{registerDepartment},</if>
                    <if test="handleMethod != null">handle_method = #{handleMethod},</if>
                    <if test="handleOpinion != null">handle_opinion = #{handleOpinion},</if>
                    <if test="inSatisfactionEval != null">in_satisfaction_eval = #{inSatisfactionEval},</if>
                    <if test="petitionNo != null">petition_no = #{petitionNo},</if>
                    <if test="isFinalized != null">is_finalized = #{isFinalized},</if>
                    <if test="peopleCount != null">people_count = #{peopleCount},</if>
                    <if test="handlingSituation != null">handling_situation = #{handlingSituation},</if>
                    <if test="handlingTime != null">handling_time = #{handlingTime},</if>
                    <if test="householdReg != null">household_reg = #{householdReg},</if>
                    <if test="ethnicity != null">ethnicity = #{ethnicity},</if>
                    <if test="hotIssue != null">hot_issue = #{hotIssue},</if>
                    <if test="remainingDays != null">remaining_days = #{remainingDays},</if>
                    <if test="isClosed != null">is_closed = #{isClosed},</if>
                    <if test="isAccumulatedCase != null">is_accumulated_case = #{isAccumulatedCase},</if>
                    <if test="isJoint != null">is_joint = #{isJoint},</if>
                    <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
                    <if test="isThreeCrossThreeSplit != null">is_three_cross_three_split = #{isThreeCrossThreeSplit},</if>
                    <if test="isInvolvingMacau != null">is_involving_macau = #{isInvolvingMacau},</if>
                    <if test="isInvolvingLaw != null">is_involving_law = #{isInvolvingLaw},</if>
                    <if test="isInvolvingHk != null">is_involving_hk = #{isInvolvingHk},</if>
                    <if test="isInvolvingOverseasChinese != null">is_involving_overseas_chinese = #{isInvolvingOverseasChinese},</if>
                    <if test="isInvolvingTaiwan != null">is_involving_taiwan = #{isInvolvingTaiwan},</if>
                    <if test="isThreatening != null">is_threatening = #{isThreatening},</if>
                    <if test="isRepeatPetition != null">is_repeat_petition = #{isRepeatPetition},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="firstPetitionOrg != null">first_petition_org = #{firstPetitionOrg},</if>
                    <if test="firstPetitionDate != null">first_petition_date = #{firstPetitionDate},</if>
                    <if test="firstComplaintMethod != null">first_complaint_method = #{firstComplaintMethod},</if>
                    <if test="complaintContent != null">complaint_content = #{complaintContent},</if>
                    <if test="petitionReason != null">petition_reason = #{petitionReason},</if>
                    <if test="gender != null">gender = #{gender},</if>
                    <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
                    <if test="idNumber != null">id_number = #{idNumber},</if>
                    <if test="idType != null">id_type = #{idType},</if>
                    <if test="occupation != null">occupation = #{occupation},</if>
                    <if test="keywords != null">keywords = #{keywords},</if>
                    <if test="lastPetitionDate != null">last_petition_date = #{lastPetitionDate},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbPetitionInfoById" parameterType="Long">
        delete from db_petition_info where id = #{id}
    </delete>

    <delete id="deleteDbPetitionInfoByIds" parameterType="Long">
        delete from db_petition_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <!-- 获取信访总数 -->
    <select id="selectTotalCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM db_petition_info
    </select>
    
    <!-- 获取本月信访数 -->
    <select id="selectCurrentMonthCount" resultType="java.lang.Long">
        SELECT COUNT(*) 
        FROM db_petition_info 
        WHERE DATE_FORMAT(register_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
    </select>
    
    <!-- 按内容分类分组统计信访信息 -->
    <select id="selectCategoryStatistics" resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        SELECT 
            SUBSTRING_INDEX(content_category, '_', 1) AS lable,
            COUNT(*) AS count
        FROM 
            db_petition_info
        WHERE 
            content_category IS NOT NULL 
            AND content_category != ''
        GROUP BY 
            SUBSTRING_INDEX(content_category, '_', 1)
        ORDER BY 
            count DESC
    </select>
</mapper>