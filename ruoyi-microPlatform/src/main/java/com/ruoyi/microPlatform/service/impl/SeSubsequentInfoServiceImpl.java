package com.ruoyi.microPlatform.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.SePublicSentiment;
import com.ruoyi.microPlatform.mapper.SePublicSentimentMapper;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeSubsequentInfoMapper;
import com.ruoyi.microPlatform.domain.SeSubsequentInfo;
import com.ruoyi.microPlatform.service.ISeSubsequentInfoService;

/**
 * 后续动态信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeSubsequentInfoServiceImpl implements ISeSubsequentInfoService {
    @Autowired
    private SeSubsequentInfoMapper seSubsequentInfoMapper;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private SePublicSentimentMapper sePublicSentimentMapper;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询后续动态信息
     *
     * @param id 后续动态信息主键
     * @return 后续动态信息
     */
    @Override
    public SeSubsequentInfo selectSeSubsequentInfoById(Long id) {
        return seSubsequentInfoMapper.selectSeSubsequentInfoById(id);
    }

    /**
     * 校验后续动态信息是否存在
     *
     * @param seSubsequentInfo
     * @return boolean
     */
    @Override
    public boolean checkSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo) {
        SeSubsequentInfo old = seSubsequentInfoMapper.checkSeSubsequentInfoUnique(seSubsequentInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询后续动态信息列表
     *
     * @param seSubsequentInfo 后续动态信息
     * @return 后续动态信息
     */
    @Override
    public List<SeSubsequentInfo> selectSeSubsequentInfoList(SeSubsequentInfo seSubsequentInfo) {
        return seSubsequentInfoMapper.selectSeSubsequentInfoList(seSubsequentInfo);
    }

    /**
     * 导入后续动态信息
     *
     * @param infos           后续动态信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeSubsequentInfo(List<SeSubsequentInfo> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入后续动态信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeSubsequentInfo info : infos) {
            try {
                // 验证是否存在这个数据
                SeSubsequentInfo old = seSubsequentInfoMapper.checkSeSubsequentInfoUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seSubsequentInfoMapper.insertSeSubsequentInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seSubsequentInfoMapper.updateSeSubsequentInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增后续动态信息
     *
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    @Override
    public int insertSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo, Long gridId) {
        seSubsequentInfo.setCreateTime(DateUtils.getNowDate());
        int i = seSubsequentInfoMapper.insertSeSubsequentInfo(seSubsequentInfo);
        if (i > 0 && seSubsequentInfo.getFormType().equals("居民吹哨")) {
            insertMsg(seSubsequentInfo, gridId);
        }
        return i;
    }

    @Async("threadPoolTaskExecutor")
    public void insertMsg(SeSubsequentInfo seSubsequentInfo, Long userGridId) {
        System.err.println(seSubsequentInfo.getFormType() + "准备发送消息了？？？？");
        if (seSubsequentInfo.getFormType().equals("居民吹哨")) {
            SePublicSentiment publicSentiment = sePublicSentimentMapper.selectSePublicSentimentById2(seSubsequentInfo.getFormId());
            if (ObjectUtil.isNotEmpty(publicSentiment)) {
                SePublicSentiment newData = new SePublicSentiment() {{
                    setId(publicSentiment.getId());
                }};
                boolean update = false;
                if (ObjectUtil.isEmpty(publicSentiment.getGridId()) && ObjectUtil.isNotEmpty(userGridId)) {
                    SysDept dept = deptMapper.selectByGridId(userGridId);
                    // 该信息归属与该网格处理
                    newData.setGridId(userGridId);
                    newData.setGridName(ObjectUtil.isNotEmpty(dept) ? dept.getDeptName() : "");
                    publicSentiment.setGridId(userGridId);
                    update = true;
                }
                if (ObjectUtil.isNotEmpty(publicSentiment.getIsBackPhone()) && publicSentiment.getIsBackPhone() == 0) {
                    newData.setIsBackPhone(1);// 有最新动态
                    update = true;
                }
                if (update) {
                    sePublicSentimentMapper.updateSePublicSentiment(newData);// 更新居民吹哨信息
                }

                // 那么最新回复者所在网格 不为空并且（填报用户的网格Id为空或者和最新回复者不一致）
                if (ObjectUtil.isNotEmpty(userGridId) && (ObjectUtil.isEmpty(publicSentiment.getUserGridId()) || (publicSentiment.getUserGridId().longValue() != userGridId.longValue()))) {
                    System.err.println("更新填报用户者的网格Id");
                    userMapper.updateUser(new SysUser() {{
                        setUserId(publicSentiment.getUserId());
                        setJobId(userGridId);
                    }});
                    // TODO： 更新用户所属网格Id到缓存
                    SysUser old = userMapper.selectUserGrid(publicSentiment.getUserId());
                    if (ObjectUtil.isNotEmpty(old)) {
                        redisCache.setCacheObject(CacheConstants.USER_GRID_KEY + publicSentiment.getUserId(), old);
                    }
                }
                System.err.println("居民吹哨信息不空:" + publicSentiment.getTown());
                if (publicSentiment.getUserId().longValue() != seSubsequentInfo.getUserId().longValue()) {
                    System.err.println("当前居民吹哨提交人" + publicSentiment.getUserId() + "不是回复的人" + seSubsequentInfo.getUserId());
                    // 提醒提交人有新动态
                    weChatService.insertMsgCommon(publicSentiment.getUserId(), "居民吹哨",
                            "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/publicSentiment/detail?detailId=" + publicSentiment.getId(), publicSentiment.getTown(),
                            "您提交的居民吹哨有新的后续动态");
                } else {
//                    //TODO: 如果提交人在该条信息下回复了，是否通知社区的人？
                    Long deptId = publicSentiment.getDeptId();
                    Long gridId = publicSentiment.getGridId();
                    String gridName = null;
                    if (ObjectUtil.isNotEmpty(gridId)) {

                        SysDept dept = deptMapper.selectByGridId(gridId);
                        if (ObjectUtil.isNotEmpty(dept)) {
                            System.err.println("给网格管理者发送消息通知2：" + deptId);
                            deptId = dept.getDeptId();
                            gridName = dept.getDeptName();
                        }
                    } else {
                        System.err.println("给社区全部管理者发送消息通知2：" + deptId);
                    }
                    //TODO: 发送消息通知
                    weChatService.insertMsg(deptId, "居民吹哨",
                            "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/publicSentiment/list?showTab=true&queryType=1",
                            publicSentiment.getTown() + (StringUtils.isNotBlank(gridName) ? "(网格：" + gridName + ")" : ""), "居民提交了一条新的居民吹哨记录");
                }
                if (ObjectUtil.isNotEmpty(seSubsequentInfo.getParentId())) {
                    // 别人回复
                    System.err.println(seSubsequentInfo.getUserId() + "回复别人信息了");
                    SeSubsequentInfo parent = seSubsequentInfoMapper.selectSeSubsequentInfoById(seSubsequentInfo.getParentId());
                    if (ObjectUtil.isNotEmpty(parent) && seSubsequentInfo.getUserId().longValue() != parent.getUserId().longValue()) {
                        System.err.println("回复和被回复者不是一个人：" + parent.getUserId());
                        weChatService.insertMsgCommon(parent.getUserId(), "居民吹哨",
                                "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/publicSentiment/detail?detailId=" + publicSentiment.getId(), publicSentiment.getTown(),
                                "您关注的居民吹哨有新的回复");
                    }
                }
            } else {
                System.err.println("居然没查到居民吹哨信息");
            }
        }
    }

    /**
     * 修改后续动态信息
     *
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    @Override
    public int updateSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo) {
        return seSubsequentInfoMapper.updateSeSubsequentInfo(seSubsequentInfo);
    }

    /**
     * 批量删除后续动态信息
     *
     * @param ids 需要删除的后续动态信息主键
     * @return 结果
     */
    @Override
    public int deleteSeSubsequentInfoByIds(Long[] ids) {
        return seSubsequentInfoMapper.deleteSeSubsequentInfoByIds(ids);
    }

    /**
     * 删除后续动态信息信息
     *
     * @param id 后续动态信息主键
     * @return 结果
     */
    @Override
    public int deleteSeSubsequentInfoById(Long id) {
        return seSubsequentInfoMapper.deleteSeSubsequentInfoById(id);
    }


    /**
     * 查询后续动态信息列表,当某条数据有 parent_id时，代表是回复别人的数据 ，则找到被回复的数据，放到当前数据中
     */
    @Override
    public List<SeSubsequentInfo> selectSeSubsequentInfoListParent(SeSubsequentInfo seSubsequentInfo) throws CloneNotSupportedException {
//        List<SeSubsequentInfo> list = new ArrayList<>();
        List<SeSubsequentInfo> seSubsequentInfos = seSubsequentInfoMapper.selectSeSubsequentInfoList(seSubsequentInfo);
//        //遍历数据，如果parentId有值，则代表当前数据是回复数据，则遍历找到被回复的数据
//        for (SeSubsequentInfo subsequentInfo : seSubsequentInfos) {
//            //创建一个新的对象 拷贝 subsequentInfo 不然会出现循环嵌套
//            SeSubsequentInfo suInfo = subsequentInfo.clone();
//            if (suInfo.getParentId() != null) {
//                List<SeSubsequentInfo> collect = seSubsequentInfos.stream().filter(se -> se.getId().longValue() == suInfo.getParentId().longValue()).collect(Collectors.toList());
//                if (collect.size() > 0) {
//                    SeSubsequentInfo se = collect.get(0);
//                    suInfo.setSeSubsequentInfo(se);
//                }
//                list.add(suInfo);
//            } else {
//                list.add(suInfo);
//            }
//        }
        //通过创建时间进行排序 升序
//        Collections.sort(list, Comparator.comparing(SeSubsequentInfo::getCreateTime, (t1, t2) -> t1.compareTo(t2)));
        return seSubsequentInfos;
    }


    /**
     * 查询后续动态信息列表数量 根据form_id和form_type
     */
    @Override
    public Map<String, Integer> selectSeSubsequentInfoListSize(SeSubsequentInfo seSubsequentInfo) {
        Map<String, Integer> map = new HashMap<>();
        List<SeSubsequentInfo> seSubsequentInfos = seSubsequentInfoMapper.selectSeSubsequentInfoList(seSubsequentInfo);
        map.put(seSubsequentInfo.getFormType(), seSubsequentInfos.size());
        return map;
    }
}
