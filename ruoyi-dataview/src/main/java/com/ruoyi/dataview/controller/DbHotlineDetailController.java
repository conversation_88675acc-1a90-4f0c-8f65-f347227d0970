package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbHotlineDetailConvert;
import com.ruoyi.dataview.domain.dto.DbHotlineDetailFormDTO;
import com.ruoyi.dataview.domain.dto.DbHotlineDetailQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbHotlineDetail;
import com.ruoyi.dataview.service.IDbHotlineDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 12345热线中心综合明细Controller
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@RestController
@RequestMapping("/fiveLevel/hotlineDetail")
@RequiredArgsConstructor
public class DbHotlineDetailController extends BaseController
{
    private final IDbHotlineDetailService dbHotlineDetailService;

    private final DbHotlineDetailConvert hotlineDetailConvert;

    /**
     * 查询12345热线中心综合明细列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbHotlineDetailQueryDTO hotlineDetailQueryDTO)
    {
        DbHotlineDetail dbHotlineDetail = hotlineDetailConvert.toDO(hotlineDetailQueryDTO);
        startPage();
        return getDataTable(hotlineDetailConvert.toVOList(dbHotlineDetailService.selectDbHotlineDetailList(dbHotlineDetail)));
    }

    /**
     * 导出12345热线中心综合明细列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:export')")
    @Log(title = "12345热线中心综合明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbHotlineDetailQueryDTO hotlineDetailQueryDTO)
    {
        DbHotlineDetail dbHotlineDetail = hotlineDetailConvert.toDO(hotlineDetailQueryDTO);
        List<DbHotlineDetail> list = dbHotlineDetailService.selectDbHotlineDetailList(dbHotlineDetail);
        ExcelUtil<DbHotlineDetail> util = new ExcelUtil<DbHotlineDetail>(DbHotlineDetail.class);
        util.exportExcel(response, list, "12345热线中心综合明细数据");
    }

    @Log(title = "12345热线中心综合明细", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbHotlineDetail> util = new ExcelUtil<DbHotlineDetail>(DbHotlineDetail.class);
        List<DbHotlineDetail> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbHotlineDetailService.importDbHotlineDetail(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbHotlineDetail> util = new ExcelUtil<DbHotlineDetail>(DbHotlineDetail.class);
        util.exportTemplateExcel(response, "12345热线中心综合明细数据");
    }

    /**
     * 获取12345热线中心综合明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(hotlineDetailConvert.toVO(dbHotlineDetailService.selectDbHotlineDetailById(id)));
    }

    /**
     * 新增12345热线中心综合明细
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:add')")
    @Log(title = "12345热线中心综合明细", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbHotlineDetailFormDTO hotlineDetailFormDTO)
    {
        DbHotlineDetail dbHotlineDetail = hotlineDetailConvert.toDO(hotlineDetailFormDTO);
        dbHotlineDetail.setCreateBy(getUsername());
        if (dbHotlineDetailService.checkDbHotlineDetail(dbHotlineDetail)){
            return error("数据已存在！");
        }
        return toAjax(dbHotlineDetailService.insertDbHotlineDetail(dbHotlineDetail));
    }

    /**
     * 修改12345热线中心综合明细
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:edit')")
    @Log(title = "12345热线中心综合明细", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbHotlineDetailFormDTO hotlineDetailFormDTO)
    {
        DbHotlineDetail dbHotlineDetail = hotlineDetailConvert.toDO(hotlineDetailFormDTO);
        dbHotlineDetail.setUpdateBy(getUsername());
        if (dbHotlineDetailService.checkDbHotlineDetail(dbHotlineDetail)){
            return error("数据已存在！");
        }
        return toAjax(dbHotlineDetailService.updateDbHotlineDetail(dbHotlineDetail));
    }

    /**
     * 删除12345热线中心综合明细
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:hotlineDetail:remove')")
    @Log(title = "12345热线中心综合明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbHotlineDetailService.deleteDbHotlineDetailByIds(ids));
    }
}
