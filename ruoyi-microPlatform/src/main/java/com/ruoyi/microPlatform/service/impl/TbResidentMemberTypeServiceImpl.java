package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbResidentMemberTypeMapper;
import com.ruoyi.microPlatform.domain.TbResidentMemberType;
import com.ruoyi.microPlatform.service.ITbResidentMemberTypeService;

/**
 * 成员标签配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
@Service
public class TbResidentMemberTypeServiceImpl implements ITbResidentMemberTypeService 
{
    @Autowired
    private TbResidentMemberTypeMapper tbResidentMemberTypeMapper;

    /**
     * 查询成员标签配置
     * 
     * @param id 成员标签配置主键
     * @return 成员标签配置
     */
    @Override
    public TbResidentMemberType selectTbResidentMemberTypeById(Long id)
    {
        return tbResidentMemberTypeMapper.selectTbResidentMemberTypeById(id);
    }

    /**
     * 校验成员标签配置是否存在
     *
     * @param tbResidentMemberType
     * @return boolean
     */
    @Override
    public boolean checkTbResidentMemberType(TbResidentMemberType tbResidentMemberType){
        TbResidentMemberType old = tbResidentMemberTypeMapper.checkTbResidentMemberTypeUnique(tbResidentMemberType);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询成员标签配置列表
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 成员标签配置
     */
    @Override
    public List<TbResidentMemberType> selectTbResidentMemberTypeList(TbResidentMemberType tbResidentMemberType)
    {
        return tbResidentMemberTypeMapper.selectTbResidentMemberTypeList(tbResidentMemberType);
    }

    /**
     * 导入成员标签配置
     *
     * @param infos       成员标签配置列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentMemberType(List<TbResidentMemberType> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入成员标签配置数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbResidentMemberType info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbResidentMemberType old = tbResidentMemberTypeMapper.checkTbResidentMemberTypeUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbResidentMemberTypeMapper.insertTbResidentMemberType(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbResidentMemberTypeMapper.updateTbResidentMemberType(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    @Override
    public int insertTbResidentMemberType(TbResidentMemberType tbResidentMemberType)
    {
        return tbResidentMemberTypeMapper.insertTbResidentMemberType(tbResidentMemberType);
    }

    /**
     * 修改成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    @Override
    public int updateTbResidentMemberType(TbResidentMemberType tbResidentMemberType)
    {
        return tbResidentMemberTypeMapper.updateTbResidentMemberType(tbResidentMemberType);
    }

    /**
     * 批量删除成员标签配置
     * 
     * @param ids 需要删除的成员标签配置主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentMemberTypeByIds(Long[] ids)
    {
        return tbResidentMemberTypeMapper.deleteTbResidentMemberTypeByIds(ids);
    }

    /**
     * 删除成员标签配置信息
     * 
     * @param id 成员标签配置主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentMemberTypeById(Long id)
    {
        return tbResidentMemberTypeMapper.deleteTbResidentMemberTypeById(id);
    }
}
