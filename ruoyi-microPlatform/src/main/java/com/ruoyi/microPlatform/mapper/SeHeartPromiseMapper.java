package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.SeHeartPromise;

/**
 * 心灵有约Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeHeartPromiseMapper 
{
    /**
     * 查询心灵有约
     * 
     * @param id 心灵有约主键
     * @return 心灵有约
     */
    public SeHeartPromise selectSeHeartPromiseById(Long id);

    /**
     * 校验心灵有约是否唯一
     *
     * @param seHeartPromise
     * @return 心灵有约
     */
    public SeHeartPromise checkSeHeartPromiseUnique(SeHeartPromise seHeartPromise);

    /**
     * 查询心灵有约列表
     * 
     * @param seHeartPromise 心灵有约
     * @return 心灵有约集合
     */
    public List<SeHeartPromise> selectSeHeartPromiseList(SeHeartPromise seHeartPromise);

    /**
     * 新增心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    public int insertSeHeartPromise(SeHeartPromise seHeartPromise);

    /**
     * 修改心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    public int updateSeHeartPromise(SeHeartPromise seHeartPromise);

    /**
     * 删除心灵有约
     * 
     * @param id 心灵有约主键
     * @return 结果
     */
    public int deleteSeHeartPromiseById(Long id);

    /**
     * 批量删除心灵有约
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeHeartPromiseByIds(Long[] ids);
}
