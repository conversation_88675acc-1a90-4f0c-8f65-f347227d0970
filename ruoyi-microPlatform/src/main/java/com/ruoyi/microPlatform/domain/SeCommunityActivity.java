package com.ruoyi.microPlatform.domain;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 社区活动对象 se_community_activity
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public class SeCommunityActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long deptId;

    /** 任务区域县 */
    @Excel(name = "活动县（市、区）")
    private String county;

    /**    街道/乡镇 */
    @Excel(name = "活动街道/乡镇")
    private String country;

    /**    村镇/社区 */
    @Excel(name = "活动村/社区")
    private String town;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String activityName;

    /** 任务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "活动开始时间", dateFormat = "yyyy-MM-dd HH:mm")
    private Date taskStart;

    /** 任务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "活动结束时间", dateFormat = "yyyy-MM-dd HH:mm")
    private Date taskEnd;

    /** 任务区域 */
//    @Excel(name = "任务区域")
    private String taskArea;

    /** 地址定位 */
    @Excel(name = "活动地址")
    private String taskAddr;

    private BigDecimal lat;

    private BigDecimal lng;

    /** 活动类型 */
    @Excel(name = "活动类型")
    private String taskType;
    /** 活动内容 */
    @Excel(name = "活动内容")
    private String taskContent;

    /** 报名截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "报名截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date signEnd;

    /** 任务状态(0 待发布 1已发布) */
    private Integer taskStatus;

    private Integer isReview;


    /** 需要报名人数 */
    @Excel(name = "活动人数上限")
    private Integer needNum;

    /** 已报名人数 */
    @Excel(name = "报名人数")
    private Integer signNum;

    /** 发起者感言 */
    @Excel(name = "发起者感言")
    private String reviewContent;

    private List<String> taskTypeArr;

    //查询不同状态下的数据 activityStatus：未开始、进行中、已结束
    private String activityStatus;

    private Long createId;

    /** 活动照片 */
    @Excel(name = "活动照片",cellType = Excel.ColumnType.IMAGE)
    private String accessory;

    //查询是否正在进行的数据（即当前活动的 任务结束时间>当前的时间 ） 0未开始、1进行中、2已结束
    private Integer activityStatusInt;

    //报名者id 用于查询当前用户已经报名的活动
    private Long userId;

    //是否报名 true 是 false 否
    private Boolean signed;

    //是否截止 true 是 false 否
    private Boolean signLimit;



    private Boolean insetUserCount;
    /** 报名者信息信息 */
    private List<SeActivityUser> seActivityUserList;

    public void setAccessory(String accessory)
    {
        this.accessory = accessory;
        if (accessory != null && !this.accessory.equals("")){
            List<String> stringList = Arrays.asList(accessory.split(","));
            this.setFileList(stringList);
            if (stringList.size() > 0){
                this.setFirstFile(stringList.get(0));
            }
        }
    }

    public String getAccessory()
    {
        return accessory;
    }

    public Integer getActivityStatusInt() {
        return activityStatusInt;
    }

    public void setActivityStatusInt(Integer activityStatusInt) {
        this.activityStatusInt = activityStatusInt;
    }

    public Boolean getSigned() {
        return signed;
    }

    public void setSigned(Boolean signed) {
        this.signed = signed;
    }

    public Boolean getSignLimit() {
        return signLimit;
    }

    public String getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(String activityStatus) {
        this.activityStatus = activityStatus;
    }

    public void setSignLimit(Boolean signLimit) {
        this.signLimit = signLimit;
    }



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public Boolean getInsetUserCount() {
        return insetUserCount;
    }

    public void setInsetUserCount(Boolean insetUserCount) {
        this.insetUserCount = insetUserCount;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId()
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setActivityName(String activityName) 
    {
        this.activityName = activityName;
    }

    public String getActivityName() 
    {
        return activityName;
    }

    public Date getTaskStart() {
        return taskStart;
    }

    public void setTaskStart(Date taskStart) {
        this.taskStart = taskStart;
    }

    public Date getTaskEnd() {
        return taskEnd;
    }

    public void setTaskEnd(Date taskEnd) {
        this.taskEnd = taskEnd;
    }

    public void setTaskArea(String taskArea)
    {
        this.taskArea = taskArea;
    }

    public String getTaskArea() 
    {
        return taskArea;
    }
    public void setTaskAddr(String taskAddr) 
    {
        this.taskAddr = taskAddr;
    }

    public String getTaskAddr() 
    {
        return taskAddr;
    }
    public void setLat(BigDecimal lat) 
    {
        this.lat = lat;
    }

    public BigDecimal getLat() 
    {
        return lat;
    }
    public void setLng(BigDecimal lng) 
    {
        this.lng = lng;
    }

    public BigDecimal getLng() 
    {
        return lng;
    }
    public void setTaskContent(String taskContent) 
    {
        this.taskContent = taskContent;
    }

    public String getTaskContent() 
    {
        return taskContent;
    }
    public void setSignEnd(Date signEnd) 
    {
        this.signEnd = signEnd;
    }



    public Date getSignEnd() 
    {
        return signEnd;
    }
    public void setTaskStatus(Integer taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public Integer getTaskStatus() 
    {
        return taskStatus;
    }
    public void setIsReview(Integer isReview) 
    {
        this.isReview = isReview;
    }

    public Integer getIsReview() 
    {
        return isReview;
    }
    public void setReviewContent(String reviewContent) 
    {
        this.reviewContent = reviewContent;
    }

    public String getReviewContent() 
    {
        return reviewContent;
    }
    public void setNeedNum(Integer needNum) 
    {
        this.needNum = needNum;
    }

    public Integer getNeedNum() 
    {
        return needNum;
    }
    public void setSignNum(Integer signNum) 
    {
        this.signNum = signNum;
    }

    public Integer getSignNum() 
    {
        return signNum;
    }
    public void setTaskType(String taskType) 
    {
        this.taskType = taskType;
        if (StringUtils.isNotBlank(taskType)){
            this.setTaskTypeArr(Arrays.asList(taskType.split(",")));
        }
    }

    public String getTaskType() 
    {
        return taskType;
    }
    public void setCreateId(Long createId) 
    {
        this.createId = createId;
    }

    public Long getCreateId() 
    {
        return createId;
    }
    public List<SeActivityUser> getSeActivityUserList()
    {
        return seActivityUserList;
    }

    public void setSeActivityUserList(List<SeActivityUser> seActivityUserList)
    {
        this.seActivityUserList = seActivityUserList;
    }

    public List<String> getTaskTypeArr() {
        if(StringUtils.isNotBlank(taskType)){
            return Arrays.asList(taskType.split(","));
        }
        return taskTypeArr;
    }

    public void setTaskTypeArr(List<String> taskTypeArr) {
        this.taskTypeArr = taskTypeArr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("activityName", getActivityName())
            .append("taskStart", getTaskStart())
            .append("taskEnd", getTaskEnd())
            .append("taskArea", getTaskArea())
            .append("taskAddr", getTaskAddr())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("taskContent", getTaskContent())
            .append("signEnd", getSignEnd())
            .append("taskStatus", getTaskStatus())
            .append("isReview", getIsReview())
            .append("reviewContent", getReviewContent())
            .append("needNum", getNeedNum())
            .append("signNum", getSignNum())
            .append("taskType", getTaskType())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("accessory", getAccessory())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("seActivityUserList", getSeActivityUserList())
            .toString();
    }
}
