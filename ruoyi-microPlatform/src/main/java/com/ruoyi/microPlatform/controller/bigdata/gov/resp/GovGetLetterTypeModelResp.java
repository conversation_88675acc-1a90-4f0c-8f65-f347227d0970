package com.ruoyi.microPlatform.controller.bigdata.gov.resp;

import com.ruoyi.microPlatform.controller.bigdata.gov.GovBaseReq;
import lombok.Data;

import java.util.List;

@Data
public class GovGetLetterTypeModelResp extends GovBaseReq {

    /**
     * [ {
     *   "py" : "",
     *   "text" : "公共事业类",
     *   "value" : "公共事业类"
     * }, {
     *   "py" : "",
     *   "text" : "房产物业类",
     *   "value" : "房产物业类"
     * }, {
     *   "py" : "",
     *   "text" : "社会治安类",
     *   "value" : "社会治安类"
     * }, {
     *   "py" : "",
     *   "text" : "社会保障类",
     *   "value" : "社会保障类"
     * }, {
     *   "py" : "",
     *   "text" : "市容环保类",
     *   "value" : "市容环保类"
     * }, {
     *   "py" : "",
     *   "text" : "城建规划类",
     *   "value" : "城建规划类"
     * }, {
     *   "py" : "",
     *   "text" : "科教文体类",
     *   "value" : "科教文体类"
     * }, {
     *   "py" : "",
     *   "text" : "交通运管类",
     *   "value" : "交通运管类"
     * }, {
     *   "py" : "",
     *   "text" : "三农问题类",
     *   "value" : "三农问题类"
     * }, {
     *   "py" : "",
     *   "text" : "行政服务类",
     *   "value" : "行政服务类"
     * }, {
     *   "py" : "",
     *   "text" : "征迁安置类",
     *   "value" : "征迁安置类"
     * }, {
     *   "py" : "",
     *   "text" : "人事就业类",
     *   "value" : "人事就业类"
     * }, {
     *   "py" : "",
     *   "text" : "其他类",
     *   "value" : "其他类"
     * } ]
     */

    private String py;

    private String text;

    private String value;
}
