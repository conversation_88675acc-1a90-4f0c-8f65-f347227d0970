package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyReportRelation;

import java.util.List;

/**
 * 党组织党员双报到关系Service接口
 */
public interface IPbPartyReportRelationService {
    /**
     * 查询
     */
    PbPartyReportRelation selectPbPartyReportRelationById(Long id);

    /**
     * 校验是否存在（relationCode唯一）
     */
    boolean checkPbPartyReportRelation(PbPartyReportRelation relation);

    /**
     * 列表
     */
    List<PbPartyReportRelation> selectPbPartyReportRelationList(PbPartyReportRelation relation);

    /**
     * 新增
     */
    int insertPbPartyReportRelation(PbPartyReportRelation relation);

    /**
     * 修改
     */
    int updatePbPartyReportRelation(PbPartyReportRelation relation);

    /**
     * 批量删除
     */
    int deletePbPartyReportRelationByIds(Long[] ids);

    /**
     * 删除
     */
    int deletePbPartyReportRelationById(Long id);

    List<CommonBaseCount> doubleReporting();
}
