package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbOrgEmployee;
import com.ruoyi.microPlatform.service.ITbOrgEmployeeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 组织情况员工信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/gridData/orgEmployee")
public class TbOrgEmployeeController extends BaseController
{
    @Autowired
    private ITbOrgEmployeeService tbOrgEmployeeService;

    /**
     * 查询组织情况员工信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbOrgEmployee tbOrgEmployee)
    {
        startPage();
        List<TbOrgEmployee> list = tbOrgEmployeeService.selectTbOrgEmployeeList(tbOrgEmployee);
        return getDataTable(list);
    }

    /**
     * 导出组织情况员工信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:export')")
    @Log(title = "组织情况员工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbOrgEmployee tbOrgEmployee)
    {
        List<TbOrgEmployee> list = tbOrgEmployeeService.selectTbOrgEmployeeList(tbOrgEmployee);
        ExcelUtil<TbOrgEmployee> util = new ExcelUtil<TbOrgEmployee>(TbOrgEmployee.class);
        util.exportExcel(response, list, "组织情况员工信息数据");
    }

    @Log(title = "组织情况员工信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbOrgEmployee> util = new ExcelUtil<TbOrgEmployee>(TbOrgEmployee.class);
        List<TbOrgEmployee> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbOrgEmployeeService.importTbOrgEmployee(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbOrgEmployee> util = new ExcelUtil<TbOrgEmployee>(TbOrgEmployee.class);
        util.exportTemplateExcel(response, "组织情况员工信息数据");
    }

    /**
     * 获取组织情况员工信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbOrgEmployeeService.selectTbOrgEmployeeById(id));
    }

    /**
     * 新增组织情况员工信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:add')")
    @Log(title = "组织情况员工信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbOrgEmployee tbOrgEmployee)
    {
        tbOrgEmployee.setCreateBy(getUsername());
        if (tbOrgEmployeeService.checkTbOrgEmployee(tbOrgEmployee)){
            return error("数据已存在！");
        }
        return toAjax(tbOrgEmployeeService.insertTbOrgEmployee(tbOrgEmployee));
    }

    /**
     * 修改组织情况员工信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:edit')")
    @Log(title = "组织情况员工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbOrgEmployee tbOrgEmployee)
    {
        tbOrgEmployee.setUpdateBy(getUsername());
        if (tbOrgEmployeeService.checkTbOrgEmployee(tbOrgEmployee)){
            return error("数据已存在！");
        }
        return toAjax(tbOrgEmployeeService.updateTbOrgEmployee(tbOrgEmployee));
    }

    /**
     * 删除组织情况员工信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:orgEmployee:remove')")
    @Log(title = "组织情况员工信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbOrgEmployeeService.deleteTbOrgEmployeeByIds(ids));
    }
}
