package com.ruoyi.common.enums;

public enum DataSourceFieldType {

    VARCHAR("varchar"),
    BIGINT("bigint"),
    DECIMAL("decimal"),
    DATETIME("datetime"),
    INT("int"),
    DOUBLE("double"),
    TEXT("text"),
    FLOAT("float");

    private final String value;

    public String getValue() {
        return value;
    }

    DataSourceFieldType(String value) {
        this.value = value;
    }
}
