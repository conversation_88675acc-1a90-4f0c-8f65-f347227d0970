package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.SeSubsequentInfo;
import com.ruoyi.microPlatform.mapper.SeSubsequentInfoMapper;
import com.ruoyi.microPlatform.service.ISeSubsequentInfoService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SePublicSentimentMapper;
import com.ruoyi.microPlatform.domain.SePublicSentiment;
import com.ruoyi.microPlatform.service.ISePublicSentimentService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 居民吹哨Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SePublicSentimentServiceImpl implements ISePublicSentimentService {
    @Autowired
    private SePublicSentimentMapper sePublicSentimentMapper;
    @Autowired
    private SeSubsequentInfoMapper seSubsequentInfoMapper;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisCache redisCache;
//    @Autowired
//    private SysUserMapper userMapper;

    /**
     * 查询居民吹哨
     *
     * @param id 居民吹哨主键
     * @return 居民吹哨
     */
    @Override
    public SePublicSentiment selectSePublicSentimentById(Long id) {
        SePublicSentiment sePublicSentiment = sePublicSentimentMapper.selectSePublicSentimentById(id);
        if (ObjectUtil.isNotEmpty(sePublicSentiment) && sePublicSentiment.getIsBackPhone() == 1) {
            handleSend(sePublicSentiment, 2, SecurityUtils.getLoginUser());
        }
        return sePublicSentiment;
    }

    /**
     * 校验居民吹哨是否存在
     *
     * @param sePublicSentiment
     * @return boolean
     */
    @Override
    public boolean checkSePublicSentiment(SePublicSentiment sePublicSentiment) {
        SePublicSentiment old = sePublicSentimentMapper.checkSePublicSentimentUnique(sePublicSentiment);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询居民吹哨列表
     *
     * @param sePublicSentiment 居民吹哨
     * @return 居民吹哨
     */
    @Override
    public List<SePublicSentiment> selectSePublicSentimentList(SePublicSentiment sePublicSentiment) {
        List<SePublicSentiment> sePublicSentiments = sePublicSentimentMapper.selectSePublicSentimentList(sePublicSentiment);
        return sePublicSentiments;
    }

    /**
     * 导入居民吹哨
     *
     * @param infos           居民吹哨列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSePublicSentiment(List<SePublicSentiment> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入居民吹哨数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SePublicSentiment info : infos) {
            try {
                // 验证是否存在这个数据
                SePublicSentiment old = sePublicSentimentMapper.checkSePublicSentimentUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    sePublicSentimentMapper.insertSePublicSentiment(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    sePublicSentimentMapper.updateSePublicSentiment(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增居民吹哨
     *
     * @param sePublicSentiment 居民吹哨
     * @return 结果
     */
    @Override
    public int insertSePublicSentiment(SePublicSentiment sePublicSentiment) {
        sePublicSentiment.setCreateTime(DateUtils.getNowDate());
        int i = sePublicSentimentMapper.insertSePublicSentiment(sePublicSentiment);
        if (i > 0) {
            handleSend(sePublicSentiment, 1, SecurityUtils.getLoginUser());
        }
        return i;
    }

    @Async("threadPoolTaskExecutor")
    public void handleSend(SePublicSentiment sePublicSentiment, Integer type, LoginUser loginUser) {
        if (type == 1) {
            // 新增
            Long deptId = sePublicSentiment.getDeptId();
            Long gridId = sePublicSentiment.getGridId();
            String gridName = null;
            if (ObjectUtil.isNotEmpty(gridId)) {
                SysDept dept = deptMapper.selectByGridId(gridId);
                if (ObjectUtil.isNotEmpty(dept)) {
                    System.err.println("给网格管理者发送消息通知：" + deptId);
                    deptId = dept.getDeptId();
                    gridName = dept.getDeptName();
                }
            } else {
                System.err.println("给社区全部管理者发送消息通知：" + deptId);
            }
            if (StringUtils.isBlank(sePublicSentiment.getTown()) && ObjectUtil.isNotEmpty(sePublicSentiment.getDeptId())) {
                SysDept sysDept = deptMapper.selectDeptAncestorsByDeptId(sePublicSentiment.getDeptId());
                if (ObjectUtil.isNotEmpty(sysDept)) {
                    System.err.println("竟然在新增的时候没有获取到社区信息");
                    redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + sysDept.getDeptId(), sysDept);
                    redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + sysDept.getAncestorsName(), sysDept);

                    String[] split = sysDept.getAncestorsName().split(",");
                    if (split.length >= 2) {
                        SePublicSentiment newData = new SePublicSentiment() {{
                            setId(sePublicSentiment.getId());
                        }};
                        newData.setCounty(split[1]);
                        if (split.length >= 3) {
                            newData.setCountry(split[2]);
                            if (split.length >= 4) {
                                newData.setTown(split[3]);
                            }
                        }
                        newData.setGridName(gridName);
                        sePublicSentimentMapper.updateSePublicSentiment(newData);
                    }
                }
            }else {
                if (StringUtils.isNotBlank(gridName)){
                    String finalGridName = gridName;
                    sePublicSentimentMapper.updateSePublicSentiment(new SePublicSentiment(){{
                        setId(sePublicSentiment.getId());
                        setGridName(finalGridName);
                    }});
                }
            }
            //TODO: 发送消息通知
            weChatService.insertMsg(deptId, "居民吹哨",
                    "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/publicSentiment/list?showTab=true&queryType=1",
                    sePublicSentiment.getTown() + (StringUtils.isNotBlank(gridName) ? "(网格：" + gridName + ")" : ""), "居民提交了一条新的居民吹哨记录");
            if (StringUtils.isNotBlank(gridName)){
                weChatService.insertMsg(sePublicSentiment.getDeptId(), "居民吹哨",
                        "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/publicSentiment/list?showTab=true&queryType=1",
                        sePublicSentiment.getTown(), "居民提交了一条新的居民吹哨记录");
            }
        } else if (type == 2) {
            // 更新已读消息
            System.err.println("查看者" + sePublicSentiment.getUserId() + "--提交者" + sePublicSentiment.getUserId());
            if (ObjectUtil.isNotEmpty(sePublicSentiment.getUserId()) && sePublicSentiment.getUserId().longValue() == loginUser.getUserId().longValue()) {
                System.err.println("更新已读消息");
                SePublicSentiment newData = new SePublicSentiment();
                newData.setId(sePublicSentiment.getId());
                newData.setIsBackPhone(0);
                sePublicSentimentMapper.updateSePublicSentiment(newData);
            }
        }
    }

    /**
     * 修改居民吹哨
     *
     * @param sePublicSentiment 居民吹哨
     * @return 结果
     */
    @Override
    public int updateSePublicSentiment(SePublicSentiment sePublicSentiment) {
        return sePublicSentimentMapper.updateSePublicSentiment(sePublicSentiment);
    }

    /**
     * 批量删除居民吹哨， 同时删除后续动态信息se_subsequent_info
     *
     * @param ids 需要删除的居民吹哨主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSePublicSentimentByIds(Long[] ids) {
        int i = sePublicSentimentMapper.deleteSePublicSentimentByIds(ids);
        //同时删除后续动态
        String formType = "居民吹哨";
        seSubsequentInfoMapper.deleteSeSubsequentInfoByPubIds(ids, formType);
        return i;
    }

    /**
     * 删除居民吹哨信息
     *
     * @param id 居民吹哨主键
     * @return 结果
     */
    @Override
    public int deleteSePublicSentimentById(Long id) {
        return sePublicSentimentMapper.deleteSePublicSentimentById(id);
    }

    @Override
    public Map<String, Object> selectSubmitCount(SePublicSentiment sePublicSentiment) {
        return sePublicSentimentMapper.selectSubmitCount(sePublicSentiment);
    }

    @Override
    public Integer getResidentWhistleCountByBigData(LargeScreenInfo largeScreenInfo) {
        return sePublicSentimentMapper.getResidentWhistleCount(largeScreenInfo);
    }
}
