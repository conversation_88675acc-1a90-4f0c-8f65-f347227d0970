package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyMember;

import java.util.List;

/**
 * 党员队伍信息管理Mapper接口
 */
public interface PbPartyMemberMapper {
    /** 查询 */
    PbPartyMember selectPbPartyMemberById(Long id);

    /** 唯一性校验（member_code唯一） */
    PbPartyMember checkPbPartyMemberUnique(PbPartyMember info);

    /** 列表 */
    List<PbPartyMember> selectPbPartyMemberList(PbPartyMember info);

    /** 新增 */
    int insertPbPartyMember(PbPartyMember info);

    /** 修改 */
    int updatePbPartyMember(PbPartyMember info);

    /** 删除 */
    int deletePbPartyMemberById(Long id);

    /** 批量删除 */
    int deletePbPartyMemberByIds(Long[] ids);

    Integer partyMemberCount();

    List<CommonBaseCount> partyMemberGroupSexCount();

    List<CommonBaseCount> partyMemberGroupAgeCount();

    List<CommonBaseCount> partyMemberGroupEducationCount();

    List<CommonBaseCount> partyMemberGroupPoliticalStatusCount();

    /**
     * 按所属党组织类型统计党员数量
     */
    List<CommonBaseCount> partyMemberGroupByOrgType();
}
