package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.microPlatform.domain.TbExportRequest;

/**
 * 数据导出审核Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public interface ITbExportRequestService 
{
    /**
     * 查询数据导出审核
     * 
     * @param id 数据导出审核主键
     * @return 数据导出审核
     */
    public TbExportRequest selectTbExportRequestById(Long id);

    /**
     * 校验数据导出审核是否存在
     *
     * @param tbExportRequest 数据导出审核
     * @return 数据导出审核
     */
    public boolean checkTbExportRequest(TbExportRequest tbExportRequest);

    /**
     * 查询数据导出审核列表
     * 
     * @param tbExportRequest 数据导出审核
     * @return 数据导出审核集合
     */
    public List<TbExportRequest> selectTbExportRequestList(TbExportRequest tbExportRequest);

    /**
     * 导入数据导出审核
     *
     * @param infos       数据导出审核列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbExportRequest(List<TbExportRequest> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增数据导出审核
     * 
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    public int insertTbExportRequest(TbExportRequest tbExportRequest);

    /**
     * 修改数据导出审核
     * 
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    public int updateTbExportRequest(TbExportRequest tbExportRequest, LoginUser loginUser);

    public int dataExport(TbExportRequest tbExportRequest, LoginUser loginUser);

    /**
     * 批量删除数据导出审核
     * 
     * @param ids 需要删除的数据导出审核主键集合
     * @return 结果
     */
    public int deleteTbExportRequestByIds(Long[] ids);

    /**
     * 删除数据导出审核信息
     * 
     * @param id 数据导出审核主键
     * @return 结果
     */
    public int deleteTbExportRequestById(Long id);
}
