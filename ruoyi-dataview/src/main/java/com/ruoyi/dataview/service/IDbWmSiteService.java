package com.ruoyi.dataview.service;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbWmSite;

import javax.sql.CommonDataSource;

/**
 * 水利局涵洞监测站点Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IDbWmSiteService 
{
    /**
     * 查询水利局涵洞监测站点
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 水利局涵洞监测站点
     */
    public DbWmSite selectDbWmSiteById(Long id);

    /**
     * 校验水利局涵洞监测站点是否存在
     *
     * @param dbWmSite 水利局涵洞监测站点
     * @return 水利局涵洞监测站点
     */
    public boolean checkDbWmSite(DbWmSite dbWmSite);

    /**
     * 查询水利局涵洞监测站点列表
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 水利局涵洞监测站点集合
     */
    public List<DbWmSite> selectDbWmSiteList(DbWmSite dbWmSite);

    /**
     * 导入水利局涵洞监测站点
     *
     * @param infos       水利局涵洞监测站点列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbWmSite(List<DbWmSite> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    public int insertDbWmSite(DbWmSite dbWmSite);

    /**
     * 修改水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    public int updateDbWmSite(DbWmSite dbWmSite);

    /**
     * 批量删除水利局涵洞监测站点
     * 
     * @param ids 需要删除的水利局涵洞监测站点主键集合
     * @return 结果
     */
    public int deleteDbWmSiteByIds(Long[] ids);

    /**
     * 删除水利局涵洞监测站点信息
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 结果
     */
    public int deleteDbWmSiteById(Long id);

    List<CommonBaseCount> wdDataStatusCount();
}
