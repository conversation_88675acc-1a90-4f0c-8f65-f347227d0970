package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;

import java.util.List;
import java.util.Map;

/**
 * 志愿报名Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeVolunteerRegisterMapper 
{
    /**
     * 查询志愿报名
     * 
     * @param id 志愿报名主键
     * @return 志愿报名
     */
    public SeVolunteerRegister selectSeVolunteerRegisterById(Long id);

    /**
     * 校验志愿报名是否唯一
     *
     * @param seVolunteerRegister
     * @return 志愿报名
     */
    public SeVolunteerRegister checkSeVolunteerRegisterUnique(SeVolunteerRegister seVolunteerRegister);

    /**
     * 查询志愿报名列表
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 志愿报名集合
     */
    public List<SeVolunteerRegister> selectSeVolunteerRegisterList(SeVolunteerRegister seVolunteerRegister);

    /**
     * 新增志愿报名
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    public int insertSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister);

    /**
     * 修改志愿报名
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    public int updateSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister);

    /**
     * 删除志愿报名
     * 
     * @param id 志愿报名主键
     * @return 结果
     */
    public int deleteSeVolunteerRegisterById(Long id);

    /**
     * 批量删除志愿报名
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeVolunteerRegisterByIds(Long[] ids);

    public Map<String,Object> selectSubmitCount(SeVolunteerRegister seVolunteerRegister);

    int selectSubmitCountByHome(HomePage homePage);

    int selectCountByBigData(BigdataParam bigdataParam);

}
