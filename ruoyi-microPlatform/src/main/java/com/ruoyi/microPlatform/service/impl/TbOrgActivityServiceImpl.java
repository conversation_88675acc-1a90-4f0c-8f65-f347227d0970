package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbOrgActivityMapper;
import com.ruoyi.microPlatform.domain.TbOrgActivity;
import com.ruoyi.microPlatform.service.ITbOrgActivityService;

/**
 * 组织日常活动开展情况Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class TbOrgActivityServiceImpl implements ITbOrgActivityService 
{
    @Autowired
    private TbOrgActivityMapper tbOrgActivityMapper;

    /**
     * 查询组织日常活动开展情况
     * 
     * @param id 组织日常活动开展情况主键
     * @return 组织日常活动开展情况
     */
    @Override
    public TbOrgActivity selectTbOrgActivityById(Long id)
    {
        return tbOrgActivityMapper.selectTbOrgActivityById(id);
    }

    /**
     * 校验组织日常活动开展情况是否存在
     *
     * @param tbOrgActivity
     * @return boolean
     */
    @Override
    public boolean checkTbOrgActivity(TbOrgActivity tbOrgActivity){
        TbOrgActivity old = tbOrgActivityMapper.checkTbOrgActivityUnique(tbOrgActivity);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询组织日常活动开展情况列表
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 组织日常活动开展情况
     */
    @Override
    public List<TbOrgActivity> selectTbOrgActivityList(TbOrgActivity tbOrgActivity)
    {
        return tbOrgActivityMapper.selectTbOrgActivityList(tbOrgActivity);
    }

    /**
     * 导入组织日常活动开展情况
     *
     * @param infos       组织日常活动开展情况列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbOrgActivity(List<TbOrgActivity> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入组织日常活动开展情况数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbOrgActivity info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbOrgActivity old = tbOrgActivityMapper.checkTbOrgActivityUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbOrgActivityMapper.insertTbOrgActivity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbOrgActivityMapper.updateTbOrgActivity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    @Override
    public int insertTbOrgActivity(TbOrgActivity tbOrgActivity)
    {
        return tbOrgActivityMapper.insertTbOrgActivity(tbOrgActivity);
    }

    /**
     * 修改组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    @Override
    public int updateTbOrgActivity(TbOrgActivity tbOrgActivity)
    {
        return tbOrgActivityMapper.updateTbOrgActivity(tbOrgActivity);
    }

    /**
     * 批量删除组织日常活动开展情况
     * 
     * @param ids 需要删除的组织日常活动开展情况主键
     * @return 结果
     */
    @Override
    public int deleteTbOrgActivityByIds(Long[] ids)
    {
        return tbOrgActivityMapper.deleteTbOrgActivityByIds(ids);
    }

    /**
     * 删除组织日常活动开展情况信息
     * 
     * @param id 组织日常活动开展情况主键
     * @return 结果
     */
    @Override
    public int deleteTbOrgActivityById(Long id)
    {
        return tbOrgActivityMapper.deleteTbOrgActivityById(id);
    }
}
