package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeFunctionReservation;

/**
 * 开放预约Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeFunctionReservationService 
{
    /**
     * 查询开放预约
     * 
     * @param id 开放预约主键
     * @return 开放预约
     */
    public SeFunctionReservation selectSeFunctionReservationById(Long id);

    /**
     * 校验开放预约是否存在
     *
     * @param seFunctionReservation 开放预约
     * @return 开放预约
     */
    public boolean checkSeFunctionReservation(SeFunctionReservation seFunctionReservation);

    /**
     * 查询开放预约列表
     * 
     * @param seFunctionReservation 开放预约
     * @return 开放预约集合
     */
    public List<SeFunctionReservation> selectSeFunctionReservationList(SeFunctionReservation seFunctionReservation);

    /**
     * 导入开放预约
     *
     * @param infos       开放预约列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeFunctionReservation(List<SeFunctionReservation> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增开放预约
     * 
     * @param seFunctionReservation 开放预约
     * @return 结果
     */
    public int insertSeFunctionReservation(SeFunctionReservation seFunctionReservation);

    /**
     * 修改开放预约
     * 
     * @param seFunctionReservation 开放预约
     * @return 结果
     */
    public int updateSeFunctionReservation(SeFunctionReservation seFunctionReservation);

    /**
     * 批量删除开放预约
     * 
     * @param ids 需要删除的开放预约主键集合
     * @return 结果
     */
    public int deleteSeFunctionReservationByIds(Long[] ids);

    /**
     * 删除开放预约信息
     * 
     * @param id 开放预约主键
     * @return 结果
     */
    public int deleteSeFunctionReservationById(Long id);


    public Map<String, Object> selectSubmitCount(SeFunctionReservation seFunctionReservation);
}
