package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbPersonType;
import com.ruoyi.microPlatform.service.ITbPersonTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员标签类型Controller
 * 
 * <AUTHOR>
 * @date 2025-04-04
 */
@RestController
@RequestMapping("/gridData/personType")
public class TbPersonTypeController extends BaseController
{
    @Autowired
    private ITbPersonTypeService tbPersonTypeService;

    /**
     * 查询人员标签类型列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbPersonType tbPersonType)
    {
        startPage();
        List<TbPersonType> list = tbPersonTypeService.selectTbPersonTypeList(tbPersonType);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(TbPersonType tbPersonType)
    {
        List<TbPersonType> list = tbPersonTypeService.selectTbPersonTypeList(tbPersonType);
        return success(list);
    }

    /**
     * 导出人员标签类型列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:export')")
    @Log(title = "人员标签类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbPersonType tbPersonType)
    {
        List<TbPersonType> list = tbPersonTypeService.selectTbPersonTypeList(tbPersonType);
        ExcelUtil<TbPersonType> util = new ExcelUtil<TbPersonType>(TbPersonType.class);
        util.exportExcel(response, list, "人员标签类型数据");
    }

    @Log(title = "人员标签类型", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:personType:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbPersonType> util = new ExcelUtil<TbPersonType>(TbPersonType.class);
        List<TbPersonType> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbPersonTypeService.importTbPersonType(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbPersonType> util = new ExcelUtil<TbPersonType>(TbPersonType.class);
        util.exportTemplateExcel(response, "人员标签类型数据");
    }

    /**
     * 获取人员标签类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(tbPersonTypeService.selectTbPersonTypeById(id));
    }

    /**
     * 新增人员标签类型
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:add')")
    @Log(title = "人员标签类型", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbPersonType tbPersonType)
    {
        tbPersonType.setCreateBy(getUsername());
        if (tbPersonTypeService.checkTbPersonType(tbPersonType)){
            return error("数据已存在！");
        }
        return toAjax(tbPersonTypeService.insertTbPersonType(tbPersonType));
    }

    /**
     * 修改人员标签类型
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:edit')")
    @Log(title = "人员标签类型", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbPersonType tbPersonType)
    {
        tbPersonType.setUpdateBy(getUsername());
        if (tbPersonTypeService.checkTbPersonType(tbPersonType)){
            return error("数据已存在！");
        }
        return toAjax(tbPersonTypeService.updateTbPersonType(tbPersonType));
    }

    /**
     * 删除人员标签类型
     */
    @PreAuthorize("@ss.hasPermi('gridData:personType:remove')")
    @Log(title = "人员标签类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(tbPersonTypeService.deleteTbPersonTypeByIds(ids));
    }
}
