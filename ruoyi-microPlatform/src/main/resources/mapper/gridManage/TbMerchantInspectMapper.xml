<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMerchantInspectMapper">

    <resultMap type="TbMerchantInspect" id="TbMerchantInspectResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantType" column="merchant_type"/>
        <result property="inspectResult" column="inspect_result"/>
        <result property="remark" column="remark"/>
        <result property="accessory" column="accessory"/>
        <result property="personName" column="person_name"/>
        <result property="personPone" column="person_pone"/>
        <result property="personId" column="person_id"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="addr" column="addr"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="handleCount" column="handle_count"/>
        <result property="isIssue" column="is_issue"/>
    </resultMap>

    <resultMap type="TbMerchantInspect" id="TbMerchantInspectDetailResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantType" column="merchant_type"/>
        <result property="inspectResult" column="inspect_result"/>
        <result property="remark" column="remark"/>
        <result property="accessory" column="accessory"/>
        <result property="personName" column="person_name"/>
        <result property="personPone" column="person_pone"/>
        <result property="personId" column="person_id"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="addr" column="addr"/>
        <result property="name" column="name"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="handleCount" column="handle_count"/>
        <result property="isIssue" column="is_issue"/>
        <collection property="merchantInspectDetail" column="inspect_id" javaType="java.util.List" resultMap="TbMerchantInspectAsDetailResult"/>
    </resultMap>

    <resultMap type="TbMerchantInspectDetail" id="TbMerchantInspectAsDetailResult">
        <result property="id" column="de_id"/>
        <result property="inspectType" column="inspect_type"/>
        <result property="inspectId" column="inspect_id"/>
        <result property="inspectDesc" column="inspect_desc"/>
        <result property="inspectResult" column="de_inspect_result"/>
        <result property="remark" column="de_remark"/>
        <result property="type" column="de_type"/>
        <result property="accessory" column="de_accessory"/>
    </resultMap>

    <sql id="selectTbMerchantInspectVo">
        select id, dept_id, county, country,name, town, grid_name, grid_id, merchant_id, merchant_type, inspect_result, remark,
        accessory, person_name, person_pone, person_id, lat, lng, addr, create_time, status,is_issue from tb_merchant_inspect
    </sql>

    <sql id="selectTbMerchantInspectDetailVo">
        select insp.id, insp.dept_id, insp.county, insp.name, insp.country, insp.town, insp.grid_name, insp.grid_id,
               insp.merchant_id, insp.merchant_type, insp.inspect_result, insp.remark,insp.is_issue,
               insp.accessory, insp.person_name, insp.person_pone, insp.person_id, insp.lat, insp.lng, insp.addr,
               insp.create_time, insp.status,de.id as de_id ,de.inspect_type,de.inspect_id,de.inspect_desc,
            de.inspect_result as de_inspect_result,de.remark as de_remark ,de.type as de_type, de.accessory as de_accessory
        from tb_merchant_inspect insp  left join tb_merchant_inspect_detail de on insp.id = de.inspect_id
    </sql>


    <select id="selectTbMerchantInspectList" parameterType="TbMerchantInspect" resultMap="TbMerchantInspectResult">
        SELECT
        mi.id, mi.dept_id, mi.county,mi.name, mi.country, mi.town, mi.grid_name, mi.grid_id, mi.merchant_id, mi.merchant_type, mi.inspect_result, mi.remark,
        mi.accessory, mi.person_name, mi.person_pone, mi.person_id, mi.lat, mi.lng, mi.addr, mi.create_time, mi.status,mi.is_issue,
        COALESCE(ssi.handle_count, 0) AS handle_count
        FROM tb_merchant_inspect mi
        LEFT JOIN (
            SELECT form_id, COUNT(*) AS handle_count
            FROM se_subsequent_info
            WHERE form_type = '门店巡查'
            GROUP BY form_id
        ) ssi ON mi.id = ssi.form_id
        <where>
            <if test="deptId != null ">and (mi.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = mi.dept_id))</if>
            <if test="county != null  and county != ''">and mi.county = #{county}</if>
            <if test="country != null  and country != ''">and mi.country = #{country}</if>
            <if test="town != null  and town != ''">and mi.town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and mi.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and mi.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and mi.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantId != null ">and mi.merchant_id = #{merchantId}</if>
            <if test="currentMonth != null and currentMonth == true ">and DATE_FORMAT(mi.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')</if>
            <if test="merchantType != null  and merchantType != ''">and mi.merchant_type = #{merchantType}</if>
            <if test="inspectResult != null ">and mi.inspect_result = #{inspectResult}</if>
            <if test="name != null and name != ''">and mi.name like concat('%', #{name}, '%')</if>
            <if test="accessory != null  and accessory != ''">and mi.accessory = #{accessory}</if>
            <if test="personName != null  and personName != ''">and mi.person_name like concat('%', #{personName}, '%')
            </if>
            <if test="personPone != null  and personPone != ''">and mi.person_pone = #{personPone}</if>
            <if test="personId != null ">and mi.person_id = #{personId}</if>
            <if test="lat != null  and lat != ''">and mi.lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and mi.lng = #{lng}</if>
            <if test="addr != null  and addr != ''">and mi.addr = #{addr}</if>
            <if test="status != null ">and mi.status = #{status}</if>
            <if test="isIssue != null ">and mi.is_issue = #{isIssue}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and mi.create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by id desc
    </select>


    <select id="selectTbMerchantInspectById" parameterType="Long" resultMap="TbMerchantInspectDetailResult">
        <include refid="selectTbMerchantInspectDetailVo"/>
        where insp.id = #{id}
    </select>



    <select id="checkTbMerchantInspectUnique" parameterType="TbMerchantInspect" resultMap="TbMerchantInspectResult">
        select id from tb_merchant_inspect where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMerchantInspect" parameterType="TbMerchantInspect" useGeneratedKeys="true" keyProperty="id">
        insert into tb_merchant_inspect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="merchantId != null">merchant_id,</if>
            <if test="merchantType != null">merchant_type,</if>
            <if test="inspectResult != null">inspect_result,</if>
            <if test="remark != null">remark,</if>
            <if test="accessory != null">accessory,</if>
            <if test="personName != null">person_name,</if>
            <if test="personPone != null">person_pone,</if>
            <if test="personId != null">person_id,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="addr != null">addr,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="name != null">name,</if>
            <if test="isIssue != null">is_issue,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="merchantId != null">#{merchantId},</if>
            <if test="merchantType != null">#{merchantType},</if>
            <if test="inspectResult != null">#{inspectResult},</if>
            <if test="remark != null">#{remark},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="personName != null">#{personName},</if>
            <if test="personPone != null">#{personPone},</if>
            <if test="personId != null">#{personId},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="addr != null">#{addr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="name != null">#{name},</if>
            <if test="isIssue != null">#{isIssue},</if>
        </trim>
    </insert>

    <update id="updateTbMerchantInspect" parameterType="TbMerchantInspect">
        update tb_merchant_inspect
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="merchantType != null">merchant_type = #{merchantType},</if>
            <if test="inspectResult != null">inspect_result = #{inspectResult},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="personPone != null">person_pone = #{personPone},</if>
            <if test="personId != null">person_id = #{personId},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="name != null">name = #{name},</if>
            <if test="isIssue != null">is_issue = #{isIssue},</if>
        </trim>
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="id == null and gridId != null"> and grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbMerchantInspectById" parameterType="Long">
        delete from tb_merchant_inspect where id = #{id}
    </delete>

    <delete id="deleteTbMerchantInspectByIds" parameterType="String">
        delete from tb_merchant_inspect where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbMerchantInspectListCount" resultType="int">
        SELECT COUNT(*) AS total_count
        FROM tb_merchant_inspect
        where merchant_id = #{merchantId}
    </select>

    <select id="selectTbMerchantInspectListCountTime" resultType="int">
        SELECT COUNT(*) AS current_month_count
        FROM tb_merchant_inspect
        WHERE merchant_id = #{merchantId}
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
    </select>

    <select id="selectSubmitCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS submitCount,
        DATE_FORMAT(MAX(create_time), '%m-%d %H:%i:%s') AS submitTime
        FROM tb_merchant_inspect
        WHERE person_id = #{personId}
        <if test="merchantId != null">
            and merchant_id = #{merchantId}
        </if>
    </select>

    <select id="selectMonthlyStats" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.util.Map">
        SELECT DATE_FORMAT(create_time, '%Y%m') AS month, COUNT(*) AS total
        FROM tb_merchant_inspect
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_merchant_inspect.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>

            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
        GROUP BY month
        ORDER BY month
    </select>


    <select id="selectCount" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="int">
        SELECT COUNT(*) AS total
        FROM tb_merchant_inspect
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_merchant_inspect.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>

            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
    </select>
</mapper>