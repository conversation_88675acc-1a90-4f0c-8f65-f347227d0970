package com.ruoyi.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.DateBaseTables.Columns;
import com.ruoyi.common.DateBaseTables.DataSourceTable;
import com.ruoyi.common.DateBaseTables.FieldType;
import com.ruoyi.common.DateBaseTables.Tables;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.DataSourceFieldType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.mapper.DataSourceMapper;
import com.ruoyi.system.service.DataSourceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class DataSourceServiceImpl implements DataSourceService {
    @Resource
    DataSourceMapper dataSourceMapper;

    @Override
    public AjaxResult createTable(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名不能为空");
        } else if (ObjectUtil.isEmpty(table.getColumnsList()) || table.getColumnsList().size() == 0) {
            return AjaxResult.error("字段数必须大于1");
        }
        if (StrUtil.isNotBlank(table.getTableName())) {
            if (table.getTableName().contains("-")) {
                throw new ServiceException("禁止使用字符 -");
            }
        }
        String tableName = dataSourceMapper.getTableName(table.getTableName(), table.getDataSourceName());
        if (StrUtil.isNotEmpty(tableName)) {
            return AjaxResult.error("当前数据库已存在该表名：" + table.getTableName());
        }
        if (checkData(table)) return AjaxResult.error("只能添加一个主键");
        dataSourceMapper.createTable(table);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult alterTableField(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名称不能为空");
        }
        if (ObjectUtil.isEmpty(table.getColumnsList()) || table.getColumnsList().size() == 0) {
            return AjaxResult.error("字段信息不能为空");
        } else {
            Columns columns = table.getColumnsList().get(0);
            if (StrUtil.isBlank(columns.getField())) {
                return AjaxResult.error("字段名不能为空");
            } else if (StrUtil.isBlank(columns.getType())) {
                return AjaxResult.error("字段类型不能为空");
            }
        }
        Columns columns = table.getColumnsList().get(0);
        if (columns.isColumnIsKey()) {
            int keyNum = this.getTableKeyNum(table);
            if (keyNum > 0) {
                return AjaxResult.error("只能添加一个主键");
            }
        }
        dataSourceMapper.alterTableField(columns, table.getTableName());

        return AjaxResult.success();
    }

    @Override
    public AjaxResult dropTableField(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名不能为空");
        } else if (ObjectUtil.isEmpty(table.getFieldList()) || table.getFieldList().size() == 0) {
            return AjaxResult.error("需要删除的字段不能为空");
        }
        dataSourceMapper.dropTableField(table.getFieldList(), table.getTableName());
        return AjaxResult.success();
    }

    @Override
    public AjaxResult dropTable(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名不能为空");
        }
        dataSourceMapper.dropTable(table.getTableName());
        return AjaxResult.success();
    }

    @Override
    public AjaxResult truncateTable(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名不能为空");
        }
        dataSourceMapper.truncateTable(table.getTableName());
        return AjaxResult.success();
    }

    @Override
    public AjaxResult addTableField(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            return AjaxResult.error("表名不能为空");
        } else if (ObjectUtil.isEmpty(table.getColumnsList()) || table.getColumnsList().size() == 0) {
            return AjaxResult.error("增加的字段不能为空");
        }
        if (checkData(table)) return AjaxResult.error("只能添加一个主键");
        dataSourceMapper.addTableField(table);
        return AjaxResult.success();
    }

    @Override
    public List<Tables> getTables(DataSourceTable table) {
        if (StrUtil.isBlank(table.getDataSourceName())) {
            throw new ServiceException("数据库名称查询为空", 500);
        }
        return dataSourceMapper.getDataSourceTables(table.getDataSourceName());
    }

    @Override
    public List<Columns> getTableColumns(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            throw new ServiceException("表名不能为空", 500);
        }
        List<Map<String, Object>> mapList = dataSourceMapper.getTableColumns(table.getTableName());
        return JSON.parseArray(JSON.toJSONString(mapList)).toJavaList(Columns.class);
    }

    @Override
    public AjaxResult getFieldTypeList() {
        ArrayList<FieldType> fieldTypes = new ArrayList<FieldType>() {{
            add(new FieldType() {{
                setName("字符");
                setType(DataSourceFieldType.VARCHAR.getValue());
            }});
            add(new FieldType() {{
                setName("整数(int)");
                setType(DataSourceFieldType.INT.getValue());
            }});
            add(new FieldType() {{
                setName("时间");
                setType(DataSourceFieldType.DATETIME.getValue());
            }});
            add(new FieldType() {{
                setName("整数(bigint)");
                setType(DataSourceFieldType.BIGINT.getValue());
            }});
            add(new FieldType() {{
                setName("十进制小数");
                setType(DataSourceFieldType.DECIMAL.getValue());
            }});
            add(new FieldType() {{
                setName("单精度浮点数");
                setType(DataSourceFieldType.FLOAT.getValue());
            }});
            add(new FieldType() {{
                setName("双精度浮点数");
                setType(DataSourceFieldType.DOUBLE.getValue());
            }});
        }};

        return AjaxResult.success(fieldTypes);
    }

    @Override
    public AjaxResult alterTableComment(DataSourceTable table) {
        if (StrUtil.isBlank(table.getTableName())) {
            throw new ServiceException("表名不能为空", 500);
        } else if (StrUtil.isBlank(table.getTableComment())) {
            throw new ServiceException("表注释不能为空", 500);
        }
        dataSourceMapper.alterTableComment(table);

        return AjaxResult.success();
    }

    private boolean checkData(DataSourceTable table) {
        this.checkTableColumn(table);
        List<Columns> columnsList = table.getColumnsList();
        int keys = 0;
        for (Columns columns : columnsList) {
            if (columns.isColumnIsKey()) {
                keys++;
            }
        }
        int keyNum = this.getTableKeyNum(table);
        if (keys == 1 && keyNum > 0 || keys > 1) {
            return true;
        }
        return false;
    }

    private int getTableKeyNum(DataSourceTable table) {
        if (ObjectUtil.isNotEmpty(table.getType()) && table.getType() == 2) {
            List<Map<String, Object>> mapList = dataSourceMapper.getTableColumns(table.getTableName());
            List<Columns> columnsList = JSON.parseArray(JSON.toJSONString(mapList)).toJavaList(Columns.class);
            int i = 0;
            for (Columns column : columnsList) {
                if (column.isColumnIsKey()) {
                    i++;
                }
            }
            return i;
        } else {
            return 0;
        }

    }

    private void checkTableColumn(DataSourceTable table) {
        List<Columns> columnsList = table.getColumnsList();
        int i = 1;
        for (Columns columns : columnsList) {
            if (table.isEnableField()) {
                columns.setField("A" + i++);
            } else {
                if (StrUtil.isBlank(columns.getField())) {
                    throw new ServiceException("字段名不能为空", 500);
                }
            }
            if (StrUtil.isBlank(columns.getType())) {
                throw new ServiceException("字段类型不能为空", 500);
            } else {
                if (ObjectUtil.isEmpty(columns.getLength()) || columns.getLength() == 0) {
                    if (columns.getType().equals(DataSourceFieldType.VARCHAR.getValue())) {
                        columns.setLength(255);
                    } else if (columns.getType().equals(DataSourceFieldType.INT.getValue())) {
                        columns.setLength(20);
                    } else if (columns.getType().equals(DataSourceFieldType.BIGINT.getValue())) {
                        columns.setLength(25);
                    }
                }
            }
        }
    }
}
