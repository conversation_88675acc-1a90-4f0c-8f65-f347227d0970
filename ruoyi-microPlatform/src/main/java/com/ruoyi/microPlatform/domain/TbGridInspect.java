package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 网格巡查对象 tb_grid_inspect
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbGridInspect extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 网格名称 */
    @Excel(name = "所属网格")
    private String gridName;

    /** 网格ID */
    private Long gridId;

    /** 名称（巡查的小区名称或门店名称或路段名称） */
    @Excel(name = "巡查地点名称")
    private String name;

    /** 巡查结果（1正常 2异常） */
    @Excel(name = "巡查结果", readConverterExp = "1=正常,2=异常")
    private Integer inspectResult;
    /** 备注 */
    @Excel(name = "其他情况")
    private String remark;

    /** 异常情况 */
    @Excel(name = "异常情况")
    private String abnormalSituation;


    /** 现场照片 */
    @Excel(name = "相关图片",cellType = Excel.ColumnType.IMAGE)
    private String accessory;

    /** 排查人 */
    @Excel(name = "排查人")
    private String personName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "排查时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 排查人手机号 */
    @Excel(name = "排查人手机号")
    private String personPone;

    /** 排查人账号 */
    private Long personId;

    /** 定位 */
    private String lat;

    /**  */
    private String lng;

    /**  */
    @Excel(name = "定位获取到的地址")
    private String addr;

    /** 数据状态（0待分配 1处理中 2已处理 3无需处理） */
    @Excel(name = "数据状态", readConverterExp = "0=待分配,1=需跟进,2=已处理,3=无需处理")
    private Integer status;

    /** 数据状态 下的数据量 */
    private Integer statusCount;

    /**
     * 后续动态数量
     */
    private Integer handleCount;

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public void setAccessory(String accessory)
    {
        this.accessory = accessory;
        if (accessory != null && !this.accessory.equals("")){
            List<String> stringList = Arrays.asList(accessory.split(","));
            this.setFileList(stringList);
            if (stringList.size() > 0){
                this.setFirstFile(stringList.get(0));
            }
        }
    }

    public String getAccessory()
    {
        return accessory;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public Integer getHandleCount() {
        return handleCount;
    }

    public void setHandleCount(Integer handleCount) {
        this.handleCount = handleCount;
    }

    public Integer getStatusCount() {
        return statusCount;
    }

    public void setStatusCount(Integer statusCount) {
        this.statusCount = statusCount;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setInspectResult(Integer inspectResult) 
    {
        this.inspectResult = inspectResult;
    }

    public Integer getInspectResult() 
    {
        return inspectResult;
    }
    public void setAbnormalSituation(String abnormalSituation) 
    {
        this.abnormalSituation = abnormalSituation;
    }

    public String getAbnormalSituation() 
    {
        return abnormalSituation;
    }
    public void setPersonName(String personName)
    {
        this.personName = personName;
    }

    public String getPersonName() 
    {
        return personName;
    }
    public void setPersonPone(String personPone) 
    {
        this.personPone = personPone;
    }

    public String getPersonPone() 
    {
        return personPone;
    }
    public void setPersonId(Long personId) 
    {
        this.personId = personId;
    }

    public Long getPersonId() 
    {
        return personId;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setAddr(String addr) 
    {
        this.addr = addr;
    }

    public String getAddr() 
    {
        return addr;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("gridId", getGridId())
            .append("name", getName())
            .append("inspectResult", getInspectResult())
            .append("abnormalSituation", getAbnormalSituation())
            .append("remark", getRemark())
            .append("accessory", getAccessory())
            .append("personName", getPersonName())
            .append("personPone", getPersonPone())
            .append("personId", getPersonId())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("addr", getAddr())
            .append("createTime", getCreateTime())
            .append("status", getStatus())
            .toString();
    }
}
