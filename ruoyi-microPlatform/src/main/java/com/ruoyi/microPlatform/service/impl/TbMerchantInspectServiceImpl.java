package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;
import com.ruoyi.microPlatform.domain.TbMerchantInspect;
import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;
import com.ruoyi.microPlatform.mapper.TbMerchantInfoMapper;
import com.ruoyi.microPlatform.mapper.TbMerchantInspectDetailMapper;
import com.ruoyi.microPlatform.mapper.TbMerchantInspectMapper;
import com.ruoyi.microPlatform.service.ITbMerchantInspectService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 门店巡查Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbMerchantInspectServiceImpl implements ITbMerchantInspectService 
{
    @Autowired
    private TbMerchantInspectMapper tbMerchantInspectMapper;
    @Autowired
    private TbMerchantInfoMapper merchantInfoMapper;

    @Autowired
    private TbMerchantInspectDetailMapper MerchantInspectDetailMapper;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询门店巡查
     * 
     * @param id 门店巡查主键
     * @return 门店巡查
     */
    @Override
    public TbMerchantInspect selectTbMerchantInspectById(Long id)
    {

        TbMerchantInspect tbMerchantInspect = tbMerchantInspectMapper.selectTbMerchantInspectById(id);


        Map<String, List<TbMerchantInspectDetail>> map = tbMerchantInspect.getMap();
        // 按 type 分组
        for (TbMerchantInspectDetail require : tbMerchantInspect.getMerchantInspectDetail()) {
            String type = require.getType();
            map.computeIfAbsent(type, k -> new ArrayList<>()).add(require);
        }


        return tbMerchantInspect;
    }

    /**
     * 校验门店巡查是否存在
     *
     * @param tbMerchantInspect
     * @return boolean
     */
    @Override
    public boolean checkTbMerchantInspect(TbMerchantInspect tbMerchantInspect){
        TbMerchantInspect old = tbMerchantInspectMapper.checkTbMerchantInspectUnique(tbMerchantInspect);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询门店巡查列表
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 门店巡查
     */
    @Override
    public List<TbMerchantInspect> selectTbMerchantInspectList(TbMerchantInspect tbMerchantInspect)
    {
        return tbMerchantInspectMapper.selectTbMerchantInspectList(tbMerchantInspect);
    }

    /**
     * 导入门店巡查
     *
     * @param infos       门店巡查列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantInspect(List<TbMerchantInspect> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入门店巡查数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbMerchantInspect info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbMerchantInspect old = tbMerchantInspectMapper.checkTbMerchantInspectUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbMerchantInspectMapper.insertTbMerchantInspect(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbMerchantInspectMapper.updateTbMerchantInspect(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertTbMerchantInspect(TbMerchantInspect tbMerchantInspect)
    {
        tbMerchantInspect.setCreateTime(DateUtils.getNowDate());
        TbMerchantInfo merchantInfo = merchantInfoMapper.selectMerchantInfoById(tbMerchantInspect.getMerchantId());

        tbMerchantInspect.setDeptId(merchantInfo.getDeptId());
        tbMerchantInspect.setCountry(merchantInfo.getCountry());
        tbMerchantInspect.setCounty(merchantInfo.getCounty());
        tbMerchantInspect.setTown(merchantInfo.getTown());
        tbMerchantInspect.setGridId(merchantInfo.getGridId());
        tbMerchantInspect.setGridName(merchantInfo.getGridName());

        //新增门店巡查
        int i = tbMerchantInspectMapper.insertTbMerchantInspect(tbMerchantInspect);

        // 2. 获取生成的 id
        Long inspectId = tbMerchantInspect.getId();

        // 3. 设置 TbMerchantInspectDetail 的 inspectId，并批量插入
        List<TbMerchantInspectDetail> details = tbMerchantInspect.getMerchantInspectDetail();
        if (details != null && !details.isEmpty()) {
//            for (TbMerchantInspectDetail detail : details) {
//                detail.setInspectId(inspectId); // 设置关联的 inspectId
//            }
            //批量插入巡查项目
            MerchantInspectDetailMapper.batchInsertTbMerchantInspectDetail(inspectId, details);
        }

        //新增门店巡查基础检查项目
        /*TbMerchantInspectDetail merchantInspectDetail = tbMerchantInspect.getMerchantInspectDetail();
        merchantInspectDetail.setInspectId(tbMerchantInspect.getId());
        merchantInspectDetail.setInspectResult(tbMerchantInspect.getInspectResult());
        int i1 = MerchantInspectDetailMapper.insertTbMerchantInspectDetail(merchantInspectDetail);*/

        // 更新门店状态
        merchantInfoMapper.updateTbMerchantInfo(new TbMerchantInfo(){{
            setId(tbMerchantInspect.getMerchantId());
            setStoreStatus(tbMerchantInspect.getInspectResult());
        }});
        if (i > 0) {
            handleSend(tbMerchantInspect, SecurityUtils.getLoginUser());
        }
        return tbMerchantInspect.getId();
    }

    @Async("threadPoolTaskExecutor")
    public void handleSend(TbMerchantInspect tbMerchantInspect, LoginUser loginUser) {

        // 新增
        Long deptId = tbMerchantInspect.getDeptId();
        if (StringUtils.isBlank(tbMerchantInspect.getTown()) && ObjectUtil.isNotEmpty(tbMerchantInspect.getDeptId())) {
            SysDept sysDept = deptMapper.selectDeptAncestorsByDeptId(tbMerchantInspect.getDeptId());
            if (ObjectUtil.isNotEmpty(sysDept)) {
                System.err.println("竟然在新增的时候没有获取到社区信息");
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + sysDept.getDeptId(), sysDept);
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + sysDept.getAncestorsName(), sysDept);

                String[] split = sysDept.getAncestorsName().split(",");
                if (split.length >= 2) {
                    TbMerchantInspect newData = new TbMerchantInspect() {{
                        setId(tbMerchantInspect.getId());
                    }};
                    newData.setCounty(split[1]);
                    if (split.length >= 3) {
                        newData.setCountry(split[2]);
                        if (split.length >= 4) {
                            newData.setTown(split[3]);
                        }
                    }
                    tbMerchantInspectMapper.updateTbMerchantInspect(newData);
                }
            }
        }
        //TODO: 发送消息通知
        weChatService.insertMsg(deptId, "门店巡查",
                "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/grid/store/newRecord/detail?merchantInspectId=" + tbMerchantInspect.getId(),
                tbMerchantInspect.getTown(), "提交了门店巡查记录，巡查结果为" + (tbMerchantInspect.getInspectResult() == 1 ? "正常" : "异常"));
    }

    /**
     * 修改门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    @Override
    public int updateTbMerchantInspect(TbMerchantInspect tbMerchantInspect)
    {
        return tbMerchantInspectMapper.updateTbMerchantInspect(tbMerchantInspect);
    }

    /**
     * 批量删除门店巡查
     * 
     * @param ids 需要删除的门店巡查主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantInspectByIds(Long[] ids)
    {
        return tbMerchantInspectMapper.deleteTbMerchantInspectByIds(ids);
    }

    /**
     * 删除门店巡查信息
     * 
     * @param id 门店巡查主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantInspectById(Long id)
    {
        return tbMerchantInspectMapper.deleteTbMerchantInspectById(id);
    }

    /**
     * 获取门店巡查基础检查项目 表累计次数和巡查记录表本月次数
     *
     */
    @Override
    public Map<String, Integer> selectTbMerchantInspectListCount(TbMerchantInspect tbMerchantInspect) {

        Map<String, Integer> map = new HashMap<>();
        //查询当前 inspect_id 下的所有数据count
        int count = tbMerchantInspectMapper.selectTbMerchantInspectListCount(tbMerchantInspect);
        map.put("巡查累计次数",count);
        //查询本月 create_time  和 inspect_id 下的所有数据
        int countTime = tbMerchantInspectMapper.selectTbMerchantInspectListCountTime(tbMerchantInspect);
        map.put("巡查本月次数",countTime);
        return map;
    }

    @Override
    public Map<String, Object> selectSubmitCount(TbMerchantInspect tbMerchantInspect) {
        return tbMerchantInspectMapper.selectSubmitCount(tbMerchantInspect);
    }


    @Override
    public List<Map<String, Object>> merchantInspectGroupMonth(LargeScreenInfo largeScreenInfo) {
        List<Map<String, Object>> gridData = tbMerchantInspectMapper.selectMonthlyStats(largeScreenInfo);//网格巡查

        // 结果用 LinkedHashMap 保持有序
        Map<String, Integer> monthCountMap = new HashMap<>();
        for (Map<String, Object> row : gridData) {
            monthCountMap.put((String) row.get("month"), ((Number) row.get("total")).intValue());
        }
        List<Map<String, Object>> finalResult = new ArrayList<>();

        // 当前月
        DateTime end = DateUtil.endOfMonth(new Date());
        // 往前推 11 个月
        DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(end, -11));
        // 按月遍历
        DateUtil.rangeToList(start, end, DateField.MONTH).forEach(date -> {
            String monthKey = DateUtil.format(date, "yyyyMM");
            Map<String, Object> item = new HashMap<>();
            item.put("month", monthKey);
            item.put("total", monthCountMap.getOrDefault(monthKey, 0));
            finalResult.add(item);
        });
        return finalResult;
    }

    @Override
    public int tbMerchantInspectCount(LargeScreenInfo largeScreenInfo) {
        return tbMerchantInspectMapper.selectCount(largeScreenInfo);
    }
}
