<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbSituationOrgMapper">

    <resultMap type="TbSituationOrg" id="TbSituationOrgResult">
            <result property="id"    column="id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="county"    column="county"    />
            <result property="country"    column="country"    />
            <result property="town"    column="town"    />
            <result property="name"    column="name"    />
            <result property="type"    column="type"    />
            <result property="category"    column="category"    />
            <result property="managerName"    column="manager_name"    />
            <result property="memberUnitCount"    column="member_unit_count"    />
            <result property="memberIndividualCount"    column="member_individual_count"    />
            <result property="hasParty"    column="has_party"    />
            <result property="partyName"    column="party_name"    />
            <result property="partyType"    column="party_type"    />
            <result property="partyPeoples"    column="party_peoples"    />
            <result property="instructorName"    column="instructor_name"    />
            <result property="supPartyName"    column="sup_party_name"    />
            <result property="orgEmployees"    column="org_employees"    />
            <result property="activityFrequency"    column="activity_frequency"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTbSituationOrgVo">
        select id, dept_id, county, country, town, name, type, category, manager_name, member_unit_count, member_individual_count, has_party, party_name, party_type, party_peoples, instructor_name, sup_party_name, org_employees, activity_frequency, create_time, create_by, update_time, update_by from tb_situation_org
    </sql>

    <select id="selectTbSituationOrgList" parameterType="TbSituationOrg" resultMap="TbSituationOrgResult">
        <include refid="selectTbSituationOrgVo"/>
        <where>
                        <if test="deptId != null "> and dept_id = #{deptId}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="country != null  and country != ''"> and country = #{country}</if>
                        <if test="town != null  and town != ''"> and town = #{town}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="type != null  and type != ''"> and type = #{type}</if>
                        <if test="category != null  and category != ''"> and category = #{category}</if>
                        <if test="managerName != null  and managerName != ''"> and manager_name like concat('%', #{managerName}, '%')</if>
                        <if test="memberUnitCount != null "> and member_unit_count = #{memberUnitCount}</if>
                        <if test="memberIndividualCount != null "> and member_individual_count = #{memberIndividualCount}</if>
                        <if test="hasParty != null  and hasParty != ''"> and has_party = #{hasParty}</if>
                        <if test="partyName != null  and partyName != ''"> and party_name like concat('%', #{partyName}, '%')</if>
                        <if test="partyType != null  and partyType != ''"> and party_type = #{partyType}</if>
                        <if test="partyPeoples != null "> and party_peoples = #{partyPeoples}</if>
                        <if test="instructorName != null  and instructorName != ''"> and instructor_name like concat('%', #{instructorName}, '%')</if>
                        <if test="supPartyName != null  and supPartyName != ''"> and sup_party_name like concat('%', #{supPartyName}, '%')</if>
                        <if test="orgEmployees != null "> and org_employees = #{orgEmployees}</if>
                        <if test="activityFrequency != null  and activityFrequency != ''"> and activity_frequency = #{activityFrequency}</if>
        </where>
    </select>

    <select id="selectTbSituationOrgById" parameterType="Long" resultMap="TbSituationOrgResult">
            <include refid="selectTbSituationOrgVo"/>
            where id = #{id}
    </select>

    <select id="checkTbSituationOrgUnique" parameterType="TbSituationOrg" resultMap="TbSituationOrgResult">
        select id from tb_situation_org where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbSituationOrg" parameterType="TbSituationOrg" useGeneratedKeys="true" keyProperty="id">
        insert into tb_situation_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">dept_id,</if>
                    <if test="county != null">county,</if>
                    <if test="country != null">country,</if>
                    <if test="town != null">town,</if>
                    <if test="name != null">name,</if>
                    <if test="type != null">type,</if>
                    <if test="category != null">category,</if>
                    <if test="managerName != null">manager_name,</if>
                    <if test="memberUnitCount != null">member_unit_count,</if>
                    <if test="memberIndividualCount != null">member_individual_count,</if>
                    <if test="hasParty != null">has_party,</if>
                    <if test="partyName != null">party_name,</if>
                    <if test="partyType != null">party_type,</if>
                    <if test="partyPeoples != null">party_peoples,</if>
                    <if test="instructorName != null">instructor_name,</if>
                    <if test="supPartyName != null">sup_party_name,</if>
                    <if test="orgEmployees != null">org_employees,</if>
                    <if test="activityFrequency != null">activity_frequency,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">#{deptId},</if>
                    <if test="county != null">#{county},</if>
                    <if test="country != null">#{country},</if>
                    <if test="town != null">#{town},</if>
                    <if test="name != null">#{name},</if>
                    <if test="type != null">#{type},</if>
                    <if test="category != null">#{category},</if>
                    <if test="managerName != null">#{managerName},</if>
                    <if test="memberUnitCount != null">#{memberUnitCount},</if>
                    <if test="memberIndividualCount != null">#{memberIndividualCount},</if>
                    <if test="hasParty != null">#{hasParty},</if>
                    <if test="partyName != null">#{partyName},</if>
                    <if test="partyType != null">#{partyType},</if>
                    <if test="partyPeoples != null">#{partyPeoples},</if>
                    <if test="instructorName != null">#{instructorName},</if>
                    <if test="supPartyName != null">#{supPartyName},</if>
                    <if test="orgEmployees != null">#{orgEmployees},</if>
                    <if test="activityFrequency != null">#{activityFrequency},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTbSituationOrg" parameterType="TbSituationOrg">
        update tb_situation_org
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="category != null">category = #{category},</if>
                    <if test="managerName != null">manager_name = #{managerName},</if>
                    <if test="memberUnitCount != null">member_unit_count = #{memberUnitCount},</if>
                    <if test="memberIndividualCount != null">member_individual_count = #{memberIndividualCount},</if>
                    <if test="hasParty != null">has_party = #{hasParty},</if>
                    <if test="partyName != null">party_name = #{partyName},</if>
                    <if test="partyType != null">party_type = #{partyType},</if>
                    <if test="partyPeoples != null">party_peoples = #{partyPeoples},</if>
                    <if test="instructorName != null">instructor_name = #{instructorName},</if>
                    <if test="supPartyName != null">sup_party_name = #{supPartyName},</if>
                    <if test="orgEmployees != null">org_employees = #{orgEmployees},</if>
                    <if test="activityFrequency != null">activity_frequency = #{activityFrequency},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbSituationOrgById" parameterType="Long">
        delete from tb_situation_org where id = #{id}
    </delete>

    <delete id="deleteTbSituationOrgByIds" parameterType="String">
        delete from tb_situation_org where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="situationOrgGroupPartyType" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            party_type lable,
            count(*) count
        from tb_situation_org
        group by party_type
    </select>

</mapper>