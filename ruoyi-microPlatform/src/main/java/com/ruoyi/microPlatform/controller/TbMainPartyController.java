package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbMainParty;
import com.ruoyi.microPlatform.service.ITbMainPartyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 矛盾纠纷当事人信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@RestController
@RequestMapping("/issue/mainParty")
public class TbMainPartyController extends BaseController
{
    @Autowired
    private ITbMainPartyService tbMainPartyService;

    /**
     * 查询矛盾纠纷当事人信息列表
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbMainParty tbMainParty)
    {
        startPage();
        List<TbMainParty> list = tbMainPartyService.selectTbMainPartyList(tbMainParty);
        return getDataTable(list);
    }

    /**
     * 导出矛盾纠纷当事人信息列表
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:export')")
    @Log(title = "矛盾纠纷当事人信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbMainParty tbMainParty)
    {
        List<TbMainParty> list = tbMainPartyService.selectTbMainPartyList(tbMainParty);
        ExcelUtil<TbMainParty> util = new ExcelUtil<TbMainParty>(TbMainParty.class);
        util.exportExcel(response, list, "矛盾纠纷当事人信息数据");
    }

    @Log(title = "矛盾纠纷当事人信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('issue:mainParty:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbMainParty> util = new ExcelUtil<TbMainParty>(TbMainParty.class);
        List<TbMainParty> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbMainPartyService.importTbMainParty(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbMainParty> util = new ExcelUtil<TbMainParty>(TbMainParty.class);
        util.exportTemplateExcel(response, "矛盾纠纷当事人信息数据");
    }

    /**
     * 获取矛盾纠纷当事人信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbMainPartyService.selectTbMainPartyById(id));
    }

    /**
     * 新增矛盾纠纷当事人信息
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:add')")
    @Log(title = "矛盾纠纷当事人信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbMainParty tbMainParty)
    {
        tbMainParty.setCreateBy(getUsername());
        if (tbMainPartyService.checkTbMainParty(tbMainParty)){
            return error("数据已存在！");
        }
        return toAjax(tbMainPartyService.insertTbMainParty(tbMainParty));
    }

    /**
     * 修改矛盾纠纷当事人信息
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:edit')")
    @Log(title = "矛盾纠纷当事人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbMainParty tbMainParty)
    {
        tbMainParty.setUpdateBy(getUsername());
        if (tbMainPartyService.checkTbMainParty(tbMainParty)){
            return error("数据已存在！");
        }
        return toAjax(tbMainPartyService.updateTbMainParty(tbMainParty));
    }

    /**
     * 删除矛盾纠纷当事人信息
     */
    @PreAuthorize("@ss.hasPermi('issue:mainParty:remove')")
    @Log(title = "矛盾纠纷当事人信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbMainPartyService.deleteTbMainPartyByIds(ids));
    }
}
