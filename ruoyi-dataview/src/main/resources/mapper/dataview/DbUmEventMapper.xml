<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbUmEventMapper">

    <resultMap type="DbUmEvent" id="DbUmEventResult">
            <result property="id"    column="id"    />
            <result property="procInstId"    column="proc_inst_id"    />
            <result property="regTime"    column="reg_time"    />
            <result property="closeTime"    column="close_time"    />
            <result property="acceptId"    column="accept_id"    />
            <result property="caseCode"    column="case_code"    />
            <result property="caseSource"    column="case_source"    />
            <result property="caseClassId"    column="case_class_id"    />
            <result property="caseClassName"    column="case_class_name"    />
            <result property="standardId"    column="standard_id"    />
            <result property="regCaseStandard"    column="reg_case_standard"    />
            <result property="closeCaseStandard"    column="close_case_standard"    />
            <result property="processLimit"    column="process_limit"    />
            <result property="limitType"    column="limit_type"    />
            <result property="verifyLimit"    column="verify_limit"    />
            <result property="inspectLimit"    column="inspect_limit"    />
            <result property="caseTitle"    column="case_title"    />
            <result property="position"    column="position"    />
            <result property="questionDesc"    column="question_desc"    />
            <result property="areaCode"    column="area_code"    />
            <result property="areaName"    column="area_name"    />
            <result property="districtId"    column="district_id"    />
            <result property="streetId"    column="street_id"    />
            <result property="communityId"    column="community_id"    />
            <result property="manageGridId"    column="manage_grid_id"    />
            <result property="workGridId"    column="work_grid_id"    />
            <result property="gridId"    column="grid_id"    />
            <result property="geoX"    column="geo_x"    />
            <result property="geoY"    column="geo_y"    />
            <result property="reporter"    column="reporter"    />
            <result property="isReceipt"    column="is_receipt"    />
            <result property="replayWay"    column="replay_way"    />
            <result property="replyWay"    column="reply_way"    />
            <result property="telNum"    column="tel_num"    />
            <result property="reportTime"    column="report_time"    />
            <result property="caseStatus"    column="case_status"    />
            <result property="isAppraise"    column="is_appraise"    />
            <result property="isCaseQuality"    column="is_case_quality"    />
            <result property="remark"    column="remark"    />
            <result property="keyWord"    column="key_word"    />
            <result property="caseLevel"    column="case_level"    />
            <result property="cjWeight"    column="cj_weight"    />
            <result property="czWeight"    column="cz_weight"    />
            <result property="lastProdeptId"    column="last_prodept_id"    />
            <result property="lastProdeptOpinion"    column="last_prodept_opinion"    />
            <result property="prodeptStatus"    column="prodept_status"    />
            <result property="isReaccept"    column="is_reaccept"    />
            <result property="endTime"    column="end_time"    />
            <result property="caseFrom"    column="case_from"    />
            <result property="reciveObject"    column="recive_object"    />
            <result property="mapshowType"    column="mapshow_type"    />
            <result property="procDefId"    column="proc_def_id"    />
            <result property="casePqCount"    column="case_pq_count"    />
            <result property="orderNum"    column="order_num"    />
            <result property="dbStatus"    column="db_status"    />
            <result property="createId"    column="create_id"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateId"    column="update_id"    />
            <result property="updateTime"    column="update_time"    />
            <result property="isDelayed"    column="is_delayed"    />
            <result property="isPostpone"    column="is_postpone"    />
            <result property="isAttach"    column="is_attach"    />
            <result property="isCheck"    column="is_check"    />
            <result property="isConfirm"    column="is_confirm"    />
            <result property="lawId"    column="law_id"    />
            <result property="cameraIndexCode"    column="camera_index_code"    />
            <result property="extId"    column="ext_id"    />
            <result property="extCode"    column="ext_code"    />
            <result property="cityZhExtId"    column="city_zh_ext_id"    />
            <result property="cityZhExtTitle"    column="city_zh_ext_title"    />
            <result property="cityZhExtCflowId"    column="city_zh_ext_cflow_id"    />
            <result property="recid"    column="recid"    />
            <result property="isDifficult"    column="is_difficult"    />
            <result property="difficultSign"    column="difficult_sign"    />
            <result property="whId"    column="wh_id"    />
            <result property="whState"    column="wh_state"    />
            <result property="classIndustryId"    column="class_industry_id"    />
            <result property="classIndustryName"    column="class_industry_name"    />
            <result property="classTypeId"    column="class_type_id"    />
            <result property="classTypeName"    column="class_type_name"    />
            <result property="classBigId"    column="class_big_id"    />
            <result property="classBigName"    column="class_big_name"    />
            <result property="classSubId"    column="class_sub_id"    />
            <result property="classSubName"    column="class_sub_name"    />
            <result property="curActName"    column="cur_act_name"    />
            <result property="curActId"    column="cur_act_id"    />
            <result property="receiveTypeId"    column="receive_type_id"    />
            <result property="receiveId"    column="receive_id"    />
            <result property="shouldFinishedTime"    column="should_finished_time"    />
            <result property="fastExpirationTime"    column="fast_expiration_time"    />
            <result property="lastProdeptName"    column="last_prodept_name"    />
            <result property="delayFlag"    column="delay_flag"    />
            <result property="delayApplyFlag"    column="delay_apply_flag"    />
            <result property="rollbackFlag"    column="rollback_flag"    />
            <result property="rollbackApplyFlag"    column="rollback_apply_flag"    />
            <result property="superviseFlag"    column="supervise_flag"    />
            <result property="urgeFlag"    column="urge_flag"    />
            <result property="revokeFlag"    column="revoke_flag"    />
            <result property="postponeFlag"    column="postpone_flag"    />
            <result property="onaccountFlag"    column="onaccount_flag"    />
            <result property="puzzleFlag"    column="puzzle_flag"    />
            <result property="puzzleActId"    column="puzzle_act_id"    />
            <result property="curArrivalTime"    column="cur_arrival_time"    />
            <result property="curAssigneeId"    column="cur_assignee_id"    />
            <result property="partitionId"    column="partition_id"    />
            <result property="curActDefId"    column="cur_act_def_id"    />
            <result property="attachThumbnail"    column="attach_thumbnail"    />
            <result property="receiveName"    column="receive_name"    />
            <result property="fromActId"    column="from_act_id"    />
            <result property="fromActDefId"    column="from_act_def_id"    />
            <result property="fromActJumpType"    column="from_act_jump_type"    />
            <result property="mapAttachPath"    column="map_attach_path"    />
            <result property="lastProdeptCompleteTime"    column="last_prodept_complete_time"    />
            <result property="lastProdeptShouldFinishedTime"    column="last_prodept_should_finished_time"    />
            <result property="firstProdeptId"    column="first_prodept_id"    />
            <result property="firstProdeptName"    column="first_prodept_name"    />
            <result property="invalidCause"    column="invalid_cause"    />
            <result property="dispatchObject"    column="dispatch_object"    />
            <result property="gdX"    column="gd_x"    />
            <result property="gdY"    column="gd_y"    />
    </resultMap>

        <resultMap id="DbUmEventDbBpmNodeJumpResult" type="DbUmEvent" extends="DbUmEventResult">
            <collection property="dbBpmNodeJumpList" notNullColumn="sub_id" javaType="java.util.List" resultMap="DbBpmNodeJumpResult" />
        </resultMap>

        <resultMap type="DbBpmNodeJump" id="DbBpmNodeJumpResult">
                <result property="id"    column="sub_id"    />
                <result property="procDefId"    column="sub_proc_def_id"    />
                <result property="procInstId"    column="sub_proc_inst_id"    />
                <result property="evtId"    column="sub_evt_id"    />
                <result property="nodeName"    column="sub_node_name"    />
                <result property="actDefId"    column="sub_act_def_id"    />
                <result property="actInstId"    column="sub_act_inst_id"    />
                <result property="fromActInstId"    column="sub_from_act_inst_id"    />
                <result property="arrivalTime"    column="sub_arrival_time"    />
                <result property="completeTime"    column="sub_complete_time"    />
                <result property="extUsedMin"    column="sub_ext_used_min"    />
                <result property="extExcludeMin"    column="sub_ext_exclude_min"    />
                <result property="extRealUsedMin"    column="sub_ext_real_used_min"    />
                <result property="extCumulativeMin"    column="sub_ext_cumulative_min"    />
                <result property="receiveTypeId"    column="sub_receive_type_id"    />
                <result property="ownerId"    column="sub_owner_id"    />
                <result property="ownerName"    column="sub_owner_name"    />
                <result property="handlerId"    column="sub_handler_id"    />
                <result property="handlerName"    column="sub_handler_name"    />
                <result property="deptId"    column="sub_dept_id"    />
                <result property="deptName"    column="sub_dept_name"    />
                <result property="extLimit"    column="sub_ext_limit"    />
                <result property="fastExpirationTime"    column="sub_fast_expiration_time"    />
                <result property="shouldFinishedTime"    column="sub_should_finished_time"    />
                <result property="checkStatus"    column="sub_check_status"    />
                <result property="jumpType"    column="sub_jump_type"    />
                <result property="remark"    column="sub_remark"    />
                <result property="createId"    column="sub_create_id"    />
                <result property="createTime"    column="sub_create_time"    />
                <result property="updateId"    column="sub_update_id"    />
                <result property="updateTime"    column="sub_update_time"    />
                <result property="isTimeout"    column="sub_is_timeout"    />
                <result property="updateContent"    column="sub_update_content"    />
                <result property="isOnAccount"    column="sub_is_on_account"    />
                <result property="dockingStatus"    column="sub_docking_status"    />
                <result property="partitionId"    column="sub_partition_id"    />
        </resultMap>

    <sql id="selectDbUmEventVo">
        select id, proc_inst_id, reg_time, close_time, accept_id, case_code, case_source, case_class_id, case_class_name, standard_id, reg_case_standard, close_case_standard, process_limit, limit_type, verify_limit, inspect_limit, case_title, position, question_desc, area_code, area_name, district_id, street_id, community_id, manage_grid_id, work_grid_id, grid_id, geo_x, geo_y, reporter, is_receipt, replay_way, reply_way, tel_num, report_time, case_status, is_appraise, is_case_quality, remark, key_word, case_level, cj_weight, cz_weight, last_prodept_id, last_prodept_opinion, prodept_status, is_reaccept, end_time, case_from, recive_object, mapshow_type, proc_def_id, case_pq_count, order_num, db_status, create_id, create_time, update_id, update_time, is_delayed, is_postpone, is_attach, is_check, is_confirm, law_id, camera_index_code, ext_id, ext_code, city_zh_ext_id, city_zh_ext_title, city_zh_ext_cflow_id, recid, is_difficult, difficult_sign, wh_id, wh_state, class_industry_id, class_industry_name, class_type_id, class_type_name, class_big_id, class_big_name, class_sub_id, class_sub_name, cur_act_name, cur_act_id, receive_type_id, receive_id, should_finished_time, fast_expiration_time, last_prodept_name, delay_flag, delay_apply_flag, rollback_flag, rollback_apply_flag, supervise_flag, urge_flag, revoke_flag, postpone_flag, onaccount_flag, puzzle_flag, puzzle_act_id, cur_arrival_time, cur_assignee_id, partition_id, cur_act_def_id, attach_thumbnail, receive_name, from_act_id, from_act_def_id, from_act_jump_type, map_attach_path, last_prodept_complete_time, last_prodept_should_finished_time, first_prodept_id, first_prodept_name, invalid_cause, dispatch_object, gd_x, gd_y from db_um_event
    </sql>

    <select id="selectDbUmEventList" parameterType="DbUmEvent" resultMap="DbUmEventResult">
        <include refid="selectDbUmEventVo"/>
        <where>
                        <if test="procInstId != null "> and proc_inst_id = #{procInstId}</if>
                        <if test="regTime != null "> and reg_time = #{regTime}</if>
                        <if test="closeTime != null "> and close_time = #{closeTime}</if>
                        <if test="acceptId != null "> and accept_id = #{acceptId}</if>
                        <if test="caseCode != null  and caseCode != ''"> and case_code = #{caseCode}</if>
                        <if test="caseSource != null  and caseSource != ''"> and case_source = #{caseSource}</if>
                        <if test="caseClassId != null  and caseClassId != ''"> and case_class_id = #{caseClassId}</if>
                        <if test="caseClassName != null  and caseClassName != ''"> and case_class_name like concat('%', #{caseClassName}, '%')</if>
                        <if test="standardId != null "> and standard_id = #{standardId}</if>
                        <if test="regCaseStandard != null  and regCaseStandard != ''"> and reg_case_standard = #{regCaseStandard}</if>
                        <if test="closeCaseStandard != null  and closeCaseStandard != ''"> and close_case_standard = #{closeCaseStandard}</if>
                        <if test="processLimit != null "> and process_limit = #{processLimit}</if>
                        <if test="limitType != null  and limitType != ''"> and limit_type = #{limitType}</if>
                        <if test="verifyLimit != null "> and verify_limit = #{verifyLimit}</if>
                        <if test="inspectLimit != null "> and inspect_limit = #{inspectLimit}</if>
                        <if test="caseTitle != null  and caseTitle != ''"> and case_title = #{caseTitle}</if>
                        <if test="position != null  and position != ''"> and position = #{position}</if>
                        <if test="questionDesc != null  and questionDesc != ''"> and question_desc = #{questionDesc}</if>
                        <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
                        <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
                        <if test="districtId != null "> and district_id = #{districtId}</if>
                        <if test="streetId != null "> and street_id = #{streetId}</if>
                        <if test="communityId != null "> and community_id = #{communityId}</if>
                        <if test="manageGridId != null  and manageGridId != ''"> and manage_grid_id = #{manageGridId}</if>
                        <if test="workGridId != null  and workGridId != ''"> and work_grid_id = #{workGridId}</if>
                        <if test="gridId != null  and gridId != ''"> and grid_id = #{gridId}</if>
                        <if test="geoX != null "> and geo_x = #{geoX}</if>
                        <if test="geoY != null "> and geo_y = #{geoY}</if>
                        <if test="reporter != null  and reporter != ''"> and reporter = #{reporter}</if>
                        <if test="isReceipt != null  and isReceipt != ''"> and is_receipt = #{isReceipt}</if>
                        <if test="replayWay != null  and replayWay != ''"> and replay_way = #{replayWay}</if>
                        <if test="replyWay != null  and replyWay != ''"> and reply_way = #{replyWay}</if>
                        <if test="telNum != null  and telNum != ''"> and tel_num = #{telNum}</if>
                        <if test="reportTime != null "> and report_time = #{reportTime}</if>
                        <if test="caseStatus != null  and caseStatus != ''"> and case_status = #{caseStatus}</if>
                        <if test="isAppraise != null  and isAppraise != ''"> and is_appraise = #{isAppraise}</if>
                        <if test="isCaseQuality != null  and isCaseQuality != ''"> and is_case_quality = #{isCaseQuality}</if>
                        <if test="keyWord != null  and keyWord != ''"> and key_word = #{keyWord}</if>
                        <if test="caseLevel != null  and caseLevel != ''"> and case_level = #{caseLevel}</if>
                        <if test="cjWeight != null "> and cj_weight = #{cjWeight}</if>
                        <if test="czWeight != null "> and cz_weight = #{czWeight}</if>
                        <if test="lastProdeptId != null "> and last_prodept_id = #{lastProdeptId}</if>
                        <if test="lastProdeptOpinion != null  and lastProdeptOpinion != ''"> and last_prodept_opinion = #{lastProdeptOpinion}</if>
                        <if test="prodeptStatus != null  and prodeptStatus != ''"> and prodept_status = #{prodeptStatus}</if>
                        <if test="isReaccept != null  and isReaccept != ''"> and is_reaccept = #{isReaccept}</if>
                        <if test="endTime != null "> and end_time = #{endTime}</if>
                        <if test="caseFrom != null  and caseFrom != ''"> and case_from = #{caseFrom}</if>
                        <if test="reciveObject != null  and reciveObject != ''"> and recive_object = #{reciveObject}</if>
                        <if test="mapshowType != null  and mapshowType != ''"> and mapshow_type = #{mapshowType}</if>
                        <if test="procDefId != null  and procDefId != ''"> and proc_def_id = #{procDefId}</if>
                        <if test="casePqCount != null "> and case_pq_count = #{casePqCount}</if>
                        <if test="orderNum != null "> and order_num = #{orderNum}</if>
                        <if test="dbStatus != null  and dbStatus != ''"> and db_status = #{dbStatus}</if>
                        <if test="createId != null "> and create_id = #{createId}</if>
                        <if test="updateId != null "> and update_id = #{updateId}</if>
                        <if test="isDelayed != null  and isDelayed != ''"> and is_delayed = #{isDelayed}</if>
                        <if test="isPostpone != null  and isPostpone != ''"> and is_postpone = #{isPostpone}</if>
                        <if test="isAttach != null  and isAttach != ''"> and is_attach = #{isAttach}</if>
                        <if test="isCheck != null  and isCheck != ''"> and is_check = #{isCheck}</if>
                        <if test="isConfirm != null  and isConfirm != ''"> and is_confirm = #{isConfirm}</if>
                        <if test="lawId != null  and lawId != ''"> and law_id = #{lawId}</if>
                        <if test="cameraIndexCode != null  and cameraIndexCode != ''"> and camera_index_code = #{cameraIndexCode}</if>
                        <if test="extId != null  and extId != ''"> and ext_id = #{extId}</if>
                        <if test="extCode != null  and extCode != ''"> and ext_code = #{extCode}</if>
                        <if test="cityZhExtId != null  and cityZhExtId != ''"> and city_zh_ext_id = #{cityZhExtId}</if>
                        <if test="cityZhExtTitle != null  and cityZhExtTitle != ''"> and city_zh_ext_title = #{cityZhExtTitle}</if>
                        <if test="cityZhExtCflowId != null  and cityZhExtCflowId != ''"> and city_zh_ext_cflow_id = #{cityZhExtCflowId}</if>
                        <if test="recid != null "> and recid = #{recid}</if>
                        <if test="isDifficult != null  and isDifficult != ''"> and is_difficult = #{isDifficult}</if>
                        <if test="difficultSign != null  and difficultSign != ''"> and difficult_sign = #{difficultSign}</if>
                        <if test="whId != null  and whId != ''"> and wh_id = #{whId}</if>
                        <if test="whState != null  and whState != ''"> and wh_state = #{whState}</if>
                        <if test="classIndustryId != null "> and class_industry_id = #{classIndustryId}</if>
                        <if test="classIndustryName != null  and classIndustryName != ''"> and class_industry_name like concat('%', #{classIndustryName}, '%')</if>
                        <if test="classTypeId != null "> and class_type_id = #{classTypeId}</if>
                        <if test="classTypeName != null  and classTypeName != ''"> and class_type_name like concat('%', #{classTypeName}, '%')</if>
                        <if test="classBigId != null "> and class_big_id = #{classBigId}</if>
                        <if test="classBigName != null  and classBigName != ''"> and class_big_name like concat('%', #{classBigName}, '%')</if>
                        <if test="classSubId != null "> and class_sub_id = #{classSubId}</if>
                        <if test="classSubName != null  and classSubName != ''"> and class_sub_name like concat('%', #{classSubName}, '%')</if>
                        <if test="curActName != null  and curActName != ''"> and cur_act_name like concat('%', #{curActName}, '%')</if>
                        <if test="curActId != null "> and cur_act_id = #{curActId}</if>
                        <if test="receiveTypeId != null "> and receive_type_id = #{receiveTypeId}</if>
                        <if test="receiveId != null "> and receive_id = #{receiveId}</if>
                        <if test="shouldFinishedTime != null "> and should_finished_time = #{shouldFinishedTime}</if>
                        <if test="fastExpirationTime != null "> and fast_expiration_time = #{fastExpirationTime}</if>
                        <if test="lastProdeptName != null  and lastProdeptName != ''"> and last_prodept_name like concat('%', #{lastProdeptName}, '%')</if>
                        <if test="delayFlag != null "> and delay_flag = #{delayFlag}</if>
                        <if test="delayApplyFlag != null "> and delay_apply_flag = #{delayApplyFlag}</if>
                        <if test="rollbackFlag != null "> and rollback_flag = #{rollbackFlag}</if>
                        <if test="rollbackApplyFlag != null "> and rollback_apply_flag = #{rollbackApplyFlag}</if>
                        <if test="superviseFlag != null "> and supervise_flag = #{superviseFlag}</if>
                        <if test="urgeFlag != null "> and urge_flag = #{urgeFlag}</if>
                        <if test="revokeFlag != null "> and revoke_flag = #{revokeFlag}</if>
                        <if test="postponeFlag != null "> and postpone_flag = #{postponeFlag}</if>
                        <if test="onaccountFlag != null "> and onaccount_flag = #{onaccountFlag}</if>
                        <if test="puzzleFlag != null "> and puzzle_flag = #{puzzleFlag}</if>
                        <if test="puzzleActId != null "> and puzzle_act_id = #{puzzleActId}</if>
                        <if test="curArrivalTime != null "> and cur_arrival_time = #{curArrivalTime}</if>
                        <if test="curAssigneeId != null "> and cur_assignee_id = #{curAssigneeId}</if>
                        <if test="curActDefId != null  and curActDefId != ''"> and cur_act_def_id = #{curActDefId}</if>
                        <if test="attachThumbnail != null  and attachThumbnail != ''"> and attach_thumbnail = #{attachThumbnail}</if>
                        <if test="receiveName != null  and receiveName != ''"> and receive_name like concat('%', #{receiveName}, '%')</if>
                        <if test="fromActId != null "> and from_act_id = #{fromActId}</if>
                        <if test="fromActDefId != null  and fromActDefId != ''"> and from_act_def_id = #{fromActDefId}</if>
                        <if test="fromActJumpType != null  and fromActJumpType != ''"> and from_act_jump_type = #{fromActJumpType}</if>
                        <if test="mapAttachPath != null  and mapAttachPath != ''"> and map_attach_path = #{mapAttachPath}</if>
                        <if test="lastProdeptCompleteTime != null "> and last_prodept_complete_time = #{lastProdeptCompleteTime}</if>
                        <if test="lastProdeptShouldFinishedTime != null "> and last_prodept_should_finished_time = #{lastProdeptShouldFinishedTime}</if>
                        <if test="firstProdeptId != null "> and first_prodept_id = #{firstProdeptId}</if>
                        <if test="firstProdeptName != null  and firstProdeptName != ''"> and first_prodept_name like concat('%', #{firstProdeptName}, '%')</if>
                        <if test="invalidCause != null  and invalidCause != ''"> and invalid_cause = #{invalidCause}</if>
                        <if test="dispatchObject != null  and dispatchObject != ''"> and dispatch_object = #{dispatchObject}</if>
                        <if test="gdX != null "> and gd_x = #{gdX}</if>
                        <if test="gdY != null "> and gd_y = #{gdY}</if>
        </where>
    </select>

    <select id="selectDbUmEventById" parameterType="Long" resultMap="DbUmEventDbBpmNodeJumpResult">
            select a.id, a.proc_inst_id, a.reg_time, a.close_time, a.accept_id, a.case_code, a.case_source, a.case_class_id, a.case_class_name, a.standard_id, a.reg_case_standard, a.close_case_standard, a.process_limit, a.limit_type, a.verify_limit, a.inspect_limit, a.case_title, a.position, a.question_desc, a.area_code, a.area_name, a.district_id, a.street_id, a.community_id, a.manage_grid_id, a.work_grid_id, a.grid_id, a.geo_x, a.geo_y, a.reporter, a.is_receipt, a.replay_way, a.reply_way, a.tel_num, a.report_time, a.case_status, a.is_appraise, a.is_case_quality, a.remark, a.key_word, a.case_level, a.cj_weight, a.cz_weight, a.last_prodept_id, a.last_prodept_opinion, a.prodept_status, a.is_reaccept, a.end_time, a.case_from, a.recive_object, a.mapshow_type, a.proc_def_id, a.case_pq_count, a.order_num, a.db_status, a.create_id, a.create_time, a.update_id, a.update_time, a.is_delayed, a.is_postpone, a.is_attach, a.is_check, a.is_confirm, a.law_id, a.camera_index_code, a.ext_id, a.ext_code, a.city_zh_ext_id, a.city_zh_ext_title, a.city_zh_ext_cflow_id, a.recid, a.is_difficult, a.difficult_sign, a.wh_id, a.wh_state, a.class_industry_id, a.class_industry_name, a.class_type_id, a.class_type_name, a.class_big_id, a.class_big_name, a.class_sub_id, a.class_sub_name, a.cur_act_name, a.cur_act_id, a.receive_type_id, a.receive_id, a.should_finished_time, a.fast_expiration_time, a.last_prodept_name, a.delay_flag, a.delay_apply_flag, a.rollback_flag, a.rollback_apply_flag, a.supervise_flag, a.urge_flag, a.revoke_flag, a.postpone_flag, a.onaccount_flag, a.puzzle_flag, a.puzzle_act_id, a.cur_arrival_time, a.cur_assignee_id, a.partition_id, a.cur_act_def_id, a.attach_thumbnail, a.receive_name, a.from_act_id, a.from_act_def_id, a.from_act_jump_type, a.map_attach_path, a.last_prodept_complete_time, a.last_prodept_should_finished_time, a.first_prodept_id, a.first_prodept_name, a.invalid_cause, a.dispatch_object, a.gd_x, a.gd_y,
 b.id as sub_id, b.proc_def_id as sub_proc_def_id, b.proc_inst_id as sub_proc_inst_id, b.evt_id as sub_evt_id, b.node_name as sub_node_name, b.act_def_id as sub_act_def_id, b.act_inst_id as sub_act_inst_id, b.from_act_inst_id as sub_from_act_inst_id, b.arrival_time as sub_arrival_time, b.complete_time as sub_complete_time, b.ext_used_min as sub_ext_used_min, b.ext_exclude_min as sub_ext_exclude_min, b.ext_real_used_min as sub_ext_real_used_min, b.ext_cumulative_min as sub_ext_cumulative_min, b.receive_type_id as sub_receive_type_id, b.owner_id as sub_owner_id, b.owner_name as sub_owner_name, b.handler_id as sub_handler_id, b.handler_name as sub_handler_name, b.dept_id as sub_dept_id, b.dept_name as sub_dept_name, b.ext_limit as sub_ext_limit, b.fast_expiration_time as sub_fast_expiration_time, b.should_finished_time as sub_should_finished_time, b.check_status as sub_check_status, b.jump_type as sub_jump_type, b.remark as sub_remark, b.create_id as sub_create_id, b.create_time as sub_create_time, b.update_id as sub_update_id, b.update_time as sub_update_time, b.is_timeout as sub_is_timeout, b.update_content as sub_update_content, b.is_on_account as sub_is_on_account, b.docking_status as sub_docking_status, b.partition_id as sub_partition_id
            from db_um_event a
            left join db_bpm_node_jump b on b.proc_inst_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkDbUmEventUnique" parameterType="DbUmEvent" resultMap="DbUmEventResult">
        select id from db_um_event where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="dbUmEventBigCount" resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        select class_big_name lable, count(*) count
        from db_um_event
        where class_type_name = #{classTypeName}
        group by class_big_name
    </select>

    <insert id="insertDbUmEvent" parameterType="DbUmEvent">
        insert into db_um_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="procInstId != null">proc_inst_id,</if>
                    <if test="regTime != null">reg_time,</if>
                    <if test="closeTime != null">close_time,</if>
                    <if test="acceptId != null">accept_id,</if>
                    <if test="caseCode != null">case_code,</if>
                    <if test="caseSource != null">case_source,</if>
                    <if test="caseClassId != null">case_class_id,</if>
                    <if test="caseClassName != null">case_class_name,</if>
                    <if test="standardId != null">standard_id,</if>
                    <if test="regCaseStandard != null">reg_case_standard,</if>
                    <if test="closeCaseStandard != null">close_case_standard,</if>
                    <if test="processLimit != null">process_limit,</if>
                    <if test="limitType != null">limit_type,</if>
                    <if test="verifyLimit != null">verify_limit,</if>
                    <if test="inspectLimit != null">inspect_limit,</if>
                    <if test="caseTitle != null">case_title,</if>
                    <if test="position != null">position,</if>
                    <if test="questionDesc != null">question_desc,</if>
                    <if test="areaCode != null">area_code,</if>
                    <if test="areaName != null">area_name,</if>
                    <if test="districtId != null">district_id,</if>
                    <if test="streetId != null">street_id,</if>
                    <if test="communityId != null">community_id,</if>
                    <if test="manageGridId != null">manage_grid_id,</if>
                    <if test="workGridId != null">work_grid_id,</if>
                    <if test="gridId != null">grid_id,</if>
                    <if test="geoX != null">geo_x,</if>
                    <if test="geoY != null">geo_y,</if>
                    <if test="reporter != null">reporter,</if>
                    <if test="isReceipt != null">is_receipt,</if>
                    <if test="replayWay != null">replay_way,</if>
                    <if test="replyWay != null">reply_way,</if>
                    <if test="telNum != null">tel_num,</if>
                    <if test="reportTime != null">report_time,</if>
                    <if test="caseStatus != null">case_status,</if>
                    <if test="isAppraise != null">is_appraise,</if>
                    <if test="isCaseQuality != null">is_case_quality,</if>
                    <if test="remark != null">remark,</if>
                    <if test="keyWord != null">key_word,</if>
                    <if test="caseLevel != null">case_level,</if>
                    <if test="cjWeight != null">cj_weight,</if>
                    <if test="czWeight != null">cz_weight,</if>
                    <if test="lastProdeptId != null">last_prodept_id,</if>
                    <if test="lastProdeptOpinion != null">last_prodept_opinion,</if>
                    <if test="prodeptStatus != null">prodept_status,</if>
                    <if test="isReaccept != null">is_reaccept,</if>
                    <if test="endTime != null">end_time,</if>
                    <if test="caseFrom != null">case_from,</if>
                    <if test="reciveObject != null">recive_object,</if>
                    <if test="mapshowType != null">mapshow_type,</if>
                    <if test="procDefId != null">proc_def_id,</if>
                    <if test="casePqCount != null">case_pq_count,</if>
                    <if test="orderNum != null">order_num,</if>
                    <if test="dbStatus != null">db_status,</if>
                    <if test="createId != null">create_id,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateId != null">update_id,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="isDelayed != null">is_delayed,</if>
                    <if test="isPostpone != null">is_postpone,</if>
                    <if test="isAttach != null">is_attach,</if>
                    <if test="isCheck != null">is_check,</if>
                    <if test="isConfirm != null">is_confirm,</if>
                    <if test="lawId != null">law_id,</if>
                    <if test="cameraIndexCode != null">camera_index_code,</if>
                    <if test="extId != null">ext_id,</if>
                    <if test="extCode != null">ext_code,</if>
                    <if test="cityZhExtId != null">city_zh_ext_id,</if>
                    <if test="cityZhExtTitle != null">city_zh_ext_title,</if>
                    <if test="cityZhExtCflowId != null">city_zh_ext_cflow_id,</if>
                    <if test="recid != null">recid,</if>
                    <if test="isDifficult != null">is_difficult,</if>
                    <if test="difficultSign != null">difficult_sign,</if>
                    <if test="whId != null">wh_id,</if>
                    <if test="whState != null">wh_state,</if>
                    <if test="classIndustryId != null">class_industry_id,</if>
                    <if test="classIndustryName != null">class_industry_name,</if>
                    <if test="classTypeId != null">class_type_id,</if>
                    <if test="classTypeName != null">class_type_name,</if>
                    <if test="classBigId != null">class_big_id,</if>
                    <if test="classBigName != null">class_big_name,</if>
                    <if test="classSubId != null">class_sub_id,</if>
                    <if test="classSubName != null">class_sub_name,</if>
                    <if test="curActName != null">cur_act_name,</if>
                    <if test="curActId != null">cur_act_id,</if>
                    <if test="receiveTypeId != null">receive_type_id,</if>
                    <if test="receiveId != null">receive_id,</if>
                    <if test="shouldFinishedTime != null">should_finished_time,</if>
                    <if test="fastExpirationTime != null">fast_expiration_time,</if>
                    <if test="lastProdeptName != null">last_prodept_name,</if>
                    <if test="delayFlag != null">delay_flag,</if>
                    <if test="delayApplyFlag != null">delay_apply_flag,</if>
                    <if test="rollbackFlag != null">rollback_flag,</if>
                    <if test="rollbackApplyFlag != null">rollback_apply_flag,</if>
                    <if test="superviseFlag != null">supervise_flag,</if>
                    <if test="urgeFlag != null">urge_flag,</if>
                    <if test="revokeFlag != null">revoke_flag,</if>
                    <if test="postponeFlag != null">postpone_flag,</if>
                    <if test="onaccountFlag != null">onaccount_flag,</if>
                    <if test="puzzleFlag != null">puzzle_flag,</if>
                    <if test="puzzleActId != null">puzzle_act_id,</if>
                    <if test="curArrivalTime != null">cur_arrival_time,</if>
                    <if test="curAssigneeId != null">cur_assignee_id,</if>
                    <if test="partitionId != null">partition_id,</if>
                    <if test="curActDefId != null">cur_act_def_id,</if>
                    <if test="attachThumbnail != null">attach_thumbnail,</if>
                    <if test="receiveName != null">receive_name,</if>
                    <if test="fromActId != null">from_act_id,</if>
                    <if test="fromActDefId != null">from_act_def_id,</if>
                    <if test="fromActJumpType != null">from_act_jump_type,</if>
                    <if test="mapAttachPath != null">map_attach_path,</if>
                    <if test="lastProdeptCompleteTime != null">last_prodept_complete_time,</if>
                    <if test="lastProdeptShouldFinishedTime != null">last_prodept_should_finished_time,</if>
                    <if test="firstProdeptId != null">first_prodept_id,</if>
                    <if test="firstProdeptName != null">first_prodept_name,</if>
                    <if test="invalidCause != null">invalid_cause,</if>
                    <if test="dispatchObject != null">dispatch_object,</if>
                    <if test="gdX != null">gd_x,</if>
                    <if test="gdY != null">gd_y,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="procInstId != null">#{procInstId},</if>
                    <if test="regTime != null">#{regTime},</if>
                    <if test="closeTime != null">#{closeTime},</if>
                    <if test="acceptId != null">#{acceptId},</if>
                    <if test="caseCode != null">#{caseCode},</if>
                    <if test="caseSource != null">#{caseSource},</if>
                    <if test="caseClassId != null">#{caseClassId},</if>
                    <if test="caseClassName != null">#{caseClassName},</if>
                    <if test="standardId != null">#{standardId},</if>
                    <if test="regCaseStandard != null">#{regCaseStandard},</if>
                    <if test="closeCaseStandard != null">#{closeCaseStandard},</if>
                    <if test="processLimit != null">#{processLimit},</if>
                    <if test="limitType != null">#{limitType},</if>
                    <if test="verifyLimit != null">#{verifyLimit},</if>
                    <if test="inspectLimit != null">#{inspectLimit},</if>
                    <if test="caseTitle != null">#{caseTitle},</if>
                    <if test="position != null">#{position},</if>
                    <if test="questionDesc != null">#{questionDesc},</if>
                    <if test="areaCode != null">#{areaCode},</if>
                    <if test="areaName != null">#{areaName},</if>
                    <if test="districtId != null">#{districtId},</if>
                    <if test="streetId != null">#{streetId},</if>
                    <if test="communityId != null">#{communityId},</if>
                    <if test="manageGridId != null">#{manageGridId},</if>
                    <if test="workGridId != null">#{workGridId},</if>
                    <if test="gridId != null">#{gridId},</if>
                    <if test="geoX != null">#{geoX},</if>
                    <if test="geoY != null">#{geoY},</if>
                    <if test="reporter != null">#{reporter},</if>
                    <if test="isReceipt != null">#{isReceipt},</if>
                    <if test="replayWay != null">#{replayWay},</if>
                    <if test="replyWay != null">#{replyWay},</if>
                    <if test="telNum != null">#{telNum},</if>
                    <if test="reportTime != null">#{reportTime},</if>
                    <if test="caseStatus != null">#{caseStatus},</if>
                    <if test="isAppraise != null">#{isAppraise},</if>
                    <if test="isCaseQuality != null">#{isCaseQuality},</if>
                    <if test="remark != null">#{remark},</if>
                    <if test="keyWord != null">#{keyWord},</if>
                    <if test="caseLevel != null">#{caseLevel},</if>
                    <if test="cjWeight != null">#{cjWeight},</if>
                    <if test="czWeight != null">#{czWeight},</if>
                    <if test="lastProdeptId != null">#{lastProdeptId},</if>
                    <if test="lastProdeptOpinion != null">#{lastProdeptOpinion},</if>
                    <if test="prodeptStatus != null">#{prodeptStatus},</if>
                    <if test="isReaccept != null">#{isReaccept},</if>
                    <if test="endTime != null">#{endTime},</if>
                    <if test="caseFrom != null">#{caseFrom},</if>
                    <if test="reciveObject != null">#{reciveObject},</if>
                    <if test="mapshowType != null">#{mapshowType},</if>
                    <if test="procDefId != null">#{procDefId},</if>
                    <if test="casePqCount != null">#{casePqCount},</if>
                    <if test="orderNum != null">#{orderNum},</if>
                    <if test="dbStatus != null">#{dbStatus},</if>
                    <if test="createId != null">#{createId},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateId != null">#{updateId},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="isDelayed != null">#{isDelayed},</if>
                    <if test="isPostpone != null">#{isPostpone},</if>
                    <if test="isAttach != null">#{isAttach},</if>
                    <if test="isCheck != null">#{isCheck},</if>
                    <if test="isConfirm != null">#{isConfirm},</if>
                    <if test="lawId != null">#{lawId},</if>
                    <if test="cameraIndexCode != null">#{cameraIndexCode},</if>
                    <if test="extId != null">#{extId},</if>
                    <if test="extCode != null">#{extCode},</if>
                    <if test="cityZhExtId != null">#{cityZhExtId},</if>
                    <if test="cityZhExtTitle != null">#{cityZhExtTitle},</if>
                    <if test="cityZhExtCflowId != null">#{cityZhExtCflowId},</if>
                    <if test="recid != null">#{recid},</if>
                    <if test="isDifficult != null">#{isDifficult},</if>
                    <if test="difficultSign != null">#{difficultSign},</if>
                    <if test="whId != null">#{whId},</if>
                    <if test="whState != null">#{whState},</if>
                    <if test="classIndustryId != null">#{classIndustryId},</if>
                    <if test="classIndustryName != null">#{classIndustryName},</if>
                    <if test="classTypeId != null">#{classTypeId},</if>
                    <if test="classTypeName != null">#{classTypeName},</if>
                    <if test="classBigId != null">#{classBigId},</if>
                    <if test="classBigName != null">#{classBigName},</if>
                    <if test="classSubId != null">#{classSubId},</if>
                    <if test="classSubName != null">#{classSubName},</if>
                    <if test="curActName != null">#{curActName},</if>
                    <if test="curActId != null">#{curActId},</if>
                    <if test="receiveTypeId != null">#{receiveTypeId},</if>
                    <if test="receiveId != null">#{receiveId},</if>
                    <if test="shouldFinishedTime != null">#{shouldFinishedTime},</if>
                    <if test="fastExpirationTime != null">#{fastExpirationTime},</if>
                    <if test="lastProdeptName != null">#{lastProdeptName},</if>
                    <if test="delayFlag != null">#{delayFlag},</if>
                    <if test="delayApplyFlag != null">#{delayApplyFlag},</if>
                    <if test="rollbackFlag != null">#{rollbackFlag},</if>
                    <if test="rollbackApplyFlag != null">#{rollbackApplyFlag},</if>
                    <if test="superviseFlag != null">#{superviseFlag},</if>
                    <if test="urgeFlag != null">#{urgeFlag},</if>
                    <if test="revokeFlag != null">#{revokeFlag},</if>
                    <if test="postponeFlag != null">#{postponeFlag},</if>
                    <if test="onaccountFlag != null">#{onaccountFlag},</if>
                    <if test="puzzleFlag != null">#{puzzleFlag},</if>
                    <if test="puzzleActId != null">#{puzzleActId},</if>
                    <if test="curArrivalTime != null">#{curArrivalTime},</if>
                    <if test="curAssigneeId != null">#{curAssigneeId},</if>
                    <if test="partitionId != null">#{partitionId},</if>
                    <if test="curActDefId != null">#{curActDefId},</if>
                    <if test="attachThumbnail != null">#{attachThumbnail},</if>
                    <if test="receiveName != null">#{receiveName},</if>
                    <if test="fromActId != null">#{fromActId},</if>
                    <if test="fromActDefId != null">#{fromActDefId},</if>
                    <if test="fromActJumpType != null">#{fromActJumpType},</if>
                    <if test="mapAttachPath != null">#{mapAttachPath},</if>
                    <if test="lastProdeptCompleteTime != null">#{lastProdeptCompleteTime},</if>
                    <if test="lastProdeptShouldFinishedTime != null">#{lastProdeptShouldFinishedTime},</if>
                    <if test="firstProdeptId != null">#{firstProdeptId},</if>
                    <if test="firstProdeptName != null">#{firstProdeptName},</if>
                    <if test="invalidCause != null">#{invalidCause},</if>
                    <if test="dispatchObject != null">#{dispatchObject},</if>
                    <if test="gdX != null">#{gdX},</if>
                    <if test="gdY != null">#{gdY},</if>
        </trim>
    </insert>

    <update id="updateDbUmEvent" parameterType="DbUmEvent">
        update db_um_event
        <trim prefix="SET" suffixOverrides=",">
                    <if test="procInstId != null">proc_inst_id = #{procInstId},</if>
                    <if test="regTime != null">reg_time = #{regTime},</if>
                    <if test="closeTime != null">close_time = #{closeTime},</if>
                    <if test="acceptId != null">accept_id = #{acceptId},</if>
                    <if test="caseCode != null">case_code = #{caseCode},</if>
                    <if test="caseSource != null">case_source = #{caseSource},</if>
                    <if test="caseClassId != null">case_class_id = #{caseClassId},</if>
                    <if test="caseClassName != null">case_class_name = #{caseClassName},</if>
                    <if test="standardId != null">standard_id = #{standardId},</if>
                    <if test="regCaseStandard != null">reg_case_standard = #{regCaseStandard},</if>
                    <if test="closeCaseStandard != null">close_case_standard = #{closeCaseStandard},</if>
                    <if test="processLimit != null">process_limit = #{processLimit},</if>
                    <if test="limitType != null">limit_type = #{limitType},</if>
                    <if test="verifyLimit != null">verify_limit = #{verifyLimit},</if>
                    <if test="inspectLimit != null">inspect_limit = #{inspectLimit},</if>
                    <if test="caseTitle != null">case_title = #{caseTitle},</if>
                    <if test="position != null">position = #{position},</if>
                    <if test="questionDesc != null">question_desc = #{questionDesc},</if>
                    <if test="areaCode != null">area_code = #{areaCode},</if>
                    <if test="areaName != null">area_name = #{areaName},</if>
                    <if test="districtId != null">district_id = #{districtId},</if>
                    <if test="streetId != null">street_id = #{streetId},</if>
                    <if test="communityId != null">community_id = #{communityId},</if>
                    <if test="manageGridId != null">manage_grid_id = #{manageGridId},</if>
                    <if test="workGridId != null">work_grid_id = #{workGridId},</if>
                    <if test="gridId != null">grid_id = #{gridId},</if>
                    <if test="geoX != null">geo_x = #{geoX},</if>
                    <if test="geoY != null">geo_y = #{geoY},</if>
                    <if test="reporter != null">reporter = #{reporter},</if>
                    <if test="isReceipt != null">is_receipt = #{isReceipt},</if>
                    <if test="replayWay != null">replay_way = #{replayWay},</if>
                    <if test="replyWay != null">reply_way = #{replyWay},</if>
                    <if test="telNum != null">tel_num = #{telNum},</if>
                    <if test="reportTime != null">report_time = #{reportTime},</if>
                    <if test="caseStatus != null">case_status = #{caseStatus},</if>
                    <if test="isAppraise != null">is_appraise = #{isAppraise},</if>
                    <if test="isCaseQuality != null">is_case_quality = #{isCaseQuality},</if>
                    <if test="remark != null">remark = #{remark},</if>
                    <if test="keyWord != null">key_word = #{keyWord},</if>
                    <if test="caseLevel != null">case_level = #{caseLevel},</if>
                    <if test="cjWeight != null">cj_weight = #{cjWeight},</if>
                    <if test="czWeight != null">cz_weight = #{czWeight},</if>
                    <if test="lastProdeptId != null">last_prodept_id = #{lastProdeptId},</if>
                    <if test="lastProdeptOpinion != null">last_prodept_opinion = #{lastProdeptOpinion},</if>
                    <if test="prodeptStatus != null">prodept_status = #{prodeptStatus},</if>
                    <if test="isReaccept != null">is_reaccept = #{isReaccept},</if>
                    <if test="endTime != null">end_time = #{endTime},</if>
                    <if test="caseFrom != null">case_from = #{caseFrom},</if>
                    <if test="reciveObject != null">recive_object = #{reciveObject},</if>
                    <if test="mapshowType != null">mapshow_type = #{mapshowType},</if>
                    <if test="procDefId != null">proc_def_id = #{procDefId},</if>
                    <if test="casePqCount != null">case_pq_count = #{casePqCount},</if>
                    <if test="orderNum != null">order_num = #{orderNum},</if>
                    <if test="dbStatus != null">db_status = #{dbStatus},</if>
                    <if test="createId != null">create_id = #{createId},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateId != null">update_id = #{updateId},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="isDelayed != null">is_delayed = #{isDelayed},</if>
                    <if test="isPostpone != null">is_postpone = #{isPostpone},</if>
                    <if test="isAttach != null">is_attach = #{isAttach},</if>
                    <if test="isCheck != null">is_check = #{isCheck},</if>
                    <if test="isConfirm != null">is_confirm = #{isConfirm},</if>
                    <if test="lawId != null">law_id = #{lawId},</if>
                    <if test="cameraIndexCode != null">camera_index_code = #{cameraIndexCode},</if>
                    <if test="extId != null">ext_id = #{extId},</if>
                    <if test="extCode != null">ext_code = #{extCode},</if>
                    <if test="cityZhExtId != null">city_zh_ext_id = #{cityZhExtId},</if>
                    <if test="cityZhExtTitle != null">city_zh_ext_title = #{cityZhExtTitle},</if>
                    <if test="cityZhExtCflowId != null">city_zh_ext_cflow_id = #{cityZhExtCflowId},</if>
                    <if test="recid != null">recid = #{recid},</if>
                    <if test="isDifficult != null">is_difficult = #{isDifficult},</if>
                    <if test="difficultSign != null">difficult_sign = #{difficultSign},</if>
                    <if test="whId != null">wh_id = #{whId},</if>
                    <if test="whState != null">wh_state = #{whState},</if>
                    <if test="classIndustryId != null">class_industry_id = #{classIndustryId},</if>
                    <if test="classIndustryName != null">class_industry_name = #{classIndustryName},</if>
                    <if test="classTypeId != null">class_type_id = #{classTypeId},</if>
                    <if test="classTypeName != null">class_type_name = #{classTypeName},</if>
                    <if test="classBigId != null">class_big_id = #{classBigId},</if>
                    <if test="classBigName != null">class_big_name = #{classBigName},</if>
                    <if test="classSubId != null">class_sub_id = #{classSubId},</if>
                    <if test="classSubName != null">class_sub_name = #{classSubName},</if>
                    <if test="curActName != null">cur_act_name = #{curActName},</if>
                    <if test="curActId != null">cur_act_id = #{curActId},</if>
                    <if test="receiveTypeId != null">receive_type_id = #{receiveTypeId},</if>
                    <if test="receiveId != null">receive_id = #{receiveId},</if>
                    <if test="shouldFinishedTime != null">should_finished_time = #{shouldFinishedTime},</if>
                    <if test="fastExpirationTime != null">fast_expiration_time = #{fastExpirationTime},</if>
                    <if test="lastProdeptName != null">last_prodept_name = #{lastProdeptName},</if>
                    <if test="delayFlag != null">delay_flag = #{delayFlag},</if>
                    <if test="delayApplyFlag != null">delay_apply_flag = #{delayApplyFlag},</if>
                    <if test="rollbackFlag != null">rollback_flag = #{rollbackFlag},</if>
                    <if test="rollbackApplyFlag != null">rollback_apply_flag = #{rollbackApplyFlag},</if>
                    <if test="superviseFlag != null">supervise_flag = #{superviseFlag},</if>
                    <if test="urgeFlag != null">urge_flag = #{urgeFlag},</if>
                    <if test="revokeFlag != null">revoke_flag = #{revokeFlag},</if>
                    <if test="postponeFlag != null">postpone_flag = #{postponeFlag},</if>
                    <if test="onaccountFlag != null">onaccount_flag = #{onaccountFlag},</if>
                    <if test="puzzleFlag != null">puzzle_flag = #{puzzleFlag},</if>
                    <if test="puzzleActId != null">puzzle_act_id = #{puzzleActId},</if>
                    <if test="curArrivalTime != null">cur_arrival_time = #{curArrivalTime},</if>
                    <if test="curAssigneeId != null">cur_assignee_id = #{curAssigneeId},</if>
                    <if test="partitionId != null">partition_id = #{partitionId},</if>
                    <if test="curActDefId != null">cur_act_def_id = #{curActDefId},</if>
                    <if test="attachThumbnail != null">attach_thumbnail = #{attachThumbnail},</if>
                    <if test="receiveName != null">receive_name = #{receiveName},</if>
                    <if test="fromActId != null">from_act_id = #{fromActId},</if>
                    <if test="fromActDefId != null">from_act_def_id = #{fromActDefId},</if>
                    <if test="fromActJumpType != null">from_act_jump_type = #{fromActJumpType},</if>
                    <if test="mapAttachPath != null">map_attach_path = #{mapAttachPath},</if>
                    <if test="lastProdeptCompleteTime != null">last_prodept_complete_time = #{lastProdeptCompleteTime},</if>
                    <if test="lastProdeptShouldFinishedTime != null">last_prodept_should_finished_time = #{lastProdeptShouldFinishedTime},</if>
                    <if test="firstProdeptId != null">first_prodept_id = #{firstProdeptId},</if>
                    <if test="firstProdeptName != null">first_prodept_name = #{firstProdeptName},</if>
                    <if test="invalidCause != null">invalid_cause = #{invalidCause},</if>
                    <if test="dispatchObject != null">dispatch_object = #{dispatchObject},</if>
                    <if test="gdX != null">gd_x = #{gdX},</if>
                    <if test="gdY != null">gd_y = #{gdY},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbUmEventById" parameterType="Long">
        delete from db_um_event where id = #{id}
    </delete>

    <delete id="deleteDbUmEventByIds" parameterType="String">
        delete from db_um_event where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

        <delete id="deleteDbBpmNodeJumpByProcInstIds" parameterType="String">
            delete from db_bpm_node_jump where proc_inst_id in
            <foreach item="procInstId" collection="array" open="(" separator="," close=")">
                #{procInstId}
            </foreach>
        </delete>

        <delete id="deleteDbBpmNodeJumpByProcInstId" parameterType="Long">
        delete from db_bpm_node_jump where proc_inst_id = #{procInstId}
    </delete>

        <insert id="batchDbBpmNodeJump">
            insert into db_bpm_node_jump( id, proc_def_id, proc_inst_id, evt_id, node_name, act_def_id, act_inst_id, from_act_inst_id, arrival_time, complete_time, ext_used_min, ext_exclude_min, ext_real_used_min, ext_cumulative_min, receive_type_id, owner_id, owner_name, handler_id, handler_name, dept_id, dept_name, ext_limit, fast_expiration_time, should_finished_time, check_status, jump_type, remark, create_id, create_time, update_id, update_time, is_timeout, update_content, is_on_account, docking_status, partition_id) values
            <foreach item="item" index="index" collection="list" separator=",">
                ( #{item.id}, #{item.procDefId}, #{item.procInstId}, #{item.evtId}, #{item.nodeName}, #{item.actDefId}, #{item.actInstId}, #{item.fromActInstId}, #{item.arrivalTime}, #{item.completeTime}, #{item.extUsedMin}, #{item.extExcludeMin}, #{item.extRealUsedMin}, #{item.extCumulativeMin}, #{item.receiveTypeId}, #{item.ownerId}, #{item.ownerName}, #{item.handlerId}, #{item.handlerName}, #{item.deptId}, #{item.deptName}, #{item.extLimit}, #{item.fastExpirationTime}, #{item.shouldFinishedTime}, #{item.checkStatus}, #{item.jumpType}, #{item.remark}, #{item.createId}, #{item.createTime}, #{item.updateId}, #{item.updateTime}, #{item.isTimeout}, #{item.updateContent}, #{item.isOnAccount}, #{item.dockingStatus}, #{item.partitionId})
            </foreach>
        </insert>
</mapper>