package com.ruoyi.microPlatform.mapper;

import java.util.List;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbSituationOrg;

/**
 * 组织情况（非公经济组织/新社会组织）Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbSituationOrgMapper 
{
    /**
     * 查询组织情况（非公经济组织/新社会组织）
     * 
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 组织情况（非公经济组织/新社会组织）
     */
    public TbSituationOrg selectTbSituationOrgById(Long id);

    /**
     * 校验组织情况（非公经济组织/新社会组织）是否唯一
     *
     * @param tbSituationOrg
     * @return 组织情况（非公经济组织/新社会组织）
     */
    public TbSituationOrg checkTbSituationOrgUnique(TbSituationOrg tbSituationOrg);

    /**
     * 查询组织情况（非公经济组织/新社会组织）列表
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 组织情况（非公经济组织/新社会组织）集合
     */
    public List<TbSituationOrg> selectTbSituationOrgList(TbSituationOrg tbSituationOrg);

    /**
     * 新增组织情况（非公经济组织/新社会组织）
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    public int insertTbSituationOrg(TbSituationOrg tbSituationOrg);

    /**
     * 修改组织情况（非公经济组织/新社会组织）
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    public int updateTbSituationOrg(TbSituationOrg tbSituationOrg);

    /**
     * 删除组织情况（非公经济组织/新社会组织）
     * 
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 结果
     */
    public int deleteTbSituationOrgById(Long id);

    /**
     * 批量删除组织情况（非公经济组织/新社会组织）
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbSituationOrgByIds(Long[] ids);


    List<CommonBaseCount> situationOrgGroupPartyType();
}
