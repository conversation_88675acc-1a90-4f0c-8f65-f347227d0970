<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.PbPartyReportRelationMapper">

    <resultMap id="PbPartyReportRelationResult" type="PbPartyReportRelation">
        <result property="id" column="id"/>
        <result property="relationCode" column="relation_code"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="targetCommunityId" column="target_community_id"/>
        <result property="targetCommunityName" column="target_community_name"/>
        <result property="contactPersonId" column="contact_person_id"/>
        <result property="contactPersonName" column="contact_person_name"/>
        <result property="memberId" column="member_id"/>
        <result property="memberName" column="member_name"/>
        <result property="residenceCommunityId" column="residence_community_id"/>
        <result property="residenceCommunityName" column="residence_community_name"/>
        <result property="residenceAddress" column="residence_address"/>
        <result property="residenceProofUrl" column="residence_proof_url"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="reportType" column="report_type"/>
        <result property="reportDate" column="report_date"/>
        <result property="agreementUrl" column="agreement_url"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPbPartyReportRelationVo">
        select id, relation_code, org_id, org_name, target_community_id, target_community_name,
               contact_person_id, contact_person_name, member_id, member_name, residence_community_id,
               residence_community_name, residence_address, residence_proof_url, dept_id, county, country,
               town, report_type, report_date, agreement_url, status, create_by, create_time, update_by, update_time
        from pb_party_report_relation
    </sql>

    <sql id="queryPbPartyReportRelationVo">
        <where>
            <if test="relationCode != null and relationCode != ''">and relation_code = #{relationCode}</if>
            <if test="orgId != null">and org_id = #{orgId}</if>
            <if test="orgName != null and orgName != ''">and org_name like concat('%', #{orgName}, '%')</if>
            <if test="targetCommunityId != null">and target_community_id = #{targetCommunityId}</if>
            <if test="targetCommunityName != null and targetCommunityName != ''">and target_community_name like concat('%', #{targetCommunityName}, '%')</if>
            <if test="contactPersonId != null">and contact_person_id = #{contactPersonId}</if>
            <if test="contactPersonName != null and contactPersonName != ''">and contact_person_name like concat('%', #{contactPersonName}, '%')</if>
            <if test="memberId != null">and member_id = #{memberId}</if>
            <if test="memberName != null and memberName != ''">and member_name like concat('%', #{memberName}, '%')</if>
            <if test="residenceCommunityId != null">and residence_community_id = #{residenceCommunityId}</if>
            <if test="residenceCommunityName != null and residenceCommunityName != ''">and residence_community_name like concat('%', #{residenceCommunityName}, '%')</if>
            <if test="residenceAddress != null and residenceAddress != ''">and residence_address like concat('%', #{residenceAddress}, '%')</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="county != null and county != ''">and county = #{county}</if>
            <if test="country != null and country != ''">and country = #{country}</if>
            <if test="town != null and town != ''">and town = #{town}</if>
            <if test="reportType != null and reportType != ''">and report_type = #{reportType}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="beginTime != null and beginTime != ''">and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')</if>
            <if test="endTime != null and endTime != ''">and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')</if>
        </where>
    </sql>

    <select id="selectPbPartyReportRelationList" parameterType="PbPartyReportRelation" resultMap="PbPartyReportRelationResult">
        <include refid="selectPbPartyReportRelationVo"/>
        <include refid="queryPbPartyReportRelationVo"/>
        order by id desc
    </select>

    <select id="selectPbPartyReportRelationById" parameterType="Long" resultMap="PbPartyReportRelationResult">
        <include refid="selectPbPartyReportRelationVo"/>
        where id = #{id}
    </select>

    <select id="checkPbPartyReportRelationUnique" parameterType="PbPartyReportRelation" resultMap="PbPartyReportRelationResult">
        select id from pb_party_report_relation where relation_code = #{relationCode}
        <if test="id != null">and id != #{id}</if>
    </select>
    <select id="doubleReporting" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select report_type as lable, count(*) count
        from pb_party_report_relation
        group by report_type
    </select>

    <insert id="insertPbPartyReportRelation" parameterType="PbPartyReportRelation" useGeneratedKeys="true" keyProperty="id">
        insert into pb_party_report_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationCode != null">relation_code,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="targetCommunityId != null">target_community_id,</if>
            <if test="targetCommunityName != null">target_community_name,</if>
            <if test="contactPersonId != null">contact_person_id,</if>
            <if test="contactPersonName != null">contact_person_name,</if>
            <if test="memberId != null">member_id,</if>
            <if test="memberName != null">member_name,</if>
            <if test="residenceCommunityId != null">residence_community_id,</if>
            <if test="residenceCommunityName != null">residence_community_name,</if>
            <if test="residenceAddress != null">residence_address,</if>
            <if test="residenceProofUrl != null">residence_proof_url,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="reportType != null">report_type,</if>
            <if test="reportDate != null">report_date,</if>
            <if test="agreementUrl != null">agreement_url,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationCode != null">#{relationCode},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="targetCommunityId != null">#{targetCommunityId},</if>
            <if test="targetCommunityName != null">#{targetCommunityName},</if>
            <if test="contactPersonId != null">#{contactPersonId},</if>
            <if test="contactPersonName != null">#{contactPersonName},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="memberName != null">#{memberName},</if>
            <if test="residenceCommunityId != null">#{residenceCommunityId},</if>
            <if test="residenceCommunityName != null">#{residenceCommunityName},</if>
            <if test="residenceAddress != null">#{residenceAddress},</if>
            <if test="residenceProofUrl != null">#{residenceProofUrl},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="reportType != null">#{reportType},</if>
            <if test="reportDate != null">#{reportDate},</if>
            <if test="agreementUrl != null">#{agreementUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePbPartyReportRelation" parameterType="PbPartyReportRelation">
        update pb_party_report_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationCode != null">relation_code = #{relationCode},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="targetCommunityId != null">target_community_id = #{targetCommunityId},</if>
            <if test="targetCommunityName != null">target_community_name = #{targetCommunityName},</if>
            <if test="contactPersonId != null">contact_person_id = #{contactPersonId},</if>
            <if test="contactPersonName != null">contact_person_name = #{contactPersonName},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="memberName != null">member_name = #{memberName},</if>
            <if test="residenceCommunityId != null">residence_community_id = #{residenceCommunityId},</if>
            <if test="residenceCommunityName != null">residence_community_name = #{residenceCommunityName},</if>
            <if test="residenceAddress != null">residence_address = #{residenceAddress},</if>
            <if test="residenceProofUrl != null">residence_proof_url = #{residenceProofUrl},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="reportType != null">report_type = #{reportType},</if>
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="agreementUrl != null">agreement_url = #{agreementUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePbPartyReportRelationById" parameterType="Long">
        delete from pb_party_report_relation where id = #{id}
    </delete>

    <delete id="deletePbPartyReportRelationByIds" parameterType="String">
        delete from pb_party_report_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


</mapper>
