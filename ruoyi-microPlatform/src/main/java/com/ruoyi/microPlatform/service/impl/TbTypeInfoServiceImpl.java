package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbTypeInfoMapper;
import com.ruoyi.microPlatform.domain.TbTypeInfo;
import com.ruoyi.microPlatform.service.ITbTypeInfoService;

/**
 * 矛盾纠纷类别编码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@Service
public class TbTypeInfoServiceImpl implements ITbTypeInfoService 
{
    @Autowired
    private TbTypeInfoMapper tbTypeInfoMapper;

    /**
     * 查询矛盾纠纷类别编码
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 矛盾纠纷类别编码
     */
    @Override
    public TbTypeInfo selectTbTypeInfoById(Integer id)
    {
        return tbTypeInfoMapper.selectTbTypeInfoById(id);
    }

    /**
     * 校验矛盾纠纷类别编码是否存在
     *
     * @param tbTypeInfo
     * @return boolean
     */
    @Override
    public boolean checkTbTypeInfo(TbTypeInfo tbTypeInfo){
        TbTypeInfo old = tbTypeInfoMapper.checkTbTypeInfoUnique(tbTypeInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询矛盾纠纷类别编码列表
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 矛盾纠纷类别编码
     */
    @Override
    public List<TbTypeInfo> selectTbTypeInfoList(TbTypeInfo tbTypeInfo)
    {
        return tbTypeInfoMapper.selectTbTypeInfoList(tbTypeInfo);
    }

    @Override
    public List<TbTypeInfo> selectTbTypeInfoList2(TbTypeInfo tbTypeInfo)
    {
        return tbTypeInfoMapper.selectTbTypeInfoList2(tbTypeInfo);
    }

    /**
     * 导入矛盾纠纷类别编码
     *
     * @param infos       矛盾纠纷类别编码列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbTypeInfo(List<TbTypeInfo> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入矛盾纠纷类别编码数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbTypeInfo info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbTypeInfo old = tbTypeInfoMapper.checkTbTypeInfoUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbTypeInfoMapper.insertTbTypeInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbTypeInfoMapper.updateTbTypeInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    @Override
    public int insertTbTypeInfo(TbTypeInfo tbTypeInfo)
    {
        return tbTypeInfoMapper.insertTbTypeInfo(tbTypeInfo);
    }

    /**
     * 修改矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    @Override
    public int updateTbTypeInfo(TbTypeInfo tbTypeInfo)
    {
        return tbTypeInfoMapper.updateTbTypeInfo(tbTypeInfo);
    }

    /**
     * 批量删除矛盾纠纷类别编码
     * 
     * @param ids 需要删除的矛盾纠纷类别编码主键
     * @return 结果
     */
    @Override
    public int deleteTbTypeInfoByIds(Integer[] ids)
    {
        return tbTypeInfoMapper.deleteTbTypeInfoByIds(ids);
    }

    /**
     * 删除矛盾纠纷类别编码信息
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 结果
     */
    @Override
    public int deleteTbTypeInfoById(Integer id)
    {
        return tbTypeInfoMapper.deleteTbTypeInfoById(id);
    }
}
