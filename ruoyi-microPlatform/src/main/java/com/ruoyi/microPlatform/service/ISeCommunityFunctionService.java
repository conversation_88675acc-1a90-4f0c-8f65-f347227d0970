package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeCommunityFunction;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;

/**
 * 社区功能室Service接口
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeCommunityFunctionService {
    /**
     * 查询社区功能室
     *
     * @param id 社区功能室主键
     * @return 社区功能室
     */
    public SeCommunityFunction selectSeCommunityFunctionById(Long id);

    /**
     * 校验社区功能室是否存在
     *
     * @param seCommunityFunction 社区功能室
     * @return 社区功能室
     */
    public boolean checkSeCommunityFunction(SeCommunityFunction seCommunityFunction);

    /**
     * 查询社区功能室列表
     *
     * @param seCommunityFunction 社区功能室
     * @return 社区功能室集合
     */
    public List<SeCommunityFunction> selectSeCommunityFunctionList(SeCommunityFunction seCommunityFunction);

    /**
     * 导入社区功能室
     *
     * @param infos           社区功能室列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeCommunityFunction(List<SeCommunityFunction> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增社区功能室
     *
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    public int insertSeCommunityFunction(SeCommunityFunction seCommunityFunction);

    /**
     * 修改社区功能室
     *
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    public int updateSeCommunityFunction(SeCommunityFunction seCommunityFunction);

    /**
     * 批量删除社区功能室
     *
     * @param ids 需要删除的社区功能室主键集合
     * @return 结果
     */
    public int deleteSeCommunityFunctionByIds(Long[] ids);

    /**
     * 删除社区功能室信息
     *
     * @param id 社区功能室主键
     * @return 结果
     */
    public int deleteSeCommunityFunctionById(Long id);


    /**
     * 查询不同类型的工作室数量
     */
    Map<String, Integer> getCommunityFunctionCount(BigdataParam bigdataParam);
}
