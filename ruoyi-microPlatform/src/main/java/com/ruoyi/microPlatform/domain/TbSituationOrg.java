package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 组织情况（非公经济组织/新社会组织）对象 tb_situation_org
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class TbSituationOrg extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县", readConverterExp = "市=、区")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区")
    private String town;

    /** 组织名称 */
    @Excel(name = "组织名称")
    private String name;

    /** 组织类型（非公经济组织，新社会组织） */
    @Excel(name = "组织类型", readConverterExp = "非=公经济组织，新社会组织")
    private String type;

    /** 类别（非公经济组织：个体工商户、有限责任公司、合伙企业  ，新社会组织：协会、商会、民办非企业单位、俱乐部、促进会、联合会） 其他 */
    @Excel(name = "类别", readConverterExp = "非=公经济组织：个体工商户、有限责任公司、合伙企业,，=新社会组织：协会、商会、民办非企业单位、俱乐部、促进会、联合会")
    private String category;

    /** 业务主管单位（行业管理部门）名称 */
    @Excel(name = "业务主管单位", readConverterExp = "行=业管理部门")
    private String managerName;

    /** 单位会员数 */
    @Excel(name = "单位会员数")
    private Integer memberUnitCount;

    /** 个人会员数 */
    @Excel(name = "个人会员数")
    private Integer memberIndividualCount;

    /** 是否建立党组织（0否1是） */
    @Excel(name = "是否建立党组织", readConverterExp = "0=否1是")
    private String hasParty;

    /** 党组织名称 */
    @Excel(name = "党组织名称")
    private String partyName;

    /** 组织类型('党委','党总支','党支部') */
    @Excel(name = "组织类型('党委','党总支','党支部')")
    private String partyType;

    /** 党员人数 */
    @Excel(name = "党员人数")
    private Integer partyPeoples;

    /** 党建指导员姓名 */
    @Excel(name = "党建指导员姓名")
    private String instructorName;

    /** 党组织隶属关系 */
    @Excel(name = "党组织隶属关系")
    private String supPartyName;

    /** 员工人数 */
    @Excel(name = "员工人数")
    private Integer orgEmployees;

    /** 活动开展频率 */
    @Excel(name = "活动开展频率")
    private String activityFrequency;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setManagerName(String managerName) 
    {
        this.managerName = managerName;
    }

    public String getManagerName() 
    {
        return managerName;
    }
    public void setMemberUnitCount(Integer memberUnitCount) 
    {
        this.memberUnitCount = memberUnitCount;
    }

    public Integer getMemberUnitCount() 
    {
        return memberUnitCount;
    }
    public void setMemberIndividualCount(Integer memberIndividualCount) 
    {
        this.memberIndividualCount = memberIndividualCount;
    }

    public Integer getMemberIndividualCount() 
    {
        return memberIndividualCount;
    }
    public void setHasParty(String hasParty) 
    {
        this.hasParty = hasParty;
    }

    public String getHasParty() 
    {
        return hasParty;
    }
    public void setPartyName(String partyName) 
    {
        this.partyName = partyName;
    }

    public String getPartyName() 
    {
        return partyName;
    }
    public void setPartyType(String partyType) 
    {
        this.partyType = partyType;
    }

    public String getPartyType() 
    {
        return partyType;
    }
    public void setPartyPeoples(Integer partyPeoples) 
    {
        this.partyPeoples = partyPeoples;
    }

    public Integer getPartyPeoples() 
    {
        return partyPeoples;
    }
    public void setInstructorName(String instructorName) 
    {
        this.instructorName = instructorName;
    }

    public String getInstructorName() 
    {
        return instructorName;
    }
    public void setSupPartyName(String supPartyName) 
    {
        this.supPartyName = supPartyName;
    }

    public String getSupPartyName() 
    {
        return supPartyName;
    }
    public void setOrgEmployees(Integer orgEmployees) 
    {
        this.orgEmployees = orgEmployees;
    }

    public Integer getOrgEmployees() 
    {
        return orgEmployees;
    }
    public void setActivityFrequency(String activityFrequency) 
    {
        this.activityFrequency = activityFrequency;
    }

    public String getActivityFrequency() 
    {
        return activityFrequency;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("name", getName())
            .append("type", getType())
            .append("category", getCategory())
            .append("managerName", getManagerName())
            .append("memberUnitCount", getMemberUnitCount())
            .append("memberIndividualCount", getMemberIndividualCount())
            .append("hasParty", getHasParty())
            .append("partyName", getPartyName())
            .append("partyType", getPartyType())
            .append("partyPeoples", getPartyPeoples())
            .append("instructorName", getInstructorName())
            .append("supPartyName", getSupPartyName())
            .append("orgEmployees", getOrgEmployees())
            .append("activityFrequency", getActivityFrequency())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
