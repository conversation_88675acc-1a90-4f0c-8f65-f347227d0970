package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小区信息对象 tb_community_info
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbCommunityInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格ID */
    private Long gridId;

    /** 所属网格 */
    @Excel(name = "所属网格编码")
    private String gridName;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 小区排序 */
    private Integer communitySort;

    /** 面积 */
    @Excel(name = "面积(㎡)")
    private String area;

    /** 商户数 */
    @Excel(name = "小区商户数")
    private Integer merchantNum;

    /** 空间区域 */
    private String geometry;

    /**
     * 查询不属于当前网格员ID管理的小区信息
     */
    private Long notGridUserId;

    private List<Map<String,Object>> geometryArray;

    public Long getNotGridUserId() {
        return notGridUserId;
    }

    public void setNotGridUserId(Long notGridUserId) {
        this.notGridUserId = notGridUserId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setCommunityName(String communityName) 
    {
        this.communityName = communityName;
    }

    public String getCommunityName() 
    {
        return communityName;
    }
    public void setCommunitySort(Integer communitySort)
    {
        this.communitySort = communitySort;
    }

    public Integer getCommunitySort()
    {
        return communitySort;
    }
    public void setGeometry(String geometry) 
    {
        this.geometry = geometry;
    }

    public String getGeometry() 
    {
        return geometry;
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setMerchantNum(Integer merchantNum) 
    {
        this.merchantNum = merchantNum;
    }

    public Integer getMerchantNum() 
    {
        return merchantNum;
    }

    public List<Map<String, Object>> getGeometryArray() {
        if (StringUtils.isNotBlank(this.geometry)){
            List<Map<String,Object>> list = new ArrayList<>();
            String[] split = geometry.split(";");
            for (String string : split){
                String[] split1 = string.split(",");
                Map<String,Object> map = new HashMap<>();
                if (split1.length == 2){
                    map.put("lng",split1[0]);
                    map.put("lat",split1[1]);
                    list.add(map);
                }
            }
            geometryArray = list;
        }
        return geometryArray;
    }

    public void setGeometryArray(List<Map<String, Object>> geometryArray) {
        if(geometryArray != null && geometryArray.size() > 0){
            List<String> list = new ArrayList<>();
            for (Map<String,Object> map : geometryArray){
                Object lng = map.get("lng");
                Object lat = map.get("lat");
                list.add(lng + "," + lat);
            }
            this.geometry = StringUtils.join(list,";");
        } else {
            this.geometry = "";
        }
        this.geometryArray = geometryArray;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .append("communityName", getCommunityName())
            .append("communitySort", getCommunitySort())
            .append("geometry", getGeometry())
            .append("area", getArea())
            .append("merchantNum", getMerchantNum())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
