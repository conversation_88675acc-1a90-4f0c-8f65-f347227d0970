package com.ruoyi.microPlatform.importListener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.util.StringUtils;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.ITbCommunityResidentService;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 小区住户居民信息数据导入监听器   （Excel中的数据 小区住户关联居民成员 1对多）
 */
public class ResidentImportListener extends AnalysisEventListener<Map<Integer, String>> {

    private TbCommunityResident currentResident = new TbCommunityResident(); // 当前主记录

    //遍历当前行的索引位置
    private int index = 0;

    private List<TbCommunityResident> dataList = new ArrayList<>(); // 主记录缓存

    private Map<Integer,TbCommunityResident>   datalistMap = new HashMap<>();

    private List<TbResidentMember> memberList = new ArrayList<>(); //子表缓存

    private int mapKey = 0;

    private boolean flagProperty = true;

    //map 存放的数据为 @Excel的name值 和 属性字段
    private Map<String , String> fileMap = new HashMap<>();

    //存储excel表中的字段名
    private Map<Integer, String> rowProperty = new HashMap<>();

    StringBuilder oldRowData = new StringBuilder();

    StringBuilder newRowData = new StringBuilder();
    @Override
    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
        //只会进入一次
        if(flagProperty){
            //先获取所有的属性字段名称放入map中
            rowProperty.putAll(rowData);
            //使用反射  根据当前rowProper  获取当前对象的属性的@Excel中的name值
            TbCommunityResident resident = new TbCommunityResident();
            reflection(resident);
            TbResidentMember member = new TbResidentMember();
            reflection(member);

            for (Map.Entry<Integer, String> rowProMap : rowProperty.entrySet()) {
                if (rowProMap.getValue().equals("与户主关系")){
                    //获得子表开始的索引位置
                    index = rowProMap.getKey();
                    break;
                }
            }
            flagProperty = false;
        }else {
             //即存储主表  又要存储 子表
            if (rowData.get(0) != null ){

                if (oldRowData.equals(" ")){
                    for (int i = 0; i < 14; i++) {
                        oldRowData.append(rowData.get(i));
                    }
                }else {
                    for (int i = 0; i < 14; i++) {
                        newRowData.append(rowData.get(i));
                    }
                    boolean equals = newRowData.toString().equals(oldRowData.toString());
                    if (equals){
                        //代表是新的住户信息
                        //需要把memberList存入上一个主表的list中，并且清空
                        datalistMap.get(mapKey-1).setList(memberList);
                        memberList.clear();
                    }
                    oldRowData = new StringBuilder(newRowData);
                }

                TbCommunityResident resident = new TbCommunityResident();
                Class<?> residentClass = resident.getClass();
                TbResidentMember member = new TbResidentMember();
                Class<?> memberClass = member.getClass();
                int num = 0;
                for (Map.Entry<Integer, String> rowProper : rowProperty.entrySet()) {
                    if (num < index){
                        //代表当前存储的是主表数据
                        //存入主表的属性字段数据
                            setData(rowData, resident, residentClass, num, rowProper);
                    }
                    if (num >= index){
                        //代表当前要存储子表数据
                        //进入此处代表存储list<TbResidentMember>的值
                            //根据 rowProper 找到对象中的某个 属性字段 然后进行赋值 num为当前rowData的key 获取value
                            setData(rowData, member, memberClass, num, rowProper);
                    }
                    num++;
                }
                datalistMap.put(mapKey,resident);
                mapKey++;
                //dataList.add(resident);

                memberList.add(member);
            }
            //如果当前行所属县（市、区）为空，并且姓名不为空， 代表只需要存储子子表
            if (rowData.get(0) == null && rowData != null && !rowData.isEmpty()){
                    TbResidentMember member = new TbResidentMember();
                    Class<?> memberClass = member.getClass();
                    int num = 0;
                    for (Map.Entry<Integer, String> rowProper : rowProperty.entrySet()) {
                        if (num >= index){
                            //代表当前要存储子表数据
                            setData(rowData, member, memberClass, num, rowProper);
                        }
                        num++;
                    }
                    memberList.add(member);
            }

        }

        //List<TbResidentMember> 中的数据 TbResidentMember的 residentId 关联 List<TbCommunityResident> TbCommunityResident 的 id
        //根据关联进行合并
        for (TbCommunityResident tbCommunityResident : dataList) {


        }
    }

    private void setData(Map<Integer, String> rowData, Object obj, Class<?> residentClass, int num, Map.Entry<Integer, String> rowProper) {
        try {
            //根据 rowProper 找到对象中的某个 属性字段 然后进行赋值 num为当前rowData的key 获取value
            String field = fileMap.get(rowProper.getValue());
            Field declaredField = residentClass.getDeclaredField(field);
            declaredField.setAccessible(true);
            //  转换值为目标类型
            Object convertedValue = convertValue(declaredField.getType(), rowData.get(num));
            declaredField.set(obj,convertedValue);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 类型转换：String -> 目标类型
     */
    private static Object convertValue(Class<?> targetType, String value) {
        if (value == null || value.isEmpty()) return null;

        try {
            // 处理常见类型
            if (targetType == String.class) {
                return value;
            } else if (targetType == Long.class || targetType == long.class) {
                return Long.parseLong(value);
            } else if (targetType == Integer.class || targetType == int.class) {
                return Integer.parseInt(value);
            } else if (targetType == Boolean.class || targetType == boolean.class) {
                return Boolean.parseBoolean(value);
            } else if (targetType == Date.class) {
                // 默认日期格式，可按需扩展
                return new SimpleDateFormat("yyyy-MM-dd").parse(value);
            } else if (targetType == BigDecimal.class) {
                return new BigDecimal(value);
            } else if (targetType.isEnum()) {
                // 枚举类型转换
                return Enum.valueOf((Class<Enum>) targetType, value);
            } else {
                throw new IllegalArgumentException("不支持的类型: " + targetType);
            }
        } catch (ParseException | NumberFormatException e) {
            throw new IllegalArgumentException("转换失败: " + value + " -> " + targetType, e);
        }
    }

    //使用反射  根据当前rowProper  获取当前对象的属性的@Excel中的name值
    private void reflection(Object resident) {
        //使用反射  根据当前rowProper  获取当前对象的属性的@Excel中的name值
        Class<?> aClass = resident.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            //不赋值list对象
            if (declaredField.getName().equals("list")){
                continue;
            }
            //设置字段可访问（针对私有字段）
            declaredField.setAccessible(true);
            // 获取字段上的 @Excel 注解实例
            Excel excelAnnotation = declaredField.getAnnotation(Excel.class);
            if (excelAnnotation != null) {
                // 5. 获取注解的 name 属性值
                String nameValue = excelAnnotation.name();
                fileMap.put(nameValue,declaredField.getName());
            }
        }
    }




    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

        // 调用服务层保存（在事务中处理）
        //tbCommunityResidentService.batchSaveWithMembers(dataList);
        System.out.println("进来了");
    }



}
