package com.ruoyi.common.utils;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * 描述 The type Tm img.
 *
 * <AUTHOR>
 * @created 2021 -07-08 10:25:10
 */
public class ImgUtil {


    /**
     * 描述 The entry point of application.
     *
     * @param args the input arguments
     * <AUTHOR>
     * @created 2021 -07-08 10:25:10
     */
    public static void main(String[] args) throws IOException {
        //            changeImgColor("D:\\IEC202105181050.JPG");
//            changeImgColor("E:\\20211203152004.jpg");
//            transparentImage("E:\\20211203152004.jpg","E:\\************1111111.png",5,"png");
//            changeImgColor("E:\\************.png");
//        getImageGRB("E:\\ruoyi\\uploadPath\\avatar\\2021\\12\\03\\20211203152004.jpg");

//        String white = ImgUtil.changeImgColor("E:\\ruoyi\\uploadPath\\avatar\\2021\\12\\03\\20211203152004.jpg", "E:\\ruoyi\\uploadPath\\");//转换白底照片
//        String s = ImgUtil.localImgToAlpha(new File("E:\\ruoyi\\uploadPath\\" + white.replace("/profile", "")));
//        System.err.println(s);;


        //        String imageStr = ImgUtil.GetImageStr("D://下载/企鹅.jpg");
//        System.out.println(imageStr);
        String s = "iVBORw0KGgoAAAANSUhEUgAAAQIAAAECCAYAAAAVT9lQAAAgAElEQVR4nOy9aZAs2XXf98u9ll7egnlv9oUDDJYZAAIIYQCCBBeAoMWdtiQH6QgHraDDlmyFbYq2IvzJDkdY/kJvUkhmyLRk64NoMShKkGhSEEmRAiGJJEAQ24DADJbZ17d3Lbn7Q/a/+tR9WZXZ3dVvHjR9Iiq6Oivz5rn3nnv2e65X13XNKZzCKbyhwX+9ETiFUziF1x9OGcEpnMIpnDKCUziFUzhlBKdwCqfAKSM4hVM4BU4ZwSmcwilwyghO4RROgVNGcAqncAqcMoJTOIVT4JQRnMIpnAKnjOC2h3UZ4KfZ4aewKThlBKdwCqdwygi+FaBN8p9qA6ewSQiP28ApQa4Hz/PW/n7c8et6vuv9r3f7x31/F5x0/19v2NT4H5sRvNHh9V4ob3T4t50R3io4kmlQ1/XiUxQFnucxm82oqgrP8yjL8rYZIOFU1/UCL4vjJvBXm77vL56r65qqqgCYzWY3XSvLcvHdvrOqKmaz2QK3LmfhSYz/t9L8wvHGv6oqyrIEGlrReOsZe82+43bq/ybAO0phEvuI53lMp1NGoxFFURCGB0qG+//rBXYi0zQlSZLF9ZPCXwupqirCMFy8V++sqgrf91e+Uzhp8bmEp/Z93984/t9q89sGfccfWHzXb7q3KIp/axe+C0diBHAzsbgDFQTBEjd9vcD3/cVECx8RcFEUBEFwS/B32ynLEt/3CYIA3/cXUsne747xqvbW4S8JeBx8T6L9vrCpBbhq/KFhBEEQnMh7Txo2RZ/HjhqIgIMgIIqixXeXsF8vsKpiHMeUZbmQYhbPk8Df87zFu8qyJM/zBSOyhFeW5eL/KIoW3/vgcNLjf7vP7zroO/5BECzMsDzPF30Lw/BbhiEcF46tEch+HI1GS79vbW2xt7d32w1knudLOPm+v3H89Yzs6fF4zN7e3tI9k8mE8XiM53mLd1mYTqcMh8OFutoG+m0d/keVGH3n96Q1Pkntw8Bhxt/9Ltja2mIymRAEwaKPr7d22wab0siOzAiAhQNOnFOSQs45/X29QXgEQUCWZUv2oMyEk8K/zSSRBiD71b5LONj710EX/schlD7ze7ubBl3jr3t0zY6nnr2dYVPrq5MR9JFIclrJlqyqiiRJSNP0ddcI6rpeTL61u+0iO2n8JZn0Ls/zyLKMOI7xPG/xLjEoObn6vnsd/l2Ectz5vR01AhfWjT+w+G7HwmoCtzPcMo1gHaFYZKIoum25p5W2aZouJln9Omn8fd8ny7KbHFLCre3+PM97L4J1+B+HEWyi/ePCJhjxuvFvg7IsieP4tqVnC5sa/87Yzzppod+lQllVWt/1sU47N3xzXGgjZvu/cLLvPA6B2YVq+9uGl6RpURQr23PHzeLZ1re2e9M0bTUjrEpvVV1r96p9qcfSSLRw2qIatn3brtUgNrGQ2gh91UJto0OL57p32N+t2WCflSlpr68KA/cFO9fWaeyuD2lj7vraFJz4XoM2BmEH2TKLto9CVKs+mwDhJKIGFrkG9rvU475qu10kIhIRkkyV4+AsnNa1r0WshSPvf1mWi2QhGxnQouirGmvRuF54N/nmqB9oVHc4YAp6l1XjoZ3WdF3jpL6maXqo8bY0eZjx76Jf0ZM0VoEVMrcCTjwbRNLKJnO4iRzr4KR8DK6zULjKGWb9A/ougrdZiX2kgCVo2aV91dR17fVpv6oq4jgmy7LForeSxz6vMcnzfGEKdOFZluWSP0UORuFwXGYt/F1nnk0CcrUR5YiIEWlxarHVdb1Ej13gRg0OM/5927cOWI2/DXWfNJy4RlAUBUmSLFI6NSmTyWTJObaK+************************************************+v62tW+IiVqx/f9xX22v3CwkGy+QBfI76JFp2fG4/HCHGnTDFZdb7tPYTxgMebj8XhJM7OagJXKRVEwHA6X+uN5HpPJZCNCpmv8D0O/7vjfyqzNjeQR6O8qH8F8PmcwGNzUhjjfcaHLR2Cv2fAXsOC6YRgubH8r9dW+rklSFkVxKLtQiSqDweAmX8WqcdO71/WpT/tRFN3kLJ3NZgup42poNqzWtVg8z2M4HC6YqYUoijr3S3SBFruSgCTpNWfAwsQJw3Dhu9F3PVMUxcLp6WpEfekHljMSLawa/y6w2oCbAStmKXzW0clx4ZYwAmAxkSIy13Nv21wg15Njd02kVbtcRmBxt9AWZ3bf2eUo1G92f4MlwqIoiKLoSIzAOiK72hcTswzOOg3bnF591WbdowVmiXoTDi3Z0qtSgPUOLaA2x6j6acGmlx+GEbT5IdaNf19B0TX+J80ITlz3UMdszF4JG7L1jtOh4w6E5zWbaur65gQeqZhdm4LUjotXXTcpq4PB4KaJBA6lDa1qX9rWqvb1v5U2nucRx/GS+WDNBC2+PkSsZ21bmlNJZGjPBzgss9H9yl+wwsWq/ZKkGqPhcLgwWyxDl9bQF6zA6Du/fdo/zvhvCk7cR2C5IxzEdLW7S0xCm280kUEQLK6t+xwX8jxnNBoxnU4XkyxchYO9JsYxGo0WuesuM7LSfDAYkGUZg8FgMbFKJDpuvn5VVZ3ta1G2SUeNtVVDraOvD9i8fLWdJAlZli1MAxG2HSc7Rus+9jldS5Jk4aOxzECmGxyYC1EULVKkrS2v8TsqCJeu8e+iX5vlajXWW53QdMtMA8v9Za9uoqN6x1F9BJKQFn8tjFX2YFu71tazi9xKKldFd5+339vGze237adNWbZM1+Y75Hm+cuOVtU1tCq7VIqxJofGR1uFKQ3ec7dyvmp9VY7sO3LRhO852LFbBcelHoHl26eAwNN7Wft/1dVy4PTeT30KwYTYxAA1uH6koh2cbIaktZbXpHjmV5vP5sfG3zji7UK3dr/8tU5J5pk1YbaE+PesmTh1mfCwht10/LlhNxG27iwmcwgG84YuXKrRmF8FhsuKkirrtKLLged4ifGoJcxNMAA62ylonHSwvUlc7sfatrtlwl7X73X5ZBqf7XbAalv53f9sUuKaJ53lLZsFJwyb78nrCG54RxHG8lKQCLEJUfdQu3ePG3uX7sIs/iiLiOF7E8jdBRFKBrRlj7VM50uzCtaFQm4GoZ9UP4SuNRovOzUi00Lb4Xd/AYdTZLh+Crd3g+hMO4wh8o8Mb3jSwqr0lYqnVXQ49PSMidKWQmIEIU1ETLcTjMoMgCBbtuiEoywzsVtssy5Z8AbBcz89N3xbTcnP5b4U07DI/LB42SUn/nzKDfvCGZwQ2wcSGcPqaB1pkriNQ1ywhWiaxqQ1XSmu1i1px6FWhu7aUXS10OAjxCuSZt+MjBrfKIWYXqGsaHAa6tAcbJrRmQlEUr3sVpVvp9T8uvOFNA5uHDsvOwz6gxePms1dVtXDEyUdgCXNTPgKFwqwX2Uptq4q72XQyKxSTt/fJp6HrWth2p5yFdQv8OKZBF7g+EesjuF0Lq96O8IYfKdfrbAm6b4jTLhDblrQKpfVq8Sj+vwmwC9+1220IzabjKm9D9yhfwpXcrlpumZz6Z/u7Cr91/3dBlwahMXY3Hom5nUI/uCUagWtPHja+eitAONrEob6qpXW22bZWxZoPs8W0S9JKbZ/P50v1BPQ+q8arPSVP6Zq7rVrP63eZH0oQkhZicRSjCcOQJElu2nSj+6wvpi+TXfexbbt1EW6VR9+d61vlP9kkvOFNgy7oIkRLBJYQb1VmWBRFi0xHvc9W7m3L/FMmpesktX3W3/l8ThzHzOfzJcYg7UD9VJixKIqlZ+CgPoG2KltnbNfYdu3nd7cj2zMrTqE/vOFNgy7o4uxiBO6ml1vlqNLCdvHsu2lqleTU9cFgsFSb0N1UA8uMR7+lacpgMFgKOSZJchPz7NO/vvepbVuP4PV2GH6rwKlGsCFwd75tai/EOrDmi+L++u76CrRA3XMTVjE6yyCk6s/n8yUGYzeTqf/z+Zw8zxeS2UYy3JyGTajPti05CW09glPoB6eMoAP6mAWuJ10bYI5bnQe6JaIKq9gdgGVZ3lRYRbhp3/xhCqtAw0AGg8GivzYsavczDAaDBUOy75Qfoq0sWtf4rvvYNu323dOIweHglBF0QBch6h5Y3psPxytH1hdWVeDR4Sz2u71nOBz28qpbCa5yZ7CcE6HvnuctVUPSu7SLtM2J1mehr/vovXDzgben0B9O2WYHdKmv1j8gwrWSeRPQtoBE8JKCsFxzQHUEgaVzE2xBEvtsV3TC87yldGy3spGSkxRRaMO5LVrSNb5dWpVNKFq1OeoUuuGUEXRAn8w2N6PNerxvBbjMCJar9Oq7dR72CXHZBeYWmLHajltP0C0wKoahdG57vmCf8e3C0W6cUvubqpD0RoFbwgjaCG6TE+S279beOwxebdBW/sqVwrD6XAC9y41727BjW3kt3WedfVbFtxqHi4/dn++C0p/b9g7YBarYPLCypFobWK+92x+bs9DGaFxow9+2b9uwbbu1F13G0DeF3D6j97pjob5sEmy/rSPavm+TuQonzghOWiramn3ygh9mG3Gf9suyXNqFp2uSRnZvgSaorSbjUdpXW/Z9wE3v1MKQI88WDLFFRPI8X7lIBYchsMO0b9t1F6SrTah/Xe1b/4CtyWgLq9j/4YCJ9vHhWCZjx1Xv2wSdrROU+k1MQAyobxm03jjUR2RlLqKrJN5Jq2Y2i89KTxtPdn+3/wvWSWx7LoDrIIQDKWx9BG613KO2rzbdBBzb77b77dmKFvSuddrHuryCLo2grf02jUPX23B237uufbul2i4M93fbXl3XS2c3rvLBuM8oNGl3dJ402Hl3Na1Nmp8n3pMur/BxP563XKPe9YofV33Stl31RbUErGfa4iJubXE5TvvuXysl7TvSNF2SkHLswYFX3/f9pUIjmwqxdbW/ToW2jGfVuQ/r2rchSgtaIGVZLo48l4CQFtk3qqNS95of249N0bEFaRxiAtPpFGApJdxmkm4CvuU1AoGkr61rbwmlTSPogz8cVAMOgpvPBbCLzxYJgeVzAY7avkD4tzG2OI6ZzWaLhWL3GLg2s+rua0FZqQw37zsQdGkEq9p321ilcaw7l6ELf6nobs1GSdE2FVpnMfTRCOy1LMuWGNwm7XQXxPztuRv2fIdNagTf8ozADpYFi5fuOyojcGsN2AlwVfd1Tq+jtK9twLYkmYhfvwNLqqJVXVeVLZdqrEXUxggsoa/C36rkbe274DrtPG/9uQCqFt2nfdepa/shpiyG626KWsUIND5afK72d1xG0Gd9tAkARWU2FaI+cWfhSXJMYEkCwAEx2NN3joODXSCWwNadC7CKOR2lfRU51Ts0+cooHA6HCy1IxGnt8zzPl/CwmsVgMFhbXKQPTKfTte0rlOcyXo1T17kAfdrXdZlQdlytl99qDtDPdBPudq8EHK6u5VFBOOvd9nxJ249NwC05BPUkQYNhHW52d12bc+qw7VtJC+3nAog4dX/fqEFX+/P5fKHJiJCVCKSMQqshuA5KN8auYiNRFDGfz1cW+FwlKV3o0/46O9ieC6A0aTFB25dV7WuBSH2WaSQzS2cs2K3Rh61gpMWnpCzhv8pUOwxY5mfb1CfP84VQa9PKNgUbMw20IKyE7hNesa8/Ce1BHFXg2vRujQAXf1UZsuqgfmtzHq2KSnS1byMPcJA37+YYSDrJ9rWOyaqqllR112egBSRVV4ysLR/BHR/LhNrOTYDlRCI7rpYGbEaitBUrrV38bfuKKlj8rTmlPtr3WqeqG0VYhb+dVwsnrd22Qdfy3JTpfWyNwGbSuZz/sPbLSfgTLGEIPxGEdbocF/9V0vOo7VvGAu1MVYvGMijbpl2QWvQCLSQ3E9JqAnZ8XLzUvtWILCMTw7H4Cw89Z/Ff177AfrfnMthnbaq3fDZtfhlXC9O1de1b6EurXczjVjnTu+DYerutJw8scdvbAeRttYtCf22I66j4r3KIqr2+7bcRjDQIOAiPCeeqakqgucVF2uxfdzEKZ8WkrUljfRXWz2H3Faxq3/op9LzNupQd7yboWPyttiRw6x5Y/0IURQsGpGQstSn8LRPsMz5iJm1M4LAOcN2/6nO7wLFNA+uMsR5mV9U9bLsLBDfslXUJraucuZuQpDbdffX63Q2PHbV9l1FoLK3EU39sRMFt324Rtu/xPG9JItsFbKENf+Go/grse63aLvzt+Kstt32LZ1/8LbS9z5ofrunm/u+237ZD86TN2VXvOsrvfeHYpoEGy9qu1tZ6vaGNeAWySdfh32ei1/XzuOMj6Wmz9CyoPes0s+XHpaLb91ofg2Uwep/FS/e7B50ILzlk3aw3LVJdt5LYtq/2VrVv/8pnoVJpbqjW9eMIL537oOetL8MyL7d9q9H82w7H1gjk5bW57nCzfXfYdhcIHpPjKuQmwrR/4cAB1YV/m/PPxbtNwnS170oi1xkpEEMR4UoN1rFlVr11fQ9uuq+IXotP7dvxsT6EVe1bfC24i7utfTFCy0Ta8Lftt1Vjlkag8VHkQKnKNt3bmi5W+2pzHtsNZC5sUgvoWn63ykG5saiBlSibYgK2/eOATWKxeNmJ78LfZQQukeh397i0rvHRc64pZb3u7sK2C1DMxVVh3UXatgD0Hhvnb/tf/bLtWOer8HDHQ8+35frr7yqnnOtTcelAJo3V3Nw5soyjLcHI4uq2L6Zr/SoubII2bxdGcGy9RxLX9Rrrt77gDsimnCmWkNocUpvC34LF/ajtWwZibW4LYiquM02/2b/WBrf/r3Ik6lpZlkvtu34Ri5PLyKyG1OYnkZpuE7PctsQk3TGLomih3tssT42ZfVY4uMzRPdzGXrNnPwiO4uw7rrPwuM/3hSMzAg2sVFQ7wTZ22wfsRGkSrZRThqC1ZbXAbIjKSjI9Y+1NPWv3A3Thbxe03pFl2SJRRgTu+/6SPSpCtm27i8gNaeoed4G5z1gcpRFYTcO+W+3asXEJSSq67rOLRibUaDRa4KIj01w73kpmbSBKkmTJSWdxFuNSYtQqTUlgf7fza9vVX42pcilcLcra/25Y0gXh0hZq7LNQLUOsqmqRHCStw9K/oitu308ajmwanARY284OXBiGiywxV5WGm+1t3WvVdC2stq2u68B1SFkvtCuR4OheXDfF2GbC3WpwHazKbHPV/LZtsVr8clzatN+296xKobXMpWt+V5mhmpsu+ukLtg3XvFgFq+jHnkWh8bbta3xuFbzujMD15IrzutzQRdNydJsjIHAH0RKWLX7RV2uR08z1frvvsZpF36Fdt+nouHAUYnIZgf0r0HjYXXEWXI3DmgX6zY5VWzREZk+f+e2CVfTT1Y4N89owrdV+1oF1zrr4SBOzkRxL/23roKtfR4XXPTaiCbZhKmtn2sMzpHaLCG0bCtHZenjWzpO6ajl0XyYwn8+XYtF2d5wSY+xEB0GwSHTpA1KTbfLO63mctyUud/z1XeNhGat8AdbzDjcvdPu/NdFkZokWLB2smt8u6EM/60BzKP/IqkjCOrACxx0fS/+KtLwhNQLXphIx2N/H4zF7e3tLz00mk0XBCftdsLW1tVSwRG3ZBJo2SeSC1R7URhiGi3dayVAUBVtbWwuCg+7JdKWFmMJ8Pl/ydFs4DIEcJw6ufg2Hw0VxDIH673kHm7zc30ej0co+wIH3fjqd3jR/arOu67Xz2xahsf9XVdVJP11gMzCt/8hNHlvVx62trZveP51OGQ6HN5mVqzaBrYKNaY6vNyOwII7oJpKsUultSG6diWH/KmnlMODalwI3CUeMxbWZu6CrFNpx4LhSxUpnG1u3UQNr09v7+9jhNirS5nPQ/31NSBe66KcPo7R+Bbspqc8idM0IMRCLj21fjti+tLMxOrkdGIG7ddY6kdwwlwbNOv303S5UEagkuAZf3e3rH3AdVboGy9LWErSbwLMOLAHY4qWqntOFY9f0HZbprXuPFpTd4itzoG3zk10EFlf7v10g+k2SUZrVuvm17VqfkX1PF/10gWUYNkKxTtux42bPlRAN2metudpG/124bQJuC0agySnLclGN2MJhJbnVLCyIgFc5+rrws9/bpImVGIcBN61W79kEbMLOlM+iTXq6uRFwUIfRZaAutElCF/c2Qrfzu05y2gjEUTRBiyM0Amc8Ht+UXHVYcMezjf5vtUZwpL0Grg1mvaoubMrhsa4dd6EGQdBKuG1po5vEP4qixY7Avu23gWv6CNZ5k12CWOfVdnPt+xC6EoSs9F0ljew721TjvsS7iomsY0wuDm5kwl5fhYdrouivFSQW2rTGvoLGjZ60jV2bqdnXNOkLR/IkWeKxtiEcZGatix8fFuxkSGrY03vcCXYdMMLFagJ98Xcdi22ORncno/DqOz62irFtz7ZvmW5fs8P2375bTECmjH63lY/dhCg3SmNDeqo3qHv7qMyHgXXtH5fGbN80d5YJwHKWpBsREbg4Cc+ujzU3LeORCdFn/DcBGwkfWoliQ3ObIAZ3d5kWs5x3vu8vJaLAQeaaBtolILu3vwt/N6rhftxxaOPS69p3HURaiC6RWMlgy3t3gSUibcixONnveof7fFmWS4zRVWE1/q4tbUOAR4Wu9o8LllFqA50L6od9p92jYpOadJ98Al2ftvGxdNw1/puC3oygjfChX136o4IGvu20X4UGlZWmlGNNlEJLuh8OFo5dSF3499EI7BiJYDVJXe37vs9kMllarADj8XiJGVgmc9hDVm2BTxG6CMo6AWE5xVjvWHcuQF3XiyPYLU59j13vgpNuH9afm2AdwPpr14HmDw7ySrRQxUDWfSwNWUGhvneN/6agl7PQtVvsdf3fVZf+0IgZtbgoisUguycIQZPwMxgMbmrD1sTToNoIguXkR8XftaPlJGvDpa19K2nUV5tgJLw976BGv363+QqrfAQCvVMVkdvi1W5CC7C01deCLagpTa0sD+ofaM7caMBhE3IO074Lrv29zkcgbWvduQxKDRZNSfi0jeUqmmzDUXNuF7rNNu06l2ETcGRGYG2YdXXpD5sgYd9h3+2iKSLQvV0pwFoI1o4XbAJ/a0a48ep17a+y91zCtg4rN8LQNj6WwdjsOr3H5utbXEWI1myytqp1xur3dSm07vgchRH0bd+FPoyg61wGt8CKNCg3kUnPCdfDaCsunm7/143/phhBL2yFgLtAxTnX1aXfBAyHw4Wt6256kYpo69m1bQqyC9UmpxRFsRb/PgPdtsAEkgyr2lcikXCXfWklhSYfDhZGX03LHhCi5zUO0g6gWQRWwogRSApr/DWeekbp14K2TTVt4I7FKnDV5b7t94WucxNkasoEdc1MRYQ0X6I7PXPYNWD7IzPjOOcy9IVjOQvrul6qS68B0OGbx3VmeF6TrDKbzRiNRku2NixvNrLebtWx1+RBM2g6LESDLSawDv8uG8/18sqJqb53tW/tc9uGGJz1YIsoYHnT1TqQWaExC8NwCSf1QbhafKQ9aTx1vU3DsCFHLdJN+ImO034fZmkjPnqPjpubz+fAgUDTjkrNh6IsOjtB82Edil3OQsuI1Y7oos/4bwqOXaFIYDf3WA1Cao2IzZal6oVgx2RaZ5wGzjII+7wkmMVV94jbuqqiJJ7dHGO9+H3x13O2/1Ij3bCU5fRWmxHxtancriSxjlEbNrQpu7Bcicem9UqrEojZuWXFiqJYwtk1vexvrmnQJxZuzSerVVnT1EYURHdu9qfVKO17LUNtOzfBNS2teWY1ORuRsiHkLhNBuLalHtu8F0trVpu6pabBccEi61a76eJqXR21xOoueg2y1QDc1FQ3i8sSZlvqs3UMuvhZwrD9tQzH2t5tDMUuKGuvu2qgtVFFNFZttbUS3bEUsclGtpqJHJEiahshcRmODWmpXS0Ct5SYzZi0jK0LbL/bFrgWzaqsR+AmD7ucgvP5fOk5+92OgZiNZQq6xx0Tt70u+rX05I7HJlX/Ljh2HkEf9ctdPLq2KdXG3ZZqnXYicEkjLRgtMlthyLblerstMag/1rO8iiG5e9hd+84SmF20WkTWg2ylsu2XGJyNgth4uNs39UXak70HDghd5cBsn2wbbvuWkIW/1TjUF41nX/vZajj2PdZ/YMN++qvxVHjZ0pvUflh/boJl+K5qnuf5kgMRWNLs+oDut5WTNb9tkYuTgltSj8Dl1kqgserXUT+SllbaWTXcquZaTJokEYvrhJT9bv/aRB95bWXrtzFDXbchS6um2v9XJTa5u/7s+X92MQuKolgq9a02bRah+m9DlVY7sZpRlmVLVZItA7VtupLM4q9Fo/bt2PfxIYhp2PG3tSv0bjE+m49hTTpoGFscx0sCQO259CN/jJ5Xv63zNo7jxaIVDYopuObMqo/LnC2jPEwlrePCRqoYW6+26yNou2Y57nGTQiyhi8DbSohr4emjSdKCcM8FsMRuiUTX7QLtAuu7EFFbdd7attZrLX+F3hlFEXt7e0s1A+WZthqEFq/1RVg71A2NwbKUc0H4un9dk0fz7eJvacSOqV3gq2DVPIh+rD0ubcmC3cWov5aR6NlV9CNh5WqdFtSejf3b+VkHrllq+ylmdCt8BCfKCASuxFVsfdM2kJVA7iYfceq21EzrAGtzZrrXrBPRjoMF63m3GpD93ZoEFte2FNI2Z5Jto4+JZp1kLtHqdzFDRSnqur6JqcKyum6dWlo0wt+933XI9gHXqWjpx46rnKl6r02p7kPmbfRjr1mfitoHFjsbrT/KCox10CYM2mjlpBnBLTENXLtbAyQOve7TBW22m67Lo93mIwCWwjcurjZW716ztrl9X9t3m9JrVUc7me6GJCtRrUajZ1wfgQhHH73XqqAadwvWTLJxcRG6VGn7u9ppc3C6Jo60IKtWW3PrMD4C+x6Nm80stL4BLUKF9BQWtfMlH0EX/dhQne617bu+CfW/jyPU9klrwZoVt7Jc3YkzglUhtjAMN72qTvMAACAASURBVJJw5A66CFuOLisx3aiBpJ4Nh7majlXV7HfrSBO0MS9rF1tbXLha3O13JTvpurVPrfNSeMsZpkUsX4HV1oS3fbe14a25JBx0n5VadqwtznbsyrJcOvJdH6sx9iF0N2qg9wRBsGCImht3HLWgFB2wjF3pv1304zJK1zFo27UamovTKtAzbluHYZSbgI3tPnQloiuFrMPMHoUlr7KVlNY5J2KZTqdLsW+9x11IGnzVvbf3WskkvC1YVdx1EslJJ5vR3SWoBW+dQJb5zOfzJRUalqsolWW5UGulglrnnMtk2kwSO+ZW67H2cF3XizMKrN/CPq9xtfb5bDZb9MkuPjeM2Tau7vxYP4j127R9rAZnacWCy7wtburnbDZb0hpsP138YPncBKuh2lClZW6u/0K/H0bjtfPWNserGM4m4MQ1grbMsDRNieN44SzUZMtRAwfpvVEU3ZROatU1l4uq/eFwuHCkaYLleRdefUwPST3r7RZx2HbkrFOdBNnwXfiLsQisRNa7LKO0dnlfvwAs1zxQ4Uxrmq2CLvz7gJ5LkmRJ81Cf1n26wJo+be8dDAbkec5wOFzg7WZnniT9dEUNrOYpH42eu5WwsbMPbaf0v21aThZX+rv3W9vMSmB3YKT2aaG6tr7Fy9p1bdllXf10paN13tjrkgCHwd/iZnF0NRk34uDCqvHXGLnPuMzHOqrcuVuHv1X329rXfeqT5ti+cx2s6peVwFZSWu1GGoWiJMLHqv9pmp4o/XSBHQdL/3bc+ozDceGWOAslfezCBZbULElPS+huaq84eVsUQkS27lwD6/E9TMTC9SXU9fK+fnvNLjqL5yr8RbD2d887KJ5iF44NO1qtZB1IU9H72s4FWNWOGMM6/LvARjrUh01Ku3Vtic6kgQ4Gg6VIhpXuJ0k/fUBmh/5azfBWwC0xDWaz2UL1UefG4/GCOQjkXBKzkDNL9qktyKFiFZ7XFCnR4OkACdXes3aXrUzU1yOrxacEEj1rQ0Zt7du49jr8JeXLslxU4rGL10oeEYqiA30XlC18ovGR6rsK1HYX/n1A0Q+Nh9qwUq2PDd0HLGO278uybOE0VAhT958k/fQxC6z5LO3P1pq4FXBLTAP3XDuBDedpUWiBuVJQHNLer99tMoi93yYOWfXOTRrq6mdbOe021Vo+DusQ68JfmoS7v9yOk+tkUyKNjTevMw1c1V9MxM2PcE2DPvh3mQYWFztfbghyFRxGJdYcuL/pfdZJLRCzPSn66QN2PCwzcHMHvqVNA5v8IWKQRLMONzhI363rekk90312QdiahZZ5SAJYb7YI3w429EvGsAtVbYhYrKqsRWSdeYfBX4xGKrhMESsdNfmHrVkoJuw6GK0Dd1Xfu/DvA64Nbwn4uM7CVYuhrU86xRqWy36dJP10aQQWV6t93EptAG6RRmA5p8td7TXLNGwoUapcG2G0oa9QnHu/y+H79lPSoi3Utgp/KyXX4Q83a0Z6rz6Wuaht18F1GIlh8bGE52oEaqdr/NdpBLatKIqYTCaLxdj2nAtd/XLfb8e+TYq34e/CJumnC9zxUQo53HywyklqBCe+DdmdKKtSuhLN/e0wi9W2rzBPH4nZBX3scOHZ5p1371HftKjDYExZ5oAHdcW23VI8DCAriauIsK4p8MmCCryKsKg5R8BlmnfWVQ1lCUFAlef4QQCeR+h5xEFEURbU1CR+RF7llPk+kYces9msKRBb1QR+gEfNIBkwT+d47Hvfsxz2xzPN0oMU6DLHq2vwgMCH2ofCo55W4HkQAD7UVYEXBAzimKIsCGl+mrL+nAJ30Wl+NY5WslpN4jhz3zbnli7bGMSqA1SsSWWftfSwSuBZZtbW7ia1hhNnBG1JJO5A2jBb2yTqun1efoeu9rsGq+v3PoxgHUi1trn6svHruiapalIgGg4pq5S9tLFh/SSgqmoIQkogLEMKSggDGMYUe3NueD6FV1CUOYVXkZETejV+HJDXJaEXUHowq/JmoXpQRhV1AWXY9HtWZ0TDhOvljDiIqKKaIIyYpHOIfagbBlMEkNVNWbUgiUipIPAI9huugRIfPJ8wbN5VlyVEATlAEJKmM9K6gBDKAgZhAEV5U/hVmpXMD3fzkswu63dQZp6lnz4aBxyUl7N5/+5itEIKbj4E1UYXPO/gSDUxffdIO0VTbGZnWyhazE7Mz757k1GFW5JivO7cAVg+Q8C1wWy4yapmarOr/S4brSuzzaroR/loz7pCpSJs1SqMqPGomc8n5EVG7UEc76dGlxWUFYEfUVIwCCLwPZjOG0le76u6g4CizkjCkH1vC+l8SpHPGcQBgziQMG8WZ+JzbX6dmoqqzPGoGAQRAY1kL/cdl1HoQ1kTJgFFmRJ7PhEeHiVZOSMrZ6RVRYVHI1M8qqqkrKEOoUwCJlRM6oIKSJIheOAnA/BhzrIGZZ199tBZaQurbHgtFi2iwxT2tO9yHY02vO1u8rKMQt9Fq9a0sE5K/abdoas2rVkc9IwYh/7f9LkGJ64RaIuv5eJhePOx4rAcsoKDBW3TPdWGjpru076gTTqctEYg7q9tvyIs7egraT5EPngBce0RFh51lVP7PnEyJKo9fvT7f5RhnLB94SzTumD3zDaz63sUccRWPGAw9yHan866ZCfZhQr+0k/9DHEYUvkeZeBRBB61B2f8MX7usxNsUc5SgjAEfP7Kz/xlfBoJNJlMiOOY4XDYtJ/st1/kRNF4f/wqvNKDqm6IqfbAK6k9n8rzCPEJPR8fiABKqCZz/DCgKkuKevW5FZo/e+y6hISyHV2vPnBoZ6o9Il04qFy4wE3w0rNVVS2etfkjatNmywpHW7VJ9KpcFeWiSOKXZXOugULAev/rcq5BG/R1FtZ1vfbcAeUWSPpbj7nsJRWZELe3allVVWvbP+7GjeMyAmgWvXIpXA/1KAyZ1WWzSkqICxjhAx4zKv6Pv/N/8dM//dNQAnUFRQZRQLF3nXC8RR0mFNOMKIqhAqgp65IgDqGowPOb6z4QNCb8jRsTdsZjmBUwCBvNAw8CD9IC4v0FX+4/l+1f27+NPIckgqKiGABVSVjU4IfN7z6klBT7QxcREOFTpRm7oy3yqmk4pyII/cUGK9nIVu21moB8MIrc2K3jWkiiGX3vC3qnPTdC71Sim/BTNMfuGtXvFk8tfJmB1pmsAqlwM3OxsGrL9utyrkEbHIYRwOpzB3Sv60Rrs/0t2BTXde2vUrks7utgE4wADpxCri3o+R4MQygKKDzOk1BXc77rO76X/+lv/a+8+V2PMSMlAMo6Y+SFBNTM5nsMByMmDAmpifZt5yoMyfBIyUmICClJ8MnqkswDCBurvsoYEpD6ECDJUuPhUVFBXRF6IfX+taLer3Ho79ckrHIiP+IKJQEFMR4D/H0HYEFJRMMVamI8IgKoIfIjYi+iqEsqAmra1XjNpwSA6/G3tLWOfrpAPod1Kdir0ovdKI8F3SdBZyW8a06IwViTVczN9RVYZ3jbe48Kt8Q0EHeFA9VdnXUHxqpMWsjT6XShMdgJEyde175t6yiwiYG2BTBtSAugqEsmZUocxAzqAPZSwIewpgpKsnyPJAqYMOXa5DKvzKdEdc3WcMRLLz1Ngceg9tmOErKiYOZXzBOfaZExihPCtGRIQO17zCnJ64oQj0EOVZhwhWxBtGEYks3TpbyIujjYsmsXWZZlDIdDbng+oR8Q1R5hVkJeUgYeediEC+45e5E4TMgoKb2AD33/9/B7v/kvwQ8aPlGuP7fCOn41fjpfwGZfWtNQc94H3AKvwsGe5GTDtzZCpPdqs5n1a7iC0QoAz/MWuSn6ax2IWhO6fivONThxjUAd14KVWrezs7O0C1HPT6fTRcKRVDDtgEuSZCn7y75rXfttzMWGG9dBV6mpLhBudjHZDVh76QQ/aM5eCLMc34/AqyiyKWHkQ5Fz+YufYy+/zpmLZ9nZGUOWNSr7IIFiDmm+r8Z7EHngFTAewmQKftz87vsQes3fLIdwANenMPQhjhuNpG6iFNR1o/6rZp7nNe/0vObabA7DIcznUJ9pFnRRQl5BkkAUUl29zIsvvky6l/Ftj38Qzp8jDwO+8o1v8s5HHsWPx1RpSuQ3jHI0GpFl2WLLtJuhaTXEuq4XC9Xe63lNKLQtgrAKtLjUThiGxHHMdDpdqvAsyW6Zg0xPhV+Fmxa1XRvWrAEWh6fYiFgURdy4cWOx8MV4syxjZ2dnkZJvcyJuK9NA6oquW4Zgs+3sAm5LzAmCm88d6FLNrWfZbhkV0dhQ0k2d7xGLlb0pnFy/g03K0eS5zitrT1qNwPM8sqokByI8/KLGDzxm5ZRB6ONdu8GX/s+/x/Unvowf5HhbIWUxJ6wqKq+i8GG3zCErGPoN8ewVKf72kEmREXk+g9qnTnOCMKQMfQoqvBqivCKpPKb7XcnzfLFN2FV5Qz9YclBJNY3jmJ29ATeKFMKAYdCM/9SHcphQ5HDj2ozB3Xfz/v/252A7poi2+NVf/Sf8+3/2J6mrGs9bT9Bd82/NAUlU10wQWI3S0kkXCDeXPl387PvaTBSFFK0pYpnbuvatxtSWj3BcOHHTwM2XhoMwkTpktQY3M6yLECwTEOR5zmAwWHKmHMc0sHi31e237dtJ1KS5fbC4BHhUVU3gA1VNGdRUYcyUCd43v8aNLz7BC7/1O4yGPvM4oyhSEi8giAOK0OO5rCbxQ8IKijSDwCccxEznc2I/IKgg9gPmRc6syolHw4YY04wqK/DDZh7wD44xT5JksQtPjFvjIHVcauv2vKAYRlShTzlLoaoo4og8igiKgGrus/XwZV74F7/F3T/x73B9fp1/7yd+jHNbO1y5fp2uWemat7YQmt0haOP11qzsqxFarcRKdUlyjY02K7nPWfq3URGZr2pjVftyMNt2bB7NpuCWHHAi7uhKZ+sNFXNwPcZ9wDIBN+S4bqL7MgfhYdN6NZmWIWgCJS2tZrPufV7l4UHj4aeioGRezygmrxGFFWe2Ez7yw98Hd+802Xvsq+9lCjt3NCq6v3+t9iCMoKZR18sSBgMocqgKiILGDKhK2BrDpFm8DAbNtbpuTICqbMyAaj9DsCwbs6Kum2si+uoaDEOqOsdPCwgT8D3m0zmDYIdP/L1/SLSTEO/EPPvyM5y76y2UVHz4w9/Jr//aPyPleHX5ZFpazVP0YLdtC+ymqz7zbyWvS59216DAblpy32npQT4vC6val6ZmYZNMAE6YEVizwaqc6rC1wXSPHYy+C1WahGwuEYKIYF07fTQO19a05a4tE5LvQeEi9WfdO0qgpCaqPaghqKHyMubpHufO7/BiUnGdPTgf8+KlZ3nlmWcYzz0iP2LmV9wIR1Tsmx5VTZhXVNOUBJ8wiSmqEi8KyYqcwPMJopC0LCh8KEOfcWZUaZlZZUHtewtvfTxIlraMR0G4OBuwGsxIBwGpX5JkFXHlMfVKxmfO8/j3/SBFUBB4OW+65yJfo2DCjISEOIoo66LxLxwD7G5CK6Hl/LR2tj0nwKYod4G1+93IhGx2tW8Tjmy0wZqQonFLn23072YfyncgZrPJ4qYnrhG0hXbsIRzWQy2twQ56H2YgaSzmob3ctsrtUcHa9MJHE94W47Xhnj7vzgE/9KBgIW0jr4DplO1zZ/AoKP0Stke88o2XePKPv8iZPZ/IC5j7Hn5W4w9irmeNBnR2uEWSVlyfzQniiBkl4TCBstmf4OUl+B71MOZqOmW3bOosTNNmr74fNhGGKIqYzmZ4gb9EsFarK8sSvILpwCP3apKyCQhe9Qt2H7gXvidkUFSEaQ7bOyTkbBETUnPn+fMEeBzPFbscWdCisRqYPddAC079WPRhDbhMwM1atU5tt32X7rWA7WnX1vZ36d9t357LcNg8ic5x3FhLK8C1k+AgZGNtNusI0eS46tAqsAUzYDlD0Y1uHAWshmKZlg3/2Fiv3mX3FKyCej9SX/ngzzIY+Iy8kKtZDgRENY224AUE1zPG1youZgk+EZMo4K7ZjBvzOTshROMh09eusB0kjOKE63lKHPtUeUY1mXOmChnlUJcVZHCmronznCCryfOCZDhgMplS7BPhdtykNHteAabfRVEsPNnjaxGTuCL1C5LCw09CXh3WzK9mcG3KuA4YJ2O4fIWtu85RVynUIcPBgOLYbGD55GEbNrSefVfltnPYBa4UtyFOLWbXMem2b6/DwRFsXfTvmg+uCbxJOHZrh7GzLPLqZJsNpgXVV5rbAVISivURtOURHCa3QHjY/HK9V9qNJUR7zb6jjRkldY0PlF4FQfOp2XdQViVlmeN5je0/JOJMMGTLG1KmkJU+eVFTeAHRubM88O1/irNvezPTnSFPlzNejGuitz/I7nvfQXXPHVwNPfI4hiihxKfEZ5ZEXKdmEgVc8yEbDbjwyJspdreYxiHTOOSqD9cDj70oYOu+e7jz7W+l2B7zSp7ipR5hGTGuhwS5T76XURQlZRzChTcx8z0uT2/A+bOUftWEOIOAWZlSbyAOroQjSzc2MtB1tmUX2EiETQgCFuaHZf6WMcEyndl3ShtZ1b7o3/O8xbkMVnv5lvIRwPKmDOvht+mV+s3aTXBzeedVoDbt0VbKX1+12PsygbYQpHAVvm6VH9fccd9lHVVB5VN7ZZMdHIVQ56SUVEET0/eiAC8KoCpIy4IcyGqPqe9Tbo+44udMxyHP53u87U+9lbf/zH9A+Yef4Zd+6ZfIxhFv/8jjPPixj8KXv84f/z+/wnNf/BrBvCQaxcyDgBklnheC51HUFW99+9u556d+ins+/zmeeOJLlDQp4lXdxO4f/OhH4e67yT7+cT7zmc9woUi4RkYdwHiUUAQRk3HN5aAEP2MyDoi3BlCl3CghIWBapETjISVlp4uga/7dGohWiore3PkU9MkzsGFumZqrJLbbvjRIq/22JcC1tS8aqet6UWLNroskSdbifVjorRG0SVRNgmLQrlPDxtPVKRuOcj82uUIf3T8cDheLSuq2OL0bopT6KrCxXbUrIlAfVPfe5gKoPTiYwKqqFv1VX3WfjtrWWNmCnXZ8rBSp97fwB14JeQ31CJ8tCjyoJiTFnDrzINpibzhmFtRU9TXOeFMGsyvcSCrOvTLhXZc8fufjvw7+HsGHH+fNyVkuZBEX3/sOuBDy1Wee5MWnn+XVuOSpR3e44z/9Qb64NcObTLh/7vHwaxl3z2ueLa/Ch+6Bv/DdPLk95/knv0r9+a+RP/08f/jaU7z86BC+827u/dBjnM+qJmoQ7VEHE8I6J5nXJHuwXSZQ5AzyPYbVHng1kTciZpuRt0M1z8Dvzvq0tLDqAzeryi4Dseai6Lfv7lLRsesn0NwnSXLTXgS7TkS37vd17VvNJkmSxe5FWI5kbAqOpRF4nrfY8DOfzxeIypPrhlFWtWFDOjaLqyiKhXRXxqFtvy0H3U0ptra9TQUVg7F1++0mFlguN2bVzTiOF2cESkXTRilVytVzXeNzFPBrFvH3Ks3xBjF5WZBd2+MPPvHbnN09wzeYcOHuOxnunqd8+Qrz2OOB7/8A/iTj4be9mejRd/OXB/fypb/396mev0SaZaTjhN3z52BvD7yS7bNnGN055dyoYuKVnN312R1vQZ7z8gsvUpiMUMs4PQ98z2vCjoK6xquX+9AH+viJbIKN4uua8y7oQ596h/IrBNb5Z8+HtPTTNccn3X5f6NWK9cLaCff95lw5DbyQs/HVPgNtvaw2bKI223ZZtRUzsZ59SQh7XUzCxpeDIGA0Gt2E56pNTWJclpvDgZppk5vEQNaNz3FhJxqwl8544KGHCM6GPPau9/Clqy/y/Ljiw4+/B65lfOr/+1W2H76Hd/1H/y7M4Y/+x7+B/3c/zvkq4ureFbZKSMZjJmSUddXkIiQjhuMtztx9Fzujgp0oYLZT4RXAvIDKYxAkeF62eo6dxV7XjWv0MNAl9axEtVqk63hbBX2ZhV0Dbe+36rwWaB9BeNz2NwVHbsl20GaiybFhOVcfcHd3aYFLM1C+gXIPbNta4GVZLvYW2GIQwOKafdbGcxW6cbc5A0vbnNtSVkWAdhu1mJHl9G3js5bO6+XvnvnA/t6+NIcw4HI+ZefCQ/Dwgzya3MtnP/fH3P3ou3nlb/8DXv3K59m+9yJc2IVLe7x29TLxa5eoifCThmHOq4Is9KgpIU7gyjVefOY5nvzC17m3SMhnc17a8Xjf/MdhOKKsq9ZF1lfSb1KthQMbW3+h/0lM68DmI6g9m/uiMJ6lC6sJvt7t94VeK9VyLPe6JJwcgFoAkoxdHNFqA21hw6IoFv4BG45R4QfriJEU73PuwGIAwoO6/XYibOETq+7Lz2GLWWgsLB6CrvG5eUCcMa7bHTle3Sw6v6whDnn5xlXSV5/j/lHAjdkN7giGfPMX/wHzz32d3bhglO1vJMpmXPJz7nn4Tq5NU7yXrjL2QoJBTB5nZFXebEqKRjx670OEwwts7xXshgOeD1LC0oOr1wjHQy7nM+70ksW43EQjVsrt/+TbPvbgBV0Mw9Kg3cei8G4XdNGndlp63sGOQQkOmZaWFtWe3VR0ku3fUtMAVg+YtcFt9pYbFVgH1nOqzksyy5EHy/F8Va+xWV0iAjnybF6BHDUyaaxH11bIkSawt7d3k1/BmgLj8XjJFLEmit0A1TU+rWNN++L3aRZ/s8u/gdCHKIh4Ye8Kwc79MPTxblRc/9xTcDnmjJ8wu2OX7e1tiAYwGjG+/04ef993kNx1F8/+b/8vl/7kG6Rpzu4DF9h65C1c+8pTvPzpJ3jiX/x+kwuQQTQvmG1FlA9dZPSOB7iep3DxLDwzXczhATPooQ5DL0ZwGDvf3QPQB/owGjt/Ni9E/bUVinSvpZmTbH9TsBF2IuRsotCqDTernrXfreS2A2m5vx1ocVItPqV9qi0tcJuPbu0svcP6EmxxVJcJ2LyIuj5I0dX/bqmsdeMD3qKA0PqBar9c1jXl9evs7o55bnIDRjHVPGO3Cnnwg98JZc7Z+4dM7jzfcJGs4J2PPcbe3nVe/YPnOHPHea589WnKuuDR972HCx/7CNm1a5SvXuOHv/1D+GmBl5f4aUmW+Dx7Y87u1jb3/PgPcu/ZO3jxf/i7i7lpoiDLRWSW5q+rjy3Q5Sy0nn+bibpKi3WhD322XZNAsTiK3uu6XtrJeZLtbyqx6EiMwA62DcNZB4a74WYVqA1gEZPXIEiiW4efQNxQzMHdgmwdcTaf290AIhytMxEOEjasxHfv1ftteNLmDfQbn76j3g51XTMejZhRMwwimKUEScwr6R5/+Il/ShUFfHYn5b0P/RTvT6fgB/zRb/wO9XOvsBUmhNdLLhBAVeOPErh4jnhrxEtfe5ripRlZmjLc3Sa/PiHYGvK1YUby7ofYescjpPeeOYgY1DUVLGodeW7U4IjQReiaV1jOJ3G3U697vg+4OwLb8LQOS5cxnVT7m4Jbcghq225CcXGbp60YfFmWC0+7/pczTwNhtQ0bIbDeeUlrGy2w6Z52kuSg0TvddFWB+842iWXbtjvi2vD3/YBBHBFFCf/Nz/1cMzbsq9hW3Q6Wk5oW/SpL0rqkCGuGfgTzjCyB+JF7eNv3fhDKilEOZZ6Rh0BZ8abc48LUY3eyn5hSNUQ1r5sKxwQQJjEDP2QrGZJVJVUSkuYZW/GAcZRA5PF8OWkYru9DcDD2tQdlVS1UgLJsdjXW+30D+Gt/7a9x5cblRYh4FVHb8KQ+tmKPjbtbLc5m7VkJqzbWbQaztQLa8LHzIFhHn31hHeNQBqVt/3VnBAspwIG0tplTcLAQJdlV3lmOM8XdbTjO2tN9dx+6nN9qGHBzJtm631wTRfev+1ib1GoZfaWBFmJd7ROgDzlaXAdbtylLyrqm9htJ6wU+XhhwZmuHaZlxPZuRTWcQDzl710X+/F/9r3jmpRfYmdXcn8Vsz+umwOlsTlzUxHmNX9UU85TA9xs/SeiT53PqoGZ8dpedM7vsvukc5+67i7MP3sMd99zFHbtnGXoh5Dlp4lNRHzBRj/29CZ4GdeG4I9w/uJUD88qq9FabW4wLyxqX3V5stTIXLH12wVHab2tDtODi3wdcjVI4AEuCTe8SrpuMvGzERyDkrDPM5ue7dd3dDR9W3ZFdb51t68D3/ZuceVZaW67pctA2k+Mozhg94+LfJ+EjTVP2zwVZmtgwDGG6hxf41F7jHvQCHy/wqSioa6jqmnwyxU8C7rjjPI//9E/DvIR0zj/867/A1pef4l1pzHwvZ3Qjh6pZkAB12FQRDiufJIy4nM4ZpSnR1jbF9BLPvPg882evkZUFV+KKwWhEXNTsUfDwlT2IR0RFTRBFeEFBXe1rYHVFTYAX+OD7FFWFV1ewzzhLaiKPxmwI/KUybtaks2dVaBFok5d7kOmqiFbfuetqvw3UvvXcS+iJfvr6KawJ7Jqu1gSQ6SyH9GE1jnXQmxG0qcmwzMXsicFaDNp2rDYO4ucHGy2s+madbX02hhzmXIM28H2/NTyjtNEuHFyzweLfJ7QTRRFlUFKlxaJ2f8C+NlQ19RfL/YUEUHmQlQVBHVKyL03nc9JL1/jCL/8j3nnhL/Cvfv3jpJ//Bt9Wh3hUZFXJyA8bJkETBRmGIVQFu3HC5PJ1qoHPOIrg2oSwqHnHI2/h7t2MYBDzSj0nLwoGBXhJxHj7DLx6nQtlwJWiIPOb+gVNlaOKqtpfACYXo/Eh1PsVkg8k8SAcMJlMFnMpUC1/jZHMB9GStNBNJGX1bb+NluwGN0uDCj93QZtwslED3/dvOtOhqqqlsx42AYfKLGy7bh01NmFGi0GOPN2bpunCLLAOPri5okwfO2tVcsV4PO51rkGe5zcdUAH0TtiwTiG7/1zXu+y4Is+bbcEeizBmRdUQ13AIUl1lhwP4Hn4YEcYRk+vXedPuLtfLjBf+6AlGf+cf8OynP839RcJwWnLVq8jPv0sokwAAIABJREFUNu3yzCvw4hUGWU1Se3hpRVFM2RoOqQYe4ayAy3Pqbz7LNz/9BV57eUJJzV4Mg9GQ4saUkpp73v0oXLzA7HNfp6gasyCMYsI4otr3Z3hhsI/ngXYQBMF+wXMo6oqyrJkX88VBIBIaYs5irFqkVVUt6EuFTq05d1RVeV376+YdGvpWnotNSFP4uQsna34o98EWL63r5oAXtS/hNJ1OXx+NwAUrxaVGu4dTWA6mTul/u1lHNpY48GGTJJRcoQQiONiOLFz1Lvd/d1+BFnLfrC3bprIgbby3L4RhM47lLCXcCgmisClFBs1CqkqyIifPc9I8J/BjsjxntLvFpSuX2draYqeCpz/5GR4ejAmu7DEKBkxGHpO64N/8s99m9998hvjV62TPv8o4GrHtxxDQ7FMoPb7wyX/D3mc/TTQrCJ67zKiMqAKPuqqo5xO2w4isLPj9X/4481/9OJeuXeWReL/qct0kchVpST4IF87Osiypi6IxDTwaH8E+b6yolwSGTSjTBi9V4pH2OJ/PgYM9Bets+T5zYKMMq9pfxWSsIHQT0iwtrgOb3aoKzq6j2W3fboDbFBwqs9CND4tzDgaDJTtfYB0aNkavmLryBdytnbLb+jCEPucarBowG2pUVMEyB7cU2Spw8be5DF1QlSWFX1FkMwZZAMOEOfNmbPyKvCoXobggDIkHCcMhhFFMVRVcJWMwTAg8n0Hl4ecVcV0wjBKKoiLLSmK/Zv70a+T1awxnNfcmOxRFwaycMaojvCigqgqyVy/jX4LdOmIr99n1Q0o8/HROEIWEBFyeT8hfuUwY+Fys6oVfwKubMmheFRAlIUUQNNWWkripb1jvRzk4YMJyJOrcCtdfI+Fhx9GG0Gz23lG1gel0urZ9bW13wTo615270UU/dnGr8lWbcBRjUl/X0fVR4FjOwrquGQwGC2ZQluVS1R55k934qJXIbnxU99sIwjqw2odCLFmWLQZO/gN30ESEukc17W1bUsf6jIPwVKJQX/zxICsyoiDYP1+gkUxJ0pQXG41G7OzswJvOc+edd3LmgQe472xO6MdU6YQXX3uBxA+ZT1PiIASvUaW9oiapfRJ8ztU+bwrHlDf22C59oqoi92r88YBk7nN5NiHYGXA2Sbhx+SqjsuZcHePNcoq65Ox4QJaXFEXKKIip/YB8MmcnHpLvH6925tw57h+dJ6wjtrY8Ll0cwM42Z8+eJR2OYF8o7Fde2J/jpsis7F05Z+1JwgKpzEVRLBbYfD5fudCsxroO3BwEt32FGVcxA53JYPG39NMHJCCVWSgNWkzAHrbiRsg2BUdOMZbklHPMVkxxuajutT4AaQM23GcZAtxcmERqe5s6KBUyiqKl7cGrjoayHFXbPK15YHGzTiPXgWQjD2782B0DSTdpII36CL4fUtYlYZbhDQZkFGReAX7NPCi57x1vhhCq7QEvZXtsE1KXM26EcG64A1VNsLtLXpaQ5gRXJiS5TxV6zJKQq15OmM0YDGIuZQVB5DPPM8LS40pY458ZM81T6knJeDgiSwumgc+sKigGIVcHOeV4wN7eHnckWwxnJWd3zxF5PvUAkjplGMYU0zk5BS/UKfVbHoDE444H7iUtPchL4jjAxwMP5mVOWQaMosHC+aV5saaVaEDVoFQdWl7ztuxTO95d42/pB1jsCbG1K2zozobNbcjbFsKxoW+rBVsat/RlQ4/2sB/dJ2ekW2fjtgsfrgMr8e13TbatJgPL6ZRakO7Jr3Lq2D3+tg04OGrdquxtIDPF7lTzfX/JWWh/s6WopKJZvGxOgecd7IgTIVo8yrLE9/adomWF8oh94VvVjAdD9l56GeYpVVFCWXHj+nX82qMeDxnlcDmf44U+WZFRTKckOwnT8ZB8K+Hs/XfxypNf5bs+9EHuvuteXv7SV3jqj75AvdeYREnWnGeKHxA9cBf3ffe388wrL/HF3/rXvNUbEu+llKMRb/6B7+Xse95J+gdf4DMf/+cUVYq3PSSeptzIbjAOE/KiZp7PibZHJNvbUFVcunSJwdYZYF9A0GxAGsYJV/MZdbQ8L27+xarwr00h1zxqPiy9dY1/F7j0qWfc7NRV4OatuM5Nu+3e0n5VVUv7baSp2H7cFs5CQR/7TGqOm42nRCM5Qmz2FByoWG1nwcupo3Zc56CND7eBxbntiGnrLNS91hegfrgl2W2CivpktzS7adON72w/ScjzmtAaTflC6ubQkpeee4HHJlNmN/ao5hnVPMPzw+Zw08vXiXYSSt9n98wZorvv5Jn0GvmdZ/kzf/4n8O9/gPSf/GPu/th3w513cvG+83zms59lPIooq4qLtU8xnTMcxrx46TUefezbeOe7f4AzD93D1//Gr/BQMuSFKiN554Pw/ke4/tWvMA8h2Rnx1ewK7ywiRoMhHpDOZlSEXL9ylek3vsl7rl9n78YN6uCgmlNAQJXvR1WcbEmBS+BtfiDXJrf1IjS20i7Wjv8K2hBOdn7FfFblqawC9102yc4WqbGlytqSjOBAK9g0IzjxFGOpY9ZpaL3rWZYtJsrdhuwuvCiKFrajQiw2F0Hvk8e2j7PO3qPty+LGAmsqwHIVWlvvvw1/t76BJVTf9xebibwm/xb2tx37NEyBvCSdTPG8gMmVa8yu78E8p55nTK5ca6olTWecGW0xHAyYJz4f+89+mh/67/8qX3ziCX7z5/86L7zwAs/4U+od+ORnf5869Dn31geJHn+EvTIjG4bMghovLfiT3/7XcLkJG55764N8w5vzyrbH08MC3jTgalKzddcdfOi/+Is8/OE/TZqmzOdzrl69ymwyJfJ8KErSvSkMR1x+7RJ7129AXlDkOXmd40cH28nbnMxaiJKSln6sFLU2vUs/0ig7x38NyAdg59cKGBtRWPXRu2yavWhHW+VhOSNXf8UExDQsHGYfQx84cdPAZtjZ1E176KR1KMpZAiz8D7YoqbXzNDg2fq9YtCRGl/om3GSHSg3TIrc2ns0aE0G4m5csUVtitRLMhhgXjECN7GsITakvnzLPyadzqD2KeQpZgV94+L5HWabgB/j4vPT8C+QBXB573H3jChe2hlx95TWufu1Z/ItnuP/cRfZeuczlF17mbW95C2/9j38SLgTU13+Z3/3K5wjObTP2Q57/vc9y/dmXuPht97N970UefuxRnornbN9/J3mZ8uZ3PQqTCPKMx//0+3npD5/n2VdfwK+a49tuXLtOvktzytL1PULPb4qVRBGDwYDIi6iykp/7uZ/jb/ziL+AVy1LNtesl2VfNr2XkdrGLiXSNf5/womUw9tShPgvR+rSsb8D6zeDA1BSzcbViPSOm11cb6QsnygjUMXUADjQCOX6kGtnKPuKk1gbUPQLLId1ohN3uu26y3dCUfZfNhnQ1E6u+WSei+maPPLOnItlU0cVEVk4yjAp47GsHVVaQpSnkxeIcQ78q8aqKZBAzu3IDLm5x96OP8PB3fSdXnvsmo91dqHNeHVTcGPl88IGHYRLw7O/8a7JXr/LWv/KfwH1n+NV/8Su85fNf4MVwwjs//J089pEfgD95mr//P/8vPProo/zGJz5JUMP1Nw15/P2PcO/b38pnv/QFnvz4rzPwAl6LS951aT+L1I8ospy68om8uNFoBkOKPKfIc6iqJqORgiQaNBqQf2BarvLKd82vFoadEzerde34d4ANcQsH0WcfRmAZmEDMwQpGqwmobZt054aoXZo8LtyS3YcaOIE4qj0mynpmxQQ0Wcr2sh5T+QgsEbnxYLuzcZXqWdf1wkdgn7c+AjeaIIeUrSYDB8QiJiWGYJ/V84v/vX189KPDs+I4ZpQMwA8Wh5oWWU6RZlRZzm4y4jIZr1xI4LvezdWg4PK1q5D43P/Rx/mx//ov8raPfRSevcwf/aNPcCObwdkE6owXnvwG9Szj7GibT33qUzC9Au98kLf80Ie5SpOW/KYsYHR5xh0kMG3MpjrwqDy48/wdDZHG+5Wo6pooCAlrj3w2Bz9gEO2fAbm/WEPCRYpxH+rrml87/tbzr3HuHP8OsG3B4TY0Cay9L1xsnovFXcJFGqql4ba2NgUnzghWeX01GFLzBNbzLm6puu7W4aOFasOP+g1YhBG7bDjP8xY2vx1sMRrXUaN3hmG4VAgFlrcIC3+bS+FKkbIsG7eA13zY96gvtiHTMMvQD5pc/f12oyAkCSNCv5EKN4KKl7aAe7f4pj8j3B1DEHJua6ex2dMc/uSrbJ8/S3VxF3ZiKGvqJ1/EC3xGr0548ErN5z/+z/nS7/0Oj3zvd/D8jctshQnnh1vs7u4yS+cwT7mwc5ZhMuLHf/Kn+MH//C/x6DsfWzjklPsQBgGDOIH5nDzNiIIQ9jeHsd/NwWAA+XJOiRUCboht1fy2hdOKolgyJ9aNfxdIIxDtWg9/H4bgRpXsu2U6uriIzuRDcHe1tn0/Lmxs96EbGrEqnxtrr+t6cWy5LT9t1R0bnouiiNlstuD+1kHoMhENzmw2YzgcLtQyEYFwsJ5+mxBl00NtvxTHdlVOvdP233qJhb8STtTHRXjUg6KqiHyvSSiqq0Y7CBqzIQzDZtMRy86pqtaBpAFh6JH7NRQz8p2EaZHBvOKJf/ibPPP0FYIXrnLn2x/inm9/FJ9Zc1ryV57hzHM3yMua+6Itpq/MefG3P4v/vod59CMf40L0xzz0oz8Odc0DF0LGH3gvLz/5NGfLmB/9iT8PZ3dIv/k040ceYfDc10mnGV5VE/g+2TzFKxtmFIfNuEkjaDqiGgUH2qLdr2EXmLuQ3PmFA9+AfrM7X+1isUzHaqmu+dgWCWtjGn1MA2vrS3DYTEHdIxpL03Rp0531C1izx0ZCNgG35OxDN3aaJMliYVuHnKRKmqYLb6/yBYbD4VJIBw4cfa7alqYpw+FwafD1m/IDxEj6nDugaAKwKKQRx/FSZps7Kfq/69yEOggJ/YgsmxJkBcFoSEljduA1+fhNg8vjWu1rEWFaMsihSmvwBowzmoNO85JgL2X21PMMLs94fuDz+I99mLeeHcG8JP/0l7lvFhKHPtnenCSJ8byC73jf++HJZ/nib/wuzxPzyqDi/A9/kO8Ov4eL5+9mfuUb/O4v/xpX0ynlzoB790rmXgFlQVp4bCXbDKKK60VTDTmQ2VcUBEmTNDS9MWkSZyKoy2VTTXSiv5apts2vq0HYsd/kQjkqiEY15zJXdC6G7bcyYi3DgmVfm9rcZHox3CLTwHX2SAPQDi9JYtnrGjQt3CRJFhzfelBHo9HSQNrwnjisnhHBKD9dYM9lELdWm9PpdJFHLptU6nmapgwGgyWTxYL1XuvcBHsCVBiGxHFMHEQMIo+dnR1+9md/lnyW4uPj2wQq1fnQd//gffG8ZMuLGNcBvHiZ3TnElQfbMeXFHa6WKcM4Ib16g3w2Z+vNb4bXLvO5P/wMflpAXsIo4av5Vc5++N0Mvus9PP3UV4j2X5oOAr77Yx+DeQVPPsvX/+SrXHzzg4x3d2CScuXGdSpqvChcpJsvzL00XWzcIUmo6pqSktH2mJ//+Z/n0qVLC6ZvPwoJWufxqvmVtqdxPeznpMHmOUgoql9Zli366yZCyfTxPG+heUpY2vWyKbgleQQ2BGJrtNsdVYoQWLvPFqmI43ihvouxWI+uBlp7HGC5vJNMD5tfoL86d0C55daBYzm5fAf2yLN1BGWdoEEQLKl8C1XTa5KJ6qLRhqJhQk6zILChT+cVjV/BwxvGXJlPqKIA7jjPPKhJBwFUKecfuIcf/ck/xwc/8j1MKJjFHvglL7/wTb58+QWubAekIaQheHfs8PBHPwh3DPnjy8+R7iZcqVI+9P0fhTsucPmPPsf//bd+gT946gke+oHv4KP/3c9y7r1vZbi7zSzPCKKwWehl2fgIBoMmM3I8bsYnTffn06MuqqVxsYlAWthWgKyb39tB6nfBfD5f4GptfjdrUSC/iI2SWP/Ht6RGIInoTqR2fdlkIC1CEYEkJzRqk5yGsruhGbTJZHITo1GhEWsn2piue5R6tB/nljpqQ4kyX+TbaHPwrAIVrrD7K8qyOTehrmsCP4Cg8aTPJlOoISZke2sLVlTKqQ3dvJgUVOfGXHjoPsiu89gPfS8PvOvtMC15hG2++a8+yyd++zd57498lDvf+w7m2ZSLH3gv3/czP8mlB3a4Omg0jZ3M44lP/j4MBnzbn343l5OaD/zEn+H+H/tBPv2b/4x//Gv/FOIQ7+Iun3r16/DYvXzov/wPufuB+6ibmupMZlPiJCHcz/3Aa0rRl00dNuJBgs9yWTLrbG2Drvm1TON21AhUgUlCT4t/MpksFUQpy3JhGliBJX+UEpsUlt5UURbBLTkNeTqdLiZdHRiNRjc52qwqKCneloQEy4eVjMfjpbi+KhS5hGazuwR2l+Cqcwcs07FnKPTJNdfuMb1Ltp4KV5RFAVUzoeP9jTcFLSfsrtACX/Yz7rhwL+9822MQJQzedAevfebzfOOTn+XVT/4xo6spH/jYd7PzgffzqX/861y+fJkf+bM/yT3f+2F+/L4LvPo3f43JZ59iPC2JrmYwKXjn+97Hi7/1h9zxfd8Dw4h3Pf4+3veuPwXVAJ59jiduvMzk0iuM77vIm3/4h/j63/5bzG+kbAf+woav6/2ToTSOVcMIKyBIQqrpvobgDKHruOua302H0TYN2gC36lwMqyVqN6zALnRpoG20uhE8N9bSCqjrg+xAeUBheWeh1G5XO3A5nrz2ciSKkdjFKQ3CMhgxFRvms5qC7ms/d2A5LVqD7xJsW7/h4FAKtSPmJKfoaDBkmk+asJu3n3Jc1xRp1hyLXiu5yGTcGb4wHA559cmnee7Xf4973/Mufu+XfoUbX3+e4sqUnTO7fOef+xGCd72Df/m//yJXX7tENptTjh4k+MgH4MqES089wzAvKeqKO++62EQuLu/BjRl8+Sm454PEhLz2lSf58i9/Au/qlK9PLxNcnvDWP/fjEEfMs5Qoz0iG26TznCra300YG0fqvkQvKamr/ey+JLxJMrtj2md+b2fQXFtc3c1FSi6y+ShutMtqP3JWb7L/t0QjcLcC23i8law2g8odLGkSrkoPBwk81quq91mvs7tV2YY33Y0tbfkDbr0CG41YBxYP6zAFSOczaPJtmr6VEIWRHuwc32FWMyoLnv/dz/Dcr32KN139/9v7thbZkuy8b9/yVlXndLe7Z3rcLSF5jDQINTIGYz/YEjT4wQL5wdgvfh/mQb/Av8MwMA96NcbYCAajsTSDLyPJEmMwGGMLDJZGM6Ldl+nu011VWZm5b37I82V+e1XsiMjcO/Ocnsl1KE5V5t6xY69YsWKtb61YUeLt+xL5229h/c7b+P2//J+4/ZP/iLfef8A7dwmQT/GD3/m3+Pkf/wjf++9/ir/1eYMcKdavzvCD9/4v3vmz/4Fnf/5XePbD9/HffvJ7+OC7v4cf1XeYfbzEL3+a4vqzDX55muHZd36ATz7e4IO/+HNUTYMn8zmw3L/rZrMBHh62Aix4QIJkV6jVxTdXGNo3vi87WYVlZVKzHTW7ENhX9uL3/MxWAhuDRjkEFThsUNTc1yQPmkb2BV2xekVY7bMZb41ZNdgPPd9Q39H1XtTK1qKgUtN7VJG5fOE6qYA2QdZmqCcFNtMGn2GDMpsCqxxFdYW8aYDmFrPqczxJU6SrBIvsCvVDgiwp8XOYAT9+tmtzvUix/uQnwH/5Cb4qz9qkAJoar90Cd//uj/H38DwKkQFPbjd48v2/xOr7/xIzAO8AAP4frgFpowaKBJOmBX74ET744XfR3jf4hTdvsG4fUKxXuK4n+LRMcFclABZ4fTPBpC6A6gHVNMGk3gCrCRbrBT5NE2CxF24b5/eNmY4v77HkUvQu6rt3LLIWj91TY7/j+9v7lC/8fax+nsU1UNOaq66a5jq5NZFIQ3MuU36XaSeuB2OuvE4Z5YrPkjR6oW0Cj+vXxWaVab/p5ljXpgUHvAXKGvn2jOKtm5A2qKYZqnkOLArc5S1uswavXBW4q2t8WleYXI2LHh9K88kVPmlX2DRrLJIUTVpjuZhieZUDixQfTxvcTABMU5STFE2Wbn2bWYpq9vy9A3Qs/zl5NFJkXb9TWxVpui+3r4lPY6P+Q+ksroGa1rq/ANhPFD3LQFFgV3YYASnGkzUpye5y1Hv7wjRqnvFerRvPWnHquhDvOCSEpWDnzvrY70RGjgRpA8zSBA81gBZYbTZY1tv6hZukwbJZY52mmCU5Fos5Vs3a+axzUdsmaOsG17M5rqoEqGrUCfDQVkC9xn0BpBMAqJFkQIsGSGps0gabSQJUfteKY+3jf4jYvuvcjVNjDEyg4yKnVqdumnrRbs7JFQGwrwfXtm3H31M/xx4cygFKU/+5AwxP2trvzObzrR40G0N147VGPQdSU1xtm0oa5mGpbpqG2wKlAJ6fb7zdU5CgQIuibICywWIyRYEUaBJcJxmWdYNkvUa1KZFfL/BKO2wIh65LV/clqhmQ1Q1mz0qkZYKbmymuywTIZyjaBEndApsaRd0CKIF8hmq9QlOnSJLHPFRq2/Yg/ltSGXOdu3HqCcjwJ0vmsy9ah/BloLMoAl3pmbTDeDDDe7QctNIM0VLfuQMMT9Lco9vB3IU+zU+BYCiTCC3v0brxd3d3nWIYTdPsUlxDKwuVAAuqMBdil6eQpkC13X1YNc9dmrpC3iZAkqPaVGg3DbDcIK9bTLMc08UMTdZglVTIymGpIEPFcJEVWGUtynKFNN0KeNYAxboGmhyzOkNeAiimSNf1tvISWiRNjaJtgx1gqLWP/yGLgOPTd+7GOSaihj9pvTLF3hf+PGdU5OSKgIPFmC93DdK01wwydQ/o32lePvD43AFVIGyDpHvIeS3/JpNdWY4aFbAmnW2/b7DUP9UNJtpWURRoaqBuaiQAPl/eAxmwbttt6K18wCoHkusFkGUo0xQfY4M0T4FZjvsCWDz4FUEwojFQzm6XJT6/avCAGveLHNOqwl+hxia5eX46U7rNiigmqJEAaQFUNbI8wSzZW38+8/gY/uuY+87diHEthhAxK7tFms//mbEIFotFZ/AU7WTqJTWj5uLTbOcqTetAlQMzB23bxA7Ybp+AqRLpqxvPtGcShUddD7blap8bmlT5aFtPijlqAEmS4dnyDhWAKkm3IOGHd1hmGe7bFuWmwuK113H9c1/BZlmjmU1wN0lQr8cLIR1DVZ3i/gnwrL4FmgLTZYt0Msdrb78F3K/RJBnSYormk1ukb30FK2RIM6BJMyRbPeElLTMPxPOf/C7L0nvuhsrmKchmAFKp0TVw9f1F0MkVAYEdbuPVtElOEsb0ae5z0wl3KfrOHXAlo3B3Gu915WYzr12vdYVo7OpBV4QKri+PgJ9xhyJNQQrfzjXCdjYkRY5ltUEFIEWBpE3x2Wd3mBQLJGWKYvoKfuXd38SvvPtbW8d+VgBZBYQy60I+cEgIQ/ffJcBfmwLJEkgyYJkA6QK4XwP5NWbVBEkyx/s/+hDXX/3a8wNSJ2iaEsm6QbLwPz/Ef4spbbu8370YOndjzD39LqLc6UHAxJ+0DJp9h3PToCPPAHTMeF3tacLTPNaswLqud8ANqxjTR6cJzd9p2tFa0PMM7SqrUQNq276TbTXvm9dquFEVh4Y10zTdbYnWXZL6v/IH2Jqz9Gn5bry+RII2adE2Fb7zvT9AjRoFMkyKaxTzV/DpJ3eYJVf4zrf+FeazHPPn/FjPCqxmGablnTMaQuL4EHG3ORka3iJvOXGY08FICg/90KpSN+sbfDxZYTnZYJplmJcFqk2Btskxb3Lcf/w5isUN3nzyFaT5UzwgR9YCq9sHPH3t6Y6n7LOt4xfiPz9TvmtbvM6m7qqy18iRgsv8jn3RvSp6PUktDI1uUG7JMy46fE/2hW0rRkayUTH73kNpsEWgWYHsnCYIqcZTptFkd4FtygBN0dXnAfsqRzqouuefJv4pkWGr/FQhUmCs5qclMp1O0aTchQhslnf499/+Nn7rH/8m5pM58Atfxdvv/Br+1/sf4bU3XsPq82dAkmCaNJgupmjaCtPmyruSMHWbhV/pr3IbNeoGs+cCtWA67/MxvHk++Sh8RVEg11Be2yIvn+DVa2CSrrBZPwDNFFfTV5FggmpZ4fqNL+GNX/0a0l/8m0AzQb2pcbsqcfP0NWzKDdK068/b3I7QKmnD0DqJFXhW0me4Cp9wPDUL0GYz8nstHqK5LJpk5iNVGFwQ9V7Lg76+D6WkPXKW8DaaWpoLzU5yYKxCoGsQk5kH7PPNVQlovgGvt6vJGBtSQuzhKsHB0gpKGh3oBcKup2geNkjT7SlA+aTAH//pH+Jvv/OrQFUBmxXqP/vf+OT99/DG134JaBug3gBXMyCpt3/7+liWQJ5vgbsk2W5tLgrg4QGYzYD1evtZVW33GazXz49eq7afl+X2en7ets/PMmy3EY8HALMWSFdAUwLZFZBcAR/d4r0fv4c33/gS0l/8G8CTV4DiCp/ebdBuGrQoMV3kuJ7Ndxuz1Je2Kdl9pNV7uJpTaekYqlxR1hSLUqyB1qzmfRBD0irIfH6f3B2aH9CXWUtwXHMO+JyxFrnBikATO+z/XE1sKjE1pkVtXYpATSEdPDV3rdnEARxDGcSyh0JozTXtv5Zj34GjeQK0QJ4k2zqCAP7+b/wDfPc/fw+rcolZkSFFhbZc4dmnn6CpVqgToCmACi3S6vEONGtBkVe0VihAdV1jMZvvComoEmvbbeEMhr5o4QBmIqFCmlVAtkFdlairFFl2hfnVK7i6ukGGBJNkjhoFgCn+4ocfYDad4upmiskiRV6iU3hGZUQzMvvIZzGoCW/Tkdl/lSOuyLSaaFFwVXat8n0yR5mMsWh0M5pakrREFETXMVbXZSgNVgTUvtpRfTkCOpraqenGdnCsaQTAqWRogQD7Saj3UdEM3ZhopU3CAAAgAElEQVQRDL9J//m3jQxopRn9rCxLrJoN0qZGnhSo79fIJhNsqg1+7e/+Hfzuf/g23nrr51FihRkStCiRAEiRokaDEjXmeBp8B9ZIbp//q5sak/T5Lkps05yz5/uBa9S734HtluimaXbX2/ZS1ADWaPGADECDHC2mqJEBSLFqV7hKFrj7/B6fvPcZrq9fQTHNUcxazK8nmCSzzgGkdiEJjZ8VX4svkPoUgWahqvwy6qTFQ3i9ji/73jdRYxYiXkcAW/E13fXqUjQvlUXAWoP60i4Nx/uogbVQo5rWOplY322Xm2+AFd7jcz3OQUxf1eKSNC/1/R7d166Rtts6g2k2Bcoan93e4Y23voKyKfE7/+Zf45//k3+GKRok5QbIMyBp8PlmicVkgbyexnXQPL5el8gmBVA1QJ5urZGqQjItgAZoNhuk08nuvma1QVoUW/eibbfl0hJsS5gVLZCtgKxGXbdAnSNLJ0CeoUaDDz76AOtnD8jKHJPpFW7eeILpVYumXgFY7FxLjdJYn7+PyHMFqEmaPdjnGnDVVcXBcVMrk/fpPhFOUJX/IaY727ETXOXbuh4vjUXAlyfj7KRUFN61/dLGga0i0InvakdJtaWau49eesQwjUuINCrAXASW7KJQMXTatBugaZAkGVA2qKoGeTFDOpmiLRIgTTAtZvin//Af4SrJ8eTVJ6imye67dPN4x6TSN77xjc7ntJK+9a1vbVeb9WYnxGma4utf//rOgpvNZri/v8d0OsU3v/nNjjKn+fzbX/8XSLBCmdzjIS2RFAVmyQLZGtiUa3xe3mF6NUVWZfjS628im86QzIAkW+HJbALgGsAebNbIBhAu2W2tRrpgGlkCuhEIqwjsWDZNs8sEJQCoSW/KT36vxWpc8u/rv1rQfJb+rXKjuIa6a0NpFIsA2JteFjjR1ZzXubZaulwDbR/o+nyuEI4t/UQXofPCByqBmOsVYVbkl4qR7Vi/EwDqzT3SvACaFm0DJNkE9/cr3Lz2Gtq2AaYT4GGDRZshQ4k8meA2rVHheVg0dQiCdPmPfvBfd/2ja1aWJd59911g3WwTeur9fd//kz/c9Z9uX1VV+I1f/3WAuTfZ83tS4I/+0/9BmpVoJhus8woNWkzLAq+0U0wnOTBLkV9PcTO7RtpmmN7cYNks8WSaoKqeYZF+2QnaURmFqvAozxXL4CRWrIpjpTLIFR7oZrYSW3EpC52ctlCKyr+1UHxE5coS/K5p6QIlx3INRksoUp8L6Po9wD7GX9f1tjKP2QugYCDJTkJdHXTVpW+lg3qO9FFX3xSY42d8T7VuditTOkXTJtuTgdPteQVZAeRJhbqpgVWJpm2wBJXoPVCLaxSQs7/+5M3ds5hl1zQNcLd3qxhOTNMUbz75ckeB8wfbVIutsl0/D5NVLd78cgpgirbdnyiku0eZODVJExRFhgIl5vkUSZNgmr2566ddGLgoxGI03D/AfBVgX9xU5VAtOLvScyGhAmREjNEC3YuioKK6hYpzHOLD0y1yYU4kBU7HLtF2lsxCrfzTtvvdgixHrszt8/tjicy3DD0VqfAyM5L9sKY0r9NMR9fgWz+ZkRdutnEdCNNH3FClfiWzHauq6lTAqaoKr7/+OlarFRaLRee0YT7XIuJPnz7tCCtXSwXcqBjsxObq7KOQ6atumZbwYvtUMH38p3yq8qASmM1mTkyAbXPyK44FdAHLGPmzER0FCc9Fo7kGfBEXIOMykVzhQPXRdNUMkeIGvG8soDDEHhsFUT4oPzTDz+UD2ufwb6ZWa1ryIUS0mXHwLNvu/Pzwww93adp0pyaTSWfiElHPsgwfffTR7vmalv30qT9q4Xo3fq7v2Ucx4mlXeWCfzxHiPycz76Gc6kYxUl9okPKm7u2hexhsCLyPb5ZeqqgB/+9TBLp66bFPtATUlNZBjVUEZJyivfP5vDe9+Jj3jL1Wj6Oi4E0mEzw8PHQSo+hPctUE9olTaZp2TlHS97y6uuqg5LFgkb1W3TJ1o3Qi2R2ffEdVtLFg3rEUOxkspqSyGOI/0C1Zx5R4tkMZdUURqKQt6aYiH2k/NZcBQEeRDeFPDJ1cEQDdwh6k5XLpLSxhk5D6SLd48j6adepqHEsx99N8VJ/TJqhwYuvE4cYq+wyegafvzt8tGh4iKiYL1tJcZl+BrWvz6quvPjoAVhOQVEg1PHooxbpsIddHk3+apsHNzc1uoeGi4+N/URQdkFnHwu4c7KOrqyvc39933DwCjiH5sQo91hLQ+8egs1kErviqxo415muBoxBpuWtXtGEIHbIiccVwrdbWb9WSbVqTj/e4stJoXWi8+lCiciXv2Y4i5WpW8xq1QnyAro9i3QGlQ9+xrz8+/ivRleJYqCWrK7/9Xp/ftq3TtfD1WeW+TzH53msonaV4qUvQaZrpSqfM0MnloyRJOok8asJpFOOUROvFZq/pe+leDAW10jTdodLqn1rQqC/jLJbUvAX2Jr1u6SbZ5BV1Baxl4CILCLp+d13rohiLTDNXgX1pcB2TPv6rS8qog+v5tPqU/5r7wGv5tw2b95GNlFDpxr77WHQW18CSph4D3WOtdUBjrQILMmq/hlKMj8ZnMTffJn9oO+wj+0w/0+YX6HN5D5N8DommuFYVhrxUAPnDCWLf2xZoAbCbPD4e9VkBY4KFJLXI+tpx8d+SprArJqL5LzbnQb/Tz0Kk92g17phFUN9rKB1lEehkt8kblmImpCKuGv7hdxaksX1R4IoTU2PHJIsnxE5UV39VkfEeVxqrvl+foPC9rIlp22GBE8UJQvzt+15XS7af59tzGmNPc9L3VYqZSCrooYnkWjVteFYnj40MkA+uhUotHHu9TkTti/7N3w+RHx/xwF9ty9f/seioGBs7BXQnKtAVanaU57ZxQrj83z7S1RDYg2VkshWgvgHUTUo05dJ0f/w0wSZOchVyX//5t+If9hk0JUkUuphQoCo6xVFoRVHw+3702eSdbrTR9gmejdm+joNmjcbyJzS+5ItrAYiRMY6b+uS0Sm3UxkUh+eFc6ftR14IuCwDnQnZKOto1ULIAjFIni84BFoa2IbMNAmXWh2vb1nnIJNDVqLsXPhBMium/CiHDl/Z6ff4QMEknWYyghMCwU7c/FGyLGV9N66aPbQuV+iyCMcDmWDDSRcpvgriW/77+j0HRKqfvoZqxpWa9FuTgEeC8HsCuhp/rOUrMqgPQ2VGoq4/WJaTlwNNy9axEJtYoqERBWa/XnZWeO+JC/behOGtC1nXdqWlPLMRVgKKPeJ8F7fi/70d5xTg6+0k+EIjkNWO2T77YWDv5FOJPaHyBx+nJajWGSLMFOQG14nSIQvITsgj4XPKEfRhzH0EMRVkEeolqOGW+rRtv/XZfQhHbVUG3ZM8F0Amnqb1K9vQkPseCMb6Ek1D/bYVdrdpM3GBIwokSV5pDwFTrwtmEmb721bcf0j7Nfo3mHJKQExpfKjEqYyofWhsxGMHQhLSYhCUf/xSjURDXXndKi+BoRaCCQtMN6KZ7UtvSfLNt8kdf0g5c0/jPBeBgaZYir1OT3aag2lLnqo3VVONPX/81ocWmGPN5fC91F2JTUHVzi63UFEOhFFpbLKZtu/UjhrbvehcFBwF/ii756BtfoIsl9YHWfRPJjrnKYYgP7E+f/MQqAquYLI50akUQFTWwphewLzDCire2o8B+otpVM8/HOxeA7ekqaVN3NXdb21FwRwVRLY7QuQYUFGpzK1xawcnG8mM2DSnYyZJeNJ9jwoi2/6p4uamGwsf2Y/sW2z4/V8WrE9zHn9D40jXTdi2Y6yNr0VAGuIsxFMILyU/MRFXFq4vqOcHCQU9q227deA6Wlru2O7KSpFuXvo9RHCA9F4Bt6Apk26d5xrMR1RXgLjErjBRCjSrQrA/1n+AU35vCTiGmMiTYBeyPbovNByAvmJSlSiz0o4g034MhSJtKzPLxujIPbV+TewgkElfiZPPxJzS+WhCE26lV0YdIx5rta9gv9P4h+QlhLNpXWr/AePkBsTRaQhG1uS3MEMp+0zCRhpLUHKZSUVPS/u46TVlNLvaRGANJB69pmg66HdP/EH+sqUme6ffWTNbVzOItWjPvEItC9z2ooCugx9XWWjXWgmK7WdYtCUeknN9Zy8WOo6L9ffzRMJqGTykrbF8tPvuubLPPZSOPbGEb5Z+9n8/S9l2JYSFlZE18a8GoUuZzbT/GoMG2R1VVnfJNyjgKuO+HL+hCSWMmoSoBEjcd8Xv+aEIRmcliEip4pJj+az98f/eRCopVcKokFTFn3xUz6PsBsDt4lu3Wdb1zA9gGn8saBfpsq7RINtatv9OcVyXAd+IzY1B5bVOvt+27cAEqGfKNq626jTbXQd/NWpS8l0QZtxEggua8JkZ+LMXIzpg0WBFYgEz9uxhS4M9q+NidbaoE0jTduRJAt1CJglVUQrYC7aH9P1YBKHHV1b+BvasB7AVdLa/YCkyKuDNkShOa7egqTn7q7kJdhSyYyImmfjyVLHf8kf+chFRCMcQxUWukabrnUioWQQVABJ7X0BVi35QnfF++q97nWqX5k6bpLryp78NnxZBP2ZyLBisCDdmoz8S/QysWJ5xmqwHYDWSIOKgELu0AW9Pf5vPbmgiqjM4B1nCVVR+VPCXQqpNGwcOYODX5SP4oT2nKatvkJycC+6D8oOJg3zW/XxUuTW11T/QdYhSZuifavssNVZSeCwmfye+49Z3K0bpbKruhw2k4DqTJZLKLksUmEykduoiMqTBGOfKMoJ8N4cROJh0EZUaMj06QiJOJgqyTnIOr4JfuTiNirifZxKLmQ0lTWy0fVND5bnSh1IXwES0kKhfLH5uToPzi9+qC8FoNkdkMP0ZPXJmj6hOrAuwj4j4aotX/+Xy2aa0ryiH5xjJszE2g4tdFgpNYw5V9pGFX1wE2sfQirIDO84eChcyPtltTOeihF1SrgeRKO+0DC13tK1ahRN/YTnB9nn32sWCMC5TS/umksrzTxBbtDy0D8oxRCR/18V/Hj5OfYKmCq1SY9sg522egW+mIv3NV1QNMNETGvvTxB+hiE2qxaa6CZvJR6RE/0vf18ccq3tBCcArQjmQVmwss7JPzY2iw7Wt9SGp6O5h9pD65NctjNapeRwuBGAFXAp1U6gLYpA+ddDFklZj9O4YUMAX2ZqyCYeSzYiix/NFJR6uIGAF9eF2ddeOLYkDaRxVMvq9d/amoFN/Q/sRaXDYzlM9TkI7uD2WHIDZxIGDvtxNfINl342exYKYLI4jdRwK4ZcYCvqemwYqAKxrQ3YkHxA+0mrz6WUz2HbW+bs3lMeT8nu3S5FcmKxqvgkzBi0Hl9bq+v/vIh1prlENzLii4sdmJzD/Q5zBqYA/sUL6rWa336vjaVUnBX0331fezysRHNiqhwKG6JXbipmm6ez7fxxU10GfoysvoSR8puOyKGmg+wCEypP+fk45SBNpRTq7VatWJCwPxyLsdbLbBlYlprFaQaDpZs5J+G/+moNi0T7UKVKhs7F9dk6ZpHp1vr6AdJ4rGosuyxHQ67QCX2l+2QX+Y1+v70QLQlSYmYcXyuG23O/sIGuo72t2jGl/n38QbXKaprtxqkelYKW/YBx9/XPKhpGNpXQy1nFwLlJUn+7uPVJkwSsX2D8kqtKTKnuOueBawB8HHBLOPwgj0liTZZ9q5NuEcApjoNmNFpAnG+NpXwK1tuymimhSjaHqMHxhKMVbh1+2sfSnG+jwqUFf7FDSNpFjex5C6F3wmJzKVq54PqCCZHhhCV0IVJsnymBiAgrUuUvehjz9jALa+/se6gC7cglaHLwU+NsTrI+JwquyGKBsXDQYLAXfYwyaj9BEnOkNNOolUO6r/rCYz4D/XQHEH7bcL7HKRRhns+9OlGbLpyLotiplYwWJ72m4saZl1xXE0cqKWBKMZ5LOCVpq5R0VleekCc61ro5+H+DOU+vofS5bfagH6NsUder5BH3G8rKyPRaPkEQDogDKHMFn3DzD0pP6qoriakKIJGwSJeK26FRR6S4cwUZWGHjKiCkkVGgVGJxL7SNNfS7nTKuCqqH4qr7crpS3N7aP5fL5b/dVnVx/aCrmCfFqWjQlI+mwbQtMkKB+e0rZtFH+GUqj/sWSVGvnI8mKsJ8m2x1ICWudDMz/HpFH2Gjw8PGCxWHS+d51l4CI7IOrbk+7v73dFQfm54gDWOtBzDZgYwsG/vb3dTVCth+Drn+9cBmuiuUxBX9173ZjDdvI8370zrQIqxevraydw6Ou/K9lL+0RFl2UZbm9vd7+T32maYjab7RQTidewr0rX19e4v7/vhLqU9L18/DlkMrlEmdZGX/8PwQRcz9CQI7B3UbUQ6RDi+PWdyzCG6wEMUATAHu3X1UzNz1jzVc1nJvX0mUDWDwUOO9fg0NdV31dNS8Us+L3G//twCH4eW4rL9oET85AQLZ9lr7c5CrpS2wlsXRLdSajJROoiqS/bRzH8GYP6+j8G8T3HbNMS+cAxcZUFHEJBReDS5vY79fFcflOIVKta3xt4nGuuiDWfac81mM/nu2u4uljQMca8yrKscy4D+6eZgAqK8TPNfNNQk/VTuXtP+Wzfl5+xLZtg4yOdpFTSykd9FvunypdWAa0fKjv2m+i/VcSKceg46RhbwK6PPz4KvT/fua//Y5jY2g4tgZisxBjiQsMKSLRw9RyQMWiQIiDRFBr64twMY811i0rbflFoQmgwQ36H+IeudjgYqhg4KfRwEsUK7AquE8/1Xi4wiFGUY6waVRyKPNs+ud5L+cD71DKy5LKM+njr60sM2BxLff0fSja6dSpSl0mV+1gUdDBcE0H9U3aMJv0pyDW59XfdsGP7qJPM5qbrhqc+RUbrxKdo1A2gIrMrvG1fJ6YrymAHnv3XtF29r09QdCVWhaCYRJ/StCu1S2GFFgnfRD52kquiUYpxmVzvH1LE1lVyua2+cYxV9H0p8GzPRWMpyqNmrnZKNSKwF1b6jmN19FhS4eBqagfYmq/q2nCC9x2ppkdhqZZW18bXfkz/2a6rPmDfkWpawEPNYQKomhRkJ7QLG3BNDHWPjiU+24ZOlZfW9eDnnGyaweiyfnzkyjOxCob8JA6kzw9RTPt0LdVKObWFYenk5xq8DGQ1uta9pyBRwCzY2TRNEIxUi8D6/Aqe9YGpPosA6AqTBVL1HfVdta4+lYDLgghRCKwdqghCYGEMGGldDLsz0UchsFaf7ZIfVQp94xhqn/9zF+whQPBYC+1ZzjV4kT/sA7CfvJrvYDco8XqeZcC6eLy/abp171UxKNjG/0Ptx/Cdq52uJgrKAf119RVw42ThOLFd2w/lX5qmuL+/B7DPa6AJG1MPgVZK3+cMZSo/6WrSemFoEdiHZ6+urh6Z5uSNrd3gI04+8oL3jnluQqh9TXbi3+TDuejk5xq8aNI+qq+vZrMmmdhTbzl4NAtt3Xu9tigKLJfLzs68UPsxFoG+Cye4Xhuqq68pxJxgLDBqVylLtgwcSffzH0t013znGlC5UalxpdcKTRwftpWm/edJWGrb05+bENt+WZZYLBYHgZhjWQQnP9fgVABiLFlwxpp56rspUWHwR004xRn0cyoYLZxhATvbfkgRqGlsU5j1PrUYtE/qklgFR/70kT6nL8V7DIyA/FQXS48G56Sz99n7NRoQGxrk+4dSvNV8d8m0TxH42ue1Ol4MQcbgBGMpgpOfazBWmuUQUj+U/daJtVwuOxOYpJmM1h/lZhy2CaDjO/O5ofZDm7IoHCzooROfyiimrr4qCOIGobGhEtH8C/aF/Q8JYp/Foei971wDVaKKT7A/vEd9dc3dCBHHRCs12U1f1s1UFyz0nFD7FsvQpKRzgoWDluu27Z5rwLx1Cpmmtr5IUsSeKyI1clVtjylbLpe7CczBI5LLVcoVPlR3g76ztkFzr6/9GOIEIJDE56s1o9EKnRTWUmB7/N4CgWyTk4lbhPWgGu1/LM5hLUlOeAr+dDrt1EHQnHqL9UynUzw8POzcM91mTBDP5nL0kbVe6V4RrOQBO1RKDJPHxvJD7Ws0hyAv8Lhi9KlplN2HQLdijcuCOBWp36iCrIKn/To0BVqFXTW4Kw7Nlcm6Iz7SfQZsX013+w6W2D6VkZqTGiYEHisPXtMHqmo/1FfXjM/Q+6mi1Odbc1+jIrrRjArBpegsT4hR2bRp5bUtRaaLlaaM8zN18chLl5upfNDnWvkj8T7eq3xxtdNHZ3UNhlCooyFlEbpfzScdEIuuH0uqMNQk5IYSCrT6khSUGGtIgSGdiDa02ae41DxXQbIouq5os9mskxOhz7bvrKutxTpi3k8nrVVAtKKsy6m8sZOM7ZH/tF7IK6voadlU1b7qs7V8tH/sA0E86z4RNCQgOZTYZ33vsbMqY2iw3T7GRPP9xJA1ozi4MTkNFKa+H2AvfHZC5HmOyWTSAeOIaAPx1W7YX13JgO5ORl2l9ccCiOpfk3TXGtFrum59fObnti6A7WuIuP1ZrSg9SUrPs9B2LRoPdJU+311DjrpCA/utzXaS8TuSfSav14IgnPwE82x/DiUFEal4tN/q7p2DTm4RnMM9sEkkBOH6jpg+pF9cTRSI0zASP3O5JjGZlTYaAezNXxu6c62c2hcFA9XkZfhquVx2gF2a3i5+sH3dRKOCGosPuPqvVgbBQrUMrFuliDqwzzTUbEqNimjkgPJgdwbqqmtdF83a1OPdFThUhX8oqbvAd+czrByci06uCEIvM9Q1UJTcrpoxyHiIOAl0xyJNRhfib3dMht6PE0BDjlrkpI9UaH3nSiTJ/kTp+XyO1Wq1E2p+bxFwi5Dneb6bfHbfRcg9sOOn/QT2Z1cQuFUeqJulqyZXaU4iKiZVLgoG62lWCp7qZ6pM+s5lcI1daNUOYWVWPvlsxdzOQaMUJrEdd4ElpyQLWtnNG+ozWp80huxgqsAA++PaLAAWGwe2q59OPrbtug84/lyJ2PGxAKE+x/rvLtLJap/Hz9UisFYA8DgfpW9zjiZMKRFEVMuOq74uIK4cGE1QUsBUlbQPLLQgKT9XpUZFru6T671PSS8+tjcCxfjYQ8hlFgL7I64oHNaHPAQj8GEcvskac66EKhNiBDE5AK7rXD62jxQj0M84GckrVVj2eg1b8lryOYQR2HRjF8ZhvwPQsSY0MqDjO0Z4jxaA1sfQMOq56MWm/WF4VKEvPu5C448hXSXUN6YP6lod+Gy7oSbUvoaS+pB6Jbvq2Pfm7zqZuU9CE1z0f5JLmdrzI2ISdzhB9TqNhOg72qiIy0ogabIX+0N3SHP2rcViox420sJ3VGWu4T3FXsbIkaHV6FK2toK2i8bC4EaxCOwAqilNX5S/8zrdiKOf28EN/Si40pf2OoRZFjHmu1kgkgKvSoEmftu2mM/nOyWiOf4q5HwW9w3ErDgMDQ45V4L9dwm28lqLkerk8P3oBJrNZrszIdRy0WeR52p5cXXWGgRaxl5lTRWMyqSa9XwG5U6BXltw1U56XlvXcecysB2bd6LKkJ/rLlH2MUmSHa7D67TvY9HJLQIVaL7Yer3GfD7vmNMqEBa8epEU6r9dhbQMODMt5/N5ZzOSZo/ppiRgO9DMROS5CT7iRieeKwE8PuY7ROSzpkfHJAvxulgiYMlnxigq9q0v3q/gpU5q3Q8R034fWXxIZZWg8WQy6WATmrTksh60/6oIdLs7qaoqzOfzR+dF6iIyBo0CFvJ/1XCq9S2IpvcNJTUv+9ofAhbyHYZuerFs1mIUFiTS93K9j23P5T6MvanJx59D6JhxD22aUuT/0E1BQ0nHX4FIjQb4+q/gMNvTe0i2r2qljkFnAQt144qte/8yrPoh8vVf/Xui37QIkiTprM4UDp10/Jvt2HMTQqR4xDHnShBso0l8TE1EH9Gs1/5kWXYQEEZ+8h7N+NOFQC00m0h0CtLxpQvcNI/PZfD1X3NQtF3r7lJ+yM+xk41ObhG0beute68+7TFd0VVPaSyLAECwbj+pr/90DVxtJkniPTfB9W76vCQJnyvhswhUsHSF0dXLR7F5IlSiPJfBft9H1lripFqtVo8sMB1nVz9cfAg9X/vv+rxpGu+5DAC8/Wf4lEVfbm5udtEOAolXV1eP5MMlk0Po5IpATU9NzKGPMzQEc2rXwCab+Epl8X9bi4DWhIJ5akbav7maxcSRz1UKzcf/GP4psEiKNc9DpdbsMw4pJTaUrPXFdrUm5SGl4qxsagakxaRicZwYOgtYqCCImkcxGvlFU6j//J2TV5Fpi+xSETHxhXXs9NwE9X1jzGc15XUCH3KuhE4OmxEZGp+QslBBtfkNsSsy+e8qvqq7+Ni2RhRCNFT+2Ae+H//Wata+/nNBUXBT0+WJP/Bztn/2A0766BDXwFJdu8tRH9uPU7sGlvr6by0E/VzP3+vrZ8z1LqJ52cdP37joqrLZbDqm+1hgoQJ5jLYcsiK7AFhX/9XisgDvqSwCwH9uRaj/Krtqkdn2qURORWdJKLITlWbNuSjGfPWda2AHi6iwnaiaN3BoH0J9dykM9i1N+8+VsALva+/YPlv+KIoPdHcS0lqyLoud2JzUFmG376NKXt0AV10IXxRHSRWxqw3XuDPNWz/jfb6FSqMOPlPf5h2oyzAG/VSkGA8lDhjNNACdeLpdSVwKw+axa9tAt3ALgM5OOD5LM91iJqriFDYNV03PU/6E+MPfaeoqL9I03Z26TLCMkzB2d2MM+cZXf/fxXycq/XWguwuUzwLQORXbRy63LkmSjguqSg7YL6RjWgg/s4qAA9BXl5CHhnK1tdqY19pkI23bprC6qiHrs3RF4OppATYX8XstJ8bVlFbDqX5C/FH/lv3kpGFITMt40cf3bSHve/9jxjfEf83/1/tdG6+0ulHsSch6ryoalSsNh/LvvhORjqWfWUVAyvPwuQa+uvQqONwtqFqe4XzxZHMAAAldSURBVCU+C9hOUt6vz9I+PDw8RIGFinm4zpU4tUUQ4o/mTrBWn4JinFB95zIMpdD4hviv+R1AFyTmxGVoWEHixWIRjZ/Ye9UqqWv/uQ9j0VnAwhCYN4QOaZ+M1X5qiq+apZoB5qtLz8IdaZruBorIsJak5urC8I8iwPyfexU4oQ7Zhtp3rsRYfO6jtvXX7ef7aH/UrQAQPJehT65C/QLC48tr+/hPPEjvZ780pZvf8x6tPRnCCLTgKnMK2CdVpko892E09+lnQRGwLy5FoH1Xoh+mGltBThv/Vz7oM7WfSkSRY1KMfaYvTdu+cyVC5dKHkprgffyx8Xzgca0+XQnVrRiiCCxGoqR99fFf+2Tj99on2z5NfTXplVzyaaMLqqj6UpjHUgQvfBvyi6Yk8Z87EKpLb1NZ7e9ckez5d/xRv57PzPP80aYjK0gU1LIsvedKjCUofRTiD69xLQwKDqqgj5FoRgqNLwAv/9lfgnMcBwXs6B4pVqKLSIioVFSW7DZ3Wpra7gUsHJH03AEOsu4eszvKaL7y7D8tvlkUxe7gTuac00elEuBAq3Bp+xTcxWKxyy23k1nb0HMlKNBacPPUYGGIP6r0uEtP+8931p16/H0MQT90fF38152l7JumBnN3oLUMYsFCWqkMQVJBEnMiP8mvQ3eXxtDZUowBd934oaRCZX0+16vZQhMxJib7ao8bpwDbFU8HUrO/XCW/1NxUHoZMW/2eq7D2V8EmH9kwlCa8xITw2C9X/B94nFijGXf6bCpTTjCi7nacXK6BtTD076EuKPuorgrHzZai05O9NFXcxx+boqzyqa4dP9NnUvbGoJNbBDrh7UQ9tf86Bmn/9XeuJjooJA4skW9X6SkOrM05ILgUsyJaXILkwj/6fjQ9lv3S8F/ofmBbZ0B5w/CWvdYWTuH/PJCV7anCDZGCfipffJdQ/2P549rNSFyE46ohQ12YfPzR3aM2j4GKRSe79uELFz5kh8kMvszLcC5ijKDrYAH7nHLdDqooMYEiRZhJNvGE7amgcFKGyrFbS+EY404TeTQZiSt0DGnEgAqSLoC6J4rWK4bAtGMV7FjZULMd6K68Y8gXeaIuoDXllcgzxQd8/NH0aP6v/Q+d+zAWnVwRUACsyclQyctOahqqQGsoEMAufEStTsyAykAzCdmugmMaR6b5F7upxGf+hnx89hvoHp9G0z2GWKB0s9l0Jp9tW4Wc76oZekVR7A6M6avebIl9tBOGbYTeP0Rab4A84bgR6HWNlUYVfPxRt0ktEPbfhjvVhRpTGZzlgBPrH2nd+KGAkJpgpyDGldWfV/+QfyvYpKTFKdXvVn/YhqN4bUzxyhDFCoui3YqBxLRP0Iz4jxbQoHKkErBZh1SCep/mWoT6ryulTqRYCikD9eldmJKu2mrWa0qzjz929yjv5bM1r0BrFLg2Jw2hs5QqUxNbTemhQs5+KKjEz2xuP8mChSFSDax/M02YK71VDOpbMhNM31cFzIbLLKgW6p8F9lwgWx9RoKwQUhmFJmIfD/uA2r7U2L5+9skV/7ZypMCrnlTUR7HiT6VMnhDdZzIQ30vHw+fDq9K3gKTKj9ZWUItnbIvgLBiBAlEkarhTkAtlH0IWfVYf3oaf1Pzlquby8VxIvVVerr0GPvP2mHcmRqCKhBbbMRgB03LpA1trSE1cvu98Pt+Zz6RY/95utqJrRdN9KLlcDwW79ZQpwH1ugo8/VBxULMBe2TF8yM+0T2NjBGdzDfi7rRt/pEFyNrLWgA3pWdRaEz7U9ydZ1FdXDpdF03ev7/MYS4CkwqyfJUkS7bqtVqtOuIwrpL6TRg0055/36zbwQ6MGmoOg7drwnotCz7B7C1Rh0iJQ0glL2fHxR10mKkwqM92Y5nJN1HIYSmcBC/Ulqa1jwZpY4oCXZYmHh4dODv+QvHtrKuvv9jvrs+nf7IPG+23Ou6tgB9u2KL4Kh/rbwD4UpcAl+8BnaXFNPoemtL5Pkvjr6mtsnc9gkhOwD6kxjMptx8B++689oPSQcdJrdfuyvreWUHfxwkfqkqgSoMUH7A9lpeJXgDHEHz5DXVUFa11RIQWZx6IXUqFoTLI+oq7YynAyVffFx9BYLoblg7Us+vimAtJ37oAe361AJElzAtp2XxrNmtV8Hp/JSexKkebz7UGz2v/VarVLP3aBbhYsdKUA0zrx8UeBOZ1MvhRg8iQkn31YkgX0ONnVJXBtGrL80arVfXtGzkFf+GPRXdlcwHa10ZXsXP0ZmxRA0iQdCwb2bdrRlFheS/+Ubbg2BVmhtoqf9y+Xy0fZknyGjZ/bU5vZjrpYNJ11/4KPyA/ySNvXPmu/gW7VpCFkV2fynLtBqQz7+EMeU1m5XAAfjbXQfuH3Gmh9eG7DZU470DXpv4jEVZQulasUmtYhYGKOui1a6dh1boJL8Hi/CrGrrr6awVm2PXqNR4Dxc/aJoTCdlDbvn++6Wq2i+MM2+HzN7lPzXXMTNKQ7hNgW31P3mNAdCfFH+WgxiHMuWid3DU79MvRttYIMyYJFFtyLoZfBNeDEs6avgm80M9XtoWnNTTdK9/f3u401tq4+JxD5Gqqrr+Ce8ivWvGVbqiRo0odcAx+vuVIvl8tHZwDYjUh9ZC0J+3nT+M81UGykjz/ENhTDsPhRH42VS3ByRXBuIvBklQL79EVTBEDcuQMWU1AAS31iXQ1dYSj7vupW0LKw+/KVtPS7uiq2SIn93vKF/nKIPzqZfO2rJWAxkyFkXSiLw9j2LX+Abr1CLVgTs5dgrDn2hccIgO5JxLGx4xdhfh1L2lcNZQL7mgOaxUfwkBNGd/PZklh2cgNda0MnqquuPhUKVz+m2lrkXndtqhJShcYJy4kUs9pp+6oENIeD7eh+EOXJ0LGxe1BsQpiPP/Zd+iyPU9NPnUUA4JHmJ1lQip/56EVbBDoRfecO2Os1vm6fzYlsMywpxLYvaXpYXX1NgbWgnctdsKv4IfyxK7/LSmmax+XGLf+GkMvC8EUlLH9IdMMO2Sw11hw7WhFc6EIX+umhL3zU4EIXutBwuiiCC13oQhdFcKELXeiiCC50oQvhoggudKEL4aIILnShC+GiCC50oQvhoggudKEL4aIILnShC+GiCC50oQvhoggudKEL4aIILnShC+GiCC50oQvhoggudKEL4aIILnShC+GiCC50oQvhoggudKELAfj/daE9vv6ThYsAAAAASUVORK5CYII=";
//        ImgUtil.GenerateImage(s, "D:\\upload\\6000000115697418.png");
    }



    public static String extractFilename(String fileName){
        return DateUtils.datePath() + "/" + IdUtils.fastUUID() + "." + fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 将背景替换为透明
     *
     * @return
     * @throws IOException the io exception
     * <AUTHOR> Que
     * @created 2021 -07-08 10:25:10 Change img color.
     */
    public static String changeImgColor(String path,String target) throws IOException {
        File file = new File(path);
        String fileName = file.getName();
        BufferedImage bi =  ImageIO.read(file);
        Image image = (Image) bi;
        //将原图片的二进制转化为ImageIcon
        ImageIcon imageIcon = new ImageIcon(image);
        int width = imageIcon.getIconWidth();
        int height = imageIcon.getIconHeight();
//
        //图片缓冲流
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics2D graphics2D = (Graphics2D) bufferedImage.getGraphics();
        graphics2D.drawImage(imageIcon.getImage(), 0, 0, imageIcon.getImageObserver());

        int alpha = 255;

        //这个背景底色的选择，我这里选择的是比较偏的位置，可以修改位置。背景色选择不知道有没有别的更优的方式（比如先过滤一遍获取颜色次数最多的，但是因为感觉做起来会比较复杂没去实现），如果有可以评论。
        int RGB=bufferedImage.getRGB(width-1, height-1);

        for(int i = bufferedImage.getMinX(); i < width; i++) {
            for(int j = bufferedImage.getMinY(); j < height; j++) {

                int rgb = bufferedImage.getRGB(i, j);

                int r = (rgb & 0xff0000) >>16;
                int g = (rgb & 0xff00) >> 8;
                int b = (rgb & 0xff);
                int R = (RGB & 0xff0000) >>16;
                int G = (RGB & 0xff00) >> 8;
                int B = (RGB & 0xff);
                //a为色差范围值，渐变色边缘处理，数值需要具体测试，50左右的效果比较可以
                int a = 45;
                if(Math.abs(R-r) < a && Math.abs(G-g) < a && Math.abs(B-b) < a ) {
                    alpha = 0;
                } else {
                    alpha = 255;
                }
                rgb = (alpha << 24)|(rgb & 0x00ffffff);
                bufferedImage.setRGB(i,j,rgb);
            }
        }

//        graphics2D.drawImage(bufferedImage, 0, 0, imageIcon.getImageObserver());

        //新建字节输出流，用来存放替换完背景的图片
//        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

//        String[] split = fileName.split("\\.");
        fileName = extractFilename(fileName);
        String baseDir = RuoYiConfig.getAvatarPath().replace("/avatar", "/signature");


        File desc = FileUploadUtils.getAbsoluteFile(baseDir, fileName);
        ImageIO.write(bufferedImage, "png", desc);
        return FileUploadUtils.getPathFileName(baseDir, fileName);
    }

    /**
     * 白底照片去除白底 形成透明底图片
     * @param file  需要去除背景的图片
     * @return  true 则去除成功 ，false 则失败
     */
    public static String localImgToAlpha(File file) {
        try {
            String fileName = file.getName();
            BufferedImage bi = ImageIO.read(new FileInputStream(file));
            ImageIcon imageIcon = new ImageIcon(bi);
            BufferedImage bufferedImage = new BufferedImage(imageIcon.getIconWidth(), imageIcon.getIconHeight(),
                    BufferedImage.TYPE_4BYTE_ABGR);
            Graphics2D g2D = (Graphics2D) bufferedImage.getGraphics();
            g2D.drawImage(imageIcon.getImage(), 0, 0, imageIcon.getImageObserver());
            int alpha = 0;
            for (int j1 = bufferedImage.getMinY(); j1 < bufferedImage.getHeight(); j1++) {
                for (int j2 = bufferedImage.getMinX(); j2 < bufferedImage.getWidth(); j2++) {
                    int rgb = bufferedImage.getRGB(j2, j1);
                    int R = (rgb & 0xff0000) >> 16;
                    int G = (rgb & 0xff00) >> 8;
                    int B = (rgb & 0xff);
                    if (((255 - R) < 30) && ((255 - G) < 30) && ((255 - B) < 30)) {
                        rgb = ((alpha + 1) << 24) | (rgb & 0x00ffffff);
                    }
                    bufferedImage.setRGB(j2, j1, rgb);
                }
            }
            g2D.drawImage(bufferedImage, 0, 0, imageIcon.getImageObserver());

            fileName = extractFilename(fileName);
            String baseDir = RuoYiConfig.getAvatarPath().replace("/avatar", "/signature");
            File desc = FileUploadUtils.getAbsoluteFile(baseDir, fileName);

            ImageIO.write(bufferedImage, "jpg", desc);// 直接输出文件

            return FileUploadUtils.getPathFileName(baseDir, fileName);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    /**
     * 设置透明   失败
     * @param filePath
     * @return
     */
    public static int[][] getImageGRB(String filePath) {
        File file = new File(filePath);

        int[][] result = null;

        if (!file.exists()) {
            return result;
        }

        try {
            BufferedImage bufImg = ImageIO.read(file);

            int height = bufImg.getHeight();
            int width = bufImg.getWidth();

            result = new int[width][height];

            for (int i = 0; i < width; i++) {
                for (int j = 0; j < height; j++) {
                    result[i][j] = bufImg.getRGB(i, j) & 0xFFFFFF;
                    int pixel = bufImg.getRGB(i, j);
                    int rgbR = (pixel & 0xff0000) >> 16;
                    int rgbG = (pixel & 0xff00) >> 8;
                    int rgbB = (pixel & 0xff);
                    System.out.println("rgbR:" + rgbR + ",rgbG:" + rgbG+ ",rgbB" + rgbB);//240这个值，比电子章的背景色的RGB值稍微小一点。可以上下浮动多试试这个值，调一调哪个阈值最好
                    if(rgbR > 240 && rgbG > 240 && rgbB > 240){
                        //将图片中大于240的色值，设为白色
                        bufImg.setRGB(i, j, new Color(255,255,255).getRGB());
                    }

                }

            }
            File outputfile = new File("E:\\222.png");

            ImageIO.write(bufImg, "png", outputfile);

        } catch (IOException e) {
            e.printStackTrace();
        }

        return result;

    }


    /**
     * 图片转化成base64字符串
     * @param imgPath
     * @return
     */
    public static String GetImageStr(String imgPath) {// 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        String imgFile = imgPath;// 待处理的图片
        InputStream in = null;
        byte[] data = null;
        String encode = null; // 返回Base64编码过的字节数组字符串
        // 对字节数组Base64编码
        BASE64Encoder encoder = new BASE64Encoder();
        try {
            // 读取图片字节数组
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            encode = encoder.encode(data);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                in.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return encode;
    }

    /**
     * base64字符串转化成图片
     *
     * @param imgData
     *            图片编码
     * @param baseDir
     *            基础路径
     * @return
     * @throws IOException
     */
    @SuppressWarnings("finally")
    public static String GenerateImage(String imgData, String baseDir) throws IOException { // 对字节数组字符串进行Base64解码并生成图片
        String fileName = "";
        if (imgData == null) // 图像数据为空
            return fileName;
        BASE64Decoder decoder = new BASE64Decoder();
        OutputStream out = null;
        try {
            fileName = DateUtils.datePath() + "/" + IdUtils.fastUUID() +".png";
            File desc = new File(baseDir + File.separator + fileName);

            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            if (!desc.exists()) {
                desc.createNewFile();
            }
            out = new FileOutputStream(baseDir + File.separator + fileName);
            fileName = FileUploadUtils.getPathFileName(baseDir, fileName);
            // Base64解码
            byte[] b = decoder.decodeBuffer(imgData);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {// 调整异常数据
                    b[i] += 256;
                }
            }
            out.write(b);
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally {
            out.flush();
            out.close();
            return fileName;
        }
    }
}

