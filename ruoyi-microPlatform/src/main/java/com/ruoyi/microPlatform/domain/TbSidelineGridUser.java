package com.ruoyi.microPlatform.domain;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 兼职网格员信息对象 tb_sideline_grid_user
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public class TbSidelineGridUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键Id */
    private Long id;

    /** 关联用户 */
    @Excel(name = "关联用户")
    private Long userId;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 类型（管道气公司、液化气公司） */
    @Excel(name = "类型")
    private String type;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 证件照 */
    @Excel(name = "证件照")
    private String idPhoto;

    /** 证件号 */
    @Excel(name = "证件号")
    private String idCard;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 网格员状态（0正常 1停用） */
    @Excel(name = "网格员状态", readConverterExp = "0=正常,1=停用")
    private Integer status;


    /** 所属县区 */
    @Excel(name = "县（市、区）", type = Excel.Type.IMPORT)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "乡镇（街道）", type = Excel.Type.IMPORT)
    private String country;

    /** 所属村镇/社区 */
    @Excel(name = "社区", type = Excel.Type.IMPORT)
    private String town;

    /** 网格编码 */
    @Excel(name = "网格编码", type = Excel.Type.IMPORT)
    private String gridName;

    /** 所属网格ID */
    @Excel(name = "所属网格ID", type = Excel.Type.IMPORT)
    private Long gridId;

    /** 网格范围 */
    @Excel(name = "网格范围", type = Excel.Type.IMPORT)
    private String gridRange;

    /**  液化气公司兼职网格员及联系方式 */
    @Excel(name = "公安", type = Excel.Type.IMPORT)
    private String pubSecurityName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "卫健", type = Excel.Type.IMPORT)
    private String defendHealthName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "市场监管", type = Excel.Type.IMPORT)
    private String marketSuperviseName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "城管部门兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String urbanManageName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "消防部门兼职网格员\n" + "及联系方式", type = Excel.Type.IMPORT)
    private String fireFightName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "供水公司兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String waterSupplyName;



    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "电业公司兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String powerName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "管道气公司兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String pipelineGasName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "液化气公司兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String liquefiedGasName;


    /**  管道气公司兼职网格员及联系方式 */
    @Excel(name = "供暖公司兼职网格员及联系方式", type = Excel.Type.IMPORT)
    private String heatingName;

    /**
     * String 兼职网格员类型
     *
     */
    private Map<String,List<String>> sidelineGridUserMap;


    /* 负责网格信息 */
    private TbSidelineGridInfo tbSidelineGridInfo;

    /** 兼职网格员所属网格信息信息 */
    private List<TbSidelineGridInfo> tbSidelineGridInfoList;


    public String getPubSecurityName() {
        return pubSecurityName;
    }

    public void setPubSecurityName(String pubSecurityName) {
        this.pubSecurityName = pubSecurityName;
    }

    public String getDefendHealthName() {
        return defendHealthName;
    }

    public void setDefendHealthName(String defendHealthName) {
        this.defendHealthName = defendHealthName;
    }

    public String getMarketSuperviseName() {
        return marketSuperviseName;
    }

    public void setMarketSuperviseName(String marketSuperviseName) {
        this.marketSuperviseName = marketSuperviseName;
    }

    public String getUrbanManageName() {
        return urbanManageName;
    }

    public void setUrbanManageName(String urbanManageName) {
        this.urbanManageName = urbanManageName;
    }

    public String getFireFightName() {
        return fireFightName;
    }

    public void setFireFightName(String fireFightName) {
        this.fireFightName = fireFightName;
    }

    public String getWaterSupplyName() {
        return waterSupplyName;
    }

    public void setWaterSupplyName(String waterSupplyName) {
        this.waterSupplyName = waterSupplyName;
    }

    public String getPowerName() {
        return powerName;
    }

    public void setPowerName(String powerName) {
        this.powerName = powerName;
    }

    public String getPipelineGasName() {
        return pipelineGasName;
    }

    public void setPipelineGasName(String pipelineGasName) {
        this.pipelineGasName = pipelineGasName;
    }

    public String getLiquefiedGasName() {
        return liquefiedGasName;
    }

    public void setLiquefiedGasName(String liquefiedGasName) {
        this.liquefiedGasName = liquefiedGasName;
    }

    public String getHeatingName() {
        return heatingName;
    }

    public void setHeatingName(String heatingName) {
        this.heatingName = heatingName;
    }

    public Map<String, List<String>> getSidelineGridUserMap() {
        return sidelineGridUserMap;
    }



    public void setSidelineGridUserMap(Map<String, List<String>> sidelineGridUserMap) {
        this.sidelineGridUserMap = sidelineGridUserMap;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getGridRange() {
        return gridRange;
    }

    public void setGridRange(String gridRange) {
        this.gridRange = gridRange;
    }

    public TbSidelineGridInfo getTbSidelineGridInfo() {
        return tbSidelineGridInfo;
    }


    public void setTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo) {
        this.tbSidelineGridInfo = tbSidelineGridInfo;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIdPhoto(String idPhoto) 
    {
        this.idPhoto = idPhoto;
    }

    public String getIdPhoto() 
    {
        return idPhoto;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    public List<TbSidelineGridInfo> getTbSidelineGridInfoList()
    {
        return tbSidelineGridInfoList;
    }

    public void setTbSidelineGridInfoList(List<TbSidelineGridInfo> tbSidelineGridInfoList)
    {
        this.tbSidelineGridInfoList = tbSidelineGridInfoList;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }


    // 生成复合key
    public String generateKey(String name, String phone, String type) {
        return name + "|" + phone + "|" + type;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("type", getType())
            .append("name", getName())
            .append("idPhoto", getIdPhoto())
            .append("idCard", getIdCard())
            .append("phone", getPhone())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("tbSidelineGridInfoList", getTbSidelineGridInfoList())
            .toString();
    }
}
