package com.ruoyi.dataview.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class SiteDetailsDataResponseVO {
    private String dtemp;

    private String signal;

    private String st;

    private String stName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sttTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ttTime;

    private String videoPath;

    private String vq;

    private String vt;

    private String wlih1;

    private String z;

    private String zt;

}
