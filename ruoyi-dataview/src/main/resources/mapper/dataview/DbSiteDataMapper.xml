<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbSiteDataMapper">

    <resultMap type="DbSiteData" id="DbSiteDataResult">
            <result property="id"    column="id"    />
            <result property="serial"    column="serial"    />
            <result property="stCode"    column="st_code"    />
            <result property="stName"    column="st_name"    />
            <result property="ttTime"    column="tt_time"    />
            <result property="sttTime"    column="stt_time"    />
            <result property="utTime"    column="ut_time"    />
            <result property="waterLevel"    column="water_level"    />
            <result property="alarmCode"    column="alarm_code"    />
            <result property="signal"    column="signal"    />
            <result property="voltage"    column="voltage"    />
            <result property="videoPath"    column="video_path"    />
            <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectDbSiteDataVo">
        select id, serial, st_code, st_name, tt_time, stt_time, ut_time, water_level, alarm_code, signal, voltage, video_path, create_time from db_site_data
    </sql>

    <select id="selectDbSiteDataList" parameterType="DbSiteData" resultMap="DbSiteDataResult">
        <include refid="selectDbSiteDataVo"/>
        <where>
                        <if test="serial != null "> and serial = #{serial}</if>
                        <if test="stCode != null  and stCode != ''"> and st_code = #{stCode}</if>
                        <if test="stName != null  and stName != ''"> and st_name like concat('%', #{stName}, '%')</if>
                        <if test="ttTime != null "> and tt_time = #{ttTime}</if>
                        <if test="sttTime != null "> and stt_time = #{sttTime}</if>
                        <if test="utTime != null "> and ut_time = #{utTime}</if>
                        <if test="waterLevel != null "> and water_level = #{waterLevel}</if>
                        <if test="alarmCode != null  and alarmCode != ''"> and alarm_code = #{alarmCode}</if>
                        <if test="signal != null "> and signal = #{signal}</if>
                        <if test="voltage != null "> and voltage = #{voltage}</if>
                        <if test="videoPath != null  and videoPath != ''"> and video_path = #{videoPath}</if>
        </where>
    </select>

    <select id="selectDbSiteDataById" parameterType="String" resultMap="DbSiteDataResult">
            <include refid="selectDbSiteDataVo"/>
            where id = #{id}
    </select>

    <select id="checkDbSiteDataUnique" parameterType="DbSiteData" resultMap="DbSiteDataResult">
        select id from db_site_data where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertDbSiteData" parameterType="DbSiteData">
        insert into db_site_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="serial != null">serial,</if>
                    <if test="stCode != null and stCode != ''">st_code,</if>
                    <if test="stName != null">st_name,</if>
                    <if test="ttTime != null">tt_time,</if>
                    <if test="sttTime != null">stt_time,</if>
                    <if test="utTime != null">ut_time,</if>
                    <if test="waterLevel != null">water_level,</if>
                    <if test="alarmCode != null">alarm_code,</if>
                    <if test="signal != null">signal,</if>
                    <if test="voltage != null">voltage,</if>
                    <if test="videoPath != null">video_path,</if>
                    <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="serial != null">#{serial},</if>
                    <if test="stCode != null and stCode != ''">#{stCode},</if>
                    <if test="stName != null">#{stName},</if>
                    <if test="ttTime != null">#{ttTime},</if>
                    <if test="sttTime != null">#{sttTime},</if>
                    <if test="utTime != null">#{utTime},</if>
                    <if test="waterLevel != null">#{waterLevel},</if>
                    <if test="alarmCode != null">#{alarmCode},</if>
                    <if test="signal != null">#{signal},</if>
                    <if test="voltage != null">#{voltage},</if>
                    <if test="videoPath != null">#{videoPath},</if>
                    <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateDbSiteData" parameterType="DbSiteData">
        update db_site_data
        <trim prefix="SET" suffixOverrides=",">
                    <if test="serial != null">serial = #{serial},</if>
                    <if test="stCode != null and stCode != ''">st_code = #{stCode},</if>
                    <if test="stName != null">st_name = #{stName},</if>
                    <if test="ttTime != null">tt_time = #{ttTime},</if>
                    <if test="sttTime != null">stt_time = #{sttTime},</if>
                    <if test="utTime != null">ut_time = #{utTime},</if>
                    <if test="waterLevel != null">water_level = #{waterLevel},</if>
                    <if test="alarmCode != null">alarm_code = #{alarmCode},</if>
                    <if test="signal != null">signal = #{signal},</if>
                    <if test="voltage != null">voltage = #{voltage},</if>
                    <if test="videoPath != null">video_path = #{videoPath},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbSiteDataById" parameterType="String">
        delete from db_site_data where id = #{id}
    </delete>

    <delete id="deleteDbSiteDataByIds" parameterType="String">
        delete from db_site_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>