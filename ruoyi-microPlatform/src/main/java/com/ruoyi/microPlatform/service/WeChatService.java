package com.ruoyi.microPlatform.service;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.WxAppConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbMsgTemplate;
import com.ruoyi.microPlatform.mapper.TbMsgTemplateMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WeChatService {
    @Autowired
    private WxAppConfig wxAppConfig;
    @Autowired
    private TbMsgTemplateMapper tbMsgTemplateMapper;
    @Autowired
    private RedisCache redisCache;

//    @PostConstruct
    public void init() throws UnknownHostException {
        String hostAddress = InetAddress.getLocalHost().getHostAddress();
        if (("**************").equals(hostAddress) || hostAddress.startsWith("172.168.10.")) {
            getAccess_token();
        }
    }

    /*
     * 获取access_token
     * appid和appsecret到小程序后台获取，当然也可以让小程序开发人员给你传过来
     * */
    public String getAccess_token() {
        //获取access_token
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential" +
                "&appid=" + wxAppConfig.getAppId() + "&secret=" + wxAppConfig.getAppSecret();
        String res = HttpUtil.get(url);
        JSONObject jsonObject = JSONObject.parseObject(res);
        String access_token = jsonObject.get("access_token").toString();
        redisCache.setCacheObject("WEIXIN_TOKEN", access_token, Constants.TOKEN_EXPIRATION, TimeUnit.HOURS);
        System.err.println("窝里获取token了" + redisCache.getCacheObject("WEIXIN_TOKEN"));
        return access_token;
    }

    public static void main(String[] args) {
        System.out.println(new WeChatService().getAccess_token());

        WeChatService weChatUtil = new WeChatService();
        String values[] = {"Jack方", "2019-5-8 10:10:10", "xxx有限公司", "JAVA开发", "xx区xx广场xx号", "请带好入职材料"};
//        weChatUtil.pushOneUser(weChatUtil.getAccess_token()
//                ,"o_fh25E0IufW7NIpezUReODfVH68","ec76b8b81cd04cf6b464bb0adf309d3b","zv0IsYDpJxgKWLHGUy8FEv0ajtJqkfhWTsFWiM7zzSU"
//                ,values);
    }

    /**
     * @param deptId
     * @param modelName
     * @param templateCode /subpackage1/publicSentiment/list?showTab=true&queryType=1
     */
    @Async("threadPoolTaskExecutor")
    public void insertMsg(Long deptId, String modelName, String templateCode, String page, String deptName, String content) {
        List<TbMsgTemplate> tbMsgTemplates = tbMsgTemplateMapper.selectTbMsgTemplateList(new TbMsgTemplate() {{
            setDeptId(deptId);
            setTemplateCode(templateCode);
            setNotRoleId(2l);
            setStatus(1);
        }});
        System.err.println("查到要发送的用户批量=====" + tbMsgTemplates.size());
        if (tbMsgTemplates.size() > 0) {
            tbMsgTemplates.stream().forEach(item -> {
                System.err.println(item.getOpenId() + "insertMsg" + item.getUserId());
                if (StringUtils.isNotBlank(item.getOpenId()) && ObjectUtil.isNotEmpty(item.getUserId())){
                    sendMsg(content, item.getOpenId(), modelName, deptName, page, templateCode);
                }
            });
        }
    }

    public void insertMsgCommon(Long userId, String modelName, String templateCode, String page, String deptName, String content) {
        List<TbMsgTemplate> tbMsgTemplates = tbMsgTemplateMapper.selectTbMsgTemplateList(new TbMsgTemplate() {{
            setUserId(userId);
            setTemplateCode(templateCode);
            setStatus(1);
        }});
        System.err.println("查到要发送的用户-单个=====" + tbMsgTemplates.size());
        if (tbMsgTemplates.size() > 0) {
            tbMsgTemplates.stream().forEach(item -> {
                System.err.println(item.getOpenId() + "insertMsgCommon" + item.getUserId());
                if (StringUtils.isNotBlank(item.getOpenId()) && ObjectUtil.isNotEmpty(item.getUserId())){
                    sendMsg(content, item.getOpenId(), modelName, deptName, page, templateCode);
                }
            });
        }
    }

    public void sendMsg(String content, String openId, String type, String remark, String page, String templateCode) {
//        // 消息内容，与模板对应
//        Map<String, Object> data = new HashMap<>();
//        System.err.println(content);
//        data.put("thing2", newMap(content.length() > 20 ? content.substring(0,20) : content));// 通知内容
//        data.put("time3", newMap(DateUtils.getDate()));// 通知时间
//        data.put("thing4", newMap(StringUtils.isNotBlank(type) ? type : "无"));// 提示说明
//        data.put("thing1", newMap(StringUtils.isNotBlank(remark) ? remark : "无"));// 社区名称
//
//        String accessToken = redisCache.getCacheObject("WEIXIN_TOKEN");
//        System.err.println("我获取到的是啥" + accessToken);
//        if (StringUtils.isBlank(accessToken)) {
//            System.err.println("需要重新请求token");
//            getAccess_token();
//            accessToken = redisCache.getCacheObject("WEIXIN_TOKEN");
//        }
//
//        String base = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=";
//        String url = base + accessToken;
//
//        Map<String, Object> requestBody = new HashMap<>();
//        requestBody.put("touser", openId);
//        requestBody.put("template_id", templateCode);
//        requestBody.put("page", page);
//        requestBody.put("data", data);
//        String s = JSON.toJSONString(requestBody);
//
//
//        try {
//            // 发送请求
//            String body = HttpUtil.createPost(url).body(s).execute().body();
//            System.out.println("第一次发送订阅信息：" + body);
//            if (body.contains("access_token is invalid or not latest")) {
//                System.err.println("token过期 需要重新请求token");
//                getAccess_token();
//                accessToken = redisCache.getCacheObject("WEIXIN_TOKEN");
//                url = base + accessToken;
//                body = HttpUtil.createPost(url).body(s).execute().body();
//                System.out.println("第二次发送订阅信息：" + body);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.err.println("发送消息出错了？？？");
//        }
    }

    private static Map<String, Object> newMap(Object o) {
        Map<String, Object> map = new HashMap<>();
        map.put("value", o);
        return map;
    }
}

