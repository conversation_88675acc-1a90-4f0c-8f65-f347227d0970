package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 门店店员对象 tb_merchant_assistant
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbMerchantAssistant extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    private Long deptId;

    /** 所属县（市、区） */
//    @Excel(name = "所属县（市、区）", needMerge = true, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /** 所属街道/乡镇 */
//    @Excel(name = "所属街道/乡镇", needMerge = true, width = 18)
    private String country;

    /** 所属村/社区 */
//    @Excel(name = "所属村/社区", needMerge = true, width = 18)
    private String town;

    /** 所属网格 */
    private Long gridId;

    /** 所属网格 */
    private String gridName;

    /** 门店ID */
    private Long merchantId;
    /**
     * 门店名称
     */
    private String merchantName;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;
    /**
     * 社区ID
     */
    private Long communityId;

    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date birthday;

    private Integer age;

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMerchantId(Long merchantId) 
    {
        this.merchantId = merchantId;
    }

    public Long getMerchantId() 
    {
        return merchantId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    @Size(min = 18, max = 18, message = "证件号码长度不能超过11个字符")
    public String getIdCard() 
    {
        return idCard;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    @Size(min = 0, max = 11, message = "电话长度不能超过11个字符")
    public String getPhone() 
    {
        return phone;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("merchantId", getMerchantId())
            .append("name", getName())
            .append("idCard", getIdCard())
            .append("phone", getPhone())
            .append("createTime", getCreateTime())
            .toString();
    }
}
