package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbGridUserCommunityMapper;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;
import com.ruoyi.microPlatform.service.ITbGridUserCommunityService;

/**
 * 网格员关联小区Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbGridUserCommunityServiceImpl implements ITbGridUserCommunityService 
{
    @Autowired
    private TbGridUserCommunityMapper tbGridUserCommunityMapper;

    /**
     * 查询网格员关联小区
     * 
     * @param id 网格员关联小区主键
     * @return 网格员关联小区
     */
    @Override
    public TbGridUserCommunity selectTbGridUserCommunityById(Long id)
    {
        return tbGridUserCommunityMapper.selectTbGridUserCommunityById(id);
    }

    /**
     * 校验网格员关联小区是否存在
     *
     * @param tbGridUserCommunity
     * @return boolean
     */
    @Override
    public boolean checkTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity){
        TbGridUserCommunity old = tbGridUserCommunityMapper.checkTbGridUserCommunityUnique(tbGridUserCommunity);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询网格员关联小区列表
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 网格员关联小区
     */
    @Override
    public List<TbGridUserCommunity> selectTbGridUserCommunityList(TbGridUserCommunity tbGridUserCommunity)
    {
        return tbGridUserCommunityMapper.selectTbGridUserCommunityList(tbGridUserCommunity);
    }

    /**
     * 导入网格员关联小区
     *
     * @param infos       网格员关联小区列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridUserCommunity(List<TbGridUserCommunity> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入网格员关联小区数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbGridUserCommunity info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbGridUserCommunity old = tbGridUserCommunityMapper.checkTbGridUserCommunityUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbGridUserCommunityMapper.insertTbGridUserCommunity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbGridUserCommunityMapper.updateTbGridUserCommunity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    @Override
    public int insertTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity)
    {
        return tbGridUserCommunityMapper.insertTbGridUserCommunity(tbGridUserCommunity);
    }

    /**
     * 修改网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    @Override
    public int updateTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity)
    {
        return tbGridUserCommunityMapper.updateTbGridUserCommunity(tbGridUserCommunity);
    }

    /**
     * 批量删除网格员关联小区
     * 
     * @param ids 需要删除的网格员关联小区主键
     * @return 结果
     */
    @Override
    public int deleteTbGridUserCommunityByIds(Long[] ids)
    {
        return tbGridUserCommunityMapper.deleteTbGridUserCommunityByIds(ids);
    }

    /**
     * 删除网格员关联小区信息
     * 
     * @param id 网格员关联小区主键
     * @return 结果
     */
    @Override
    public int deleteTbGridUserCommunityById(Long id)
    {
        return tbGridUserCommunityMapper.deleteTbGridUserCommunityById(id);
    }
}
