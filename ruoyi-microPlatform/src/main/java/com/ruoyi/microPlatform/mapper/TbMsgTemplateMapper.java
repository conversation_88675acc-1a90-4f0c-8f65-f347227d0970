package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMsgTemplate;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbMsgTemplateMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TbMsgTemplate selectTbMsgTemplateById(Long id);

    /**
     * 校验【请填写功能名称】是否唯一
     *
     * @param tbMsgTemplate
     * @return 【请填写功能名称】
     */
    public TbMsgTemplate checkTbMsgTemplateUnique(TbMsgTemplate tbMsgTemplate);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param tbMsgTemplate 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TbMsgTemplate> selectTbMsgTemplateList(TbMsgTemplate tbMsgTemplate);

    /**
     * 新增【请填写功能名称】
     *
     * @param tbMsgTemplate 【请填写功能名称】
     * @return 结果
     */
    public int insertTbMsgTemplate(TbMsgTemplate tbMsgTemplate);

    /**
     * 修改【请填写功能名称】
     *
     * @param tbMsgTemplate 【请填写功能名称】
     * @return 结果
     */
    public int updateTbMsgTemplate(TbMsgTemplate tbMsgTemplate);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTbMsgTemplateById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMsgTemplateByIds(Long[] ids);
}
