package com.ruoyi.system.service.impl;

import java.io.IOException;
import java.util.List;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysVideoInfo;
import com.ruoyi.common.utils.ImgUtil;
import com.ruoyi.system.mapper.SysVideoInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.mapper.SysNoticeMapper;
import com.ruoyi.system.service.ISysNoticeService;
import org.springframework.util.ObjectUtils;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {
    @Autowired
    private SysNoticeMapper noticeMapper;

    @Autowired
    private SysVideoInfoMapper videoMapper;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice) {
        return noticeMapper.selectNoticeList(notice);
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int insertNotice(SysNotice notice, String pathUrl) {
        SysNotice sysNotice = dealData(notice, pathUrl);
        return noticeMapper.insertNotice(notice);
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int updateNotice(SysNotice notice) {
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds) {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }

    //判断是否上传视频，更新视频表
    @Override
    public String isUpLoadVideo(SysNotice notice) {
        String videoPath = "";
        if (notice.getNoticeContent().contains("ql-video")) {
            String content = notice.getNoticeContent();
            String[] qlVideos = content.split("ql-video");
            for (int i = 0; i < qlVideos.length; i++) {//循环每一个视频地址
                String video = qlVideos[i];
                if (video.contains("http://")) {
                    String videoName = video.substring(video.indexOf("http://"), video.indexOf(".mp4"));
                    if (i == 1) {
                        videoPath = videoName + ".mp4";
                    }
                    //除了本数据之外的是否还有其他数据使用该地址
                    List<SysVideoInfo> sysVideoInfos = videoMapper.selectSysVideoInfoList(new SysVideoInfo() {{
                        setWebAddr(videoName);
                    }});
                    SysVideoInfo video1 = null;
                    if (sysVideoInfos.size() > 0 && sysVideoInfos.size() == 1) {
                        video1 = sysVideoInfos.get(0);
                    }
                    if (!ObjectUtils.isEmpty(video1) && !ObjectUtils.isEmpty(video1.getDataId())) {
                        if (!video1.getDataId().equals(notice.getNoticeId())) {
                            //判断视频是否被使用
                            return "该视频已被使用，请重新上传视频！";
                        }
                    } else if (!ObjectUtils.isEmpty(video1) && ObjectUtils.isEmpty(video1.getDataId())) {
                        //更新视频表
                        SysVideoInfo finalVideo = video1;
                        videoMapper.updateSysVideoInfo(new SysVideoInfo() {{
                            setDataId(notice.getNoticeId());
                            finalVideo.getId();
                        }});
                    }
                }
            }
        }
        return videoPath;
    }

    /**
     * 地址
     *
     * @param notice
     * @param pathUrl
     * @return
     */
    public SysNotice dealData(SysNotice notice, String pathUrl) {
        String filePath = RuoYiConfig.getUploadNoticePath();
        // 上传并返回新文件名称
        String fileName = "";
        String[] imgList = notice.getNoticeContent().split("<img src=\"data:image/png;base64,");
        for (int i = 1; i < imgList.length; i++) {
            String img64 = imgList[i].substring(0, imgList[i].indexOf("\">"));//取出该数组中的图片base64编码值
            try {
                fileName = ImgUtil.GenerateImage(img64, filePath);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String newContent = notice.getNoticeContent().replace(("data:image/png;base64," + img64), pathUrl + fileName);
            notice.setNoticeContent(newContent);
        }
        imgList = notice.getNoticeContent().split("<img src=\"data:image/jpg;base64,");
        for (int i = 1; i < imgList.length; i++) {
            String img64 = imgList[i].substring(0, imgList[i].indexOf("\">"));//取出该数组中的图片base64编码值
            try {
                fileName = ImgUtil.GenerateImage(img64, filePath);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String newContent = notice.getNoticeContent().replace(("data:image/jpg;base64," + img64), pathUrl + fileName);
            notice.setNoticeContent(newContent);
        }
        imgList = notice.getNoticeContent().split("<img src=\"data:image/jpeg;base64,");
        for (int i = 1; i < imgList.length; i++) {
            String img64 = imgList[i].substring(0, imgList[i].indexOf("\">"));//取出该数组中的图片base64编码值
            try {
                fileName = ImgUtil.GenerateImage(img64, filePath);
            } catch (IOException e) {
                e.printStackTrace();
            }
            String newContent = notice.getNoticeContent().replace(("data:image/jpeg;base64," + img64), pathUrl + fileName);
            notice.setNoticeContent(newContent);
        }
        return notice;
    }
}
