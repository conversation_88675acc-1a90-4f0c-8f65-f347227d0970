package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.service.WeChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeFunctionReservationMapper;
import com.ruoyi.microPlatform.domain.SeFunctionReservation;
import com.ruoyi.microPlatform.service.ISeFunctionReservationService;

/**
 * 开放预约Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeFunctionReservationServiceImpl implements ISeFunctionReservationService {
    @Autowired
    private SeFunctionReservationMapper seFunctionReservationMapper;

    @Autowired
    private WeChatService weChatService;

    /**
     * 查询开放预约
     *
     * @param id 开放预约主键
     * @return 开放预约
     */
    @Override
    public SeFunctionReservation selectSeFunctionReservationById(Long id) {
        SeFunctionReservation seFunctionReservation = seFunctionReservationMapper.selectSeFunctionReservationById(id);
        if (seFunctionReservation.getMsg() == 1){
            handleSend(seFunctionReservation, 3, SecurityUtils.getLoginUser().getUser());
        }
        return seFunctionReservation;
    }

    /**
     * 校验开放预约是否存在
     *
     * @param seFunctionReservation
     * @return boolean
     */
    @Override
    public boolean checkSeFunctionReservation(SeFunctionReservation seFunctionReservation) {
        SeFunctionReservation old = seFunctionReservationMapper.checkSeFunctionReservationUnique(seFunctionReservation);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询开放预约列表
     *
     * @param seFunctionReservation 开放预约
     * @return 开放预约
     */
    @Override
    public List<SeFunctionReservation> selectSeFunctionReservationList(SeFunctionReservation seFunctionReservation) {
        return seFunctionReservationMapper.selectSeFunctionReservationList(seFunctionReservation);
    }

    /**
     * 导入开放预约
     *
     * @param infos           开放预约列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeFunctionReservation(List<SeFunctionReservation> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入开放预约数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeFunctionReservation info : infos) {
            try {
                // 验证是否存在这个数据
                SeFunctionReservation old = seFunctionReservationMapper.checkSeFunctionReservationUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seFunctionReservationMapper.insertSeFunctionReservation(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seFunctionReservationMapper.updateSeFunctionReservation(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增开放预约
     *
     * @param seFunctionReservation 开放预约
     * @return 结果
     */
    @Override
    public int insertSeFunctionReservation(SeFunctionReservation seFunctionReservation) {
        seFunctionReservation.setCreateTime(DateUtils.getNowDate());
        int i = seFunctionReservationMapper.insertSeFunctionReservation(seFunctionReservation);
        if (i > 0) {
            handleSend(seFunctionReservation, 1, SecurityUtils.getLoginUser().getUser());
        }
        return i;
    }

    /**
     * 修改开放预约
     *
     * @param seFunctionReservation 开放预约
     * @return 结果
     */
    @Override
    public int updateSeFunctionReservation(SeFunctionReservation seFunctionReservation) {
        int i = seFunctionReservationMapper.updateSeFunctionReservation(seFunctionReservation);
        if (i > 0 && ObjectUtil.isNotEmpty(seFunctionReservation.getStatus()) && seFunctionReservation.getStatus() == 1) {
            handleSend(seFunctionReservation, 2, null);
        }
        return i;
    }

    @Async("threadPoolTaskExecutor")
    public void handleSend(SeFunctionReservation seFunctionReservation, Integer type, SysUser user) {
        if (type == 1) {
            //TODO: 发送消息通知
            System.err.println("给管理者消息通知");
            weChatService.insertMsg(seFunctionReservation.getDeptId(), "功能室预约",
                    "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/openReservation/list?showTab=true&queryType=1",
                    seFunctionReservation.getTown(), "居民提交了一条新的功能室预约记录");
        } else if (type == 2) {
            System.err.println("给提交者发送审批消息通知");
            //TODO: 给提交者发送审批消息通知
            weChatService.insertMsgCommon(seFunctionReservation.getUserId(), "功能室预约",
                    "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/openReservation/detail?ids=" + seFunctionReservation.getId(), seFunctionReservation.getTown(),
                    "您提交的功能室预约已审批：" + (seFunctionReservation.getAuditResult() == 1 ? "通过。" : "不通过。"));
        }else if (type == 3){
            // 更新已读消息
            System.err.println("查看者" + user.getUserId() + "--提交者" + seFunctionReservation.getUserId());
            if (ObjectUtil.isNotEmpty(seFunctionReservation.getUserId()) && seFunctionReservation.getUserId().longValue() == user.getUserId().longValue()){
                System.err.println("更新已读消息");
                seFunctionReservationMapper.updateSeFunctionReservation(new SeFunctionReservation(){{
                    setId(seFunctionReservation.getId());
                    setMsg(0);
                }});
            }
        }

    }

    /**
     * 批量删除开放预约
     *
     * @param ids 需要删除的开放预约主键
     * @return 结果
     */
    @Override
    public int deleteSeFunctionReservationByIds(Long[] ids) {
        return seFunctionReservationMapper.deleteSeFunctionReservationByIds(ids);
    }

    /**
     * 删除开放预约信息
     *
     * @param id 开放预约主键
     * @return 结果
     */
    @Override
    public int deleteSeFunctionReservationById(Long id) {
        return seFunctionReservationMapper.deleteSeFunctionReservationById(id);
    }

    @Override
    public Map<String, Object> selectSubmitCount(SeFunctionReservation seFunctionReservation) {
        return seFunctionReservationMapper.selectSubmitCount(seFunctionReservation);
    }
}
