package com.ruoyi.dataview.domain.po;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 12345热线中心综合明细对象 db_hotline_detail
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbHotlineDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 工单标识 */
    @Excel(name = "工单标识")
    private String recId;

    /** 地址描述 */
    @Excel(name = "地址描述")
    private String address;

    /** 工单描述 */
    @Excel(name = "工单描述")
    private String eventDesc;

    /** 诉求来源 */
    @Excel(name = "诉求来源")
    private String eventSrcName;

    /** 工单类型 */
    @Excel(name = "工单类型")
    private String recTypeName;

    /** 一级归口类型 */
    @Excel(name = "一级归口类型")
    private String eventTypeName;

    /** 二级归口类型 */
    @Excel(name = "二级归口类型")
    private String mainTypeName;

    /** 三级归口类型 */
    @Excel(name = "三级归口类型")
    private String subTypeName;

    /** 所属区域 */
    @Excel(name = "所属区域")
    private String districtName;

    /** 所属街道 */
    @Excel(name = "所属街道")
    private String streetName;

    /** 一级承办部门 */
    @Excel(name = "一级承办部门")
    private String firstUnitName;

    /** 四级归口 */
    @Excel(name = "四级归口")
    private String thirdTypeName;

    /** 诉求目的 */
    @Excel(name = "诉求目的")
    private String caseGoal;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 工单状态 */
    @Excel(name = "工单状态")
    private String eventStatus;

    /** 办理开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "办理开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handleStartTime;

    /** 办理截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "办理截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handleDeadline;

    /** 第三方平台截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "第三方平台截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date thirdPartyDeadline;

    /** 诉求人 */
    @Excel(name = "诉求人")
    private String appealerName;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String appealerContact;

    /** 是否回访(0否,1是) */
    @Excel(name = "是否回访(0否,1是)")
    private Integer isReturnVisit;

    /** 办理意见 */
    @Excel(name = "办理意见")
    private String handleOpinion;

    /** 办结意见 */
    @Excel(name = "办结意见")
    private String finishOpinion;

    /** 回访满意度 */
    @Excel(name = "回访满意度")
    private String returnVisitScore;

    /** 回访内容 */
    @Excel(name = "回访内容")
    private String returnVisitContent;

    /** 话务员 */
    @Excel(name = "话务员")
    private String operatorName;

    /** 办理结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "办理结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handleEndTime;

}
