package com.ruoyi.microPlatform.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbSituationOrgMapper;
import com.ruoyi.microPlatform.domain.TbSituationOrg;
import com.ruoyi.microPlatform.service.ITbSituationOrgService;

/**
 * 组织情况（非公经济组织/新社会组织）Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class TbSituationOrgServiceImpl implements ITbSituationOrgService {
    @Autowired
    private TbSituationOrgMapper tbSituationOrgMapper;

    /**
     * 查询组织情况（非公经济组织/新社会组织）
     *
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 组织情况（非公经济组织/新社会组织）
     */
    @Override
    public TbSituationOrg selectTbSituationOrgById(Long id) {
        return tbSituationOrgMapper.selectTbSituationOrgById(id);
    }

    /**
     * 校验组织情况（非公经济组织/新社会组织）是否存在
     *
     * @param tbSituationOrg
     * @return boolean
     */
    @Override
    public boolean checkTbSituationOrg(TbSituationOrg tbSituationOrg) {
        TbSituationOrg old = tbSituationOrgMapper.checkTbSituationOrgUnique(tbSituationOrg);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询组织情况（非公经济组织/新社会组织）列表
     *
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 组织情况（非公经济组织/新社会组织）
     */
    @Override
    public List<TbSituationOrg> selectTbSituationOrgList(TbSituationOrg tbSituationOrg) {
        return tbSituationOrgMapper.selectTbSituationOrgList(tbSituationOrg);
    }

    /**
     * 导入组织情况（非公经济组织/新社会组织）
     *
     * @param infos           组织情况（非公经济组织/新社会组织）列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSituationOrg(List<TbSituationOrg> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入组织情况（非公经济组织/新社会组织）数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbSituationOrg info : infos) {
            try {
                // 验证是否存在这个数据
                TbSituationOrg old = tbSituationOrgMapper.checkTbSituationOrgUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbSituationOrgMapper.insertTbSituationOrg(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbSituationOrgMapper.updateTbSituationOrg(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增组织情况（非公经济组织/新社会组织）
     *
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    @Override
    public int insertTbSituationOrg(TbSituationOrg tbSituationOrg) {
        tbSituationOrg.setCreateTime(DateUtils.getNowDate());
        return tbSituationOrgMapper.insertTbSituationOrg(tbSituationOrg);
    }

    /**
     * 修改组织情况（非公经济组织/新社会组织）
     *
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    @Override
    public int updateTbSituationOrg(TbSituationOrg tbSituationOrg) {
        tbSituationOrg.setUpdateTime(DateUtils.getNowDate());
        return tbSituationOrgMapper.updateTbSituationOrg(tbSituationOrg);
    }

    /**
     * 批量删除组织情况（非公经济组织/新社会组织）
     *
     * @param ids 需要删除的组织情况（非公经济组织/新社会组织）主键
     * @return 结果
     */
    @Override
    public int deleteTbSituationOrgByIds(Long[] ids) {
        return tbSituationOrgMapper.deleteTbSituationOrgByIds(ids);
    }

    /**
     * 删除组织情况（非公经济组织/新社会组织）信息
     *
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 结果
     */
    @Override
    public int deleteTbSituationOrgById(Long id) {
        return tbSituationOrgMapper.deleteTbSituationOrgById(id);
    }

    @Override
    public List<CommonBaseCount> situationOrgGroupPartyType() {
        // 固定的分组类别：党委、党总支、党支部
        String[] types = new String[]{"党委", "党总支", "党支部"};
        List<CommonBaseCount> commonBaseCounts = tbSituationOrgMapper.situationOrgGroupPartyType();
        Map<String, Integer> collect = commonBaseCounts.stream().collect(Collectors.toMap(CommonBaseCount::getLable, CommonBaseCount::getCount));
        // 组装返回结果，保证顺序固定且不存在的类别补0
        List<CommonBaseCount> res = new ArrayList<>();
        for (String t : types) {
            CommonBaseCount item = new CommonBaseCount();
            item.setLable(t);
            item.setCount(collect.getOrDefault(t, 0));
            res.add(item);
        }
        return res;
    }
}
