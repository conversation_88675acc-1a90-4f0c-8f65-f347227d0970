package com.ruoyi.system.service;

import com.ruoyi.common.DateBaseTables.Columns;
import com.ruoyi.common.DateBaseTables.DataSourceTable;
import com.ruoyi.common.DateBaseTables.Tables;
import com.ruoyi.common.core.domain.AjaxResult;

import java.util.List;

public interface DataSourceService {
    /**
     * 创建表
     */
    AjaxResult createTable(DataSourceTable table);

    /**
     * 修改表字段
     */
    AjaxResult alterTableField(DataSourceTable table);

    /**
     * 删除表字段
     */
    AjaxResult dropTableField(DataSourceTable table);

    /**
     * 删除表
     */
    AjaxResult dropTable(DataSourceTable table);

    /**
     * 清空表数据
     */
    AjaxResult truncateTable(DataSourceTable table);

    /**
     * 增加表字段
     */
    AjaxResult addTableField(DataSourceTable table);

    /**
     * 数据库的表信息
     */
    List<Tables> getTables(DataSourceTable table);

    /**
     * 表结构信息
     */
    List<Columns> getTableColumns(DataSourceTable table);

    /**
     * 获取字段类型集合
     */
    AjaxResult getFieldTypeList();

    /**
     * 修改表注释
     */
    AjaxResult alterTableComment(DataSourceTable table);
}
