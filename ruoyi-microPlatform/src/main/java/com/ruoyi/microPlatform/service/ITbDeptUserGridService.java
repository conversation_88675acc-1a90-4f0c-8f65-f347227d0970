package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbDeptUserGrid;

/**
 * 社区干部关联网格Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbDeptUserGridService 
{
    /**
     * 查询社区干部关联网格
     * 
     * @param id 社区干部关联网格主键
     * @return 社区干部关联网格
     */
    public TbDeptUserGrid selectTbDeptUserGridById(Long id);

    /**
     * 校验社区干部关联网格是否存在
     *
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 社区干部关联网格
     */
    public boolean checkTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 查询社区干部关联网格列表
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 社区干部关联网格集合
     */
    public List<TbDeptUserGrid> selectTbDeptUserGridList(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 导入社区干部关联网格
     *
     * @param infos       社区干部关联网格列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbDeptUserGrid(List<TbDeptUserGrid> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    public int insertTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 修改社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    public int updateTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid);

    /**
     * 批量删除社区干部关联网格
     * 
     * @param ids 需要删除的社区干部关联网格主键集合
     * @return 结果
     */
    public int deleteTbDeptUserGridByIds(Long[] ids);

    /**
     * 删除社区干部关联网格信息
     * 
     * @param id 社区干部关联网格主键
     * @return 结果
     */
    public int deleteTbDeptUserGridById(Long id);
}
