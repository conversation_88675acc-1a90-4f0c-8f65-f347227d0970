package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 党组织党员双报到关系对象 pb_party_report_relation
 */
@Data
public class PbPartyReportRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关系编号 */
    private String relationCode;

    /** 报到党组织ID */
    private Long orgId;

    /** 报到党组织名称 */
    private String orgName;

    /** 结对社区ID */
    private Long targetCommunityId;

    /** 结对社区名称 */
    private String targetCommunityName;

    /** 对接负责人党员ID */
    private Long contactPersonId;

    /** 对接负责人姓名 */
    private String contactPersonName;

    /** 报到党员ID */
    private Long memberId;

    /** 报到党员姓名 */
    private String memberName;

    /** 居住地社区ID */
    private Long residenceCommunityId;

    /** 居住地社区名称 */
    private String residenceCommunityName;

    /** 居住地详细地址 */
    private String residenceAddress;

    /** 居住证明照片URL */
    private String residenceProofUrl;

    /** 所属部门ID */
    private Long deptId;

    /** 所属县（市、区） */
    private String county;

    /** 所属街道/乡镇 */
    private String country;

    /** 所属村/社区 */
    private String town;

    /** 报到类型（ORG_REPORT组织报到/MEMBER_REPORT个人报到） */
    private String reportType;

    /** 报到时间 */
    private Date reportDate;

    /** 结对协议扫描件URL（组织报到用） */
    private String agreementUrl;

    /** 关系状态（1有效 0失效） */
    private Integer status;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

}
