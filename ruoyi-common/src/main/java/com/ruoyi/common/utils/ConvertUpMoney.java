package com.ruoyi.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @Title: ConvertUpMoney
 * @Description:  将数字金额转换为大写中文金额
 * @date: 2019年6月18日 下午10:52:27
 */
public class ConvertUpMoney {

    // 大写数字
    private static final String[] NUMBERS = {"零","壹","贰","叁","肆","伍","陆","柒","捌","玖"};
    // 整数部分的单位
//    private static final String[] IUNIT = {"元","拾","佰","仟","万","拾","佰","仟","亿","拾","佰","仟","万","拾","佰","仟"};
    private static final String[] IUNIT = {"元","拾","佰","仟","万"};
    // 小数部分的单位
//    private static final String[] DUNIT = {"角","分","厘"};
    private static final String[] DUNIT = {"角","分"};

    private static final String[] NUMB = {"零","零","零","零","零"};
    private static final String[] NUMS = {"零","零"};


    /**
     *  转换为大写的中文金额
     * @param str 字符串类型的 金额数字
     * @return
     */
    public static String toChinese(String str) {
        // 判断输入的金额字符串是否符合要求
        if (StringUtils.isBlank(str) || !str.matches("(-)?[\\d]*(.)?[\\d]*")) {
            return "抱歉，请输入数字！";
        }

        if("0".equals(str) || "0.00".equals(str) || "0.0".equals(str)) {
            return "零,零,零,零,零,零,零";
        }

        // 判断金额数字中是否存在负号"-"
        boolean flag = false;
        if(str.startsWith("-")){
            // 标志位，标志此金额数字为负数
            flag = true;
            str = str.replaceAll("-", "");
        }

        // 去掉金额数字中的逗号","
        str = str.replaceAll(",", "");
        String integerStr;//整数部分数字
        String decimalStr;//小数部分数字

        // 初始化：分离整数部分和小数部分
        if(str.indexOf(".")>0) {
            integerStr = str.substring(0,str.indexOf("."));
            decimalStr = str.substring(str.indexOf(".") + 1);
        }else if(str.indexOf(".")==0) {
            integerStr = "";
            decimalStr = str.substring(1);
        }else {
            integerStr = str;
            decimalStr = "";
        }

        // beyond超出计算能力，直接返回
        if(integerStr.length()>IUNIT.length) {
            return "超出计算能力！";
        }

        // 整数部分数字
        int[] integers = toIntArray(integerStr);

        // 判断整数部分是否存在输入012的情况
        if (integers.length>1 && integers[0] == 0) {
            return "抱歉，输入数字不符合要求！";
        }

        // 小数部分数字
        int[] decimals = toIntArray(decimalStr);
        // 返回最终的大写金额
        String result = getChineseInteger(integers) + getChineseDecimal(decimals);
        if(flag){
            // 如果是负数，加上"负"
            return "负" + result;
        }else{
            return result;
        }
    }

    /**
     *  将字符串转为int数组
     * @param number  数字
     * @return
     */
    private static int[] toIntArray(String number) {
        int[] array = new int[number.length()];
        for(int i = 0;i<number.length();i++) {
            array[i] = Integer.parseInt(number.substring(i,i+1));
        }
        return array;
    }

    /**
     *  将整数部分转为大写的金额
     * @param integers 整数部分数字
     * @return
     */
    public static String getChineseInteger(int[] integers) {
        String[] numstrs = {"零","零","零","零","零"};
        int length = integers.length;
        if (length == 1 && integers[0] == 0) {
            return "零,零,零,零,零,";
        }
        int j = integers.length - 1;
        for(int i = numstrs.length - 1; i > 0; i--) {
            if (j >= 0){
                numstrs[i] = NUMBERS[integers[j]];
                j--;
            }

        }
        return StringUtils.join(numstrs,",") + ",";
    }

    /**
     *  将小数部分转为大写的金额
     * @param decimals 小数部分的数字
     * @return
     */
    private static String getChineseDecimal(int[] decimals) {
        String[] numstrs = {"零","零"};
        int length = decimals.length;
        if (length == 1 && decimals[0] == 0) {
            return "零,零";
        }
        int j = 0;
        for(int i = 0; i < length; i++) {
            if (j <decimals.length){
                numstrs[i] = NUMBERS[decimals[j]];
                j++;
            }

        }
        return StringUtils.join(numstrs,",");
    }

    // Test
    public static void main(String[] args) {
        String number = "12.56";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));

        number = "1234567890563886.123";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));
//
        number = "1601";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));
//
        number = "156,0";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));

        number = "-156,0";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));

        number = "0.12";
        System.out.println(number+": "+ ConvertUpMoney.toChinese(number));
//
//        number = "0.0";
//        System.out.println(number+": "+ConvertUpMoney.toChinese(number));



    }
}