package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重大舆情信息对象 major_opinion_events
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MajorOpinionEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 事件唯一编号 */
    @Excel(name = "事件唯一编号")
    private String eventId;

    /** 事件当前状态（1：待报送； 2：已报送/待处置； 3：处置中； 4：已办结； 5：已归档） */
    @Excel(name = "事件当前状态", readConverterExp = "1=待报送,2=已报送/待处置,3=处置中,4=已办结,5=已归档")
    private Integer eventStatus;

    /** 紧急程度（一般，紧急，特急） */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 舆情级别（一般，较大，重大，特别重大） */
    @Excel(name = "舆情级别")
    private String severityLevel;

    /** 事件标题/摘要 */
    @Excel(name = "事件标题/摘要")
    private String eventTitle;

    /** 事件情况概要（核心内容、数据支撑） */
    @Excel(name = "事件情况概要")
    private String eventSummary;

    /** 事件关键词 */
    @Excel(name = "事件关键词")
    private String keywords;

    /** 涉事主体 */
    @Excel(name = "涉事主体")
    private String involvedEntities;

    /** 影响地域范围（本地、全省、全国） */
    @Excel(name = "影响地域范围")
    private String geographicScope;

    /** 事件发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    /** 附件 */
    @Excel(name = "附件")
    private String accessory;

    /** 事件发生地id */
    @Excel(name = "事件发生地id")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区")
    private String town;

    /** 所属网格Id */
    @Excel(name = "所属网格Id")
    private Long gridId;

    /** 所属网格 */
    @Excel(name = "所属网格")
    private String gridName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventId", getEventId())
            .append("eventStatus", getEventStatus())
            .append("urgencyLevel", getUrgencyLevel())
            .append("severityLevel", getSeverityLevel())
            .append("eventTitle", getEventTitle())
            .append("eventSummary", getEventSummary())
            .append("keywords", getKeywords())
            .append("involvedEntities", getInvolvedEntities())
            .append("geographicScope", getGeographicScope())
            .append("eventTime", getEventTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("accessory", getAccessory())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .toString();
    }
}
