package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 便民电话对象 se_convenient_phone
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SeConvenientPhone extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    private String gridName;
    /** 图标 */
    @Excel(name = "图标", cellType = Excel.ColumnType.IMAGE)
    private String icon;

    /** 便民服务类型 */
    @Excel(name = "便民服务名称")
    private String name;

    @Excel(name = "便民服务内容")
    private String type;

    private String lat;

    /**  */
    private String lng;

    /** 电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 资质照片 */
    private String image;

    /** 状态（1可用，0待审核，2停用） */
    private Integer status;

    /** 审核人 */
    private String auditUser;

    /** 审核时间 */
    private Date auditTime;

    /** 添加方式(1 管理端添加，2居民推荐) */
//    @Excel(name = "添加方式")
    private Integer addType;

    /** 创建账户ID */
    private Long createId;

    private Integer queryType;

    private Integer auditResult;

    /** 回复意见 */
//    @Excel(name = "回复意见")
    private String auditReason;

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIcon(String icon) 
    {
        this.icon = icon;
    }

    public String getIcon() 
    {
        return icon;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }
    @Size(min = 0, max = 11, message = "电话长度不能超过11个字符")
    public String getPhone() 
    {
        return phone;
    }
    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setAuditUser(String auditUser) 
    {
        this.auditUser = auditUser;
    }

    public String getAuditUser() 
    {
        return auditUser;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAddType(Integer addType)
    {
        this.addType = addType;
    }

    public Integer getAddType()
    {
        return addType;
    }
    public void setCreateId(Long createId) 
    {
        this.createId = createId;
    }

    public Long getCreateId() 
    {
        return createId;
    }

    public Integer getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(Integer auditResult) {
        this.auditResult = auditResult;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("name", getName())
            .append("icon", getIcon())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("phone", getPhone())
            .append("image", getImage())
            .append("status", getStatus())
            .append("auditUser", getAuditUser())
            .append("auditTime", getAuditTime())
            .append("addType", getAddType())
            .append("createId", getCreateId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
