package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbPartyCenter;
import com.ruoyi.microPlatform.service.ITbPartyCenterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 党群服务中心报到情况Controller
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/gridData/partyCenter")
public class TbPartyCenterController extends BaseController
{
    @Autowired
    private ITbPartyCenterService tbPartyCenterService;

    /**
     * 查询党群服务中心报到情况列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbPartyCenter tbPartyCenter)
    {
        startPage();
        List<TbPartyCenter> list = tbPartyCenterService.selectTbPartyCenterList(tbPartyCenter);
        return getDataTable(list);
    }

    /**
     * 导出党群服务中心报到情况列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:export')")
    @Log(title = "党群服务中心报到情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbPartyCenter tbPartyCenter)
    {
        List<TbPartyCenter> list = tbPartyCenterService.selectTbPartyCenterList(tbPartyCenter);
        ExcelUtil<TbPartyCenter> util = new ExcelUtil<TbPartyCenter>(TbPartyCenter.class);
        util.exportExcel(response, list, "党群服务中心报到情况数据");
    }

    @Log(title = "党群服务中心报到情况", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbPartyCenter> util = new ExcelUtil<TbPartyCenter>(TbPartyCenter.class);
        List<TbPartyCenter> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbPartyCenterService.importTbPartyCenter(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbPartyCenter> util = new ExcelUtil<TbPartyCenter>(TbPartyCenter.class);
        util.exportTemplateExcel(response, "党群服务中心报到情况数据");
    }

    /**
     * 获取党群服务中心报到情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbPartyCenterService.selectTbPartyCenterById(id));
    }

    /**
     * 新增党群服务中心报到情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:add')")
    @Log(title = "党群服务中心报到情况", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbPartyCenter tbPartyCenter)
    {
        tbPartyCenter.setCreateBy(getUsername());
        if (tbPartyCenterService.checkTbPartyCenter(tbPartyCenter)){
            return error("数据已存在！");
        }
        return toAjax(tbPartyCenterService.insertTbPartyCenter(tbPartyCenter));
    }

    /**
     * 修改党群服务中心报到情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:edit')")
    @Log(title = "党群服务中心报到情况", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbPartyCenter tbPartyCenter)
    {
        tbPartyCenter.setUpdateBy(getUsername());
        if (tbPartyCenterService.checkTbPartyCenter(tbPartyCenter)){
            return error("数据已存在！");
        }
        return toAjax(tbPartyCenterService.updateTbPartyCenter(tbPartyCenter));
    }

    /**
     * 删除党群服务中心报到情况
     */
    @PreAuthorize("@ss.hasPermi('gridData:partyCenter:remove')")
    @Log(title = "党群服务中心报到情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbPartyCenterService.deleteTbPartyCenterByIds(ids));
    }
}
