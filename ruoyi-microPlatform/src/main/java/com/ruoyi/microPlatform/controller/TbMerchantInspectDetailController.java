package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;
import com.ruoyi.microPlatform.service.ITbMerchantInspectDetailService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 门店巡查基础检查项目Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridManage/merchantInspectDetail")
public class TbMerchantInspectDetailController extends BaseController
{
    @Autowired
    private ITbMerchantInspectDetailService tbMerchantInspectDetailService;

    /**
     * 查询门店巡查基础检查项目列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        startPage();
        List<TbMerchantInspectDetail> list = tbMerchantInspectDetailService.selectTbMerchantInspectDetailList(tbMerchantInspectDetail);
        return getDataTable(list);
    }

    /**
     * 导出门店巡查基础检查项目列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:export')")
    @Log(title = "门店巡查基础检查项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        List<TbMerchantInspectDetail> list = tbMerchantInspectDetailService.selectTbMerchantInspectDetailList(tbMerchantInspectDetail);
        ExcelUtil<TbMerchantInspectDetail> util = new ExcelUtil<TbMerchantInspectDetail>(TbMerchantInspectDetail.class);
        util.exportExcel(response, list, "门店巡查基础检查项目数据");
    }

    @Log(title = "门店巡查基础检查项目", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbMerchantInspectDetail> util = new ExcelUtil<TbMerchantInspectDetail>(TbMerchantInspectDetail.class);
        List<TbMerchantInspectDetail> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbMerchantInspectDetailService.importTbMerchantInspectDetail(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbMerchantInspectDetail> util = new ExcelUtil<TbMerchantInspectDetail>(TbMerchantInspectDetail.class);
        util.exportTemplateExcel(response, "门店巡查基础检查项目数据");
    }

    /**
     * 获取门店巡查基础检查项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(tbMerchantInspectDetailService.selectTbMerchantInspectDetailById(id));
    }

    /**
     * 新增门店巡查基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:add')")
    @Log(title = "门店巡查基础检查项目", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        tbMerchantInspectDetail.setCreateBy(getUsername());
        if (tbMerchantInspectDetailService.checkTbMerchantInspectDetail(tbMerchantInspectDetail)){
            return error("数据已存在！");
        }
        return toAjax(tbMerchantInspectDetailService.insertTbMerchantInspectDetail(tbMerchantInspectDetail));
    }

    /**
     * 修改门店巡查基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:edit')")
    @Log(title = "门店巡查基础检查项目", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        tbMerchantInspectDetail.setUpdateBy(getUsername());
        if (tbMerchantInspectDetailService.checkTbMerchantInspectDetail(tbMerchantInspectDetail)){
            return error("数据已存在！");
        }
        return toAjax(tbMerchantInspectDetailService.updateTbMerchantInspectDetail(tbMerchantInspectDetail));
    }

    /**
     * 删除门店巡查基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:merchantInspectDetail:remove')")
    @Log(title = "门店巡查基础检查项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(tbMerchantInspectDetailService.deleteTbMerchantInspectDetailByIds(ids));
    }


}
