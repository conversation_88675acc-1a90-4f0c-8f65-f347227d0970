package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.microPlatform.mapper.TbResidentMemberTypeMapper;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 居民住户成员信息对象 tb_resident_member
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbResidentMember extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    //@Excel(name = "所属部门")
    private Long deptId;

    /** 所属县（市、区） */
    //@Excel(name = "所属县（市、区）")
    private String county;

    /** 所属街道/乡镇 */
    //@Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    //@Excel(name = "所属村/社区")
    private String town;

    /** 所属网格 */
    //@Excel(name = "所属网格")
    private Long gridId;

    /** 所属网格 */
    //@Excel(name = "所属网格")
    private String gridName;

    /** 关联住户 */
    private Long residentId;

    /** 与户主关系 */
    @Excel(name = "与户主关系")
    private String householdRelation;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 性别 mz_type*/
    @Excel(name = "性别", comboReadDict = true, dictType = "sys_user_sex")
    private String sex;

    /** 民族 */
    @Excel(name = "民族", comboReadDict = true, dictType = "mz_type")
    private String nation;

    /** 政治面貌 */
    @Excel(name = "政治面貌", comboReadDict = true, dictType = "politic_type")
    private String politicalStatus;

    /** 学历 */
    @Excel(name = "学历", comboReadDict = true, dictType = "education_type")
    private String educationBackground;

    /** 户籍地 */
    @Excel(name = "户籍地")
    private String registerResidence;

    /** 婚姻状况 */
    @Excel(name = "婚姻状况", comboReadDict = true, dictType = "hyzk_type")
    private String maritalStatus;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 出生年月 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date birthday;

    /** 特长 */
    @Excel(name = "特长")
    private String talent;

    /** 电话 */
    @Excel(name = "联系方式")
    private String phone;

    @Excel(name = "人员标签", comboReadDict = true, dictType = "person_type")
    private String personLabel;

    /** 工作单位或从事职业 */
    @Excel(name = "工作单位或从事职业")
    private String jobUnit;

    /** 党组织关系所在地 */
    @Excel(name = "党组织关系所在地")
    private String partyOrganizationLocation;

    @Excel(name = "所在社会组织名称")
    private String socialOrg;

    /** 是否是新就业群体0否 1是 */
//    @Excel(name = "是否是新就业群体", combo = {"是", "否"}, readConverterExp = "1=是,0=否")
    private String hasHewGroup;

    /** 新就业群体类型 */
//    @Excel(name = "新就业群体类型")
    private String groupType;

    /** 所属平台 （顺丰、中通、美团、饿了么、滴滴、其他） */
//    @Excel(name = "所属平台")
    private String sitePlatform;

    /** 所属站点 */
    private Long siteId;
    /**
     * 所在行业协会商会id
     */
    private Long associateId;


    /** 是否为行业协会商会负责人(0否 1是) */
//    @Excel(name = "是否为行业协会商会负责人", combo = {"是", "否"}, readConverterExp = "1=是,0=否")
    private String  hasDirector;

    private String hasNewGroup;

    /** 协会名称 */
//    @Excel(name = "所在行业协会商会")
    private String  associateName;

    /** 职务('会长','副会长','秘书长') */
//    @Excel(name = "行业协会商会职务", combo = {"会长", "副会长", "秘书长"})
    private String  position;

    /**
     * 年龄
     */
    private Integer age;

    //成员标签配置
    private List<TbResidentMemberType> tbResidentMemberTypeList;

    //成员标签value
    private List<String>  typeValueList;

    /**
     * 人员标签检索---废弃
     */
//    private List<String> list;
    /**
     * 小区ID
     */
    private Long communityId;
    /**
     * 标签类型id组
     */
    private Integer[] personTypeIds;
    /**
     * 标签类型对象数组
     */
    private List<TbPersonType> personTypes;
    /**
     * 人员标签是否更新状态
     */
    private Integer label;

    private List<Integer> list = new ArrayList<>();
    /**
     * 旧手机号
     */
    private String oldPhone;

    private List<Long> mainIds;

    private Integer isExport;

    //所属小区名称
    private String communityName;


    /**
     * 所属小区的空间区域
     */
    //@Excel(name = "所属小区的空间区域", type = Excel.Type.EXPORT)
    private String geometry;

    /**
     * 楼栋号
     */
    private String buildingNum;

    /**
     * 单元号
     */
    private String unitNum;

    /**
     * 楼层号
     */
    private String floorNum;

    /**
     * 户号
     */
    private String roomNum;
    /**
     * 网格排序
     */
    private Integer sort;

    private Integer offset;

    private Integer pageSize;

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getBuildingNum() {
        return buildingNum;
    }

    public void setBuildingNum(String buildingNum) {
        this.buildingNum = buildingNum;
    }

    public String getUnitNum() {
        return unitNum;
    }

    public void setUnitNum(String unitNum) {
        this.unitNum = unitNum;
    }

    public String getFloorNum() {
        return floorNum;
    }

    public void setFloorNum(String floorNum) {
        this.floorNum = floorNum;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public Integer getIsExport() {
        return isExport;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public void setIsExport(Integer isExport) {
        this.isExport = isExport;
    }

    public List<Long> getMainIds() {
        return mainIds;
    }

    public void setMainIds(List<Long> mainIds) {
        this.mainIds = mainIds;
    }

    public String getOldPhone() {
        return oldPhone;
    }

    public void setOldPhone(String oldPhone) {
        this.oldPhone = oldPhone;
    }

    public Integer getLabel() {
        return label;
    }

    public void setLabel(Integer label) {
        this.label = label;
    }

    public List<Integer> getList() {
        return list;
    }

    public void setList(List<Integer> list) {
        this.list = list;
    }

    public List<TbPersonType> getPersonTypes() {
        return personTypes;
    }

    public void setPersonTypes(List<TbPersonType> personTypes) {
        this.personTypes = personTypes;
//        if (personTypes != null && personTypes.size() > 0){
//            String personLabel = personTypes.stream().map(TbPersonType::getLabel).collect(Collectors.joining(","));
//            this.setPersonLabel(personLabel);
//            System.err.println("??" + this.personLabel);
//        }
    }

    public Integer[] getPersonTypeIds() {
        return personTypeIds;
    }

    public void setPersonTypeIds(Integer[] personTypeIds) {
        this.personTypeIds = personTypeIds;
    }

    public String getHasNewGroup() {
        return hasNewGroup;
    }

    public void setHasNewGroup(String hasNewGroup) {
        this.hasNewGroup = hasNewGroup;
    }

    public String getSitePlatform() {
        return sitePlatform;
    }

    public void setSitePlatform(String sitePlatform) {
        this.sitePlatform = sitePlatform;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getAssociateId() {
        return associateId;
    }

    public void setAssociateId(Long associateId) {
        this.associateId = associateId;
    }


    public String getAssociateName() {
        return associateName;
    }

    public String getHasHewGroup() {
        return hasHewGroup;
    }

    public void setHasHewGroup(String hasHewGroup) {
        this.hasHewGroup = hasHewGroup;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public void setAssociateName(String associateName) {
        this.associateName = associateName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getHasDirector() {
        return hasDirector;
    }

    public void setHasDirector(String hasDirector) {
        this.hasDirector = hasDirector;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public String getPersonLabel() {
        return personLabel;
    }

    public void setPersonLabel(String personLabel) {
        this.personLabel = personLabel;
    }

    public String getSocialOrg() {
        return socialOrg;
    }

    public void setSocialOrg(String socialOrg) {
        this.socialOrg = socialOrg;
    }

    public String getPartyOrganizationLocation() {
        return partyOrganizationLocation;
    }

    public void setPartyOrganizationLocation(String partyOrganizationLocation) {
        this.partyOrganizationLocation = partyOrganizationLocation;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setResidentId(Long residentId) 
    {
        this.residentId = residentId;
    }

    public Long getResidentId() 
    {
        return residentId;
    }
    public void setHouseholdRelation(String householdRelation) 
    {
        this.householdRelation = householdRelation;
    }

    public String getHouseholdRelation() 
    {
        return householdRelation;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }
    public void setNation(String nation) 
    {
        this.nation = nation;
    }

    public String getNation() 
    {
        return nation;
    }
    public void setBirthday(Date birthday)
    {
        this.birthday = birthday;
    }

    public Date getBirthday()
    {
        return birthday;
    }
    public void setPoliticalStatus(String politicalStatus) 
    {
        this.politicalStatus = politicalStatus;
    }

    public String getPoliticalStatus() 
    {
        return politicalStatus;
    }
    public void setEducationBackground(String educationBackground) 
    {
        this.educationBackground = educationBackground;
    }

    public String getEducationBackground() 
    {
        return educationBackground;
    }
    public void setRegisterResidence(String registerResidence) 
    {
        this.registerResidence = registerResidence;
    }

    public String getRegisterResidence() 
    {
        return registerResidence;
    }
    public void setMaritalStatus(String maritalStatus) 
    {
        this.maritalStatus = maritalStatus;
    }

    public String getMaritalStatus() 
    {
        return maritalStatus;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setJobUnit(String jobUnit) 
    {
        this.jobUnit = jobUnit;
    }

    public String getJobUnit() 
    {
        return jobUnit;
    }
    public void setTalent(String talent) 
    {
        this.talent = talent;
    }

    public String getTalent() 
    {
        return talent;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public List<TbResidentMemberType> getTbResidentMemberTypeList() {
        return tbResidentMemberTypeList;
    }

    public void setTbResidentMemberTypeList(List<TbResidentMemberType> tbResidentMemberTypeList) {
        this.tbResidentMemberTypeList = tbResidentMemberTypeList;
    }

    public List<String> getTypeValueList() {
        return typeValueList;
    }

    public void setTypeValueList(List<String> typeValueList) {
        this.typeValueList = typeValueList;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .append("residentId", getResidentId())
            .append("householdRelation", getHouseholdRelation())
            .append("name", getName())
            .append("sex", getSex())
            .append("nation", getNation())
            .append("birthday", getBirthday())
            .append("politicalStatus", getPoliticalStatus())
            .append("educationBackground", getEducationBackground())
            .append("registerResidence", getRegisterResidence())
            .append("maritalStatus", getMaritalStatus())
            .append("idCard", getIdCard())
            .append("jobUnit", getJobUnit())
            .append("partyOrganizationLocation", getPartyOrganizationLocation())
            .append("talent", getTalent())
            .append("phone", getPhone())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
