<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbGridInspectMapper">

    <resultMap type="TbGridInspect" id="TbGridInspectResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="name" column="name"/>
        <result property="inspectResult" column="inspect_result"/>
        <result property="abnormalSituation" column="abnormal_situation"/>
        <result property="remark" column="remark"/>
        <result property="accessory" column="accessory"/>
        <result property="personName" column="person_name"/>
        <result property="personPone" column="person_pone"/>
        <result property="personId" column="person_id"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="addr" column="addr"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="statusCount" column="status_count"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="handleCount" column="handle_count"/>

    </resultMap>

    <sql id="selectTbGridInspectVo">
        select id, dept_id, county, country, town, grid_name, grid_id, name, inspect_result, abnormal_situation, remark,
         accessory, person_name, person_pone, person_id, lat, lng, addr, create_time, status, update_by, update_time from tb_grid_inspect
    </sql>

    <select id="selectTbGridInspectList" parameterType="TbGridInspect" resultMap="TbGridInspectResult">
        select gi.id, gi.dept_id, gi.county, gi.country, gi.town, gi.grid_name, gi.grid_id, gi.name, gi.inspect_result, gi.abnormal_situation, gi.remark,
        gi.accessory, gi.person_name, gi.person_pone, gi.person_id, gi.lat, gi.lng, gi.addr, gi.create_time, gi.status, gi.update_by, gi.update_time,
        COALESCE(ssi.handle_count, 0) AS handle_count
        from tb_grid_inspect gi
        LEFT JOIN (
        SELECT form_id, COUNT(*) AS handle_count
        FROM se_subsequent_info
        WHERE form_type = '网格巡查'
        GROUP BY form_id
        ) ssi ON gi.id = ssi.form_id
        <where>
            <if test="deptId != null ">and (gi.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = gi.dept_id))</if>
            <if test="county != null  and county != ''">and gi.county = #{county}</if>
            <if test="country != null  and country != ''">and gi.country = #{country}</if>
            <if test="town != null  and town != ''">and gi.town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and gi.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and gi.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and gi.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name != null  and name != ''">and gi.name like concat('%', #{name}, '%')</if>
            <if test="inspectResult != null ">and gi.inspect_result = #{inspectResult}</if>
            <if test="abnormalSituation != null  and abnormalSituation != ''">and gi.abnormal_situation =
                #{abnormalSituation}
            </if>
            <if test="accessory != null  and accessory != ''">and gi.accessory = #{accessory}</if>
            <if test="personName != null  and personName != ''">and gi.person_name like concat('%', #{personName}, '%')
            </if>
            <if test="personPone != null  and personPone != ''">and gi.person_pone = #{personPone}</if>
            <if test="personId != null ">and gi.person_id = #{personId}</if>
            <if test="lat != null  and lat != ''">and gi.lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and gi.lng = #{lng}</if>
            <if test="addr != null  and addr != ''">and gi.addr = #{addr}</if>
            <if test="status != null ">and gi.status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and gi.create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectTbGridInspectById" parameterType="Long" resultMap="TbGridInspectResult">
        <include refid="selectTbGridInspectVo"/>
        where id = #{id}
    </select>

    <select id="checkTbGridInspectUnique" parameterType="TbGridInspect" resultMap="TbGridInspectResult">
        select id from tb_grid_inspect where name = #{name}
        <if test="gridId != null">
            and grid_id = #{gridId}
        </if>
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbGridInspect" parameterType="TbGridInspect" useGeneratedKeys="true" keyProperty="id">
        insert into tb_grid_inspect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="name != null">name,</if>
            <if test="inspectResult != null">inspect_result,</if>
            <if test="abnormalSituation != null">abnormal_situation,</if>
            <if test="remark != null">remark,</if>
            <if test="accessory != null">accessory,</if>
            <if test="personName != null">person_name,</if>
            <if test="personPone != null">person_pone,</if>
            <if test="personId != null">person_id,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="addr != null">addr,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="name != null">#{name},</if>
            <if test="inspectResult != null">#{inspectResult},</if>
            <if test="abnormalSituation != null">#{abnormalSituation},</if>
            <if test="remark != null">#{remark},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="personName != null">#{personName},</if>
            <if test="personPone != null">#{personPone},</if>
            <if test="personId != null">#{personId},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="addr != null">#{addr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbGridInspect" parameterType="TbGridInspect">
        update tb_grid_inspect
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="inspectResult != null">inspect_result = #{inspectResult},</if>
            <if test="abnormalSituation != null">abnormal_situation = #{abnormalSituation},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="personName != null">person_name = #{personName},</if>
            <if test="personPone != null">person_pone = #{personPone},</if>
            <if test="personId != null">person_id = #{personId},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbGridInspectById" parameterType="Long">
        delete from tb_grid_inspect where id = #{id}
    </delete>

    <delete id="deleteTbGridInspectByIds" parameterType="String">
        delete from tb_grid_inspect where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbGridInspectByStatus" resultMap="TbGridInspectResult">
        SELECT
            status,
            COUNT(*) AS status_count
        FROM
            tb_grid_inspect
        where
            grid_id = #{gridId}
        GROUP BY status;
    </select>

    <select id="selectTbGridInspectByUpdateTime" resultMap="TbGridInspectResult">
        SELECT
            person_name, create_time, inspect_result, status
        FROM
            tb_grid_inspect
        where
            grid_id = #{gridId}
        order by create_time desc limit 1
    </select>

    <select id="selectSubmitCount" parameterType="TbGridInspect" resultType="map">
        select count(*) as submitCount, max(create_time) as submitTime from tb_grid_inspect
        where person_id = #{personId}
        <if test="gridId != null">
            and grid_id = #{gridId}
        </if>
    </select>

    <select id="selectTbGridInspectSize" resultType="int">
        SELECT COUNT(*) AS total_count
        FROM tb_grid_inspect where grid_id = #{gridId}
    </select>

    <select id="selectMonthlyStats" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.util.Map">
        SELECT DATE_FORMAT(create_time, '%Y%m') AS month, COUNT(*) AS total
        FROM tb_grid_inspect
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_inspect.dept_id))</if>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
        GROUP BY month
        ORDER BY month
    </select>

    <select id="selectCount" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="int">
        SELECT COUNT(*) AS total
        FROM tb_grid_inspect
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_inspect.dept_id))</if>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
    </select>
</mapper>