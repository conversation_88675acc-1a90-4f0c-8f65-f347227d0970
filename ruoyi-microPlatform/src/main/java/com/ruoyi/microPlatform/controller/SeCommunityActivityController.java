package com.ruoyi.microPlatform.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.service.ISeCommunityActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 社区活动Controller
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/sentiment/activity")
public class SeCommunityActivityController extends BaseController {
    @Autowired
    private ISeCommunityActivityService seCommunityActivityService;

    /**
     * 查询社区活动列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeCommunityActivity seCommunityActivity) {
        startPage();
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(seCommunityActivity.getDeptId())){
                seCommunityActivity.setDeptId(getDataDeptId());
            }
        }
        List<SeCommunityActivity> list = seCommunityActivityService.selectSeCommunityActivityList(seCommunityActivity);
        return getDataTable(list);
    }


    /**
     * 查询社区活动列表 任务数，和报名数
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:list')")
    @GetMapping("/mapSize")
    public AjaxResult mapSize(SeCommunityActivity seCommunityActivity) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(seCommunityActivity.getDeptId())){
                seCommunityActivity.setDeptId(getDataDeptId());
            }
        }
        Map<String,Integer> map = seCommunityActivityService.selectSeCommunityActivityMap(seCommunityActivity);
        return success(map);
    }

    /**
     * 查询当前用户已经报名的活动
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:list')")
    @GetMapping("/listByUserId")
    public TableDataInfo listByUserId(SeCommunityActivity seCommunityActivity) {
        startPage();
        if (ObjectUtil.isEmpty(seCommunityActivity.getUserId())){
            seCommunityActivity.setUserId(getUserId());
        }
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(seCommunityActivity.getDeptId())){
                seCommunityActivity.setDeptId(getDataDeptId());
            }
        }
        List<SeCommunityActivity> list = seCommunityActivityService.selectSeCommunityActivityListByUserId(seCommunityActivity);
        return getDataTable(list);
    }


    /**
     * 导出社区活动列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:export')")
    @Log(title = "社区活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeCommunityActivity seCommunityActivity) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(seCommunityActivity.getDeptId())){
                seCommunityActivity.setDeptId(getDataDeptId());
            }
        }
        List<SeCommunityActivity> list = seCommunityActivityService.selectSeCommunityActivityList(seCommunityActivity);
        ExcelUtil<SeCommunityActivity> util = new ExcelUtil<SeCommunityActivity>(SeCommunityActivity.class);
        util.exportExcel(response, list, "社区活动数据");
    }

    @Log(title = "社区活动", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:activity:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SeCommunityActivity> util = new ExcelUtil<SeCommunityActivity>(SeCommunityActivity.class);
        List<SeCommunityActivity> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seCommunityActivityService.importSeCommunityActivity(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SeCommunityActivity> util = new ExcelUtil<SeCommunityActivity>(SeCommunityActivity.class);
        util.exportTemplateExcel(response, "社区活动数据");
    }

    /**
     * 获取社区活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        SysUser user = getLoginUser().getUser();
        return success(seCommunityActivityService.selectSeCommunityActivityById(id,user));
    }

    public void getDeptInfo(SeCommunityActivity seCommunityActivity){
        if (ObjectUtil.isEmpty(seCommunityActivity.getDeptId())){
            seCommunityActivity.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(seCommunityActivity.getDeptId());
        if (ObjectUtil.isNotEmpty(dept)){
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2){
                seCommunityActivity.setCounty(split[1]);
                if (split.length >= 3){
                    seCommunityActivity.setCountry(split[2]);
                    if (split.length >= 4){
                        seCommunityActivity.setTown(split[3]);
                    }
                }
            }
        }
    }

    /**
     * 新增社区活动
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:add')")
    @Log(title = "社区活动", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeCommunityActivity seCommunityActivity) {
        seCommunityActivity.setCreateBy(getNickName());
        getDeptInfo(seCommunityActivity);
        if (seCommunityActivityService.checkSeCommunityActivity(seCommunityActivity)) {
            return error("数据已存在！");
        }
        return toAjax(seCommunityActivityService.insertSeCommunityActivity(seCommunityActivity));
    }

    /**
     * 修改社区活动
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:edit')")
    @Log(title = "社区活动", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeCommunityActivity seCommunityActivity) {
        seCommunityActivity.setUpdateBy(getNickName());
        if (seCommunityActivityService.checkSeCommunityActivity(seCommunityActivity)) {
            return error("数据已存在！");
        }
        return toAjax(seCommunityActivityService.updateSeCommunityActivity(seCommunityActivity));
    }



    /**
     * 删除社区活动
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activity:remove')")
    @Log(title = "社区活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(seCommunityActivityService.deleteSeCommunityActivityByIds(ids));
    }


    /**
     *  社区活动接口：未开始的，正在进行中的，已结束的（不同状态切换，不用一起返回）
     *     如果是许昌市deptLevel == 4：需要有个总数，再有各县区数
     *     如果是县区级deptLevel == 5：需要有个总数，再有各乡镇/街道数
     *     如果是县区级deptLevel == 6：需要有个总数，再有各村/社区数
     * @param taskStatusType  活动状态：0未开始，1进行中，2已结束，null不筛选
     * @return
     */
    @GetMapping("/actCountList")
    public AjaxResult selectActCountList(@RequestParam(value = "taskStatusType", required = false) Integer taskStatusType)
    {
        Integer deptLevel = SecurityUtils.getLoginUser().getUser().getDept().getDeptLevel();
        if (deptLevel > 6){
            return success(Collections.emptyMap());
        }
        String county = null;
        String country = null;

        if (deptLevel == 5){
            county = SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        } else if (deptLevel == 6){
            SeCommunityActivity activity = new SeCommunityActivity();
            getDeptInfo(activity);
            county = activity.getCounty();
            country = activity.getCountry();
        }
        Map<String, Object> map = seCommunityActivityService.selectActCountList(deptLevel, county, country,taskStatusType);

        return success(map);
    }
}
