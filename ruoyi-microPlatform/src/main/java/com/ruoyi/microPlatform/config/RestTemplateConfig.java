package com.ruoyi.microPlatform.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * RestTemplate 配置类：用于定义 RestTemplate Bean 并设置超时时间
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                // 设置连接超时时间
                .setConnectTimeout(Duration.ofSeconds(10))
                // 设置读取超时时间
                .setReadTimeout(Duration.ofSeconds(30))
                .build();
    }
}