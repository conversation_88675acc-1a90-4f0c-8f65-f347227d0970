package com.ruoyi.microPlatform.scheduled;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component(value = "signReminderTask")
public class SignReminderTask {
    @Autowired
    private ITbResidentMemberService residentMemberService;

    /**
     * 更新人员表中的年龄
     */
    public void updateAge(){
        residentMemberService.updatePersonLabel();
    }
}
