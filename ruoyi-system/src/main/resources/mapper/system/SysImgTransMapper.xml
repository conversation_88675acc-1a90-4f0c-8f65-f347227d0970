<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysImgTransMapper">
    
    <resultMap type="SysImgTrans" id="SysImgTransResult">
        <result property="id"    column="id"    />
        <result property="srcImg"    column="src_img"    />
        <result property="targetImg"    column="target_img"    />
        <result property="isTrans"    column="is_trans"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
    </resultMap>

    <sql id="selectSysImgTransVo">
        select id, src_img, target_img, is_trans, is_delete, user_id, user_name from sys_img_trans
    </sql>

    <select id="selectSysImgTransList" parameterType="SysImgTrans" resultMap="SysImgTransResult">
        <include refid="selectSysImgTransVo"/>
        <where>  
            <if test="srcImg != null  and srcImg != ''"> and src_img = #{srcImg}</if>
            <if test="targetImg != null  and targetImg != ''"> and target_img = #{targetImg}</if>
            <if test="isTrans != null "> and is_trans = #{isTrans}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
    </select>
    
    <select id="selectSysImgTransById" parameterType="Long" resultMap="SysImgTransResult">
        <include refid="selectSysImgTransVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysImgTrans" parameterType="SysImgTrans" useGeneratedKeys="true" keyProperty="id">
        insert into sys_img_trans
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="srcImg != null">src_img,</if>
            <if test="targetImg != null">target_img,</if>
            <if test="isTrans != null">is_trans,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="srcImg != null">#{srcImg},</if>
            <if test="targetImg != null">#{targetImg},</if>
            <if test="isTrans != null">#{isTrans},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
         </trim>
    </insert>

    <update id="updateSysImgTrans" parameterType="SysImgTrans">
        update sys_img_trans
        <trim prefix="SET" suffixOverrides=",">
            <if test="srcImg != null">src_img = #{srcImg},</if>
            <if test="targetImg != null">target_img = #{targetImg},</if>
            <if test="isTrans != null">is_trans = #{isTrans},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysImgTransById" parameterType="Long">
        delete from sys_img_trans where id = #{id}
    </delete>

    <delete id="deleteSysImgTransByIds" parameterType="String">
        delete from sys_img_trans where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>