package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbGridInspect;
import com.ruoyi.microPlatform.service.ITbGridInspectService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格巡查Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridManage/gridInspect")
public class TbGridInspectController extends BaseController
{
    @Autowired
    private ITbGridInspectService tbGridInspectService;

    /**
     * 查询网格巡查列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbGridInspect tbGridInspect)
    {
        startPage();
        handle(tbGridInspect);
        List<TbGridInspect> list = tbGridInspectService.selectTbGridInspectList(tbGridInspect);
        return getDataTable(list);
    }

    public void handle(TbGridInspect tbGridInspect) {
        if (ObjectUtil.isNotEmpty(tbGridInspect.getQueryType())) {
            if (tbGridInspect.getQueryType() == 1) {
                // 查询个人提交的审核通过的
                tbGridInspect.setPersonId(getUserId());
            }
        }

        if (!SecurityUtils.isAdmin(getUserId()) && ObjectUtil.isEmpty(tbGridInspect.getGridId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbGridInspect.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // // 只能查看相应部门下的网格巡查数据
                if (ObjectUtil.isEmpty(tbGridInspect.getDeptId()) && ObjectUtil.isEmpty(tbGridInspect.getGridId())) {
                    tbGridInspect.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出网格巡查列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:export')")
    @Log(title = "网格巡查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbGridInspect tbGridInspect)
    {
        handle(tbGridInspect);
        List<TbGridInspect> list = tbGridInspectService.selectTbGridInspectList(tbGridInspect);
        ExcelUtil<TbGridInspect> util = new ExcelUtil<TbGridInspect>(TbGridInspect.class);
        util.exportExcel(response, list, "网格巡查数据");
    }

    @Log(title = "网格巡查", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbGridInspect> util = new ExcelUtil<TbGridInspect>(TbGridInspect.class);
        List<TbGridInspect> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbGridInspectService.importTbGridInspect(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbGridInspect> util = new ExcelUtil<TbGridInspect>(TbGridInspect.class);
        util.exportTemplateExcel(response, "网格巡查数据");
    }

    /**
     * 获取网格巡查详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbGridInspectService.selectTbGridInspectById(id));
    }

    public void getDeptInfo(TbGridInspect tbGridInspect) {
        if (ObjectUtil.isEmpty(tbGridInspect.getDeptId())) {
            tbGridInspect.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbGridInspect.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbGridInspect.setCounty(split[1]);
            if (split.length >= 3) {
                tbGridInspect.setCountry(split[2]);
                if (split.length >= 4) {
                    tbGridInspect.setTown(split[3]);
                }
            }
        }
    }

    /**
     * 新增网格巡查
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:add')")
    @Log(title = "网格巡查", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbGridInspect tbGridInspect)
    {
        if (ObjectUtil.isEmpty(tbGridInspect.getGridId())){
            return error("关联网格不可为空！");
        }
        getDeptInfo(tbGridInspect);
        tbGridInspect.setCreateBy(getNickName());
        LoginUser loginUser = getLoginUser();
        //添加排查人姓名
        if (StringUtils.isBlank(tbGridInspect.getPersonName())){
            tbGridInspect.setPersonName(getNickName());
        }
        //添加排查人id
        if (ObjectUtil.isEmpty(tbGridInspect.getPersonId())){
            tbGridInspect.setPersonId(getUserId());
        }
        //添加排查人电话号
        if (StringUtils.isBlank(tbGridInspect.getPersonPone())){
            tbGridInspect.setPersonPone(loginUser.getUser().getPhonenumber());
        }

        return toAjax(tbGridInspectService.insertTbGridInspect(tbGridInspect));
    }

    /**
     * 修改网格巡查
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:edit')")
    @Log(title = "网格巡查", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbGridInspect tbGridInspect)
    {
        tbGridInspect.setUpdateBy(getNickName());
        if (ObjectUtil.isEmpty(tbGridInspect.getGridId())){
            return error("关联网格不可为空！");
        }
        return toAjax(tbGridInspectService.updateTbGridInspect(tbGridInspect));
    }

    /**
     * 删除网格巡查
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:remove')")
    @Log(title = "网格巡查", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbGridInspectService.deleteTbGridInspectByIds(ids));
    }

    /**
     * 根据网格ID 查询网格巡查   根据status
     */
    @PreAuthorize("@ss.hasPermi('gridManage:gridInspect:list')")
    @GetMapping("/listByStatus")
    public AjaxResult listByStatus(TbGridInspect tbGridInspect)
    {
        Map<Object, Object> map = tbGridInspectService.selectTbGridInspectListByStatus(tbGridInspect);
        return success(map);
    }

    /**
     * 查询当前用户的提交总数 和最新提交日期
     * @param tbGridInspect
     * @return
     */
    @GetMapping("/submitCount")
    public AjaxResult submitCount(TbGridInspect tbGridInspect) {
        tbGridInspect.setPersonId(getUserId());
        return success(tbGridInspectService.selectSubmitCount(tbGridInspect));
    }




}
