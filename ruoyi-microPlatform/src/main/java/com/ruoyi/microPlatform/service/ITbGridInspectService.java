package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbGridInspect;

import java.util.List;
import java.util.Map;

/**
 * 网格巡查Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbGridInspectService 
{
    /**
     * 查询网格巡查
     * 
     * @param id 网格巡查主键
     * @return 网格巡查
     */
    public TbGridInspect selectTbGridInspectById(Long id);

    /**
     * 校验网格巡查是否存在
     *
     * @param tbGridInspect 网格巡查
     * @return 网格巡查
     */
    public boolean checkTbGridInspect(TbGridInspect tbGridInspect);

    /**
     * 查询网格巡查列表
     * 
     * @param tbGridInspect 网格巡查
     * @return 网格巡查集合
     */
    public List<TbGridInspect> selectTbGridInspectList(TbGridInspect tbGridInspect);

    /**
     * 导入网格巡查
     *
     * @param infos       网格巡查列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridInspect(List<TbGridInspect> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增网格巡查
     * 
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    public int insertTbGridInspect(TbGridInspect tbGridInspect);

    /**
     * 修改网格巡查
     * 
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    public int updateTbGridInspect(TbGridInspect tbGridInspect);

    /**
     * 批量删除网格巡查
     * 
     * @param ids 需要删除的网格巡查主键集合
     * @return 结果
     */
    public int deleteTbGridInspectByIds(Long[] ids);

    /**
     * 删除网格巡查信息
     * 
     * @param id 网格巡查主键
     * @return 结果
     */
    public int deleteTbGridInspectById(Long id);

    /**
     * 查询网格巡查列表数量 根据status
     */
    Map<Object,Object> selectTbGridInspectListByStatus(TbGridInspect tbGridInspect);
    /**
     * 查询当前用户的提交总数 和最新提交日期
     * @param tbGridInspect
     * @return
     */
    Map<String, Object> selectSubmitCount(TbGridInspect tbGridInspect);



    List<Map<String, Object>> gridInspectGroupMonth(LargeScreenInfo largeScreenInfo);



    Integer gridInspectCount(LargeScreenInfo largeScreenInfo);
}
