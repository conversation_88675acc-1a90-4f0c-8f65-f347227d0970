package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbAssociateInfo;

/**
 * 行业协会商会信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbAssociateInfoMapper 
{
    /**
     * 查询行业协会商会信息
     * 
     * @param id 行业协会商会信息主键
     * @return 行业协会商会信息
     */
    public TbAssociateInfo selectTbAssociateInfoById(Long id);

    /**
     * 校验行业协会商会信息是否唯一
     *
     * @param tbAssociateInfo
     * @return 行业协会商会信息
     */
    public TbAssociateInfo checkTbAssociateInfoUnique(TbAssociateInfo tbAssociateInfo);

    /**
     * 查询行业协会商会信息列表
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 行业协会商会信息集合
     */
    public List<TbAssociateInfo> selectTbAssociateInfoList(TbAssociateInfo tbAssociateInfo);

    /**
     * 新增行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    public int insertTbAssociateInfo(TbAssociateInfo tbAssociateInfo);

    /**
     * 修改行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    public int updateTbAssociateInfo(TbAssociateInfo tbAssociateInfo);

    /**
     * 删除行业协会商会信息
     * 
     * @param id 行业协会商会信息主键
     * @return 结果
     */
    public int deleteTbAssociateInfoById(Long id);

    /**
     * 批量删除行业协会商会信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbAssociateInfoByIds(Long[] ids);
}
