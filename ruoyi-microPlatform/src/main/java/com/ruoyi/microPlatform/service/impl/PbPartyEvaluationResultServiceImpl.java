package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyEvaluationResult;
import com.ruoyi.microPlatform.mapper.PbPartyEvaluationResultMapper;
import com.ruoyi.microPlatform.service.IPbPartyEvaluationResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 党建评价结果Service业务层处理
 */
@Service
public class PbPartyEvaluationResultServiceImpl implements IPbPartyEvaluationResultService {

    @Autowired
    private PbPartyEvaluationResultMapper mapper;

    @Override
    public PbPartyEvaluationResult selectPbPartyEvaluationResultById(Long id) {
        return mapper.selectPbPartyEvaluationResultById(id);
    }

    @Override
    public boolean checkPbPartyEvaluationResult(PbPartyEvaluationResult info) {
        return mapper.checkPbPartyEvaluationResultUnique(info) != null;
    }

    @Override
    public List<PbPartyEvaluationResult> selectPbPartyEvaluationResultList(PbPartyEvaluationResult info) {
        return mapper.selectPbPartyEvaluationResultList(info);
    }

    @Override
    public int insertPbPartyEvaluationResult(PbPartyEvaluationResult info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertPbPartyEvaluationResult(info);
    }

    @Override
    public int updatePbPartyEvaluationResult(PbPartyEvaluationResult info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updatePbPartyEvaluationResult(info);
    }

    @Override
    public int deletePbPartyEvaluationResultByIds(Long[] ids) {
        return mapper.deletePbPartyEvaluationResultByIds(ids);
    }

    @Override
    public int deletePbPartyEvaluationResultById(Long id) {
        return mapper.deletePbPartyEvaluationResultById(id);
    }

    @Override
    public Map<String, List<CommonBaseCount>> starVillage() {
        return Collections.emptyMap();
    }

    @Override
    public List<CommonBaseCount> starCommunity(String evaluationBatch) {
        List<CommonBaseCount> dbCounts = mapper.groupByStarLevel(evaluationBatch);
        Map<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) countMap.put(t, cnt == null ? 0 : cnt);
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(5);
        for (int star = 1; star <= 5; star++) {
            CommonBaseCount item = new CommonBaseCount();
            item.setType(star);
            item.setLable(star + "星");
            item.setCount(countMap.getOrDefault(star, 0));
            res.add(item);
        }
        return res;
    }
}
