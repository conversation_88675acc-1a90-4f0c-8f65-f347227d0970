package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 网格员信息对象 tb_grid_user
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbGridUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）", oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /**
     * 所属街道/乡镇
     */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /**
     * 所属村/社区
     */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /**
     * 所属网格
     */
    @Excel(name = "所属网格编码", width = 18)
    private String gridName;


    /**
     * 所属网格id
     */
    private Long gridId;

    /**
     * 所属部门
     */
    private Long deptId;

    /**
     * 关联用户
     */
//    @Excel(name = "关联用户")
    private Long userId;

    /**
     * 网格员姓名
     */
    @Excel(name = "网格员姓名")
    private String name;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String phone;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String postDesc;
    /**
     * 职务
     */
    @Excel(name = "职务")
    private String postDetail;


    @Excel(name = "证件号码")
    private String idCard;


    /**
     * 角色(网格长，网格员)
     */
    @Excel(name = "角色类型", combo = {"网格长", "网格员"})
    private String postStr;

    /**
     * 网格员状态（0正常 1停用）
     */
    @Excel(name = "网格员状态", combo = {"正常", "停用"}, readConverterExp = "0=正常,1=停用", type = Excel.Type.EXPORT)
    private String status;

    /**
     * 证件照
     */
    @Excel(name = "证件照", cellType = Excel.ColumnType.IMAGE, type = Excel.Type.EXPORT)
    private String idPhoto;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 20, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date birthday;


    /** 年龄 */
    @Excel(name = "年龄", type = Excel.Type.EXPORT)
    private Integer age;
    /**
     * 子网格
     */
    private String gridArr;

    private Integer isPage = 1;

    public Integer getIsPage() {
        return isPage;
    }

    public void setIsPage(Integer isPage) {
        this.isPage = isPage;
    }

    public String getGridArr() {
        return gridArr;
    }

    public void setGridArr(String gridArr) {
        this.gridArr = gridArr;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCounty() {
        return county;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTown() {
        return town;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setIdPhoto(String idPhoto) {
        this.idPhoto = idPhoto;
    }

    public String getIdPhoto() {
        return idPhoto;
    }

    public void setPostDesc(String postDesc) {
        this.postDesc = postDesc;
    }

    public String getPostDesc() {
        return postDesc;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setPostDetail(String postDetail) {
        this.postDetail = postDetail;
    }

    public String getPostDetail() {
        return postDetail;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public String getPostStr() {
        return postStr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("gridName", getGridName())
                .append("gridId", getGridId())
                .append("deptId", getDeptId())
                .append("userId", getUserId())
                .append("name", getName())
                .append("phone", getPhone())
                .append("idPhoto", getIdPhoto())
                .append("postDesc", getPostDesc())
                .append("status", getStatus())
                .append("postDetail", getPostDetail())
                .append("postStr", getPostStr())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
