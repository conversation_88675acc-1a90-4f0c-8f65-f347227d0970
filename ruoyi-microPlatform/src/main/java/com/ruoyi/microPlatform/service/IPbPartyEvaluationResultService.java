package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyEvaluationResult;

import java.util.List;
import java.util.Map;

/**
 * 党建评价结果Service接口
 */
public interface IPbPartyEvaluationResultService {
    /**
     * 查询
     */
    PbPartyEvaluationResult selectPbPartyEvaluationResultById(Long id);

    /**
     * 唯一性校验（result_code）
     */
    boolean checkPbPartyEvaluationResult(PbPartyEvaluationResult info);

    /**
     * 列表
     */
    List<PbPartyEvaluationResult> selectPbPartyEvaluationResultList(PbPartyEvaluationResult info);

    /**
     * 新增
     */
    int insertPbPartyEvaluationResult(PbPartyEvaluationResult info);

    /**
     * 修改
     */
    int updatePbPartyEvaluationResult(PbPartyEvaluationResult info);

    /**
     * 批量删除
     */
    int deletePbPartyEvaluationResultByIds(Long[] ids);

    /**
     * 删除
     */
    int deletePbPartyEvaluationResultById(Long id);

    Map<String, List<CommonBaseCount>> starVillage();

    /**
     * 星级社区统计（按star_level=1..5分组，缺失补0）
     * @param evaluationBatch 批次
     * @return 分组统计
     */
    List<CommonBaseCount> starCommunity(String evaluationBatch);
}
