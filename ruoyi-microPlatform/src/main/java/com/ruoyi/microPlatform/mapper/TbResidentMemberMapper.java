package com.ruoyi.microPlatform.mapper;

import java.util.List;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import org.apache.ibatis.annotations.Param;

/**
 * 居民住户成员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbResidentMemberMapper 
{
    /**
     * 查询居民住户成员信息
     * 
     * @param id 居民住户成员信息主键
     * @return 居民住户成员信息
     */
    public TbResidentMember selectTbResidentMemberById(Long id);

    /**
     * 校验居民住户成员信息是否唯一
     *
     * @param tbResidentMember
     * @return 居民住户成员信息
     */
    public TbResidentMember checkTbResidentMemberUnique(TbResidentMember tbResidentMember);

    /**
     * 查询居民住户成员信息列表
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 居民住户成员信息集合
     */
    public List<TbResidentMember> selectTbResidentMemberList(TbResidentMember tbResidentMember);

    public List<Long> selectTbResidentMemberCount(TbResidentMember tbResidentMember);

    /**
     * 新增居民住户成员信息
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    public int insertTbResidentMember(TbResidentMember tbResidentMember);

    /**
     * 修改居民住户成员信息
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    public int updateTbResidentMember(TbResidentMember tbResidentMember);

    /**
     * 删除居民住户成员信息
     * 
     * @param id 居民住户成员信息主键
     * @return 结果
     */
    public int deleteTbResidentMemberById(Long id);

    /**
     * 批量删除居民住户成员信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbResidentMemberByIds(Long[] ids);

    public int deleteTbResidentMemberByMainIds(Long[] ids);

    /**
     * 批量新增居民住户成员信息
     *
     * @param tbResidentMemberList 居民住户成员信息列表
     * @return 结果
     */
    public int batchTbResidentMember(@Param("list") List<TbResidentMember> tbResidentMemberList, @Param("main")TbCommunityResident resident);


    /**
     * 通过小区住户居民信息主键删除居民住户成员信息信息
     *
     * @param id 小区住户居民信息ID
     * @return 结果
     */
    public int deleteTbResidentMemberByResidentId(Long id);

    public TbResidentMember selectResidentMemberByPhone(TbResidentMember residentMember);

    public List<TbResidentMember> selectResidentMemberList(TbResidentMember tbResidentMember);

    List<TbResidentMember> selectResidentMemberSpaceList(LargeScreenInfo largeScreenInfo);

    public int updateMemberAge(TbResidentMember residentMember);

    int countResidentList(TbResidentMember tbResidentMember);

    List<TbResidentMember> selectTbResidentMemberListByMainIds(TbResidentMember member);


    int keyPopulationsCount(TbResidentMember tbResidentMember);

    List<CommonBaseCount> keyPopulationsCountGroupByType(TbResidentMember tbResidentMember);

}
