package com.ruoyi.dataview.convert;

import com.ruoyi.dataview.domain.dto.DbWmSiteFormDTO;
import com.ruoyi.dataview.domain.dto.DbWmSiteQueryDTO;
import com.ruoyi.dataview.domain.po.DbWmSite;
import com.ruoyi.dataview.domain.vo.DbWmSiteVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * DbWmSiteConvert
 *
 * <p>
 * MapStruct 转换接口，用于 DTO ⇆ DO(PO) ⇆ VO 之间的对象转换。
 * 避免在 Controller/Service 中手动编写重复的 set/get 代码。
 *
 * 使用说明：
 * 1. @Mapper(componentModel = "spring") 表示该接口会被 Spring 扫描为 Bean，可直接注入使用。
 * 2. MapStruct 会在编译期自动生成实现类（例如：DbWmSiteConvertImpl）。
 * 3. 如果字段名一致，会自动映射；如果字段不同，可用 @Mapping 注解指定。
 *
 * 推荐使用场景：
 * - Controller 接收前端参数 → DTO → DO（持久化对象，数据库表对应实体）
 * - Service 查询数据库 → DO → VO（返回给前端视图对象）
 */
@Mapper(componentModel = "spring")
public interface DbWmSiteConvert {

    // ===================== DTO -> DO =====================

    /**
     * 查询参数 DTO → DO
     * 用于 Controller/Service 处理查询时，将 DTO 转换为 DO，方便传递给 Mapper 层。
     */
    DbWmSite toDO(DbWmSiteQueryDTO dto);

    /**
     * 表单参数 DTO → DO
     * 用于保存/更新场景，将前端提交的表单数据转换为数据库实体对象。
     */
    DbWmSite toDO(DbWmSiteFormDTO dto);

    // ===================== DO -> VO =====================

    /**
     * DO → VO
     * 用于返回单个对象给前端展示。
     */
    DbWmSiteVO toVO(DbWmSite entity);

    /**
     * DO 列表 → VO 列表
     * 常用于分页查询结果或批量查询返回。
     */
    List<DbWmSiteVO> toVOList(List<DbWmSite> list);

    // ===================== DTO 列表 -> DO 列表 =====================

    /**
     * QueryDTO 列表 → DO 列表
     * 一般在批量查询或批量操作时使用。
     */
    List<DbWmSite> toDOListFromQueryDTO(List<DbWmSiteQueryDTO> list);

    /**
     * FormDTO 列表 → DO 列表
     * 一般在批量保存/更新时使用。
     */
    List<DbWmSite> toDOListFromFormDTO(List<DbWmSiteFormDTO> list);
}

