<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbBranchInfoMapper">

    <resultMap type="TbBranchInfo" id="TbBranchInfoResult">
            <result property="id"    column="id"    />
            <result property="branchName"    column="branch_name"    />
            <result property="secretaryName"    column="secretary_name"    />
            <result property="phone"    column="phone"    />
            <result property="deptId"    column="dept_id"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
    </resultMap>

        <resultMap id="TbBranchInfoTbBranchGridInfoResult" type="TbBranchInfo" extends="TbBranchInfoResult">
            <collection property="tbBranchGridInfoList" notNullColumn="sub_id" javaType="java.util.List" resultMap="TbBranchGridInfoResult" />
        </resultMap>

        <resultMap type="TbBranchGridInfo" id="TbBranchGridInfoResult">
                <result property="id"    column="sub_id"    />
                <result property="branchId"    column="sub_branch_id"    />
                <result property="gridId"    column="sub_grid_id"    />
                <result property="deptId"    column="sub_dept_id"    />
                <result property="county"    column="sub_county"    />
                <result property="country"    column="sub_country"    />
                <result property="town"    column="sub_town"    />
                <result property="gridName"    column="sub_grid_name"    />
                <result property="createBy"    column="sub_create_by"    />
                <result property="createTime"    column="sub_create_time"    />
                <result property="updateBy"    column="sub_update_by"    />
                <result property="updateTime"    column="sub_update_time"    />
        </resultMap>

    <sql id="selectTbBranchInfoVo">
        select id, branch_name,dept_id, secretary_name, phone, create_by, create_time, update_by, update_time from tb_branch_info
    </sql>

    <select id="selectTbBranchInfoList" parameterType="TbBranchInfo" resultMap="TbBranchInfoResult">
        <include refid="selectTbBranchInfoVo"/>
        <where>
                        <if test="branchName != null  and branchName != ''"> and branch_name like concat('%', #{branchName}, '%')</if>
                        <if test="secretaryName != null  and secretaryName != ''"> and secretary_name like concat('%', #{secretaryName}, '%')</if>
                        <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectTbBranchInfoById" parameterType="Long" resultMap="TbBranchInfoTbBranchGridInfoResult">
            select a.id, a.branch_name, a.secretary_name, a.phone, a.create_by, a.create_time, a.update_by, a.update_time,
 b.id as sub_id, b.branch_id as sub_branch_id, b.grid_id as sub_grid_id, b.dept_id as sub_dept_id, b.county as sub_county, b.country as sub_country, b.town as sub_town, b.grid_name as sub_grid_name, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time
            from tb_branch_info a
            left join tb_branch_grid_info b on b.branch_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkTbBranchInfoUnique" parameterType="TbBranchInfo" resultMap="TbBranchInfoResult">
        select id from tb_branch_info where branch_name = #{branchName} and secretary_name = #{secretaryName}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbBranchInfo" parameterType="TbBranchInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_branch_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="branchName != null">branch_name,</if>
                    <if test="secretaryName != null">secretary_name,</if>
                    <if test="phone != null">phone,</if>
                    <if test="deptId != null">dept_id,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="branchName != null">#{branchName},</if>
                    <if test="secretaryName != null">#{secretaryName},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="deptId != null">#{deptId},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbBranchInfo" parameterType="TbBranchInfo">
        update tb_branch_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="branchName != null">branch_name = #{branchName},</if>
                    <if test="secretaryName != null">secretary_name = #{secretaryName},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbBranchInfoById" parameterType="Long">
        delete from tb_branch_info where id = #{id}
    </delete>

    <delete id="deleteTbBranchInfoByIds" parameterType="String">
        delete from tb_branch_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

        <delete id="deleteTbBranchGridInfoByBranchIds" parameterType="String">
            delete from tb_branch_grid_info where branch_id in
            <foreach item="branchId" collection="array" open="(" separator="," close=")">
                #{branchId}
            </foreach>
        </delete>

        <delete id="deleteTbBranchGridInfoByBranchId" parameterType="TbBranchGridInfo">
        delete from tb_branch_grid_info where branch_id = #{branchId} and grid_id = #{gridId}
    </delete>

        <insert id="batchTbBranchGridInfo">
            insert into tb_branch_grid_info( id, branch_id, grid_id, dept_id, county, country, town, grid_name, create_by, create_time, update_by, update_time) values
            <foreach item="item" index="index" collection="list" separator=",">
                ( #{item.id}, #{item.branchId}, #{item.gridId}, #{item.deptId}, #{item.county}, #{item.country}, #{item.town}, #{item.gridName}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
            </foreach>
        </insert>

    <select id="selectTbBranchInfoListByGrid" resultMap="TbBranchInfoResult">
        select a.id, a.branch_name, a.secretary_name, a.phone, a.create_by, a.create_time, a.update_by, a.update_time

        from tb_branch_info a
                 left join tb_branch_grid_info b on b.branch_id = a.id
        <where>
            <if test="gridId">
                and b.grid_id = #{gridId}
            </if>
        </where>

    </select>
</mapper>