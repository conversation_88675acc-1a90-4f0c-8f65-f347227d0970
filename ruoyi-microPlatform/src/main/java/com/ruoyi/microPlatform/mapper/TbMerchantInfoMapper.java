package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;
import org.apache.poi.hssf.record.chart.ObjectLinkRecord;

/**
 * 门店/商户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbMerchantInfoMapper 
{
    /**
     * 查询门店/商户信息
     * 
     * @param id 门店/商户信息主键
     * @return 门店/商户信息
     */
    public TbMerchantInfo selectTbMerchantInfoById(Long id);

    /**
     * 校验门店/商户信息是否唯一
     *
     * @param tbMerchantInfo
     * @return 门店/商户信息
     */
    public TbMerchantInfo checkTbMerchantInfoUnique(TbMerchantInfo tbMerchantInfo);

    /**
     * 查询门店/商户信息列表
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 门店/商户信息集合
     */
    public List<TbMerchantInfo> selectTbMerchantInfoList(TbMerchantInfo tbMerchantInfo);

    public List<Long> selectTbMerchantInfoCount(TbMerchantInfo tbMerchantInfo);

    /**
     * 新增门店/商户信息
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    public int insertTbMerchantInfo(TbMerchantInfo tbMerchantInfo);

    /**
     * 修改门店/商户信息
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    public int updateTbMerchantInfo(TbMerchantInfo tbMerchantInfo);

    /**
     * 删除门店/商户信息
     * 
     * @param id 门店/商户信息主键
     * @return 结果
     */
    public int deleteTbMerchantInfoById(Long id);

    /**
     * 批量删除门店/商户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMerchantInfoByIds(Long[] ids);

    public TbMerchantInfo selectMerchantInfoById(Long id);

    List<TbMerchantInfo> selectTbMerchantInfoSpaceList(LargeScreenInfo largeScreenInfo);

    List<Map<String, Object>> getMerchantInfoByType(LargeScreenInfo largeScreenInfo);

}
