package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;

/**
 * 网格员关联小区Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbGridUserCommunityService 
{
    /**
     * 查询网格员关联小区
     * 
     * @param id 网格员关联小区主键
     * @return 网格员关联小区
     */
    public TbGridUserCommunity selectTbGridUserCommunityById(Long id);

    /**
     * 校验网格员关联小区是否存在
     *
     * @param tbGridUserCommunity 网格员关联小区
     * @return 网格员关联小区
     */
    public boolean checkTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 查询网格员关联小区列表
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 网格员关联小区集合
     */
    public List<TbGridUserCommunity> selectTbGridUserCommunityList(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 导入网格员关联小区
     *
     * @param infos       网格员关联小区列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridUserCommunity(List<TbGridUserCommunity> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    public int insertTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 修改网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    public int updateTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 批量删除网格员关联小区
     * 
     * @param ids 需要删除的网格员关联小区主键集合
     * @return 结果
     */
    public int deleteTbGridUserCommunityByIds(Long[] ids);

    /**
     * 删除网格员关联小区信息
     * 
     * @param id 网格员关联小区主键
     * @return 结果
     */
    public int deleteTbGridUserCommunityById(Long id);
}
