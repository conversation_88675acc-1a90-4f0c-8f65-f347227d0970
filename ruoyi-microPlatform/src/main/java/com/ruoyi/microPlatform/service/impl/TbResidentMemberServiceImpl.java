package com.ruoyi.microPlatform.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbPersonType;
import com.ruoyi.microPlatform.domain.TbResidentMemberType;
import com.ruoyi.microPlatform.mapper.TbResidentMemberTypeMapper;
import com.ruoyi.microPlatform.service.ITbPersonTypeService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbResidentMemberMapper;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 居民住户成员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbResidentMemberServiceImpl implements ITbResidentMemberService {
    @Autowired
    private TbResidentMemberMapper tbResidentMemberMapper;
    @Autowired
    private TbResidentMemberTypeMapper tbResidentMemberTypeMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询居民住户成员信息
     *
     * @param id 居民住户成员信息主键
     * @return 居民住户成员信息
     */
    @Override
    public TbResidentMember selectTbResidentMemberById(Long id) {
        TbResidentMember residentMember = tbResidentMemberMapper.selectTbResidentMemberById(id);
        if (ObjectUtil.isNotEmpty(residentMember.getPersonTypes()) && residentMember.getPersonTypes().size() > 0) {
            residentMember.setPersonTypeIds(residentMember.getPersonTypes().stream().map(TbPersonType::getId).toArray(Integer[]::new));
            residentMember.setTypeValueList(residentMember.getPersonTypes().stream().map(TbPersonType::getLabel).collect(Collectors.toList()));
            String personLabel = residentMember.getPersonTypes().stream().map(TbPersonType::getLabel).collect(Collectors.joining(","));
            residentMember.setPersonLabel(personLabel);
        }
//        String personLabel = residentMember.getPersonLabel();
//        if (personLabel != null && !personLabel.equals("")) {
//            residentMember.setTypeValueList(Arrays.asList(personLabel.split(",\\s*")));
//        }
        return residentMember;
    }

    /**
     * 校验居民住户成员信息是否存在
     *
     * @param tbResidentMember
     * @return boolean
     */
    @Override
    public boolean checkTbResidentMember(TbResidentMember tbResidentMember) {
        TbResidentMember old = tbResidentMemberMapper.checkTbResidentMemberUnique(tbResidentMember);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询居民住户成员信息列表
     *
     * @param tbResidentMember 居民住户成员信息
     * @return 居民住户成员信息
     */
    @Override
    public TableDataInfo selectTbResidentMemberList(TbResidentMember tbResidentMember) {
        // 1. 查询总数
        int total = tbResidentMemberMapper.countResidentList(tbResidentMember);

        List<TbResidentMember> tbResidentMembers = tbResidentMemberMapper.selectTbResidentMemberList(tbResidentMember);
//        if (ObjectUtil.isNotEmpty(tbResidentMember.getResidentId())) {
//            for (TbResidentMember residentMember : tbResidentMembers) {
//                String personLabel = residentMember.getPersonLabel();
//                if (personLabel != null && !personLabel.equals("")) {
//                    residentMember.setTypeValueList(Arrays.asList(personLabel.split(",\\s*")));
//                }
//            }
//        }
        // 3. 构建分页返回
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(tbResidentMembers);
        rspData.setTotal(total);
        return rspData;
    }


    @Override
    public List<TbResidentMember> selectTbResidentMemberListByMainIds(TbResidentMember member) {


        return tbResidentMemberMapper.selectTbResidentMemberListByMainIds(member);
    }

    @Override
    public Integer selectTbResidentMemberCount(TbResidentMember tbResidentMember) {
        List<Long> longs = tbResidentMemberMapper.selectTbResidentMemberCount(tbResidentMember);
        return longs.size();
    }

    /**
     * 导入居民住户成员信息
     *
     * @param infos           居民住户成员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentMember(List<TbResidentMember> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入居民住户成员信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbResidentMember info : infos) {
            try {
                // 验证是否存在这个数据
                TbResidentMember old = tbResidentMemberMapper.checkTbResidentMemberUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbResidentMemberMapper.insertTbResidentMember(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbResidentMemberMapper.updateTbResidentMember(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增居民住户成员信息
     *
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    @Override
//    @Transactional
    public int insertTbResidentMember(TbResidentMember tbResidentMember) {
        tbResidentMember.setCreateTime(DateUtils.getNowDate());
        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank(tbResidentMember.getIdCard())) {
                String s = tbResidentMember.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbResidentMember.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    // 转换为LocalDate
                    LocalDate birthDate = idCard.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();

                    tbResidentMember.setBirthday(idCard);
                    tbResidentMember.setAge(Period.between(birthDate, LocalDate.now()).getYears());
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        if (ObjectUtil.isNotEmpty(tbResidentMember.getOptionType()) && tbResidentMember.getOptionType() == 1) {
            if (ObjectUtil.isNotEmpty(tbResidentMember.getTypeValueList()) && tbResidentMember.getTypeValueList().size() > 0) {
                tbResidentMember.setPersonLabel(tbResidentMember.getTypeValueList().stream().collect(Collectors.joining(",")));
            } else {
                if (StringUtils.isNotBlank(tbResidentMember.getPersonLabel())) {
                    String s = tbResidentMember.getPersonLabel().replaceAll("（", "(").replaceAll("）", ")").replaceAll("——", "-").replaceAll("，", ",");
                    tbResidentMember.setPersonLabel(s);
                    tbResidentMember.setTypeValueList(Arrays.asList(s.split(",")));
                }
            }

            // 人员标签已更新
            tbResidentMember.setLabel(1);
        }

        if (StringUtils.isNotBlank(tbResidentMember.getGridName())) {
            tbResidentMember.setSort(Integer.parseInt(tbResidentMember.getGridName()));
        }
        int i = tbResidentMemberMapper.insertTbResidentMember(tbResidentMember);
        if (ObjectUtil.isNotEmpty(tbResidentMember.getOptionType()) && tbResidentMember.getOptionType() == 1) {
            insertPersonType2(tbResidentMember);
        } else {
            insertPersonType(tbResidentMember);
        }
        this.updatePersonGrid(tbResidentMember);
        return i;
    }


    /**
     * 修改居民住户成员信息
     *
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTbResidentMember(TbResidentMember tbResidentMember) {
        tbResidentMember.setUpdateTime(DateUtils.getNowDate());
        tbResidentMember.setLabel(0);
        if (ObjectUtil.isNotEmpty(tbResidentMember.getOptionType()) && tbResidentMember.getOptionType() == 1) {
            if (ObjectUtil.isNotEmpty(tbResidentMember.getTypeValueList()) && tbResidentMember.getTypeValueList().size() > 0) {
                tbResidentMember.setPersonLabel(tbResidentMember.getTypeValueList().stream().collect(Collectors.joining(",")));
            }
            // 人员标签已更新
            tbResidentMember.setLabel(1);
        }
        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank(tbResidentMember.getIdCard())) {
                String s = tbResidentMember.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbResidentMember.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    // 转换为LocalDate
                    LocalDate birthDate = idCard.toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();

                    tbResidentMember.setBirthday(idCard);
                    tbResidentMember.setAge(Period.between(birthDate, LocalDate.now()).getYears());
                }
            }
        } catch (Exception e) {
            throw new ServiceException("身份证号解析出生年月出错！");
        }
        if (StringUtils.isNotBlank(tbResidentMember.getGridName())) {
            tbResidentMember.setSort(Integer.parseInt(tbResidentMember.getGridName()));
        }
        int i = tbResidentMemberMapper.updateTbResidentMember(tbResidentMember);
        tbResidentMemberTypeMapper.deleteTbResidentMemberTypeByMemberId(tbResidentMember.getId());
        if (ObjectUtil.isNotEmpty(tbResidentMember.getOptionType()) && tbResidentMember.getOptionType() == 1) {
            insertPersonType2(tbResidentMember);
        } else {
            insertPersonType(tbResidentMember);
        }
        this.updatePersonLabel();
        // 旧手机号为空，新手机号不空
        // 新旧手机号不相等 才需要更新绑定居民用户网格
        if ((StringUtils.isBlank(tbResidentMember.getOldPhone()) && StringUtils.isNotBlank(tbResidentMember.getPhone())) &&
                (StringUtils.isNotBlank(tbResidentMember.getOldPhone()) && StringUtils.isNotBlank(tbResidentMember.getPhone()) && tbResidentMember.getOldPhone().equals(tbResidentMember.getPhone()))
        ) {
            this.updatePersonGrid(tbResidentMember);
        }
        return i;
    }

    public void insertPersonType(TbResidentMember tbResidentMember) {
        if (ObjectUtil.isNotEmpty(tbResidentMember.getPersonTypeIds()) && tbResidentMember.getPersonTypeIds().length > 0) {
            //在新增该人员标签
            tbResidentMemberTypeMapper.insertTbResidentMemberTypeByValues(tbResidentMember.getId(), tbResidentMember.getPersonTypeIds());
        }
    }

    /**
     * 移动端标签更新办法
     *
     * @param tbResidentMember
     */
    public void insertPersonType2(TbResidentMember tbResidentMember) {
        if (ObjectUtil.isNotEmpty(tbResidentMember.getTypeValueList()) && tbResidentMember.getTypeValueList().size() > 0) {
            List<Integer> personTypeIds = new ArrayList<>();
            for (String s : tbResidentMember.getTypeValueList()) {
                Integer personTypeId = redisCache.getCacheObject(CacheConstants.PERSON_TYPE_NAME_KEY + s);
                if (ObjectUtil.isNotEmpty(personTypeId)) {
                    personTypeIds.add(personTypeId);
                } else {
                    throw new ServiceException(s + "人员标签不存在");
                }
            }
            if (personTypeIds.size() > 0) {
                //在新增该人员标签
                tbResidentMemberTypeMapper.insertTbResidentMemberTypeByValues(tbResidentMember.getId(), personTypeIds.stream().toArray(Integer[]::new));
            }

        }
    }

    @Override
    @Async("threadPoolTaskExecutor")
    public void updatePersonLabel() {
        // 未更新标签的人， 标签会直接查询出来
        List<TbResidentMember> tbResidentMembers = tbResidentMemberMapper.selectResidentMemberList(new TbResidentMember() {{
            setLabel(0);
        }});
        tbResidentMembers.stream().forEach(item -> {
            item.setLabel(1);
            tbResidentMemberMapper.updateTbResidentMember(item);
        });
    }

    @Override
    public void updatePersonAge() {
        tbResidentMemberMapper.updateMemberAge(new TbResidentMember());
    }

    /**
     * 绑定用户更新gridId
     */
    @Async("threadPoolTaskExecutor")
    public void updatePersonGrid(TbResidentMember tbResidentMember) {
        if (StringUtils.isNotBlank(tbResidentMember.getPhone())) {
            // 联系电话不为空, 查询出来用户，为这些用户绑定gridId
            List<SysUser> users = userMapper.selectUserListByPhone(new SysUser() {{
                setPhonenumber(tbResidentMember.getPhone());
            }});
            Map<String, Long> userNameList = new HashMap<>();
            users.stream().forEach(item -> {
                if (ObjectUtil.isEmpty(item.getJobId()) || item.getJobId().longValue() != tbResidentMember.getGridId().longValue()) {
                    int i = userMapper.updateUser(new SysUser() {{
                        setUserId(item.getUserId());
                        if (ObjectUtil.isNotEmpty(item.getRoles()) && item.getRoles().size() > 0) {
                            // 普通用户 用户所在的社区进行更新
                            setDeptId(tbResidentMember.getDeptId());
                        }
                        setJobId(tbResidentMember.getGridId());
                    }});
                    System.err.println("更新账号" + item.getNickName() + "的网格ID：" + i);
                    if (i > 0) {
                        userNameList.put(item.getUserName(), item.getUserId());
                    }
                }
            });

            if (userNameList.size() > 0) {
                System.err.println("准备开始更新缓存数据了！");
//                SysDept dept = deptMapper.selectDeptById(tbResidentMember.getDeptId());

                userNameList.forEach((userName, userId) -> {
                    Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + userId + "*");
                    for (String key : keys) {
                        LoginUser loginUser = redisCache.getCacheObject(key);
                        if (ObjectUtil.isNotEmpty(loginUser)) {
                            System.err.println("刷新用户grid信息Dept信息：" + loginUser.getUser().getNickName());
                            SysUser sysUser = userMapper.selectUserById(userId);
//                            if (ObjectUtil.isNotEmpty(dept)) {
//                                sysUser.setDept(dept);
//                            }
//                            sysUser.setJobId(tbResidentMember.getGridId());
                            loginUser.setUser(sysUser);
                            loginUser.setDeptId(sysUser.getDeptId());
                            tokenService.setLoginUser(loginUser);
                        }
                    }
                });

            }

        }
    }

    /**
     * 批量删除居民住户成员信息
     *
     * @param ids 需要删除的居民住户成员信息主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentMemberByIds(Long[] ids) {
        return tbResidentMemberMapper.deleteTbResidentMemberByIds(ids);
    }

    /**
     * 删除居民住户成员信息信息
     *
     * @param id 居民住户成员信息主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentMemberById(Long id) {
        return tbResidentMemberMapper.deleteTbResidentMemberById(id);
    }


    @Override
    public int keyPopulationsCount(TbResidentMember tbResidentMember) {
        return tbResidentMemberMapper.keyPopulationsCount(tbResidentMember);
    }

    @Resource
    private ITbPersonTypeService tbPersonTypeService;

    @Override
    public List<CommonBaseCount> keyPopulationsCountGroupByType(TbResidentMember tbResidentMember) {
        TbPersonType tbPersonType = new TbPersonType();
        List<TbPersonType> tbPersonTypes = tbPersonTypeService.selectTbPersonTypeList(tbPersonType);
        List<CommonBaseCount> commonBaseCounts = tbResidentMemberMapper.keyPopulationsCountGroupByType(tbResidentMember);

        // 将数据库已分组统计的结果转为 Map，key 为标签名，value 为数量（空值按 0 处理）
        Map<String, Integer> countMap = Optional.ofNullable(commonBaseCounts).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(c -> c.getLable() != null)
                .collect(Collectors.toMap(
                        CommonBaseCount::getLable,
                        c -> Optional.ofNullable(c.getCount()).orElse(0),
                        (a, b) -> a
                ));

        // 使用 tbPersonTypes 的标签全集补齐没有分组到的数据，默认数量为 0
        return Optional.ofNullable(tbPersonTypes).orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(t -> {
                    CommonBaseCount c = new CommonBaseCount();
                    c.setLable(t.getLabel());
                    c.setCount(countMap.getOrDefault(t.getLabel(), 0));
                    return c;
                })
                .collect(Collectors.toList());
    }
}
