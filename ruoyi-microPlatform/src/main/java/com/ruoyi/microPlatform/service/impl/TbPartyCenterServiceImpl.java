package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbPartyCenterMapper;
import com.ruoyi.microPlatform.domain.TbPartyCenter;
import com.ruoyi.microPlatform.service.ITbPartyCenterService;

/**
 * 党群服务中心报到情况Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class TbPartyCenterServiceImpl implements ITbPartyCenterService 
{
    @Autowired
    private TbPartyCenterMapper tbPartyCenterMapper;

    /**
     * 查询党群服务中心报到情况
     * 
     * @param id 党群服务中心报到情况主键
     * @return 党群服务中心报到情况
     */
    @Override
    public TbPartyCenter selectTbPartyCenterById(Long id)
    {
        return tbPartyCenterMapper.selectTbPartyCenterById(id);
    }

    /**
     * 校验党群服务中心报到情况是否存在
     *
     * @param tbPartyCenter
     * @return boolean
     */
    @Override
    public boolean checkTbPartyCenter(TbPartyCenter tbPartyCenter){
        TbPartyCenter old = tbPartyCenterMapper.checkTbPartyCenterUnique(tbPartyCenter);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询党群服务中心报到情况列表
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 党群服务中心报到情况
     */
    @Override
    public List<TbPartyCenter> selectTbPartyCenterList(TbPartyCenter tbPartyCenter)
    {
        return tbPartyCenterMapper.selectTbPartyCenterList(tbPartyCenter);
    }

    /**
     * 导入党群服务中心报到情况
     *
     * @param infos       党群服务中心报到情况列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbPartyCenter(List<TbPartyCenter> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入党群服务中心报到情况数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbPartyCenter info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbPartyCenter old = tbPartyCenterMapper.checkTbPartyCenterUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbPartyCenterMapper.insertTbPartyCenter(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbPartyCenterMapper.updateTbPartyCenter(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    @Override
    public int insertTbPartyCenter(TbPartyCenter tbPartyCenter)
    {
        return tbPartyCenterMapper.insertTbPartyCenter(tbPartyCenter);
    }

    /**
     * 修改党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    @Override
    public int updateTbPartyCenter(TbPartyCenter tbPartyCenter)
    {
        return tbPartyCenterMapper.updateTbPartyCenter(tbPartyCenter);
    }

    /**
     * 批量删除党群服务中心报到情况
     * 
     * @param ids 需要删除的党群服务中心报到情况主键
     * @return 结果
     */
    @Override
    public int deleteTbPartyCenterByIds(Long[] ids)
    {
        return tbPartyCenterMapper.deleteTbPartyCenterByIds(ids);
    }

    /**
     * 删除党群服务中心报到情况信息
     * 
     * @param id 党群服务中心报到情况主键
     * @return 结果
     */
    @Override
    public int deleteTbPartyCenterById(Long id)
    {
        return tbPartyCenterMapper.deleteTbPartyCenterById(id);
    }
}
