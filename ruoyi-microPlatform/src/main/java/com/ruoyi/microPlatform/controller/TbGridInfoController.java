package com.ruoyi.microPlatform.controller;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.service.ITbGridInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/gridInfo")
public class TbGridInfoController extends BaseController {
    @Autowired
    private ITbGridInfoService tbGridInfoService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询网格信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbGridInfo tbGridInfo) {
        startPage();
        handle(tbGridInfo);
        List<TbGridInfo> list = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        return getDataTable(list);
    }

    @GetMapping("/selectGridList")
    public AjaxResult selectGridList(TbGridInfo tbGridInfo) {
        handle(tbGridInfo);
        List<TbGridInfo> list = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        return success(list);
    }

    @GetMapping("/getWorkGrid")
    public AjaxResult getWorkGrid() {
        String drillNo = getLoginUser().getUser().getDrillNo();
        List<TbGridInfo> list = tbGridInfoService.getWorkGrid(new TbGridInfo() {{
            if (StringUtils.isNotBlank(drillNo)) {
                setGridArr(drillNo);
            }
        }});
        AjaxResult success = success(list);
        success.put("nowGrid", SecurityUtils.getGridId());
        return success;
    }

    public void handle(TbGridInfo tbGridInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbGridInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(tbGridInfo.getDeptId())) {
                    tbGridInfo.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出网格信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:export')")
    @Log(title = "网格信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbGridInfo tbGridInfo) {
        if (ObjectUtil.isNotEmpty(tbGridInfo.getIsExportAll()) && tbGridInfo.getIsExportAll() == 0) {
            startPage(pageNum, pageSize);
        }
        handle(tbGridInfo);
        List<TbGridInfo> list = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        ExcelUtil<TbGridInfo> util = new ExcelUtil<TbGridInfo>(TbGridInfo.class);
        util.exportExcel(response, list, "网格信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbGridInfo tbGridInfo) {
        if (ObjectUtil.isNotEmpty(tbGridInfo.getIsExportAll()) && tbGridInfo.getIsExportAll() == 0) {
            startPage();
        }
        handle(tbGridInfo);
        Integer integer = tbGridInfoService.selectExportGridInfoCount(tbGridInfo);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbGridInfo);
    }

    @Log(title = "网格信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbGridInfo> util = new ExcelUtil<TbGridInfo>(TbGridInfo.class);
        List<TbGridInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbGridInfoService.importTbGridInfo(infos, updateSupport, operName, userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbGridInfo> util = new ExcelUtil<TbGridInfo>(TbGridInfo.class);
        util.exportTemplateExcel(response, "网格信息数据");
    }

    /**
     * 获取网格信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbGridInfoService.selectTbGridInfoById(id));
    }

    /**
     * 移动端获取网格信息详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/gridDetail/{id}")
    public AjaxResult gridDetail(@PathVariable("id") Long id) {
        TbGridInfo gridInfo = tbGridInfoService.selectGridInfoById(id);
        if (ObjectUtil.isNotEmpty(gridInfo)) {
            try {
                SysUser user = getLoginUser().getUser();
                if (user.getDept().getDeptLevel() == 8) {
                    String drillNo = user.getDrillNo();
                    if (StringUtils.isNotBlank(drillNo)) {
                        List<String> strings = Arrays.asList(drillNo.split(","));
                        if (strings.contains(id + "")) {
                            gridInfo.setManageGrid(true);
                        } else {
                            gridInfo.setManageGrid(false);
                        }
                    } else {
                        gridInfo.setManageGrid(false);
                    }
                } else if (user.getDept().getDeptLevel() == 7) {
                    // 村社区
                    if (user.getDept().getDeptName().equals(gridInfo.getTown())) {
                        gridInfo.setManageGrid(true);
                    } else {
                        gridInfo.setManageGrid(false);
                    }

                } else if (user.getDept().getDeptLevel() == 6) {
                    // 乡镇街道
                    if (user.getDept().getDeptName().equals(gridInfo.getCountry())) {
                        gridInfo.setManageGrid(true);
                    } else {
                        gridInfo.setManageGrid(false);
                    }
                } else if (user.getDept().getDeptLevel() == 5) {
                    // 县
                    if (user.getDept().getDeptName().equals(gridInfo.getCounty())) {
                        gridInfo.setManageGrid(true);
                    } else {
                        gridInfo.setManageGrid(false);
                    }
                } else {
                    gridInfo.setManageGrid(true);
                }
            } catch (Exception e) {
                gridInfo.setManageGrid(false);
            }
        }
        return success(gridInfo);
    }

    public void getDeptInfo(TbGridInfo tbGridInfo) {
        if (ObjectUtil.isEmpty(tbGridInfo.getDeptId())) {
            tbGridInfo.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbGridInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbGridInfo.setCounty(split[1]);
            if (split.length >= 3) {
                tbGridInfo.setCountry(split[2]);
                if (split.length >= 4) {
                    tbGridInfo.setTown(split[3]);
                }
            }
        }
    }

    /**
     * 新增网格信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:add')")
    @Log(title = "网格信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbGridInfo tbGridInfo) {

        getDeptInfo(tbGridInfo);
        tbGridInfo.setCreateBy(getUsername());
        if (tbGridInfoService.checkTbGridInfo(tbGridInfo)) {
            return error("该网格名称已存在！");
        }
        return toAjax(tbGridInfoService.insertTbGridInfo(tbGridInfo));
    }

    /**
     * 修改网格信息   同时修改与该网格有关联的表，使用异步调用。
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:edit')")
    @Log(title = "网格信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbGridInfo tbGridInfo) {
        tbGridInfo.setUpdateBy(getNickName());
        getDeptInfo(tbGridInfo);
        if (tbGridInfoService.checkTbGridInfo(tbGridInfo)) {
            return error("该网格已存在！");
        }
        return toAjax(tbGridInfoService.updateTbGridInfo(tbGridInfo));
    }

    /**
     * 删除网格信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridInfo:remove')")
    @Log(title = "网格信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbGridInfoService.deleteTbGridInfoByIds(ids));
    }
}
