package com.ruoyi.dataview.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.dataview.domain.po.DbMayorMailbox;

/**
 * 市长信箱主Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IDbMayorMailboxService 
{
    /**
     * 查询市长信箱主
     * 
     * @param id 市长信箱主主键
     * @return 市长信箱主
     */
    public DbMayorMailbox selectDbMayorMailboxById(Long id);

    /**
     * 校验市长信箱主是否存在
     *
     * @param dbMayorMailbox 市长信箱主
     * @return 市长信箱主
     */
    public boolean checkDbMayorMailbox(DbMayorMailbox dbMayorMailbox);

    /**
     * 查询市长信箱主列表
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 市长信箱主集合
     */
    public List<DbMayorMailbox> selectDbMayorMailboxList(DbMayorMailbox dbMayorMailbox);

    /**
     * 导入市长信箱主
     *
     * @param infos       市长信箱主列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbMayorMailbox(List<DbMayorMailbox> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增市长信箱主
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    public int insertDbMayorMailbox(DbMayorMailbox dbMayorMailbox);

    /**
     * 修改市长信箱主
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    public int updateDbMayorMailbox(DbMayorMailbox dbMayorMailbox);

    /**
     * 批量删除市长信箱主
     * 
     * @param ids 需要删除的市长信箱主主键集合
     * @return 结果
     */
    public int deleteDbMayorMailboxByIds(Long[] ids);

    /**
     * 删除市长信箱主信息
     * 
     * @param id 市长信箱主主键
     * @return 结果
     */
    public int deleteDbMayorMailboxById(Long id);

    Map<String,Object> dbMayorMailboxCount();
}
