package com.ruoyi.microPlatform.controller.bigdata.gov;

import cn.hutool.http.ContentType;
import cn.hutool.http.Method;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GovApiEnum {

    GET_TOKEN("/EpointWebBuilder/henanProvinceManagement.action?cmd=getToken", Method.POST, "市长信箱-登录", ContentType.JSON),
    GET_UNIFIED_MANAGEMENT_LETTER_LIST("/EpointWebBuilder/henanProvinceManagement.action?cmd=getUnifiedManagementLetterList", Method.POST, "市长信箱-获取信件列表接口", ContentType.FORM_URLENCODED),
    GET_LETTER_TYPE_MODEL("/EpointWebBuilder/henanProvinceManagement.action?cmd=getLetterTypeModel", Method.POST, "市长信箱-信件类别", ContentType.FORM_URLENCODED),
    GET_STATUS_MODEL("/EpointWebBuilder/henanProvinceManagement.action?cmd=getStatusModel", Method.POST, "市长信箱-信件状态", ContentType.FORM_URLENCODED),
    ;

    /*
     * 接口路径
     */
    private final String path;
    /*
     * 接口请求方式
     */
    private final Method httpMethod;
    /*
     * 接口描述
     */
    private final String des;
    /*
     * 参数位置
     */
    private final ContentType dataWhere;

}