package com.ruoyi.microPlatform.controller.bigdata.gov;

/**
 * 自定义异常类UnauthorizedException，用于表示未授权的访问异常。
 * 此类继承自Exception，旨在提供一种专门用于处理未授权访问的异常机制。
 * 当访问某些资源或执行某些操作时，如果权限不足，可以抛出此异常。
 */
public class GovUnauthorizedException extends Exception {

    /**
     * 构造函数，用于创建一个带有详细消息的UnauthorizedException实例。
     *
     * @param message 异常的详细描述，有助于理解未授权访问的具体原因。
     */
    public GovUnauthorizedException(String message) {
        super(message);
    }
}
