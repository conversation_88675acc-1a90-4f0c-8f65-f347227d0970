package com.ruoyi.microPlatform.mapper;

import java.util.List;

import com.ruoyi.microPlatform.domain.*;

/**
 * 网格信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbGridInfoMapper 
{
    /**
     * 查询网格信息
     * 
     * @param id 网格信息主键
     * @return 网格信息
     */
    public TbGridInfo selectTbGridInfoById(Long id);

    public TbGridInfo selectGridInfoById(Long id);

    public List<TbDeptUser> selectDeptUserByGridId(Long id);

    public List<TbGridUser> selectGridUserByGridId(Long id);

    /**
     * 校验网格信息是否唯一
     *
     * @param tbGridInfo
     * @return 网格信息
     */
    public TbGridInfo checkTbGridInfoUnique(TbGridInfo tbGridInfo);

    /**
     * 查询网格信息列表
     * 
     * @param tbGridInfo 网格信息
     * @return 网格信息集合
     */
    public List<TbGridInfo> selectTbGridInfoList(TbGridInfo tbGridInfo);

    public List<Long> selectExportGridInfoCount(TbGridInfo tbGridInfo);

    public List<TbGridInfo> getWorkGrid(TbGridInfo tbGridInfo);

    /**
     * 新增网格信息
     * 
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    public int insertTbGridInfo(TbGridInfo tbGridInfo);

    /**
     * 修改网格信息
     * 
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    public int updateTbGridInfo(TbGridInfo tbGridInfo);

    /**
     * 删除网格信息
     * 
     * @param id 网格信息主键
     * @return 结果
     */
    public int deleteTbGridInfoById(Long id);

    /**
     * 批量删除网格信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbGridInfoByIds(Long[] ids);


    /**
     * 获得网格的数据list
     * @return
     */
    List<TbGridInfo> selectTbGridInfoSpaceList(LargeScreenInfo largeScreenInfo);


    TbGridInfo getGridInfo(LargeScreenInfo largeScreenInfo);
}
