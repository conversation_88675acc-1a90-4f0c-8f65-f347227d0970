package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网格信息对象 tb_grid_info
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbGridInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 所属部门
     */
    private Long deptId;

    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）", width = 18, oneKey = "ONE_DEPT_LIST",  childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true)
    private String county;

    /**
     * 所属街道/乡镇
     */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /**
     * 所属村/社区
     */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /**
     * 网格编码
     */
//    @Excel(name = "网格编码")
    private String gridCode;

    /**
     * 网格名称
     */
    @Excel(name = "网格编码")
    private String gridName;

    /**
     * 网格排序
     */
    private Integer gridSort;

    /**
     * 下辖小区（楼院）
     */
    @Excel(name = "下辖小区（楼院）", width = 20)
    private String administrativePlan;

    @Excel(name = "网格范围", width = 20)
    private String boundary;

    private String area;

    /**
     * 是否建立（1是 2否）
     */
    @Excel(name = "是否建立党组织", combo = {"是", "否"}, readConverterExp = "1=是,2=否")
    private Integer establishStatus;

    /**
     * 党支部/党小组
     */
    @Excel(name = "党支部/党小组", combo = {"党支部", "党小组"})
    private String functionType;

    /**
     * 党组织负责人及联系方式
     */
    @Excel(name = "党组织负责人及联系方式", width = 25)
    private String personPhone;

    /**
     * 网格状态（1正常 2异常）
     */
//    @Excel(name = "网格状态", readConverterExp = "1=正常,2=异常", combo = {"正常", "异常"})
    private Integer status;

    /**
     * 空间区域
     */
    @Excel(name = "空间区域", type = Excel.Type.EXPORT)
    private String geometry;

    private List<Map<String, Object>> geometryArray;

    /**
     * 小区数
     */
    private Integer communityNum;

    /**
     * 照片
     */
//    @Excel(name = "照片", cellType = Excel.ColumnType.IMAGE)
    private String accessory;
    /**
     * 社区干部
     */
    private List<TbDeptUser> deptUserList;
    /**
     * 网格员
     */
    private List<TbGridUser> gridUserList;

    /**
     * 兼职网格员
     */
    private List<TbSidelineGridUser> sidelineGridUserList;



    /**
     * String 兼职网格员类型
     *
     */
    private Map<String,List<TbSidelineGridUser>> sidelineGridUserMap;

    //支部数据
    private List<TbBranchInfo> tbBranchInfoList;

    /**
     * 兼职网格员检索项
     */
    private String type;

    private String name;

    private String idCard;

    private String phone;

    //支部名称
    private String branchName;
    //支部书记
    private String secretaryName;
    //支部书记电话
    private String secretaryPhone;

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getSecretaryName() {
        return secretaryName;
    }

    public void setSecretaryName(String secretaryName) {
        this.secretaryName = secretaryName;
    }

    public String getSecretaryPhone() {
        return secretaryPhone;
    }

    public void setSecretaryPhone(String secretaryPhone) {
        this.secretaryPhone = secretaryPhone;
    }

    public List<TbBranchInfo> getTbBranchInfoList() {
        return tbBranchInfoList;
    }

    public void setTbBranchInfoList(List<TbBranchInfo> tbBranchInfoList) {
        this.tbBranchInfoList = tbBranchInfoList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<TbSidelineGridUser> getSidelineGridUserList() {
        return sidelineGridUserList;
    }

    public void setSidelineGridUserList(List<TbSidelineGridUser> sidelineGridUserList) {
        this.sidelineGridUserList = sidelineGridUserList;
    }

    public Map<String, List<TbSidelineGridUser>> getSidelineGridUserMap() {
        return sidelineGridUserMap;
    }

    public void setSidelineGridUserMap(Map<String, List<TbSidelineGridUser>> sidelineGridUserMap) {
        this.sidelineGridUserMap = sidelineGridUserMap;
    }

    public Boolean manageGrid;

    public Boolean getManageGrid() {
        return manageGrid;
    }


    public void setManageGrid(Boolean manageGrid) {
        this.manageGrid = manageGrid;
    }

    public String getGridCode() {
        return gridCode;
    }

    public void setGridCode(String gridCode) {
        this.gridCode = gridCode;
    }

    public Integer getEstablishStatus() {
        return establishStatus;
    }

    public void setEstablishStatus(Integer establishStatus) {
        this.establishStatus = establishStatus;
    }

    public String getFunctionType() {
        return functionType;
    }

    public void setFunctionType(String functionType) {
        this.functionType = functionType;
    }

    public String getPersonPhone() {
        return personPhone;
    }

    public void setPersonPhone(String personPhone) {
        this.personPhone = personPhone;
    }

    public List<TbDeptUser> getDeptUserList() {
        return deptUserList;
    }

    public void setDeptUserList(List<TbDeptUser> deptUserList) {
        this.deptUserList = deptUserList;
    }

    public List<TbGridUser> getGridUserList() {
        return gridUserList;
    }

    public void setGridUserList(List<TbGridUser> gridUserList) {
        this.gridUserList = gridUserList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCounty() {
        return county;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTown() {
        return town;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridSort(Integer gridSort) {
        this.gridSort = gridSort;
    }

    public Integer getGridSort() {
        return gridSort;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setAdministrativePlan(String administrativePlan) {
        this.administrativePlan = administrativePlan;
    }

    public String getAdministrativePlan() {
        return administrativePlan;
    }

    public void setBoundary(String boundary) {
        this.boundary = boundary;
    }

    public String getBoundary() {
        return boundary;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getArea() {
        return area;
    }

    public void setCommunityNum(Integer communityNum) {
        this.communityNum = communityNum;
    }

    public Integer getCommunityNum() {
        return communityNum;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public List<Map<String, Object>> getGeometryArray() {
        if (StringUtils.isNotBlank(this.geometry)) {
            List<Map<String, Object>> list = new ArrayList<>();
            String[] split = geometry.split(";");
            for (String string : split) {
                String[] split1 = string.split(",");
                Map<String, Object> map = new HashMap<>();
                if (split1.length == 2) {
                    map.put("lng", split1[0]);
                    map.put("lat", split1[1]);
                    list.add(map);
                }
            }
            geometryArray = list;
        }
        return geometryArray;
    }

    public void setGeometryArray(List<Map<String, Object>> geometryArray) {
        if (geometryArray != null && geometryArray.size() > 0) {
            List<String> list = new ArrayList<>();
            for (Map<String, Object> map : geometryArray) {
                Object lng = map.get("lng");
                Object lat = map.get("lat");
                list.add(lng + "," + lat);
            }
            this.geometry = StringUtils.join(list, ";");
        } else {
            this.geometry = "";
        }
        this.geometryArray = geometryArray;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deptId", getDeptId())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("gridName", getGridName())
                .append("gridSort", getGridSort())
                .append("geometry", getGeometry())
                .append("administrativePlan", getAdministrativePlan())
                .append("boundary", getBoundary())
                .append("area", getArea())
                .append("communityNum", getCommunityNum())
                .append("remark", getRemark())
                .append("accessory", getAccessory())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("status", getStatus())
                .toString();
    }
}
