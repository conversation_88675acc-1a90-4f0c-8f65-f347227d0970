package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbSituationOrg;

/**
 * 组织情况（非公经济组织/新社会组织）Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ITbSituationOrgService 
{
    /**
     * 查询组织情况（非公经济组织/新社会组织）
     * 
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 组织情况（非公经济组织/新社会组织）
     */
    public TbSituationOrg selectTbSituationOrgById(Long id);

    /**
     * 校验组织情况（非公经济组织/新社会组织）是否存在
     *
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 组织情况（非公经济组织/新社会组织）
     */
    public boolean checkTbSituationOrg(TbSituationOrg tbSituationOrg);

    /**
     * 查询组织情况（非公经济组织/新社会组织）列表
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 组织情况（非公经济组织/新社会组织）集合
     */
    public List<TbSituationOrg> selectTbSituationOrgList(TbSituationOrg tbSituationOrg);

    /**
     * 导入组织情况（非公经济组织/新社会组织）
     *
     * @param infos       组织情况（非公经济组织/新社会组织）列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSituationOrg(List<TbSituationOrg> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增组织情况（非公经济组织/新社会组织）
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    public int insertTbSituationOrg(TbSituationOrg tbSituationOrg);

    /**
     * 修改组织情况（非公经济组织/新社会组织）
     * 
     * @param tbSituationOrg 组织情况（非公经济组织/新社会组织）
     * @return 结果
     */
    public int updateTbSituationOrg(TbSituationOrg tbSituationOrg);

    /**
     * 批量删除组织情况（非公经济组织/新社会组织）
     * 
     * @param ids 需要删除的组织情况（非公经济组织/新社会组织）主键集合
     * @return 结果
     */
    public int deleteTbSituationOrgByIds(Long[] ids);

    /**
     * 删除组织情况（非公经济组织/新社会组织）信息
     * 
     * @param id 组织情况（非公经济组织/新社会组织）主键
     * @return 结果
     */
    public int deleteTbSituationOrgById(Long id);


    List<CommonBaseCount> situationOrgGroupPartyType();
}
