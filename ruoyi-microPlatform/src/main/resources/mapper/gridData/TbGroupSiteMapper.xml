<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbGroupSiteMapper">

    <resultMap type="TbGroupSite" id="TbGroupSiteResult">
            <result property="id"    column="id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="county"    column="county"    />
            <result property="country"    column="country"    />
            <result property="town"    column="town"    />
            <result property="siteName"    column="site_name"    />
            <result property="siteType"    column="site_type"    />
            <result property="sitePlatform"    column="site_platform"    />
            <result property="siteAddress"    column="site_address"    />
            <result property="sitePhone"    column="site_phone"    />
            <result property="totalWorkers"    column="total_workers"    />
            <result property="ageDistribution"    column="age_distribution"    />
            <result property="partyPeoples"    column="party_peoples"    />
            <result property="turnoverRate"    column="turnover_rate"    />
            <result property="reportPeoples"    column="report_peoples"    />
            <result property="lat"    column="lat"    />
            <result property="lng"    column="lng"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTbGroupSiteVo">
        select id, dept_id, county, country, town, site_name, site_type, site_platform, site_address, site_phone, total_workers, age_distribution, party_peoples, turnover_rate, report_peoples, lat, lng, create_time, create_by, update_time, update_by from tb_group_site
    </sql>

    <select id="selectTbGroupSiteList" parameterType="TbGroupSite" resultMap="TbGroupSiteResult">
        <include refid="selectTbGroupSiteVo"/>
        <where>
                        <if test="deptId != null "> and dept_id = #{deptId}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="country != null  and country != ''"> and country = #{country}</if>
                        <if test="town != null  and town != ''"> and town = #{town}</if>
                        <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
                        <if test="siteType != null  and siteType != ''"> and site_type = #{siteType}</if>
                        <if test="sitePlatform != null  and sitePlatform != ''"> and site_platform = #{sitePlatform}</if>
                        <if test="siteAddress != null  and siteAddress != ''"> and site_address = #{siteAddress}</if>
                        <if test="sitePhone != null  and sitePhone != ''"> and site_phone = #{sitePhone}</if>
                        <if test="totalWorkers != null  and totalWorkers != ''"> and total_workers = #{totalWorkers}</if>
                        <if test="ageDistribution != null  and ageDistribution != ''"> and age_distribution = #{ageDistribution}</if>
                        <if test="partyPeoples != null "> and party_peoples = #{partyPeoples}</if>
                        <if test="turnoverRate != null  and turnoverRate != ''"> and turnover_rate = #{turnoverRate}</if>
                        <if test="reportPeoples != null "> and report_peoples = #{reportPeoples}</if>
                        <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
                        <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
        </where>
    </select>

    <select id="selectTbGroupSiteById" parameterType="Long" resultMap="TbGroupSiteResult">
            <include refid="selectTbGroupSiteVo"/>
            where id = #{id}
    </select>

    <select id="checkTbGroupSiteUnique" parameterType="TbGroupSite" resultMap="TbGroupSiteResult">
        select id from tb_group_site where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbGroupSite" parameterType="TbGroupSite" useGeneratedKeys="true" keyProperty="id">
        insert into tb_group_site
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">dept_id,</if>
                    <if test="county != null">county,</if>
                    <if test="country != null">country,</if>
                    <if test="town != null">town,</if>
                    <if test="siteName != null">site_name,</if>
                    <if test="siteType != null">site_type,</if>
                    <if test="sitePlatform != null">site_platform,</if>
                    <if test="siteAddress != null">site_address,</if>
                    <if test="sitePhone != null">site_phone,</if>
                    <if test="totalWorkers != null">total_workers,</if>
                    <if test="ageDistribution != null">age_distribution,</if>
                    <if test="partyPeoples != null">party_peoples,</if>
                    <if test="turnoverRate != null">turnover_rate,</if>
                    <if test="reportPeoples != null">report_peoples,</if>
                    <if test="lat != null">lat,</if>
                    <if test="lng != null">lng,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="deptId != null">#{deptId},</if>
                    <if test="county != null">#{county},</if>
                    <if test="country != null">#{country},</if>
                    <if test="town != null">#{town},</if>
                    <if test="siteName != null">#{siteName},</if>
                    <if test="siteType != null">#{siteType},</if>
                    <if test="sitePlatform != null">#{sitePlatform},</if>
                    <if test="siteAddress != null">#{siteAddress},</if>
                    <if test="sitePhone != null">#{sitePhone},</if>
                    <if test="totalWorkers != null">#{totalWorkers},</if>
                    <if test="ageDistribution != null">#{ageDistribution},</if>
                    <if test="partyPeoples != null">#{partyPeoples},</if>
                    <if test="turnoverRate != null">#{turnoverRate},</if>
                    <if test="reportPeoples != null">#{reportPeoples},</if>
                    <if test="lat != null">#{lat},</if>
                    <if test="lng != null">#{lng},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTbGroupSite" parameterType="TbGroupSite">
        update tb_group_site
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="siteName != null">site_name = #{siteName},</if>
                    <if test="siteType != null">site_type = #{siteType},</if>
                    <if test="sitePlatform != null">site_platform = #{sitePlatform},</if>
                    <if test="siteAddress != null">site_address = #{siteAddress},</if>
                    <if test="sitePhone != null">site_phone = #{sitePhone},</if>
                    <if test="totalWorkers != null">total_workers = #{totalWorkers},</if>
                    <if test="ageDistribution != null">age_distribution = #{ageDistribution},</if>
                    <if test="partyPeoples != null">party_peoples = #{partyPeoples},</if>
                    <if test="turnoverRate != null">turnover_rate = #{turnoverRate},</if>
                    <if test="reportPeoples != null">report_peoples = #{reportPeoples},</if>
                    <if test="lat != null">lat = #{lat},</if>
                    <if test="lng != null">lng = #{lng},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbGroupSiteById" parameterType="Long">
        delete from tb_group_site where id = #{id}
    </delete>

    <delete id="deleteTbGroupSiteByIds" parameterType="String">
        delete from tb_group_site where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>