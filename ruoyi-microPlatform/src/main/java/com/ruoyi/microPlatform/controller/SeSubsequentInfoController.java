package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeSubsequentInfo;
import com.ruoyi.microPlatform.service.ISeSubsequentInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 后续动态信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/subsequentInfo")
public class SeSubsequentInfoController extends BaseController {
    @Autowired
    private ISeSubsequentInfoService seSubsequentInfoService;

    /**
     * 查询后续动态信息列表
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeSubsequentInfo seSubsequentInfo) {
        startPage();
        List<SeSubsequentInfo> list = seSubsequentInfoService.selectSeSubsequentInfoList(seSubsequentInfo);
        return getDataTable(list);
    }


    /**
     * 查询后续动态信息列表,当某条数据有 parent_id时，则找到被回复的数据，放到当前数据中
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:list')")
    @GetMapping("/listParent")
    public TableDataInfo listParent(SeSubsequentInfo seSubsequentInfo) throws CloneNotSupportedException {
        startPage();
        List<SeSubsequentInfo> list = seSubsequentInfoService.selectSeSubsequentInfoListParent(seSubsequentInfo);
        return getDataTable2(list);
    }


    /**
     * 查询后续动态信息列表 数量 根据form_id和form_type
     * ---废弃
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:list')")
    @GetMapping("/listSize")
    public AjaxResult listSize(SeSubsequentInfo seSubsequentInfo) {
        Map<String, Integer> map = seSubsequentInfoService.selectSeSubsequentInfoListSize(seSubsequentInfo);
        return success(map);
    }


    /**
     * 导出后续动态信息列表
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:export')")
    @Log(title = "后续动态信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeSubsequentInfo seSubsequentInfo) {
        List<SeSubsequentInfo> list = seSubsequentInfoService.selectSeSubsequentInfoList(seSubsequentInfo);
        ExcelUtil<SeSubsequentInfo> util = new ExcelUtil<SeSubsequentInfo>(SeSubsequentInfo.class);
        util.exportExcel(response, list, "后续动态信息数据");
    }

    @Log(title = "后续动态信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SeSubsequentInfo> util = new ExcelUtil<SeSubsequentInfo>(SeSubsequentInfo.class);
        List<SeSubsequentInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seSubsequentInfoService.importSeSubsequentInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SeSubsequentInfo> util = new ExcelUtil<SeSubsequentInfo>(SeSubsequentInfo.class);
        util.exportTemplateExcel(response, "后续动态信息数据");
    }

    /**
     * 获取后续动态信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(seSubsequentInfoService.selectSeSubsequentInfoById(id));
    }

    /**
     * 新增后续动态信息
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:add')")
    @Log(title = "后续动态信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeSubsequentInfo seSubsequentInfo) {
        if (ObjectUtil.isEmpty(seSubsequentInfo.getUserId())) {
            seSubsequentInfo.setUserId(getUserId());
        }
        seSubsequentInfo.setNickName(getNickName());
        seSubsequentInfo.setCreateBy(getUsername());
        // 工作人员回复
        return toAjax(seSubsequentInfoService.insertSeSubsequentInfo(seSubsequentInfo, SecurityUtils.getGridId()));
    }

    /**
     * 修改后续动态信息
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:edit')")
    @Log(title = "后续动态信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeSubsequentInfo seSubsequentInfo) {
        seSubsequentInfo.setUpdateBy(getNickName());
        return toAjax(seSubsequentInfoService.updateSeSubsequentInfo(seSubsequentInfo));
    }

    /**
     * 删除后续动态信息
     */
//    @PreAuthorize("@ss.hasPermi('sentiment:subsequentInfo:remove')")
    @Log(title = "后续动态信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(seSubsequentInfoService.deleteSeSubsequentInfoByIds(ids));
    }
}
