package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 主库事项统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Mapper
public interface EventStatisticsMainMapper {

    /**
     * 获取综合事项统计数据
     * @param bigdataParam 查询参数
     * @return 统计数据
     */
    Map<String, Object> getComprehensiveEventStatistics(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取舆情事项统计数据
     * @param bigdataParam 查询参数
     * @return 统计数据
     */
    Map<String, Object> getMajorOpinionEventStatistics(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取综合事项当日新增数量
     * @param bigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getComprehensiveEventTodayCount(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取综合事项待办数量
     * @param bigdataParam 查询参数
     * @return 待办数量
     */
    Integer getComprehensiveEventPendingCount(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取综合事项累计办理数量
     * @param bigdataParam 查询参数
     * @return 累计办理数量
     */
    Integer getComprehensiveEventTotalHandledCount(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取综合事项办结数量
     * @param bigdataParam 查询参数
     * @return 办结数量
     */
    Integer getComprehensiveEventCompletedCount(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取综合事项满意度统计
     * @param bigdataParam 查询参数
     * @return 满意度统计
     */
    Map<String, Object> getComprehensiveEventSatisfactionStats(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取舆情事项当日新增数量
     * @param bigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getMajorOpinionEventTodayCount(@Param("param") BigdataParam bigdataParam);

    /**
     * 获取舆情事项累计数量
     * @param bigdataParam 查询参数
     * @return 累计数量
     */
    Integer getMajorOpinionEventTotalCount(@Param("param") BigdataParam bigdataParam);
}
