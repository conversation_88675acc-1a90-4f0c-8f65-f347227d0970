package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyReportRelation;

import java.util.List;

/**
 * 党组织党员双报到关系Mapper接口
 */
public interface PbPartyReportRelationMapper {
    /**
     * 查询
     */
    PbPartyReportRelation selectPbPartyReportRelationById(Long id);

    /**
     * 校验是否唯一（relation_code唯一）
     */
    PbPartyReportRelation checkPbPartyReportRelationUnique(PbPartyReportRelation relation);

    /**
     * 列表查询
     */
    List<PbPartyReportRelation> selectPbPartyReportRelationList(PbPartyReportRelation relation);

    /**
     * 新增
     */
    int insertPbPartyReportRelation(PbPartyReportRelation relation);

    /**
     * 更新
     */
    int updatePbPartyReportRelation(PbPartyReportRelation relation);

    /**
     * 删除
     */
    int deletePbPartyReportRelationById(Long id);

    /**
     * 批量删除
     */
    int deletePbPartyReportRelationByIds(Long[] ids);

    List<CommonBaseCount> doubleReporting();
}
