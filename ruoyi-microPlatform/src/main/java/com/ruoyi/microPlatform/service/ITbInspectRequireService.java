package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.TbInspectRequire;

/**
 * 门店基础检查项目Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbInspectRequireService 
{
    /**
     * 查询门店基础检查项目
     * 
     * @param id 门店基础检查项目主键
     * @return 门店基础检查项目
     */
    public TbInspectRequire selectTbInspectRequireById(Integer id);

    /**
     * 校验门店基础检查项目是否存在
     *
     * @param tbInspectRequire 门店基础检查项目
     * @return 门店基础检查项目
     */
    public boolean checkTbInspectRequire(TbInspectRequire tbInspectRequire);

    /**
     * 查询门店基础检查项目列表
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 门店基础检查项目集合
     */
    public List<TbInspectRequire> selectTbInspectRequireList(TbInspectRequire tbInspectRequire);

    /**
     * 导入门店基础检查项目
     *
     * @param infos       门店基础检查项目列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbInspectRequire(List<TbInspectRequire> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    public int insertTbInspectRequire(TbInspectRequire tbInspectRequire);

    /**
     * 修改门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    public int updateTbInspectRequire(TbInspectRequire tbInspectRequire);

    /**
     * 批量删除门店基础检查项目
     * 
     * @param ids 需要删除的门店基础检查项目主键集合
     * @return 结果
     */
    public int deleteTbInspectRequireByIds(Integer[] ids);

    /**
     * 删除门店基础检查项目信息
     * 
     * @param id 门店基础检查项目主键
     * @return 结果
     */
    public int deleteTbInspectRequireById(Integer id);

    Map<String,List<TbInspectRequire>> selectTbInspectRequireListByType(TbInspectRequire tbInspectRequire);


}
