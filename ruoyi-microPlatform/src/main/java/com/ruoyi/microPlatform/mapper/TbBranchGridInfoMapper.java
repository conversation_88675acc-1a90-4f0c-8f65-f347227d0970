package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;

/**
 * 支部关联网格Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface TbBranchGridInfoMapper 
{
    /**
     * 查询支部关联网格
     * 
     * @param id 支部关联网格主键
     * @return 支部关联网格
     */
    public TbBranchGridInfo selectTbBranchGridInfoById(Long id);

    /**
     * 校验支部关联网格是否唯一
     *
     * @param tbBranchGridInfo
     * @return 支部关联网格
     */
    public TbBranchGridInfo checkTbBranchGridInfoUnique(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 查询支部关联网格列表
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 支部关联网格集合
     */
    public List<TbBranchGridInfo> selectTbBranchGridInfoList(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 新增支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    public int insertTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 修改支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    public int updateTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 删除支部关联网格
     * 
     * @param id 支部关联网格主键
     * @return 结果
     */
    public int deleteTbBranchGridInfoById(Long id);

    /**
     * 批量删除支部关联网格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbBranchGridInfoByIds(Long[] ids);
}
