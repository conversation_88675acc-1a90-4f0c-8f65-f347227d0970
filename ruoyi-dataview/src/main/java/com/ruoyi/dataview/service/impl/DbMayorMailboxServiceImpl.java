package com.ruoyi.dataview.service.impl;

import java.util.*;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbMayorMailboxMapper;
import com.ruoyi.dataview.domain.po.DbMayorMailbox;
import com.ruoyi.dataview.service.IDbMayorMailboxService;

/**
 * 市长信箱主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbMayorMailboxServiceImpl implements IDbMayorMailboxService {
    @Autowired
    private DbMayorMailboxMapper dbMayorMailboxMapper;

    /**
     * 查询市长信箱主
     *
     * @param id 市长信箱主主键
     * @return 市长信箱主
     */
    @Override
    public DbMayorMailbox selectDbMayorMailboxById(Long id) {
        return dbMayorMailboxMapper.selectDbMayorMailboxById(id);
    }

    /**
     * 校验市长信箱主是否存在
     *
     * @param dbMayorMailbox
     * @return boolean
     */
    @Override
    public boolean checkDbMayorMailbox(DbMayorMailbox dbMayorMailbox) {
        DbMayorMailbox old = dbMayorMailboxMapper.checkDbMayorMailboxUnique(dbMayorMailbox);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询市长信箱主列表
     *
     * @param dbMayorMailbox 市长信箱主
     * @return 市长信箱主
     */
    @Override
    public List<DbMayorMailbox> selectDbMayorMailboxList(DbMayorMailbox dbMayorMailbox) {
        return dbMayorMailboxMapper.selectDbMayorMailboxList(dbMayorMailbox);
    }

    /**
     * 导入市长信箱主
     *
     * @param infos           市长信箱主列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbMayorMailbox(List<DbMayorMailbox> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入市长信箱主数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbMayorMailbox info : infos) {
            try {
                // 验证是否存在这个数据
                DbMayorMailbox old = dbMayorMailboxMapper.checkDbMayorMailboxUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbMayorMailboxMapper.insertDbMayorMailbox(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbMayorMailboxMapper.updateDbMayorMailbox(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增市长信箱主
     *
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    @Override
    public int insertDbMayorMailbox(DbMayorMailbox dbMayorMailbox) {
        dbMayorMailbox.setCreateTime(DateUtils.getNowDate());
        return dbMayorMailboxMapper.insertDbMayorMailbox(dbMayorMailbox);
    }

    /**
     * 修改市长信箱主
     *
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    @Override
    public int updateDbMayorMailbox(DbMayorMailbox dbMayorMailbox) {
        dbMayorMailbox.setUpdateTime(DateUtils.getNowDate());
        return dbMayorMailboxMapper.updateDbMayorMailbox(dbMayorMailbox);
    }

    /**
     * 批量删除市长信箱主
     *
     * @param ids 需要删除的市长信箱主主键
     * @return 结果
     */
    @Override
    public int deleteDbMayorMailboxByIds(Long[] ids) {
        return dbMayorMailboxMapper.deleteDbMayorMailboxByIds(ids);
    }

    /**
     * 删除市长信箱主信息
     *
     * @param id 市长信箱主主键
     * @return 结果
     */
    @Override
    public int deleteDbMayorMailboxById(Long id) {
        return dbMayorMailboxMapper.deleteDbMayorMailboxById(id);
    }

    @Override
    public Map<String, Object> dbMayorMailboxCount() {
        Map<String, Object> res = new HashMap<>();
        Integer i = dbMayorMailboxMapper.dbMayorMailboxAllCount();
        res.put("allCount", i);
        List<CommonBaseCount> commonBaseCounts = dbMayorMailboxMapper.dbMayorMailboxCountGroupByCategory();
        res.put("categoryGroup", commonBaseCounts);
        return res;
    }
}
