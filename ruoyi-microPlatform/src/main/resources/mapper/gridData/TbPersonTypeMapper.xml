<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbPersonTypeMapper">

    <resultMap type="TbPersonType" id="TbPersonTypeResult">
        <result property="id" column="id"/>
        <result property="label" column="label"/>
        <result property="value" column="value"/>
        <result property="text" column="label"/>
    </resultMap>


    <sql id="selectTbPersonTypeVo">
        select id, label from tb_person_type
    </sql>

    <select id="selectTbPersonTypeList" parameterType="TbPersonType" resultMap="TbPersonTypeResult">
        <include refid="selectTbPersonTypeVo"/>
        <where>
            <if test="label != null  and label != ''">and label = #{label}</if>
        </where>
    </select>

    <select id="selectTbPersonTypeById" parameterType="Integer" resultMap="TbPersonTypeResult">
        <include refid="selectTbPersonTypeVo"/>
        where id = #{id}
    </select>

    <select id="checkTbPersonTypeUnique" parameterType="TbPersonType" resultMap="TbPersonTypeResult">
        select id from tb_person_type where label = #{label}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbPersonType" parameterType="TbPersonType" useGeneratedKeys="true" keyProperty="id">
        insert into tb_person_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="label != null">label,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="label != null">#{label},</if>
        </trim>
    </insert>

    <update id="updateTbPersonType" parameterType="TbPersonType">
        update tb_person_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="label != null">label = #{label},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbPersonTypeById" parameterType="Integer">
        delete from tb_person_type where id = #{id}
    </delete>

    <delete id="deleteTbPersonTypeByIds" parameterType="String">
        delete from tb_person_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectLableRank" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbPersonTypeResult">
        SELECT
        pt.label  AS label,
        COUNT(DISTINCT rm.id) AS value
        FROM tb_person_type pt
        LEFT JOIN tb_resident_member_type rmt
        ON pt.id = rmt.type_id
        LEFT JOIN tb_resident_member rm
        ON rmt.member_id = rm.id
        <where>
            <if test="deptId != null">
                AND (rm.dept_id = #{deptId}
                OR EXISTS (
                SELECT 1
                FROM sys_dept t
                WHERE find_in_set(#{deptId}, t.ancestors)
                AND t.dept_id = rm.dept_id))
            </if>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="gridName != null">
                and rm.grid_name = #{gridName}
            </if>
            <if test="communityName != null">
                and rm.resident_id in (select id from tb_community_resident where community_name = #{communityName})
            </if>
            <if test="residentId != null">
                and rm.resident_id = #{residentId}
            </if>
            <if test="label != null">
                and pt.label = #{label}
            </if>
        </where>
        GROUP BY pt.label
        ORDER BY value DESC
    </select>
</mapper>