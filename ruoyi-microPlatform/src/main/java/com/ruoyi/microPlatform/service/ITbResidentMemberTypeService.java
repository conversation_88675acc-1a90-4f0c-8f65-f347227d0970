package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbResidentMemberType;

/**
 * 成员标签配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface ITbResidentMemberTypeService 
{
    /**
     * 查询成员标签配置
     * 
     * @param id 成员标签配置主键
     * @return 成员标签配置
     */
    public TbResidentMemberType selectTbResidentMemberTypeById(Long id);

    /**
     * 校验成员标签配置是否存在
     *
     * @param tbResidentMemberType 成员标签配置
     * @return 成员标签配置
     */
    public boolean checkTbResidentMemberType(TbResidentMemberType tbResidentMemberType);

    /**
     * 查询成员标签配置列表
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 成员标签配置集合
     */
    public List<TbResidentMemberType> selectTbResidentMemberTypeList(TbResidentMemberType tbResidentMemberType);

    /**
     * 导入成员标签配置
     *
     * @param infos       成员标签配置列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentMemberType(List<TbResidentMemberType> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    public int insertTbResidentMemberType(TbResidentMemberType tbResidentMemberType);

    /**
     * 修改成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    public int updateTbResidentMemberType(TbResidentMemberType tbResidentMemberType);

    /**
     * 批量删除成员标签配置
     * 
     * @param ids 需要删除的成员标签配置主键集合
     * @return 结果
     */
    public int deleteTbResidentMemberTypeByIds(Long[] ids);

    /**
     * 删除成员标签配置信息
     * 
     * @param id 成员标签配置主键
     * @return 结果
     */
    public int deleteTbResidentMemberTypeById(Long id);
}
