package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SysVideoInfo;

/**
 * 视频信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
public interface SysVideoInfoMapper 
{
    /**
     * 查询视频信息
     * 
     * @param id 视频信息主键
     * @return 视频信息
     */
    public SysVideoInfo selectSysVideoInfoById(Long id);

    /**
     * 查询视频信息列表
     * 
     * @param sysVideoInfo 视频信息
     * @return 视频信息集合
     */
    public List<SysVideoInfo> selectSysVideoInfoList(SysVideoInfo sysVideoInfo);

    /**
     * 新增视频信息
     * 
     * @param sysVideoInfo 视频信息
     * @return 结果
     */
    public int insertSysVideoInfo(SysVideoInfo sysVideoInfo);

    /**
     * 修改视频信息
     * 
     * @param sysVideoInfo 视频信息
     * @return 结果
     */
    public int updateSysVideoInfo(SysVideoInfo sysVideoInfo);

    /**
     * 删除视频信息
     * 
     * @param id 视频信息主键
     * @return 结果
     */
    public int deleteSysVideoInfoById(Long id);

    /**
     * 批量删除视频信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysVideoInfoByIds(Long[] ids);
}
