package com.ruoyi.web.controller.system;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.RSAUtils;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbGridUser;
import com.ruoyi.microPlatform.service.ITbGridInfoService;
import com.ruoyi.microPlatform.service.ITbGridUserService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysRoleService roleService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysPostService postService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private ITbGridUserService gridUserService;
    @Autowired
    private ITbGridInfoService gridInfoService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        list.parallelStream().forEach(item -> {
            if (StringUtils.isNotBlank(item.getDrillNo())) {
                String drillNo = item.getDrillNo();
                String gridNo = "";
                String[] split = drillNo.split(",");
                for (int i = 0; i < split.length; i++) {
                    TbGridInfo gridInfo = gridInfoService.selectTbGridInfoById(Long.parseLong(split[i]));
                    {
                        if (ObjectUtil.isNotEmpty(gridInfo)) {
                            gridNo += gridInfo.getGridName() + "，";
                        }
                    }
                }
                item.setGrid(gridNo);
            }
            List<SysRole> sysRoles = roleService.selectRolesByUserId2(item.getUserId());
            item.setRoles(sysRoles);
        });
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = {"/", "/{userId}"})
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        if (StringUtils.isNotNull(userId)) {
            userService.checkUserDataScope(userId);
            SysUser sysUser = userService.selectUserById(userId);
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            String dataDeptName = getDataDeptName();
            if (StringUtils.isNotBlank(dataDeptName)) {
                String[] split = dataDeptName.split("\\+");
                ajax.put("deptDataName", split[0]);
                ajax.put("deptDataPhone", split.length > 1 ? split[1] : "");
                ajax.put("deptDataId", split.length > 2 ? split[2] : "");
            } else {
                ajax.put("deptDataName", "");
                ajax.put("deptDataPhone", "");
                ajax.put("deptDataId", "");
            }
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
        }

        //ajax.put("roles", SysUser.isAdmin(getUserId()) ? roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()) : roles.stream().filter(r -> !r.isAdmin() && !r.isShuJi() && !r.isShuJiExport()).collect(Collectors.toList()));
        ajax.put("roles",
                (SysUser.isAdmin(getUserId()) || "许昌市".equals(ajax.get("deptDataName")))
                        ? roles.stream()
                        .filter(r -> !r.isAdmin())
                        .collect(Collectors.toList())
                        : roles.stream()
                        .filter(r -> !r.isAdmin() && !r.isShuJi() && !r.isShuJiExport())
                        .collect(Collectors.toList())
        );

        ajax.put("posts", postService.selectPostAll());
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult add(@Validated @RequestBody SysUser user) throws InvalidKeySpecException, NoSuchAlgorithmException {
        deptService.checkDeptDataScope(user.getDeptId());
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && !userService.checkPhoneUnique(user)) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
//        else if (StringUtils.isNotEmpty(user.getEmail())
//                && !userService.checkEmailUnique(user))
//        {
//            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
//        }
        user.setCreateBy(getUsername());
        user.setSpare(RSAUtils.publicEncrypt(user.getPassword(), RSAUtils.getPublicKey(RuoYiConfig.getPublicKey())));
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        if (ObjectUtil.isNotEmpty(user.getRoleIds()) && user.getRoleIds().length > 0) {
            List<Long> roles = Arrays.asList(user.getRoleIds());
            if (roles.contains(2l) && user.getRoleIds().length > 1) {
                return error("新增用户'" + user.getUserName() + "'失败，用户类型不可既是普通用户又是管理员");
            }
            SysDept dept = deptService.selectDeptById(user.getDeptId());
            if (ObjectUtil.isNotEmpty(dept)) {
                if (dept.getDeptLevel().intValue() == 8 && roles.contains(4l)) {
                    return error("新增网格级用户'" + user.getUserName() + "'失败，网格级用户角色不可是村/社区数据管理员");
                }
                if (dept.getDeptLevel().intValue() == 8) {
                    if (StringUtils.isNotBlank(user.getPhonenumber())) {
                        // 重新划分为网格
                        List<Long> longs = gridUserService.selectGridIdByPhone(user.getPhonenumber());
                        if (longs.size() > 0) {
                            if (longs.contains(dept.getGridId())) {
                                user.setDrillNo(longs.stream().map(Object::toString).collect(Collectors.joining(",")));
                            } else {
                                user.setDrillNo(longs.stream().map(Object::toString).collect(Collectors.joining(",")) + "," + dept.getGridId());
                            }
                        }
                    }
                }
            }
        }
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@Validated @RequestBody SysUser user) {
        boolean delete = false;
        if (ObjectUtil.isNotEmpty(user.getRoleIds()) && user.getRoleIds().length > 0) {
            if (Arrays.asList(user.getRoleIds()).contains(2l) && user.getRoleIds().length > 1) {
                return error("新增用户'" + user.getUserName() + "'失败，用户类型不可既是普通用户又是管理员");
            }
            if (Arrays.asList(user.getRoleIds()).contains(2l)) {
                delete = true;
            }
        }



        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        deptService.checkDeptDataScope(user.getDeptId());
        roleService.checkRoleDataScope(user.getRoleIds());
        if (!userService.checkUserNameUnique(user)) {
            return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && !(userService.checkPhoneUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
//        else if (StringUtils.isNotEmpty(user.getEmail())
//                && !userService.checkEmailUnique(user))
//        {
//            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
//        }
        user.setUpdateBy(getUsername());
        int i = userService.updateUser(user);
        if (i > 0) {
            updateOnLineUserInfo(user.getUserId(), delete);
        }
        return toAjax(i);
    }

    /**
     * 更新用户信息
     *
     * @param userId
     * @throws IOException
     */
    @Async("threadPoolTaskExecutor")
    public void updateOnLineUserInfo(Long userId, boolean delete) {
        SysUser user = userService.selectUserById(userId);
        if (user.getDept().getDeptLevel().intValue() == 8) {
            if (StringUtils.isNotBlank(user.getPhonenumber())) {
                // 重新划分为网格
                List<Long> longs = gridUserService.selectGridIdByPhone(user.getPhonenumber());
                if (longs.size() > 0) {
                    if (longs.contains(user.getDept().getGridId())) {
                        user.setDrillNo(longs.stream().map(Object::toString).collect(Collectors.joining(",")));
                    } else {
                        user.setDrillNo(longs.stream().map(Object::toString).collect(Collectors.joining(",")) + "," + user.getDept().getGridId());
                    }
                    userService.updateUserProfile(new SysUser() {{
                        setUserId(userId);
                        setDrillNo(user.getDrillNo());
                    }});
                } else {
                    userService.updateUserProfile(new SysUser() {{
                        setUserId(userId);
                        setDrillNo(user.getDept().getGridId() + "");
                    }});
                    user.setDrillNo(user.getDept().getGridId() + "");
                }
            }
        }
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + userId + ":*");
        for (String key : keys) {
            LoginUser loginUser = redisCache.getCacheObject(key);
            if (ObjectUtil.isNotEmpty(loginUser) && userId.longValue() == loginUser.getUserId().longValue()) {//更新这个人的信息
                if (delete) {
                    redisCache.deleteObject(key);
                } else {
                    System.err.println("刷新用户信息啦：" + loginUser.getUser().getNickName());
                    System.err.println("刷新用户权限信息：" + loginUser.getUser().getNickName());
                    // 权限集合
                    Set<String> permissions = permissionService.getMenuPermission(user);
                    loginUser.setPermissions(permissions);
                    loginUser.setUser(user);
                    loginUser.setDeptId(user.getDeptId());
                    tokenService.setLoginUser(loginUser);
                }
            }
        }
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] userIds) {
        if (ArrayUtils.contains(userIds, getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    @RepeatSubmit
    public AjaxResult resetPwd(@RequestBody SysUser user) throws InvalidKeySpecException, NoSuchAlgorithmException {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setSpare(RSAUtils.publicEncrypt(user.getPassword(), RSAUtils.getPublicKey(RuoYiConfig.getPublicKey())));
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        user.setIsUpdate(0);
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    @RepeatSubmit
    public AjaxResult changeStatus(@RequestBody SysUser user) {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(getUsername());
        int i = userService.updateUserStatus(user);
        if (i > 0 && user.getStatus().equals("0")) {
            this.updateOnLineUserInfo(user.getUserId(), false);
        }
        return toAjax(i);
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId) {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds) {
        userService.checkUserDataScope(userId);
        roleService.checkRoleDataScope(roleIds);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept) {
        return success(deptService.selectDeptTreeList(dept));
    }

    @GetMapping("getSelectUser")
    public AjaxResult getSelectUser() {
        return AjaxResult.success(userService.selectUserList(new SysUser()));
    }

    @GetMapping("getUserByPost")
    public AjaxResult getUserByPost(String post) {
        return AjaxResult.success(userService.selectUserByPost(post));
    }

}
