package com.ruoyi.microPlatform.service.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbPersonTypeMapper;
import com.ruoyi.microPlatform.domain.TbPersonType;
import com.ruoyi.microPlatform.service.ITbPersonTypeService;

import javax.annotation.PostConstruct;

/**
 * 人员标签类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-04
 */
@Service
public class TbPersonTypeServiceImpl implements ITbPersonTypeService 
{
    @Autowired
    private TbPersonTypeMapper tbPersonTypeMapper;
    @Autowired
    RedisCache redisCache;

    @PostConstruct
    public void init1() throws UnknownHostException {
        String hostAddress = InetAddress.getLocalHost().getHostAddress();
        if (("**************").equals(hostAddress) || hostAddress.startsWith("172.168.10.")) {
            init();
        }
    }

    public void init(){
        Collection<String> keys = redisCache.keys(CacheConstants.PERSON_TYPE_NAME_KEY + "*");
        redisCache.deleteObject(keys);
        keys.clear();
        List<TbPersonType> personTypes = tbPersonTypeMapper.selectTbPersonTypeList(new TbPersonType());
        for (TbPersonType personType : personTypes) {
            redisCache.setCacheObject(CacheConstants.PERSON_TYPE_NAME_KEY + personType.getLabel(), personType.getId());
        }
    }

    /**
     * 查询人员标签类型
     * 
     * @param id 人员标签类型主键
     * @return 人员标签类型
     */
    @Override
    public TbPersonType selectTbPersonTypeById(Integer id)
    {
        return tbPersonTypeMapper.selectTbPersonTypeById(id);
    }

    /**
     * 校验人员标签类型是否存在
     *
     * @param tbPersonType
     * @return boolean
     */
    @Override
    public boolean checkTbPersonType(TbPersonType tbPersonType){
        TbPersonType old = tbPersonTypeMapper.checkTbPersonTypeUnique(tbPersonType);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询人员标签类型列表
     * 
     * @param tbPersonType 人员标签类型
     * @return 人员标签类型
     */
    @Override
    public List<TbPersonType> selectTbPersonTypeList(TbPersonType tbPersonType)
    {
        return tbPersonTypeMapper.selectTbPersonTypeList(tbPersonType);
    }

    /**
     * 导入人员标签类型
     *
     * @param infos       人员标签类型列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbPersonType(List<TbPersonType> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入人员标签类型数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbPersonType info : infos)
        {
            try
            {
                info.setId(null);
                // 验证是否存在这个数据
                TbPersonType old = tbPersonTypeMapper.checkTbPersonTypeUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbPersonTypeMapper.insertTbPersonType(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbPersonTypeMapper.updateTbPersonType(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    @Override
    public int insertTbPersonType(TbPersonType tbPersonType)
    {
        int i = tbPersonTypeMapper.insertTbPersonType(tbPersonType);
        this.init();
        return i;
    }

    /**
     * 修改人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    @Override
    public int updateTbPersonType(TbPersonType tbPersonType)
    {
        int i = tbPersonTypeMapper.updateTbPersonType(tbPersonType);
        this.init();
        return i;
    }

    /**
     * 批量删除人员标签类型
     * 
     * @param ids 需要删除的人员标签类型主键
     * @return 结果
     */
    @Override
    public int deleteTbPersonTypeByIds(Integer[] ids)
    {
        return tbPersonTypeMapper.deleteTbPersonTypeByIds(ids);
    }

    /**
     * 删除人员标签类型信息
     * 
     * @param id 人员标签类型主键
     * @return 结果
     */
    @Override
    public int deleteTbPersonTypeById(Integer id)
    {
        return tbPersonTypeMapper.deleteTbPersonTypeById(id);
    }
}
