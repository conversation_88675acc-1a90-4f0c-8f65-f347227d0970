package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.domain.SysInfoChild;
import com.ruoyi.system.mapper.SysInfoTestMapper;
import com.ruoyi.system.domain.SysInfoTest;
import com.ruoyi.system.service.ISysInfoTestService;

/**
 * 测试管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-10
 */
@Service
public class SysInfoTestServiceImpl implements ISysInfoTestService 
{
    @Autowired
    private SysInfoTestMapper sysInfoTestMapper;

    /**
     * 查询测试管理
     * 
     * @param id 测试管理主键
     * @return 测试管理
     */
    @Override
    public SysInfoTest selectSysInfoTestById(Integer id)
    {
        return sysInfoTestMapper.selectSysInfoTestById(id);
    }

    /**
     * 查询测试管理列表
     * 
     * @param sysInfoTest 测试管理
     * @return 测试管理
     */
    @Override
    public List<SysInfoTest> selectSysInfoTestList(SysInfoTest sysInfoTest)
    {
        return sysInfoTestMapper.selectSysInfoTestList(sysInfoTest);
    }

    /**
     * 新增测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSysInfoTest(SysInfoTest sysInfoTest)
    {
        int rows = sysInfoTestMapper.insertSysInfoTest(sysInfoTest);
        insertSysInfoChild(sysInfoTest);
        return rows;
    }

    /**
     * 修改测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSysInfoTest(SysInfoTest sysInfoTest)
    {
        sysInfoTestMapper.deleteSysInfoChildByInfoId(sysInfoTest.getId());
        insertSysInfoChild(sysInfoTest);
        return sysInfoTestMapper.updateSysInfoTest(sysInfoTest);
    }

    /**
     * 批量删除测试管理
     * 
     * @param ids 需要删除的测试管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSysInfoTestByIds(Integer[] ids)
    {
        sysInfoTestMapper.deleteSysInfoChildByInfoIds(ids);
        return sysInfoTestMapper.deleteSysInfoTestByIds(ids);
    }

    /**
     * 删除测试管理信息
     * 
     * @param id 测试管理主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSysInfoTestById(Integer id)
    {
        sysInfoTestMapper.deleteSysInfoChildByInfoId(id);
        return sysInfoTestMapper.deleteSysInfoTestById(id);
    }

    /**
     * 新增测试子信息
     * 
     * @param sysInfoTest 测试管理对象
     */
    public void insertSysInfoChild(SysInfoTest sysInfoTest)
    {
        List<SysInfoChild> sysInfoChildList = sysInfoTest.getSysInfoChildList();
        Integer id = sysInfoTest.getId();
        if (StringUtils.isNotNull(sysInfoChildList))
        {
            List<SysInfoChild> list = new ArrayList<SysInfoChild>();
            for (SysInfoChild sysInfoChild : sysInfoChildList)
            {
                sysInfoChild.setInfoId(id);
                list.add(sysInfoChild);
            }
            if (list.size() > 0)
            {
                sysInfoTestMapper.batchSysInfoChild(list);
            }
        }
    }
}
