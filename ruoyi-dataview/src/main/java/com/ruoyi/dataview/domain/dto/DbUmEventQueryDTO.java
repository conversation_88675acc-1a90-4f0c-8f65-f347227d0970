package com.ruoyi.dataview.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 运管服案件案件数据对象 db_um_event
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbUmEventQueryDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 流程实例ID */
    private Long procInstId;

    /** 立案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date regTime;

    /** 结案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date closeTime;

    /** 受理号 */
    private Long acceptId;

    /** 案件号 */
    private String caseCode;

    /** 案件来源（取字典表） */
    private String caseSource;

    /** 案件类别（选分级分类id） */
    private String caseClassId;

    /** 问题类别名称 */
    private String caseClassName;

    /** 立案标准ID（立案标准和结案标准拼接） */
    private Long standardId;

    /** 立案标准 */
    private String regCaseStandard;

    /** 结案标准 */
    private String closeCaseStandard;

    /** 处理时限 */
    private BigDecimal processLimit;

    /** 时限类型（0：小时 1：工作日） */
    private String limitType;

    /** 核实时限（默认单位小时 默认值0） */
    private BigDecimal verifyLimit;

    /** 核查时限（默认单位小时 默认值0） */
    private BigDecimal inspectLimit;

    /** 案件标题 */
    private String caseTitle;

    /** 事发位置 */
    private String position;

    /** 案件描述 */
    private String questionDesc;

    /** 所属区域 */
    private String areaCode;

    /** 所属区域名称 */
    private String areaName;

    /** 所在区域 */
    private Long districtId;

    /** 所在街道 */
    private Long streetId;

    /** 所在社区 */
    private Long communityId;

    /** 自定义网格 */
    private String manageGridId;

    /** 所在工作网格 */
    private String workGridId;

    /** 所在单元网格 */
    private String gridId;

    /** GEO X轴坐标 */
    private BigDecimal geoX;

    /** GEO Y轴坐标 */
    private BigDecimal geoY;

    /** 举报人 */
    private String reporter;

    /** 是否需要回复 */
    private String isReceipt;

    /** 回复方式 */
    private String replayWay;

    /** 回复号码或者邮件等 */
    private String replyWay;

    /** 来电号码 */
    private String telNum;

    /** 上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /** 案件状态，默认是'0'(-1作废,0 未结案,1 已结案 2已立案  -5结束，不予结案) */
    private String caseStatus;

    /** 是否参与评价 */
    private String isAppraise;

    /** 是否是优质案件 */
    private String isCaseQuality;

    /** 关键字 */
    private String keyWord;

    /** 案件等级(0：一般；1：重要；2：重大) */
    private String caseLevel;

    /** 采集权重 */
    private BigDecimal cjWeight;

    /** 处置权重 */
    private BigDecimal czWeight;

    /** 最后一次处置单位ID */
    private Long lastProdeptId;

    /** 最后一次处置单位意见 */
    private String lastProdeptOpinion;

    /** 处置核查状态（0未读 1已读 2处置中 3核查通过 4核查不通过   默认 0） */
    private String prodeptStatus;

    /** 是否再受理件（ 0 否 1是）默认0 */
    private String isReaccept;

    /** 案件结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 0.本系统自身的案件，1.接口表导入的案件 */
    private String caseFrom;

    /** 受理对象(是否市级立案)：2、市级，3、区级，4、街镇 */
    private String reciveObject;

    /** 地图展示类型(未结案、已结案、已超时) */
    private String mapshowType;

    /** 流程定义ID */
    private String procDefId;

    /** 派遣次数 */
    private Integer casePqCount;

    /** 排序号 */
    private Integer orderNum;

    /** 数据状态（0：删除  ； 1 正常）默认1 */
    private String dbStatus;

    /** 创建用户id */
    private Long createId;

    /** 修改用户id */
    private Long updateId;

    /** 允许申请延时 （0 不允许 1 允许） */
    private String isDelayed;

    /** 允许申请缓办（0 不允许 1 允许） */
    private String isPostpone;

    /** 是否必须上传附件（0否 1 是） */
    private String isAttach;

    /** 是否需要核查（0否1是） */
    private String isCheck;

    /** 是否确权案件，0：否；1：是 */
    private String isConfirm;

    /** 执法线索ID */
    private String lawId;

    /** 智能视频上报摄像头编码 */
    private String cameraIndexCode;

    /** 第三方案件ID */
    private String extId;

    /** 第三方案件CODE */
    private String extCode;

    /** 珠海市平台案件id */
    private String cityZhExtId;

    /** 珠海市平台案件案件号 */
    private String cityZhExtTitle;

    /** 珠海市平台案件当前办理流程id */
    private String cityZhExtCflowId;

    /** 市区对接案件标识 */
    private Integer recid;

    /** 是否疑难案件，0：否；1：是 */
    private String isDifficult;

    /** 疑难案件标注 */
    private String difficultSign;

    /** 部件ID */
    private String whId;

    /** 部件状态(完好、破损、丢失、废弃、移除) */
    private String whState;

    /** 行业ID */
    private Long classIndustryId;

    /** 行业名称 */
    private String classIndustryName;

    /** 案件类型ID */
    private Long classTypeId;

    /** 案件类型名称 */
    private String classTypeName;

    /** 大类ID */
    private Long classBigId;

    /** 大类名称 */
    private String classBigName;

    /** 小类ID */
    private Long classSubId;

    /** 小类名称 */
    private String classSubName;

    /** 当前环节名称 */
    private String curActName;

    /** 当前环节ID */
    private Long curActId;

    /** 接受对象类型 */
    private Long receiveTypeId;

    /** 接受对象id */
    private Long receiveId;

    /** 截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shouldFinishedTime;

    /** 快到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fastExpirationTime;

    /** 最后一次处置单位名称 */
    private String lastProdeptName;

    /** 延期标记 */
    private Integer delayFlag;

    /** 延期申请标记 */
    private Integer delayApplyFlag;

    /** 回退标记 */
    private Integer rollbackFlag;

    /** 回退申请标记 */
    private Integer rollbackApplyFlag;

    /** 督办标记 */
    private Integer superviseFlag;

    /** 催办标记 */
    private Integer urgeFlag;

    /** 撤件标记 */
    private Integer revokeFlag;

    /** 缓办标记 */
    private Integer postponeFlag;

    /** 挂账标记 */
    private Integer onaccountFlag;

    /** 疑难案件标记 */
    private Integer puzzleFlag;

    /** 疑难环节ID */
    private Long puzzleActId;

    /** 来件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date curArrivalTime;

    /** 当前环节签收人id */
    private Long curAssigneeId;

    /** 分区ID,目前根据结束状态分区，未结束案件分配到id=10000分区，结束案件进一步按年分区 */
    private Integer partitionId;

    /** 当前环节定义ID */
    private String curActDefId;

    /** 缩略图 */
    private String attachThumbnail;

    /** 接收对象名称 */
    private String receiveName;

    /** 上一环节ID */
    private Long fromActId;

    /** 上一环节定义ID */
    private String fromActDefId;

    /** 上一环节跳转类型 0:正常 1：回退 2：撤件 3监察召回 */
    private String fromActJumpType;

    /** 地图点图片路径 */
    private String mapAttachPath;

    /** 最后一次专业部门完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastProdeptCompleteTime;

    /** 最后一次专业部门应完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastProdeptShouldFinishedTime;

    /** 第一次处置单位ID */
    private Long firstProdeptId;

    /** 第一次处置单位名 */
    private String firstProdeptName;

    /** 作废理由 */
    private String invalidCause;

    /** 派遣对象(3 区、4街道) */
    private String dispatchObject;

    /** 高德X轴坐标 */
    private BigDecimal gdX;

    /** 高德Y轴坐标 */
    private BigDecimal gdY;

    /** 运管服案件流程流转记录信息 */
    private List<DbBpmNodeJump> dbBpmNodeJumpList;

}
