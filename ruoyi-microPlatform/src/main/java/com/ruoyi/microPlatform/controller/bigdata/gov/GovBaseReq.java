package com.ruoyi.microPlatform.controller.bigdata.gov;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * DoubleMaiBaseReq类用于封装虚拟集群基础请求信息。
 * 该类实现了Serializable接口，使得对象可以被序列化，便于在网络传输中使用。
 */
@Getter
@Setter
public class GovBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;
    private String token;

    /**
     * {"controls":[],"custom":"epointjweb4.0@-jDrZnbcevX_pdOZj6uRdPtw9Uk=@MTc1NzUxMzA3OA==","status":{"code":"200","text":"","url":"","state":"error"}}
     */
    private StatusDto status;
    private String custom;
    private List<String> controls;
    public static class StatusDto {
        private String code;
        private String text;
        private String url;
        private String state;
    }
}
