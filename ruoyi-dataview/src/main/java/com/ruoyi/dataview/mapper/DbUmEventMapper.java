package com.ruoyi.dataview.mapper;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbUmEvent;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;

/**
 * 运管服案件案件数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DbUmEventMapper 
{
    /**
     * 查询运管服案件案件数据
     * 
     * @param id 运管服案件案件数据主键
     * @return 运管服案件案件数据
     */
    public DbUmEvent selectDbUmEventById(Long id);

    /**
     * 校验运管服案件案件数据是否唯一
     *
     * @param dbUmEvent
     * @return 运管服案件案件数据
     */
    public DbUmEvent checkDbUmEventUnique(DbUmEvent dbUmEvent);

    /**
     * 查询运管服案件案件数据列表
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 运管服案件案件数据集合
     */
    public List<DbUmEvent> selectDbUmEventList(DbUmEvent dbUmEvent);

    /**
     * 新增运管服案件案件数据
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    public int insertDbUmEvent(DbUmEvent dbUmEvent);

    /**
     * 修改运管服案件案件数据
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    public int updateDbUmEvent(DbUmEvent dbUmEvent);

    /**
     * 删除运管服案件案件数据
     * 
     * @param id 运管服案件案件数据主键
     * @return 结果
     */
    public int deleteDbUmEventById(Long id);

    /**
     * 批量删除运管服案件案件数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbUmEventByIds(Long[] ids);

    /**
     * 批量删除运管服案件流程流转记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbBpmNodeJumpByProcInstIds(Long[] ids);
    
    /**
     * 批量新增运管服案件流程流转记录
     * 
     * @param dbBpmNodeJumpList 运管服案件流程流转记录列表
     * @return 结果
     */
    public int batchDbBpmNodeJump(List<DbBpmNodeJump> dbBpmNodeJumpList);
    

    /**
     * 通过运管服案件案件数据主键删除运管服案件流程流转记录信息
     * 
     * @param id 运管服案件案件数据ID
     * @return 结果
     */
    public int deleteDbBpmNodeJumpByProcInstId(Long id);


    List<CommonBaseCount> dbUmEventBigCount(String classTypeName);
}
