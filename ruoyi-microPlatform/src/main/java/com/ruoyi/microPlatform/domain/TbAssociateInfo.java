package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 行业协会商会信息对象 tb_associate_info
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class TbAssociateInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县", readConverterExp = "市=、区")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区")
    private String town;

    /** 登记级别( '无','市级','县区级') */
    @Excel(name = "登记级别( '无','市级','县区级')")
    private String level;

    /** 协会名称 */
    @Excel(name = "协会名称")
    private String name;

    /** 办公地址 */
    @Excel(name = "办公地址")
    private String address;

    /** 负责人 */
    @Excel(name = "负责人")
    private String director;

    /** 负责人id */
    @Excel(name = "负责人id")
    private Long directorId;

    /** 是否有协会商会的党组织挂靠在社区党组织（0否1是） */
    @Excel(name = "是否有协会商会的党组织挂靠在社区党组织", readConverterExp = "0=否1是")
    private String partyAttachment;

    /** 党组织名称 */
    @Excel(name = "党组织名称")
    private String partyName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setLevel(String level) 
    {
        this.level = level;
    }

    public String getLevel() 
    {
        return level;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setDirector(String director) 
    {
        this.director = director;
    }

    public String getDirector() 
    {
        return director;
    }
    public void setDirectorId(Long directorId) 
    {
        this.directorId = directorId;
    }

    public Long getDirectorId() 
    {
        return directorId;
    }
    public void setPartyAttachment(String partyAttachment) 
    {
        this.partyAttachment = partyAttachment;
    }

    public String getPartyAttachment() 
    {
        return partyAttachment;
    }
    public void setPartyName(String partyName) 
    {
        this.partyName = partyName;
    }

    public String getPartyName() 
    {
        return partyName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("level", getLevel())
            .append("name", getName())
            .append("address", getAddress())
            .append("director", getDirector())
            .append("directorId", getDirectorId())
            .append("partyAttachment", getPartyAttachment())
            .append("partyName", getPartyName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
