package com.ruoyi.system.task;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.entity.SysImgTrans;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ImgUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysImgTransMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/**
 * 系统定时任务
 */
@Component("systemTask")
public class systemTask {

    private static String defaultBaseDir = RuoYiConfig.getProfile();

    @Autowired
    private SysImgTransMapper imgTransMapper;
    @Autowired
    private SysUserMapper userMapper;

    public static void setDefaultBaseDir(String defaultBaseDir) {
        systemTask.defaultBaseDir = defaultBaseDir;
    }

    public static String getDefaultBaseDir() {
        return defaultBaseDir;
    }

    /**
     * 定时任务  在页面上添加
     * @throws IOException
     */
    public void setImgTrans() throws IOException {

        List<SysImgTrans> sysImgTrans = imgTransMapper.selectSysImgTransList(new SysImgTrans() {{
            setIsTrans(0);//没有被转换的
        }});
        for (int i = 0; i < sysImgTrans.size(); i++) {
            SysImgTrans img = new SysImgTrans();
            //修改透明度
            String white = ImgUtil.changeImgColor(getDefaultBaseDir() + img.getSrcImg().replace("/profile", ""), getDefaultBaseDir());//转换白底照片
//            String s = ImgUtil.localImgToAlpha(new File(getDefaultBaseDir() + white.replace("/profile", "")));
            img.setTargetImg(white);
            img.setIsTrans(1);
            imgTransMapper.updateSysImgTrans(img);
            System.err.println("=================================更新转换表========================================================");
            userMapper.updateUserSignature(img.getUserName(),white);
//            if (img.getUserName().equals(SecurityUtils.getUserId())){
//                LoginUser loginUser = SecurityUtils.getLoginUser();
//                loginUser.getUser().setSignature(white);
//                tokenService.setLoginUser(loginUser);
//            }
//            System.err.println("**********************************更新用户************************************************");
        }
    }
}
