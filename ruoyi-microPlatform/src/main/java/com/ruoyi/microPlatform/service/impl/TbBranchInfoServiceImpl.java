package com.ruoyi.microPlatform.service.impl;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.mapper.TbBranchGridInfoMapper;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.microPlatform.mapper.TbBranchInfoMapper;
import com.ruoyi.microPlatform.service.ITbBranchInfoService;

/**
 * 机关企事业单位（支部）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class TbBranchInfoServiceImpl implements ITbBranchInfoService 
{
    @Autowired
    private TbBranchInfoMapper tbBranchInfoMapper;

    @Autowired
    private TbBranchGridInfoMapper tbBranchGridInfoMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    /**
     * 查询机关企事业单位（支部）
     * 
     * @param id 机关企事业单位（支部）主键
     * @return 机关企事业单位（支部）
     */
    @Override
    public TbBranchInfo selectTbBranchInfoById(Long id)
    {
        return tbBranchInfoMapper.selectTbBranchInfoById(id);
    }

    /**
     * 校验机关企事业单位（支部）是否存在
     *
     * @param tbBranchInfo
     * @return boolean
     */
    @Override
    public boolean checkTbBranchInfo(TbBranchInfo tbBranchInfo){
        TbBranchInfo old = tbBranchInfoMapper.checkTbBranchInfoUnique(tbBranchInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询机关企事业单位（支部）列表
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 机关企事业单位（支部）
     */
    @Override
    public List<TbBranchInfo> selectTbBranchInfoList(TbBranchInfo tbBranchInfo)
    {
        return tbBranchInfoMapper.selectTbBranchInfoList(tbBranchInfo);
    }

    /**
     * 导入机关企事业单位（支部）
     *
     * @param infos       机关企事业单位（支部）列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbBranchInfo(List<TbBranchInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入机关企事业单位（支部）数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        // 手机号：1开头，第二位为3-9，总共11位
        // 座机号格式：支持 (区号)号码、区号-号码、纯号码
        Pattern phonePattern = Pattern.compile(
                "(?<!\\d)" +                   // 避免前面有数字干扰
                        "(" +
                        "1[3-9]\\d{9}" +           // 手机号码
                        "|" +
                        "(0\\d{2,3}[-\\s]?)?\\d{7,8}" + // 座机号码（带/不带区号）
                        ")" +
                        "(?!\\d)"                      // 避免尾部数字干扰
        );

        for (TbBranchInfo info : infos)
        {
            updateNum++;
            try
            {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getCounty())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getBranchName() + " 所属县（市、区） 不可为空"+ "。数据条目：" + updateNum);
                } else {
                    if (StringUtils.isBlank(info.getCountry())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getBranchName() + " 所属街道/乡镇 不可为空"+ "。数据条目：" + updateNum);
                    } else {
                        if (StringUtils.isBlank(info.getTown())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getBranchName() + " 所属村/社区 不可为空"+ "。数据条目：" + updateNum);
                        } else {
                            if (StringUtils.isBlank(info.getGridName())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据" + info.getBranchName() + "所属网格 不可为空"+ "。数据条目：" + updateNum);
                            } else {
                                boolean flag = true;
                                String msg = "";
                                if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getBranchName() + "支部数据"+ "。数据条目：" + updateNum);
                                }

                                if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getBranchName() + "支部数据"+ "。数据条目：" + updateNum);
                                }

                                if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getBranchName() + "支部数据"+ "。数据条目：" + updateNum);
                                }

                                if (!flag) {
                                    failureNum++;
                                    failureMsg.append(msg);
                                } else {
                                    SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                    if (ObjectUtil.isEmpty(dept)) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 支部数据所属部门未找到！"+ "数据条目：" + updateNum);
                                    } else {
                                        info.setDeptId(dept.getDeptId());
                                        TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                            setDeptId(dept.getDeptId());
                                            setGridName(info.getGridName());
                                        }});
                                        if (ObjectUtil.isEmpty(gridInfo)) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 支部数据所属网格未找到！"+ "数据条目：" + updateNum);
                                        } else {
                                            info.setGridId(gridInfo.getId());
                                            //进行数据新增
                                            //判断是否有多条数据
                                            List<String> branchNames = getBranchNames(info.getBranchName());
                                            List<Map<String, String>>  secretaryNames = getSecretaryNames(info.getSecretaryName(),phonePattern);
                                            if (ObjectUtil.isEmpty(branchNames) || ObjectUtil.isEmpty(secretaryNames)){
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据 缺少支部/书记 信息。"+"数据条目" +updateNum);
                                                continue;
                                            }
                                            // 3. 校验数量一致性
                                            if (branchNames.size() != secretaryNames.size()) {
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据  "+ "党支部数量(" + branchNames.size() + ")与书记信息数量(" + secretaryNames.size() + ")不匹配。请检查是否长度是否相等。" +"数据条目" +updateNum);
                                                continue;
                                            }
                                            int i = 0 ;
                                            for (Map<String, String> secretaryName : secretaryNames) {
                                                String name = secretaryName.get("name");
                                                String phone = secretaryName.get("phone");
                                                info.setBranchName(branchNames.get(i));
                                                info.setSecretaryName(name);
                                                info.setPhone(phone);
                                                // 验证是否存在这个数据
                                                TbBranchInfo old = tbBranchInfoMapper.checkTbBranchInfoUnique(info);
                                                if (ObjectUtil.isEmpty(old))
                                                {
                                                    handleNewBranch(info, operName);
                                                    successNum++;
                                                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功,"+"数据条目：" + updateNum);
                                                }
                                                else if (isUpdateSupport)
                                                {
                                                    handleUpdateBranch(old, info, operName);
                                                    successNum++;
                                                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功,"+"数据条目：" + updateNum);
                                                }
                                                else
                                                {
                                                    //handleExistingBranch(info, operName);
                                                    info.setId(old.getId());
                                                    TbBranchGridInfo existGrid = tbBranchGridInfoMapper.checkTbBranchGridInfoUnique(buildGridQuery(info.getDeptId(), info.getId(), info.getGridId()));
                                                    if (ObjectUtil.isEmpty(existGrid)) {
                                                        insertOrUpdateGridInfo(info, null, operName);
                                                    }else {
                                                        failureNum++;
                                                        failureMsg.append("<br/>" + failureNum + "、数据支部关联网格 " + existGrid.getId() + " 已存在,"+"数据条目：" + updateNum);
                                                    }
                                                }
                                                i++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }


    /**
     * 截取
     */
    public List<String> getBranchNames(String branchName) {
        // 1. 拆分党支部列表
        List<String> branchNames = Arrays.stream(branchName.split("\\n"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
        return branchNames;
    }
    public List<Map<String, String>> getSecretaryNames(String branchName,Pattern phonePattern) {
        // 2. 拆分书记信息
        List<Map<String, String>> result = new ArrayList<>();
        // 优化后的电话号码正则：严格匹配11位且第二位为3-9

        Matcher matcher = phonePattern.matcher(branchName);
        int lastEnd = 0;

        while (matcher.find()) {
            String phone = matcher.group();
            int phoneStart = matcher.start();

            // 确保不会包含前一个电话号码的尾随数字
            String namePart = branchName.substring(lastEnd, phoneStart);
            String name = processName(namePart);

            // 添加到结果列表
            addEntry(result, name, phone);
            lastEnd = matcher.end();
        }
        return result;
    }
    private void addEntry(List<Map<String, String>> result, String name, String phone) {
        Map<String, String> entry = new HashMap<>();
        entry.put("name", name);
        entry.put("phone", phone);
        result.add(entry);
    }

    private String processName(String raw) {
        if (raw == null) return "";


        return raw
                .replaceAll("（[^）]*）", "")               // 清除中文括号内容
                .replaceAll("\\([^)]*\\)", "")             // 清除英文括号内容
                .replaceAll("^[、,，\\s\\t\\n\\r\\f]+", "") // ✅ 去掉前缀无关字符
                .replaceAll("[：:，、\\s\\t\\n\\r\\f]+$", "") // 去掉结尾干扰字符
                .trim();
    }


    /** 处理新增分支 */
    private void handleNewBranch(TbBranchInfo info, String operName) {
        info.setCreateTime(new Date());
        info.setCreateBy(operName);
        tbBranchInfoMapper.insertTbBranchInfo(info);
        insertOrUpdateGridInfo(info, null, operName);
    }




    /** 处理更新分支 */
    private void handleUpdateBranch(TbBranchInfo oldBranch, TbBranchInfo newInfo, String operName) {
        newInfo.setUpdateBy(operName);
        newInfo.setUpdateTime(new Date());
        newInfo.setId(oldBranch.getId());
        tbBranchInfoMapper.updateTbBranchInfo(newInfo);

        // 关联网格处理
        TbBranchGridInfo existGrid = tbBranchGridInfoMapper.checkTbBranchGridInfoUnique(buildGridQuery(newInfo.getDeptId(), newInfo.getDeptId(), newInfo.getGridId()));

        insertOrUpdateGridInfo(newInfo, existGrid, operName);
    }

    /** 处理已存在分支 */
    private void handleExistingBranch(TbBranchInfo info, String operName) {
        TbBranchGridInfo existGrid = tbBranchGridInfoMapper.checkTbBranchGridInfoUnique(buildGridQuery(info.getDeptId(), info.getId(), info.getGridId()));
        if (ObjectUtil.isEmpty(existGrid)) {
            insertOrUpdateGridInfo(info, null, operName);
        }
    }

    /** 构建网格查询条件 */
    private TbBranchGridInfo buildGridQuery(Long deptId,Long branchId, Long gridId) {
        TbBranchGridInfo tbBranchGridInfo = new TbBranchGridInfo();
        tbBranchGridInfo.setBranchId(deptId);
        tbBranchGridInfo.setBranchId(branchId);
        tbBranchGridInfo.setGridId(gridId);
        return tbBranchGridInfo;
    }

    /** 统一处理网格数据插入/更新 */
    private void insertOrUpdateGridInfo(TbBranchInfo branchInfo, TbBranchGridInfo existGrid, String operName) {
        TbBranchGridInfo gridInfo = buildBranchGridInfo(branchInfo, operName);

        if (ObjectUtil.isEmpty(existGrid)) {
            tbBranchGridInfoMapper.insertTbBranchGridInfo(gridInfo);
        } else {
            gridInfo.setId(existGrid.getId());
            tbBranchGridInfoMapper.updateTbBranchGridInfo(gridInfo);
        }
    }

    /** 构建网格对象（修复双括号初始化问题） */
    private TbBranchGridInfo buildBranchGridInfo(TbBranchInfo branchInfo, String operName) {
        TbBranchGridInfo tbBranchGridInfo = new TbBranchGridInfo();
            tbBranchGridInfo.setBranchId(branchInfo.getId());
            tbBranchGridInfo.setGridId(branchInfo.getGridId());
            tbBranchGridInfo.setDeptId(branchInfo.getDeptId());
            tbBranchGridInfo.setCounty(branchInfo.getCounty());
            tbBranchGridInfo.setCountry(branchInfo.getCountry());
            tbBranchGridInfo.setTown(branchInfo.getTown());
            tbBranchGridInfo.setGridName(branchInfo.getGridName());
            tbBranchGridInfo.setCreateBy(operName);
            tbBranchGridInfo.setCreateTime(new Date());
            tbBranchGridInfo.setUpdateBy(operName);
            tbBranchGridInfo.setUpdateTime(new Date());
        return tbBranchGridInfo;
    }


    /**
     * 新增机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTbBranchInfo(TbBranchInfo tbBranchInfo)
    {
        tbBranchInfo.setCreateTime(DateUtils.getNowDate());
        int rows = tbBranchInfoMapper.insertTbBranchInfo(tbBranchInfo);
        //insertTbBranchGridInfo(tbBranchInfo);
        return rows;
    }

    /**
     * 修改机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTbBranchInfo(TbBranchInfo tbBranchInfo)
    {
        tbBranchInfo.setUpdateTime(DateUtils.getNowDate());
        //tbBranchInfoMapper.deleteTbBranchGridInfoByBranchId(tbBranchInfo.getId());
        //insertTbBranchGridInfo(tbBranchInfo);
        return tbBranchInfoMapper.updateTbBranchInfo(tbBranchInfo);
    }

    /**
     * 批量删除机关企事业单位（支部）
     * 
     * @param ids 需要删除的机关企事业单位（支部）主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbBranchInfoByIds(Long[] ids)
    {
        tbBranchInfoMapper.deleteTbBranchGridInfoByBranchIds(ids);
        return tbBranchInfoMapper.deleteTbBranchInfoByIds(ids);
    }

    /**
     * 删除机关企事业单位（支部）信息
     * 
     * @param tbBranchGridInfo 机关企事业单位（支部）主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbBranchInfoById(TbBranchGridInfo tbBranchGridInfo)
    {
        int i = tbBranchInfoMapper.deleteTbBranchGridInfoByBranchId(tbBranchGridInfo);

        List<TbBranchGridInfo> tbBranchGridInfos = tbBranchGridInfoMapper.selectTbBranchGridInfoList(new TbBranchGridInfo() {{
            setBranchId(tbBranchGridInfo.getBranchId());
        }});
        if (ObjectUtil.isEmpty(tbBranchGridInfos)){
            tbBranchInfoMapper.deleteTbBranchInfoById(tbBranchGridInfo.getBranchId());
        }


        return i;
    }

    /**
     * 新增支部关联网格信息
     * 
     * @param tbBranchInfo 机关企事业单位（支部）对象
     */
    public void insertTbBranchGridInfo(TbBranchInfo tbBranchInfo)
    {
        List<TbBranchGridInfo> tbBranchGridInfoList = tbBranchInfo.getTbBranchGridInfoList();
        Long id = tbBranchInfo.getId();
        if (ObjectUtil.isNotEmpty(tbBranchGridInfoList))
        {
            List<TbBranchGridInfo> list = new ArrayList<TbBranchGridInfo>();
            for (TbBranchGridInfo tbBranchGridInfo : tbBranchGridInfoList)
            {
                tbBranchGridInfo.setBranchId(id);
                list.add(tbBranchGridInfo);
            }
            if (list.size() > 0)
            {
                tbBranchInfoMapper.batchTbBranchGridInfo(list);
            }
        }
    }

    @Override
    public List<TbGridInfo> selectTbBranchInfoListByGrid(TbBranchInfo tbBranchInfo, List<TbGridInfo> gridInfos) {
        for (TbGridInfo gridInfo : gridInfos) {
            //查询当前网格下的支部数据
            List<TbBranchInfo> list = tbBranchInfoMapper.selectTbBranchInfoListByGrid(new TbBranchInfo() {{
                    setGridId(gridInfo.getId());
            }});
            gridInfo.setTbBranchInfoList(list);
        }
        return gridInfos;
    }
}
