package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;
import com.ruoyi.microPlatform.service.ITbGridInfoService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbSidelineGridUser;
import com.ruoyi.microPlatform.service.ITbSidelineGridUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 兼职网格员信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/microPlatform/sidelineGridUser")
public class TbSidelineGridUserController extends BaseController
{
    @Autowired
    private ITbSidelineGridUserService tbSidelineGridUserService;
    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ITbGridInfoService tbGridInfoService;
    /**
     * 查询兼职网格员信息列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbSidelineGridUser tbSidelineGridUser)
    {
        startPage();
        List<TbSidelineGridUser> list = tbSidelineGridUserService.selectTbSidelineGridUserList(tbSidelineGridUser);
        return getDataTable(list);
    }

    /**
     * 查询兼职网格员信息列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:list')")
    @GetMapping("/listByGrid")
    public TableDataInfo listByGrid(TbSidelineGridUser tbSidelineGridUser)
    {
        startPage();
        TbGridInfo tbGridInfo = new TbGridInfo();
        tbGridInfo.setDeptId(tbSidelineGridUser.getDeptId());
        tbGridInfo.setId(tbSidelineGridUser.getGridId());
        handle(tbGridInfo);
        tbGridInfo.setType(tbSidelineGridUser.getType());
        tbGridInfo.setName(tbSidelineGridUser.getName());
        tbGridInfo.setPhone(tbSidelineGridUser.getPhone());
        tbGridInfo.setIdCard(tbSidelineGridUser.getIdCard());
        List<TbGridInfo> gridInfos = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        List<TbGridInfo> list = tbSidelineGridUserService.selectTbSidelineGridUserlistByGrid(tbSidelineGridUser,gridInfos);
        return getDataTable3(list, gridInfos);
    }

    public void handle(TbGridInfo tbGridInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbGridInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(tbGridInfo.getDeptId())) {
                    tbGridInfo.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出兼职网格员信息列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:export')")
    @Log(title = "兼职网格员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbSidelineGridUser tbSidelineGridUser)
    {
        TbGridInfo tbGridInfo = new TbGridInfo();
        List<TbGridInfo> gridInfos = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        List<TbSidelineGridUser> list = tbSidelineGridUserService.selectTbSidelineGridUserList(tbSidelineGridUser);
        ExcelUtil<TbSidelineGridUser> util = new ExcelUtil<TbSidelineGridUser>(TbSidelineGridUser.class);
        util.exportExcel(response, list, "兼职网格员信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbSidelineGridUser tbSidelineGridUser) {
        if (ObjectUtil.isNotEmpty(tbSidelineGridUser.getIsExportAll()) && tbSidelineGridUser.getIsExportAll() == 0) {
            startPage();
        }
        Integer integer = tbSidelineGridUserService.selectTbSidelineGridUserCount(tbSidelineGridUser);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbSidelineGridUser);
    }

    @Log(title = "兼职网格员信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbSidelineGridUser> util = new ExcelUtil<TbSidelineGridUser>(TbSidelineGridUser.class);
        List<TbSidelineGridUser> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbSidelineGridUserService.importTbSidelineGridUser(infos, updateSupport, operName,userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbSidelineGridUser> util = new ExcelUtil<TbSidelineGridUser>(TbSidelineGridUser.class);
        util.exportTemplateExcel(response, "兼职网格员信息数据");
    }

    /**
     * 获取兼职网格员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbSidelineGridUserService.selectTbSidelineGridUserById(id));
    }

    /**
     * 新增兼职网格员信息
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:add')")
    @Log(title = "兼职网格员信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbSidelineGridUser tbSidelineGridUser)
    {
        tbSidelineGridUser.setCreateBy(getUsername());
        if (tbSidelineGridUserService.checkTbSidelineGridUser(tbSidelineGridUser)){
            return error("数据已存在！");
        }
        return toAjax(tbSidelineGridUserService.insertTbSidelineGridUser(tbSidelineGridUser));
    }

    /**
     * 修改兼职网格员信息
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:edit')")
    @Log(title = "兼职网格员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbSidelineGridUser tbSidelineGridUser)
    {
        tbSidelineGridUser.setUpdateBy(getUsername());
        if (tbSidelineGridUserService.checkTbSidelineGridUser(tbSidelineGridUser)){
            return error("数据已存在！");
        }
        return toAjax(tbSidelineGridUserService.updateTbSidelineGridUser(tbSidelineGridUser));
    }

    /**
     * 删除兼职网格员信息
     */
    /*@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:remove')")
    @Log(title = "兼职网格员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbSidelineGridUserService.deleteTbSidelineGridUserByIds(ids));
    }*/


    /**
     * 删除兼职网格员信息
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridUser:remove')")
    @Log(title = "兼职网格员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    @RepeatSubmit
    public AjaxResult remove(TbSidelineGridInfo tbSidelineGridInfo)
    {
        return toAjax(tbSidelineGridUserService.deleteTbSidelineGridUserById(tbSidelineGridInfo));
    }

    

}
