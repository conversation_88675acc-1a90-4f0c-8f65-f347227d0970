package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbSidelineGridInfoMapper;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;
import com.ruoyi.microPlatform.service.ITbSidelineGridInfoService;

/**
 * 兼职网格员所属网格信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class TbSidelineGridInfoServiceImpl implements ITbSidelineGridInfoService 
{
    @Autowired
    private TbSidelineGridInfoMapper tbSidelineGridInfoMapper;

    /**
     * 查询兼职网格员所属网格信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 兼职网格员所属网格信息
     */
    @Override
    public TbSidelineGridInfo selectTbSidelineGridInfoById(Long id)
    {
        return tbSidelineGridInfoMapper.selectTbSidelineGridInfoById(id);
    }

    /**
     * 校验兼职网格员所属网格信息是否存在
     *
     * @param tbSidelineGridInfo
     * @return boolean
     */
    @Override
    public boolean checkTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo){
        TbSidelineGridInfo old = tbSidelineGridInfoMapper.checkTbSidelineGridInfoUnique(tbSidelineGridInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询兼职网格员所属网格信息列表
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 兼职网格员所属网格信息
     */
    @Override
    public List<TbSidelineGridInfo> selectTbSidelineGridInfoList(TbSidelineGridInfo tbSidelineGridInfo)
    {
        return tbSidelineGridInfoMapper.selectTbSidelineGridInfoList(tbSidelineGridInfo);
    }

    /**
     * 导入兼职网格员所属网格信息
     *
     * @param infos       兼职网格员所属网格信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSidelineGridInfo(List<TbSidelineGridInfo> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入兼职网格员所属网格信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbSidelineGridInfo info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbSidelineGridInfo old = tbSidelineGridInfoMapper.checkTbSidelineGridInfoUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbSidelineGridInfoMapper.insertTbSidelineGridInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbSidelineGridInfoMapper.updateTbSidelineGridInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    @Override
    public int insertTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo)
    {
        return tbSidelineGridInfoMapper.insertTbSidelineGridInfo(tbSidelineGridInfo);
    }

    /**
     * 修改兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    @Override
    public int updateTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo)
    {
        return tbSidelineGridInfoMapper.updateTbSidelineGridInfo(tbSidelineGridInfo);
    }

    /**
     * 批量删除兼职网格员所属网格信息
     * 
     * @param ids 需要删除的兼职网格员所属网格信息主键
     * @return 结果
     */
    @Override
    public int deleteTbSidelineGridInfoByIds(Long[] ids)
    {
        return tbSidelineGridInfoMapper.deleteTbSidelineGridInfoByIds(ids);
    }

    /**
     * 删除兼职网格员所属网格信息信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 结果
     */
    @Override
    public int deleteTbSidelineGridInfoById(Long id)
    {
        return tbSidelineGridInfoMapper.deleteTbSidelineGridInfoById(id);
    }
}
