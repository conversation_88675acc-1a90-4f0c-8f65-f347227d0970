package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 社区功能室服务预约对象 se_function_reservation
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public class SeFunctionReservation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    private String gridName;

    /** 功能室id */
    private Long functionId;

    /** 功能室名称 */
    @Excel(name = "功能室名称")
    private String functionName;

    /** 预约人账户 */
    private Long userId;

    /** 预约人 */
    @Excel(name = "预约人")
    private String nickName;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String phone;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预约时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerTime;

    /** 预约使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预约使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reserveUseStart;


    /** 预约截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预约截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reserveUseEnd;

    /** 数据状态（0审批 1已完成） */
    @Excel(name = "数据状态", readConverterExp = "0=待审批,1=已审批")
    private Integer status;

    /** 审批人 */
    @Excel(name = "审批人")
    private String auditUser;

    /** 审批时间 */
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审批结果（1通过 0不通过） */
    @Excel(name = "审批结果", readConverterExp = "1=通过,0=不通过")
    private Integer auditResult;

    /** 回复意见 */
    @Excel(name = "回复意见")
    private String auditReason;
    /**
     * 提报人看消息情况
     */
    private Integer msg;


    //时间条件 日day 周week 月month
    private String time;

    /** 搜索开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 搜索结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getMsg() {
        return msg;
    }

    public void setMsg(Integer msg) {
        this.msg = msg;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }
    public void setCounty(String county)
    {
        this.county = county;
    }

    public String getCounty()
    {
        return county;
    }
    public void setCountry(String country)
    {
        this.country = country;
    }

    public String getCountry()
    {
        return country;
    }
    public void setTown(String town)
    {
        this.town = town;
    }

    public String getTown()
    {
        return town;
    }
    public void setGridName(String gridName)
    {
        this.gridName = gridName;
    }

    public String getGridName()
    {
        return gridName;
    }
    public void setFunctionId(Long functionId)
    {
        this.functionId = functionId;
    }

    public Long getFunctionId()
    {
        return functionId;
    }
    public void setFunctionName(String functionName)
    {
        this.functionName = functionName;
    }

    public String getFunctionName()
    {
        return functionName;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    public String getNickName()
    {
        return nickName;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setReserveUseStart(Date reserveUseStart)
    {
        this.reserveUseStart = reserveUseStart;
    }

    public Date getReserveUseStart()
    {
        return reserveUseStart;
    }
    public void setReserveUseEnd(Date reserveUseEnd)
    {
        this.reserveUseEnd = reserveUseEnd;
    }

    public Date getReserveUseEnd()
    {
        return reserveUseEnd;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setAuditUser(String auditUser)
    {
        this.auditUser = auditUser;
    }

    public String getAuditUser()
    {
        return auditUser;
    }
    public void setAuditTime(Date auditTime)
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime()
    {
        return auditTime;
    }
    public void setAuditResult(Integer auditResult)
    {
        this.auditResult = auditResult;
    }

    public Integer getAuditResult()
    {
        return auditResult;
    }
    public void setAuditReason(String auditReason)
    {
        this.auditReason = auditReason;
    }

    public String getAuditReason()
    {
        return auditReason;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deptId", getDeptId())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("gridName", getGridName())
                .append("functionId", getFunctionId())
                .append("functionName", getFunctionName())
                .append("userId", getUserId())
                .append("nickName", getNickName())
                .append("phone", getPhone())
                .append("createTime", getCreateTime())
                .append("reserveUseStart", getReserveUseStart())
                .append("reserveUseEnd", getReserveUseEnd())
                .append("remark", getRemark())
                .append("status", getStatus())
                .append("auditUser", getAuditUser())
                .append("auditTime", getAuditTime())
                .append("auditResult", getAuditResult())
                .append("auditReason", getAuditReason())
                .toString();
    }
}
