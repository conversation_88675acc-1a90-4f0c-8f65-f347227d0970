package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbDeptUser;

/**
 * 社区干部信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbDeptUserMapper 
{
    /**
     * 查询社区干部信息
     * 
     * @param id 社区干部信息主键
     * @return 社区干部信息
     */
    public TbDeptUser selectTbDeptUserById(Long id);

    /**
     * 校验社区干部信息是否唯一
     *
     * @param tbDeptUser
     * @return 社区干部信息
     */
    public TbDeptUser checkTbDeptUserUnique(TbDeptUser tbDeptUser);

    /**
     * 查询社区干部信息列表
     * 
     * @param tbDeptUser 社区干部信息
     * @return 社区干部信息集合
     */
    public List<TbDeptUser> selectTbDeptUserList(TbDeptUser tbDeptUser);

    public List<Long> selectExportDeptUserCount(TbDeptUser tbDeptUser);

    /**
     * 新增社区干部信息
     * 
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    public int insertTbDeptUser(TbDeptUser tbDeptUser);

    /**
     * 修改社区干部信息
     * 
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    public int updateTbDeptUser(TbDeptUser tbDeptUser);

    /**
     * 删除社区干部信息
     * 
     * @param id 社区干部信息主键
     * @return 结果
     */
    public int deleteTbDeptUserById(Long id);

    /**
     * 批量删除社区干部信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbDeptUserByIds(Long[] ids);

    List<TbDeptUser> getDeptUserPostType(LargeScreenInfo largeScreenInfo);


    Integer partyMemberCount();

    List<CommonBaseCount> partyMemberGroupSexCount();

    // Secretary team statistics
    List<CommonBaseCount> secretaryGroupSexCount();

    List<CommonBaseCount> secretaryGroupPartyCount();

    List<CommonBaseCount> secretaryGroupAgeCount();

    List<CommonBaseCount> secretaryGroupEducationCount();
}
