<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.XuLinMatterMapper">

    <!-- 获取居民吹哨统计数据 -->
    <select id="countResidentWhistleStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN status = '已办结' THEN 1 ELSE 0 END) AS processed,
            SUM(CASE WHEN status IN ('待处理', '处理中') THEN 1 ELSE 0 END) AS pending
        FROM se_public_sentiment
        WHERE 1=1
        <if test="bigdataParam.year != null">
            AND YEAR(create_time) = #{bigdataParam.year}
        </if>
    </select>

    <!-- 获取网格巡查问题统计数据 -->
    <select id="countGridInspectionStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN event_status = 1 THEN 1 ELSE 0 END) AS processed,
            SUM(CASE WHEN event_status = 0 THEN 1 ELSE 0 END) AS pending
        FROM biz_general_matter
        WHERE event_source = 1
        <if test="bigdataParam.year != null">
            AND YEAR(create_time) = #{bigdataParam.year}
        </if>
    </select>

    <!-- 获取住户走访问题统计数据 -->
    <select id="countHouseholdVisitStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN event_status = 1 THEN 1 ELSE 0 END) AS processed,
            SUM(CASE WHEN event_status = 0 THEN 1 ELSE 0 END) AS pending
        FROM biz_general_matter
        WHERE event_source = 3
        <if test="bigdataParam.year != null">
            AND YEAR(create_time) = #{bigdataParam.year}
        </if>
    </select>

    <!-- 获取门店巡查问题统计数据 -->
    <select id="countStoreInspectionStats" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN event_status = 1 THEN 1 ELSE 0 END) AS processed,
            SUM(CASE WHEN event_status = 0 THEN 1 ELSE 0 END) AS pending
        FROM biz_general_matter
        WHERE event_source = 2
        <if test="bigdataParam.year != null">
            AND YEAR(create_time) = #{bigdataParam.year}
        </if>
    </select>

</mapper>
