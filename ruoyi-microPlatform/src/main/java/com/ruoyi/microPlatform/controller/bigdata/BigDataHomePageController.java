package com.ruoyi.microPlatform.controller.bigdata;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.BizDailySubmission;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.IBizDailySubmissionService;
import com.ruoyi.microPlatform.service.ILargeScreenService;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页 大数据相关api
 */
@RestController
@RequestMapping("/bigData/homePage")
public class BigDataHomePageController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BigDataHomePageController.class);

    @Resource
    private IEventStatisticsMainService eventStatisticsMainService;

    @Resource
    private IEventStatisticsSlaveService eventStatisticsSlaveService;

    /**
     * 事项统计
     * 这些是所有的数据来源
     * 事项总数 和分类（综合事项（biz_comprehensive_event） + db_um_event（运管服） + db_hotline_detail（12345热线） + db_enterprise_appeal（万人助企） + db_emergency_event（应急局事项） + 市长信箱 +db_petition_info（信访）+ + major_opinion_events（舆情事项））
     *
     * db_um_event（运管服） + db_hotline_detail（12345热线） + db_enterprise_appeal（万人助企） + db_emergency_event（应急局事项） + 市长信箱 +db_petition_info（信访）
     * @DataSource(DataSourceType.SLAVE)
     *
     * 统计： 当日新增事项数 、待办事项数、 累计办理事项数 、办结率 、满意度
     */
    @GetMapping("/getEventStatistics")
    public AjaxResult getEventStatistics(BigdataParam bigdataParam) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取主库统计数据（综合事项 + 舆情事项）
            Map<String, Object> mainStats = eventStatisticsMainService.getMainEventStatistics(bigdataParam);

            // 转换参数类型用于从库查询
            DbBigdataParam dbBigdataParam = new DbBigdataParam();
            dbBigdataParam.setDeptId(bigdataParam.getDeptId());
            dbBigdataParam.setCounty(bigdataParam.getCounty());
            dbBigdataParam.setYear(bigdataParam.getYear());
            dbBigdataParam.setStartTimeLong(bigdataParam.getStartTimeLong());
            dbBigdataParam.setEndTimeLong(bigdataParam.getEndTimeLong());

            // 获取从库统计数据（运管服 + 12345热线 + 万人助企 + 应急事项 + 市长信箱 + 信访）
            Map<String, Object> slaveStats = eventStatisticsSlaveService.getSlaveEventStatistics(dbBigdataParam);

            // 汇总统计数据
            // 当日新增事项数
            int todayCount = getIntValue(mainStats, "comprehensiveTodayCount") +
                           getIntValue(mainStats, "majorOpinionTodayCount") +
                           getIntValue(slaveStats, "umEventTodayCount") +
                           getIntValue(slaveStats, "hotlineDetailTodayCount") +
                           getIntValue(slaveStats, "enterpriseAppealTodayCount") +
                           getIntValue(slaveStats, "emergencyEventTodayCount") +
                           getIntValue(slaveStats, "mayorMailboxTodayCount") +
                           getIntValue(slaveStats, "petitionInfoTodayCount");

            // 待办事项数
            int pendingCount = getIntValue(mainStats, "comprehensivePendingCount") +
                             getIntValue(slaveStats, "umEventPendingCount") +
                             getIntValue(slaveStats, "hotlineDetailPendingCount") +
                             getIntValue(slaveStats, "emergencyEventPendingCount");

            // 累计办理事项数
            int totalHandledCount = getIntValue(mainStats, "comprehensiveTotalHandledCount") +
                                  getIntValue(mainStats, "majorOpinionTotalCount") +
                                  getIntValue(slaveStats, "umEventTotalHandledCount") +
                                  getIntValue(slaveStats, "hotlineDetailTotalHandledCount") +
                                  getIntValue(slaveStats, "enterpriseAppealTotalCount") +
                                  getIntValue(slaveStats, "emergencyEventTotalHandledCount") +
                                  getIntValue(slaveStats, "mayorMailboxTotalCount") +
                                  getIntValue(slaveStats, "petitionInfoTotalCount");

            // 办结事项数
            int completedCount = getIntValue(mainStats, "comprehensiveCompletedCount") +
                               getIntValue(slaveStats, "umEventCompletedCount") +
                               getIntValue(slaveStats, "hotlineDetailCompletedCount") +
                               getIntValue(slaveStats, "emergencyEventCompletedCount") +
                               getIntValue(slaveStats, "petitionInfoCompletedCount");

            // 办结率
            double completionRate = totalHandledCount > 0 ?
                Math.round((completedCount * 100.0 / totalHandledCount) * 100.0) / 100.0 : 0.0;

            // 满意度（主要从综合事项和12345热线获取）
            Map<String, Object> comprehensiveSatisfaction = (Map<String, Object>) mainStats.get("comprehensiveSatisfactionStats");
            Map<String, Object> hotlineSatisfaction = (Map<String, Object>) slaveStats.get("hotlineDetailSatisfactionStats");

            double satisfactionRate = calculateOverallSatisfactionRate(comprehensiveSatisfaction, hotlineSatisfaction);

            // 构建返回结果
            result.put("todayCount", todayCount);
            result.put("pendingCount", pendingCount);
            result.put("totalHandledCount", totalHandledCount);
            result.put("completedCount", completedCount);
            result.put("completionRate", completionRate);
            result.put("satisfactionRate", satisfactionRate);

            // 详细分类统计
            result.put("mainStats", mainStats);
            result.put("slaveStats", slaveStats);

        } catch (Exception e) {
            logger.error("获取事项统计数据失败", e);
            return error("获取事项统计数据失败：" + e.getMessage());
        }

        return success(result);
    }

    /**
     * 安全获取整数值
     */
    private int getIntValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    /**
     * 计算综合满意度
     */
    private double calculateOverallSatisfactionRate(Map<String, Object> comprehensiveSatisfaction,
                                                   Map<String, Object> hotlineSatisfaction) {
        double comprehensiveRate = 0.0;
        double hotlineRate = 0.0;
        int comprehensiveTotal = 0;
        int hotlineTotal = 0;

        if (comprehensiveSatisfaction != null) {
            Object rate = comprehensiveSatisfaction.get("satisfactionRate");
            Object total = comprehensiveSatisfaction.get("totalSatisfactionCount");
            if (rate instanceof Number) {
                comprehensiveRate = ((Number) rate).doubleValue();
            }
            if (total instanceof Number) {
                comprehensiveTotal = ((Number) total).intValue();
            }
        }

        if (hotlineSatisfaction != null) {
            Object rate = hotlineSatisfaction.get("satisfactionRate");
            Object total = hotlineSatisfaction.get("totalSatisfactionCount");
            if (rate instanceof Number) {
                hotlineRate = ((Number) rate).doubleValue();
            }
            if (total instanceof Number) {
                hotlineTotal = ((Number) total).intValue();
            }
        }

        // 加权平均计算综合满意度
        int totalCount = comprehensiveTotal + hotlineTotal;
        if (totalCount > 0) {
            double weightedRate = (comprehensiveRate * comprehensiveTotal + hotlineRate * hotlineTotal) / totalCount;
            return Math.round(weightedRate * 100.0) / 100.0;
        }

        return 0.0;
    }

    /**
     * 每日要情
     */
    @Resource
    private IBizDailySubmissionService bizDailySubmissionService;


    /**
     * 每日要请分页列表
     */
    @GetMapping("/tb/bizDailySubmissionListPage")
    public TableDataInfo bizDailySubmissionListPage(BizDailySubmission bizDailySubmission) {
        startPage();
        List<BizDailySubmission> list = bizDailySubmissionService.selectBizDailySubmissionList(bizDailySubmission);
        return getDataTable(list);
    }

    @Resource
    private ITbResidentMemberService tbResidentMemberService;

    /**
     * 重点人群总数
     */
    @GetMapping("/tb/keyPopulationsCount")
    public AjaxResult keyPopulationsCount(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCount(tbResidentMember));
    }

    /**
     * 重点人群分类数量
     */
    @GetMapping("/tb/keyPopulationsCountGroupByType")
    public AjaxResult keyPopulationsCountGroupByType(TbResidentMember tbResidentMember) {
        return success(tbResidentMemberService.keyPopulationsCountGroupByType(tbResidentMember));
    }


    @Resource
    private ILargeScreenService largeScreenService;

    /**
     * 干部岗位类型数量
     */
    @GetMapping("/getDeptUserPostType")
    public AjaxResult getDeptUserPostType(LargeScreenInfo largeScreenInfo) {
//        handle(largeScreenInfo);
        Map<String, Integer> map = largeScreenService.getDeptUserPostType(largeScreenInfo);
        return success(map);
    }

}
