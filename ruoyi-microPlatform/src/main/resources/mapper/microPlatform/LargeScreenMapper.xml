<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.LargeScreenMapper">

    <resultMap type="LargeScreenInfo" id="LargeScreenInfoResult">
        <result property="gridInfo" column="grid_info"/>
        <result property="communityInfo" column="community_info"/>
        <result property="merchantInfo" column="merchant_info"/>
        <result property="communityResident" column="community_resident"/>
        <result property="residentMember" column="resident_member"/>
    </resultMap>


    <sql id="deptFilter">
        AND (
        ${alias}.dept_id = #{deptId}
        OR EXISTS (
        SELECT 1
        FROM sys_dept t
        WHERE find_in_set(#{deptId}, t.ancestors)
        AND t.dept_id = ${alias}.dept_id
        )
        )
    </sql>
    <select id="getStatisticsByJurisdiction" resultMap="LargeScreenInfoResult">
        SELECT
        (SELECT COUNT(*) FROM tb_grid_info g
        <where>
            <if test="deptId != null">
                <include refid="deptFilter">
                    <property name="alias" value="g"/>
                </include>
            </if>
        </where>
        ) AS 'grid_info',
        (SELECT COUNT(*) FROM tb_community_info c
        <where>
            <if test="deptId != null">
                <include refid="deptFilter">
                    <property name="alias" value="c"/>
                </include>
            </if>
        </where>
        ) AS 'community_info',
        (SELECT COUNT(*) FROM tb_merchant_info m
        <where>
        <if test="deptId != null">
            <include refid="deptFilter">
                <property name="alias" value="m"/>
            </include>
        </if>
        </where>
        ) AS 'merchant_info',
        (SELECT COUNT(*) FROM tb_community_resident r
        <where>
            <if test="deptId != null">
                <include refid="deptFilter">
                    <property name="alias" value="r"/>
                </include>
            </if>
        </where>
        ) AS 'community_resident',
        (SELECT COUNT(*) FROM tb_resident_member rm
        <where>
            <if test="deptId != null">
                <include refid="deptFilter">
                    <property name="alias" value="rm"/>
                </include>
            </if>
        </where>
        ) AS 'resident_member'
    </select>

    <select id="getGridUnderCount" resultMap="LargeScreenInfoResult">
        SELECT
        (SELECT COUNT(*) FROM tb_community_resident r
        where
            grid_name = #{gridName}
        ) AS 'community_resident',
        (SELECT COUNT(*) FROM tb_resident_member rm
        where
            grid_name = #{gridName}
        ) AS 'resident_member',
        (SELECT COUNT(*) FROM tb_merchant_info m
         where
             grid_name = #{gridName}
        ) AS 'merchant_info'
    </select>

    <select id="getCommunityUnderCount" resultMap="LargeScreenInfoResult">
        SELECT
            (SELECT COUNT(*) FROM tb_community_resident r
             where
                 grid_name = #{gridName}
            ) AS 'community_resident',
            (SELECT COUNT(*) FROM tb_resident_member rm
             where
                 grid_name = #{gridName}
            ) AS 'resident_member'
    </select>

    <select id="countGridInfo" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_grid_info g
        <where>
            <if test="county != null">
               and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and id = #{gridId}
            </if>
        </where>
    </select>

    <select id="countCommunityInfo" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_community_info c
        <where>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
        </where>
    </select>

    <select id="countMerchantInfo" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_merchant_info m
        <where>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
        </where>
    </select>

    <select id="countCommunityResident" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_community_resident r
        <where>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="communityId != null">
                and community_id = #{communityId}
            </if>

        </where>
    </select>

    <select id="countResidentMember" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM tb_resident_member rm
        <where>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="communityId != null">
                and resident_id in (select id from tb_community_resident where community_id = #{communityId})
            </if>
        </where>
    </select>
</mapper>