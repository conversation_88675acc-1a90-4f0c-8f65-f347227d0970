package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyMember;

import java.util.List;

/**
 * 党员队伍信息管理Service接口
 */
public interface IPbPartyMemberService {
    /** 查询 */
    PbPartyMember selectPbPartyMemberById(Long id);

    /** 唯一性校验（member_code） */
    boolean checkPbPartyMember(PbPartyMember info);

    /** 列表 */
    List<PbPartyMember> selectPbPartyMemberList(PbPartyMember info);

    /** 新增 */
    int insertPbPartyMember(PbPartyMember info);

    /** 修改 */
    int updatePbPartyMember(PbPartyMember info);

    /** 批量删除 */
    int deletePbPartyMemberByIds(Long[] ids);

    /** 删除 */
    int deletePbPartyMemberById(Long id);

    Integer partyMemberCount();

    List<CommonBaseCount> partyMemberGroupSexCount();

    List<CommonBaseCount> partyMemberGroupAgeCount();

    /**
     * 按学历分组统计
     */
    List<CommonBaseCount> partyMemberGroupEducationCount();

    /**
     * 按政治面貌分组统计
     */
    List<CommonBaseCount> partyMemberGroupPoliticalStatusCount();

    List<CommonBaseCount> personnelDistribution();
}
