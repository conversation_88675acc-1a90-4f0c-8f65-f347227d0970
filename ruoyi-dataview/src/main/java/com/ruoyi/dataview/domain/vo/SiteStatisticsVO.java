package com.ruoyi.dataview.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SiteStatisticsVO {

    private Integer code;
    private String  msg;

    private List<DataItem> data;

    @Data
    public static class DataItem {
        private String stCode;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date endTime;
        private String codeAscii;
        private String codeProperty;
        private String orderNo;
        private String previousValue;
        private String firstValue;
        private Double maxValue;
        private Double minValue;
        private Double averageValue;
        private String sectionCumulativeValue;
        private String sectionCumulativeDayAvgValue;
        private String cumulativeValue;
        private Integer total;
    }
}
