package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SePublicSentiment;
import com.ruoyi.microPlatform.service.ISePublicSentimentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 居民吹哨Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/publicSentiment")
public class SePublicSentimentController extends BaseController {
    @Autowired
    private ISePublicSentimentService sePublicSentimentService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询居民吹哨列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:list')")
    @GetMapping("/list")
    public TableDataInfo list(SePublicSentiment sePublicSentiment) {
        startPage();
        handle(sePublicSentiment);
        List<SePublicSentiment> list = sePublicSentimentService.selectSePublicSentimentList(sePublicSentiment);
        return getDataTable(list);
    }

    public void handle(SePublicSentiment sePublicSentiment) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (SecurityUtils.hasRole("common")) {
                // 普通用户
                sePublicSentiment.setUserId(getUserId());
            } else {
                if (ObjectUtil.isNotEmpty(sePublicSentiment.getQueryType())) {
                    if (sePublicSentiment.getQueryType() == 2) {
                        // 查询个人提交的
                        sePublicSentiment.setUserId(getUserId());
                        return;
                    } else if (sePublicSentiment.getQueryType() == 1) {
                        // 社区记录
                        sePublicSentiment.setUserId(null);
//                        sePublicSentiment.setGridIdNull(1);
                    } else if (sePublicSentiment.getQueryType() == 3) {
                        // 我的网格
                        sePublicSentiment.setUserId(null);
                        sePublicSentiment.setGridId(SecurityUtils.getGridId());
                    }
                }

                // 非普通用户
                if (ObjectUtil.isEmpty(sePublicSentiment.getDeptId())) {
                    sePublicSentiment.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出居民吹哨列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:export')")
    @Log(title = "居民吹哨", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SePublicSentiment sePublicSentiment) {
        handle(sePublicSentiment);
        List<SePublicSentiment> list = sePublicSentimentService.selectSePublicSentimentList(sePublicSentiment);
        ExcelUtil<SePublicSentiment> util = new ExcelUtil<SePublicSentiment>(SePublicSentiment.class);
        util.exportExcel(response, list, "居民吹哨数据");
    }

    @Log(title = "居民吹哨", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SePublicSentiment> util = new ExcelUtil<SePublicSentiment>(SePublicSentiment.class);
        List<SePublicSentiment> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = sePublicSentimentService.importSePublicSentiment(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SePublicSentiment> util = new ExcelUtil<SePublicSentiment>(SePublicSentiment.class);
        util.exportTemplateExcel(response, "居民吹哨数据");
    }

    /**
     * 获取居民吹哨详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sePublicSentimentService.selectSePublicSentimentById(id));
    }

    public void getDeptInfo(SePublicSentiment sePublicSentiment) {
        if (ObjectUtil.isEmpty(sePublicSentiment.getDeptId())) {
            // 说明从小程序中添加，取用户所在社区信息
            System.err.println("说明从小程序中添加，取用户所在社区信息");
            sePublicSentiment.setDeptId(getDataDeptId());
            LoginUser loginUser1 = getLoginUser();
            if (SecurityUtils.hasRole("common")){
                // 普通人员提交的话才会设置网格， 非的话只能是common
                sePublicSentiment.setGridId(loginUser1.getUser().getJobId());
                if (ObjectUtil.isEmpty(getLoginUser().getUser().getJobId())) {
                    // 网格Id为空，给一次机会, 从缓存中取一下
                    SysUser user1 = redisCache.getCacheObject(CacheConstants.USER_GRID_KEY + loginUser1.getUserId());
                    if (ObjectUtil.isNotEmpty(user1) && ObjectUtil.isNotEmpty(user1.getJobId())) {
                        // 网格不为空, 给网格部门下的人员发送消息通知
                        sePublicSentiment.setGridId(user1.getJobId());

                        SysUser user = userMapper.selectUserByUserName(getUsername());
                        LoginUser loginUser = getLoginUser();
                        if (ObjectUtil.isNotEmpty(user)) {
                            loginUser.setUser(user);
                            loginUser.setDeptId(user.getDeptId());
                            tokenService.setLoginUser(loginUser);
                        }
                    }
                }
            }
        }
        SysDept dept = getDept(sePublicSentiment.getDeptId());
        if (ObjectUtil.isNotEmpty(dept)) {
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2) {
                sePublicSentiment.setCounty(split[1]);
                if (split.length >= 3) {
                    sePublicSentiment.setCountry(split[2]);
                    if (split.length >= 4) {
                        sePublicSentiment.setTown(split[3]);
                    }
                }
            }
        } else {
            System.err.println(sePublicSentiment.getDeptId() + "?????????");
        }
    }

    /**
     * 新增居民吹哨
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:add')")
    @Log(title = "居民吹哨", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SePublicSentiment sePublicSentiment) {
        sePublicSentiment.setCreateBy(getUsername());
//        if (sePublicSentimentService.checkSePublicSentiment(sePublicSentiment)) {
//            return error("数据已存在！");
//        }
        getDeptInfo(sePublicSentiment);
        sePublicSentiment.setUserId(getUserId());
        return toAjax(sePublicSentimentService.insertSePublicSentiment(sePublicSentiment));
    }

    /**
     * 修改居民吹哨
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:edit')")
    @Log(title = "居民吹哨", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SePublicSentiment sePublicSentiment) {
        sePublicSentiment.setUpdateBy(getUsername());
        if (sePublicSentimentService.checkSePublicSentiment(sePublicSentiment)) {
            return error("数据已存在！");
        }
        getDeptInfo(sePublicSentiment);
        return toAjax(sePublicSentimentService.updateSePublicSentiment(sePublicSentiment));
    }

    @GetMapping("/submitCount")
    public AjaxResult submitCount(SePublicSentiment sePublicSentiment) {
        sePublicSentiment.setUserId(getUserId());
        return success(sePublicSentimentService.selectSubmitCount(sePublicSentiment));
    }

    /**
     * 删除居民吹哨 同时删除后续动态信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:publicSentiment:remove')")
    @Log(title = "居民吹哨", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(sePublicSentimentService.deleteSePublicSentimentByIds(ids));
    }

}
