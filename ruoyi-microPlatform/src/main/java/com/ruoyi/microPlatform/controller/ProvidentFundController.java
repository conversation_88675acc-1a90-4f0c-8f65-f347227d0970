package com.ruoyi.microPlatform.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.microPlatform.domain.ProvidentFundRequest;
import com.ruoyi.microPlatform.service.IProvidentFundService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 公积金Controller
 *
 */
@RestController
@RequestMapping("/api/gjj")
public class ProvidentFundController extends BaseController {

    @Autowired
    private IProvidentFundService providentFundService;

    /**
     * 接口地址：http://*************:8081/api/name3/+apiName
     * 方法描述：公积金接口查询通用接口
     * 请求方式：POST
     * 响应格式：JSON
     *
     * @param providentFundRequest
     *
     * @return
     */
    @PostMapping("/queryInfo")
    public AjaxResult getGjjInfo(@RequestBody ProvidentFundRequest providentFundRequest) {
        try {
            // 调用 service 方法，传入单位编号
            Map<String, String> result = providentFundService.getUnitBusinessDetails(providentFundRequest);
            return AjaxResult.success("查询成功", result);
        } catch (Exception e) {
            logger.error("查询单位业务明细失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }


}
