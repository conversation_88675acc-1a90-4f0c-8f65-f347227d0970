package com.ruoyi.dataview.mapper;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbCityComponent;

import java.util.List;

/**
 * 运管服部件数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface DbCityComponentMapper {
    /**
     * 查询运管服部件数据
     *
     * @param id 运管服部件数据主键
     * @return 运管服部件数据
     */
    public DbCityComponent selectDbCityComponentById(Long id);

    /**
     * 校验运管服部件数据是否唯一
     *
     * @param dbCityComponent
     * @return 运管服部件数据
     */
    public DbCityComponent checkDbCityComponentUnique(DbCityComponent dbCityComponent);

    /**
     * 查询运管服部件数据列表
     *
     * @param dbCityComponent 运管服部件数据
     * @return 运管服部件数据集合
     */
    public List<DbCityComponent> selectDbCityComponentList(DbCityComponent dbCityComponent);

    /**
     * 新增运管服部件数据
     *
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    public int insertDbCityComponent(DbCityComponent dbCityComponent);

    /**
     * 修改运管服部件数据
     *
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    public int updateDbCityComponent(DbCityComponent dbCityComponent);

    /**
     * 删除运管服部件数据
     *
     * @param id 运管服部件数据主键
     * @return 结果
     */
    public int deleteDbCityComponentById(Long id);

    /**
     * 批量删除运管服部件数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbCityComponentByIds(Long[] ids);

    /**
     * 查询运管服部件数据列表
     *
     * @param dbCityComponent 运管服部件数据
     * @return 运管服部件数据集合
     */
    int selectDbCityComponentCount(DbCityComponent dbCityComponent);



    List<CommonBaseCount> selectCountGroupByMajorCode();
}
