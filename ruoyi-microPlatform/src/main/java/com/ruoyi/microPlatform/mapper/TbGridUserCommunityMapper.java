package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;

/**
 * 网格员关联小区Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbGridUserCommunityMapper 
{
    /**
     * 查询网格员关联小区
     * 
     * @param id 网格员关联小区主键
     * @return 网格员关联小区
     */
    public TbGridUserCommunity selectTbGridUserCommunityById(Long id);

    /**
     * 校验网格员关联小区是否唯一
     *
     * @param tbGridUserCommunity
     * @return 网格员关联小区
     */
    public TbGridUserCommunity checkTbGridUserCommunityUnique(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 查询网格员关联小区列表
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 网格员关联小区集合
     */
    public List<TbGridUserCommunity> selectTbGridUserCommunityList(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 新增网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    public int insertTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 修改网格员关联小区
     * 
     * @param tbGridUserCommunity 网格员关联小区
     * @return 结果
     */
    public int updateTbGridUserCommunity(TbGridUserCommunity tbGridUserCommunity);

    /**
     * 删除网格员关联小区
     * 
     * @param id 网格员关联小区主键
     * @return 结果
     */
    public int deleteTbGridUserCommunityById(Long id);

    public int deleteTbGridUserCommunityByUserId(Long id);

    /**
     * 批量删除网格员关联小区
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbGridUserCommunityByIds(Long[] ids);

    public int deleteTbGridUserCommunityByUserIds(Long[] ids);

    public Integer selectPersonCount(Long[] ids);
}
