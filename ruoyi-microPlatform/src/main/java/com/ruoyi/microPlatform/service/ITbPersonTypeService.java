package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbPersonType;

/**
 * 人员标签类型Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-04
 */
public interface ITbPersonTypeService 
{
    /**
     * 查询人员标签类型
     * 
     * @param id 人员标签类型主键
     * @return 人员标签类型
     */
    public TbPersonType selectTbPersonTypeById(Integer id);

    /**
     * 校验人员标签类型是否存在
     *
     * @param tbPersonType 人员标签类型
     * @return 人员标签类型
     */
    public boolean checkTbPersonType(TbPersonType tbPersonType);

    /**
     * 查询人员标签类型列表
     * 
     * @param tbPersonType 人员标签类型
     * @return 人员标签类型集合
     */
    public List<TbPersonType> selectTbPersonTypeList(TbPersonType tbPersonType);

    /**
     * 导入人员标签类型
     *
     * @param infos       人员标签类型列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbPersonType(List<TbPersonType> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    public int insertTbPersonType(TbPersonType tbPersonType);

    /**
     * 修改人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    public int updateTbPersonType(TbPersonType tbPersonType);

    /**
     * 批量删除人员标签类型
     * 
     * @param ids 需要删除的人员标签类型主键集合
     * @return 结果
     */
    public int deleteTbPersonTypeByIds(Integer[] ids);

    /**
     * 删除人员标签类型信息
     * 
     * @param id 人员标签类型主键
     * @return 结果
     */
    public int deleteTbPersonTypeById(Integer id);
}
