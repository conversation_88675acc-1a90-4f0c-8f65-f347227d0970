package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbInspectRequire;
import com.ruoyi.microPlatform.service.ITbInspectRequireService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 门店基础检查项目Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridManage/inspectRequire")
public class TbInspectRequireController extends BaseController
{
    @Autowired
    private ITbInspectRequireService tbInspectRequireService;

    /**
     * 查询门店基础检查项目列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbInspectRequire tbInspectRequire)
    {
        startPage();
        List<TbInspectRequire> list = tbInspectRequireService.selectTbInspectRequireList(tbInspectRequire);
        return getDataTable(list);
    }


    /**
     * 查询门店基础检查项目列表 根据type区分数据，存入map返回
     */
//    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:list')")
    @GetMapping("/listByType")
    public AjaxResult listByType(TbInspectRequire tbInspectRequire)
    {
        Map<String,List<TbInspectRequire>> map = tbInspectRequireService.selectTbInspectRequireListByType(tbInspectRequire);
        return success(map);
    }



    /**
     * 导出门店基础检查项目列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:export')")
    @Log(title = "门店基础检查项目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbInspectRequire tbInspectRequire)
    {
        List<TbInspectRequire> list = tbInspectRequireService.selectTbInspectRequireList(tbInspectRequire);
        ExcelUtil<TbInspectRequire> util = new ExcelUtil<TbInspectRequire>(TbInspectRequire.class);
        util.exportExcel(response, list, "门店基础检查项目数据");
    }

    @Log(title = "门店基础检查项目", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbInspectRequire> util = new ExcelUtil<TbInspectRequire>(TbInspectRequire.class);
        List<TbInspectRequire> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbInspectRequireService.importTbInspectRequire(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbInspectRequire> util = new ExcelUtil<TbInspectRequire>(TbInspectRequire.class);
        util.exportTemplateExcel(response, "门店基础检查项目数据");
    }

    /**
     * 获取门店基础检查项目详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(tbInspectRequireService.selectTbInspectRequireById(id));
    }

    /**
     * 新增门店基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:add')")
    @Log(title = "门店基础检查项目", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbInspectRequire tbInspectRequire)
    {
        tbInspectRequire.setCreateBy(getUsername());
        if (tbInspectRequireService.checkTbInspectRequire(tbInspectRequire)){
            return error("数据已存在！");
        }
        return toAjax(tbInspectRequireService.insertTbInspectRequire(tbInspectRequire));
    }

    /**
     * 修改门店基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:edit')")
    @Log(title = "门店基础检查项目", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbInspectRequire tbInspectRequire)
    {
        tbInspectRequire.setUpdateBy(getUsername());
        if (tbInspectRequireService.checkTbInspectRequire(tbInspectRequire)){
            return error("数据已存在！");
        }
        return toAjax(tbInspectRequireService.updateTbInspectRequire(tbInspectRequire));
    }

    /**
     * 删除门店基础检查项目
     */
    @PreAuthorize("@ss.hasPermi('gridManage:inspectRequire:remove')")
    @Log(title = "门店基础检查项目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(tbInspectRequireService.deleteTbInspectRequireByIds(ids));
    }
}
