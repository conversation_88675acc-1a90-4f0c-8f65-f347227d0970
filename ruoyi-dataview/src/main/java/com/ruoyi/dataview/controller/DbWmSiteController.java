package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbWmSiteConvert;
import com.ruoyi.dataview.domain.dto.DbWmSiteFormDTO;
import com.ruoyi.dataview.domain.dto.DbWmSiteQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbWmSite;
import com.ruoyi.dataview.service.IDbWmSiteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 水利局涵洞监测站点Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/fiveLevel/wmSite")
@RequiredArgsConstructor
public class DbWmSiteController extends BaseController
{
    private final IDbWmSiteService dbWmSiteService;

    private final DbWmSiteConvert wmSiteConvert;

    /**
     * 查询水利局涵洞监测站点列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbWmSiteQueryDTO wmSiteQueryDTO)
    {
        DbWmSite dbWmSite = wmSiteConvert.toDO(wmSiteQueryDTO);
        startPage();
        return getDataTable(wmSiteConvert.toVOList(dbWmSiteService.selectDbWmSiteList(dbWmSite)));
    }

    /**
     * 导出水利局涵洞监测站点列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:export')")
    @Log(title = "水利局涵洞监测站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbWmSiteQueryDTO wmSiteQueryDTO)
    {
        DbWmSite dbWmSite = wmSiteConvert.toDO(wmSiteQueryDTO);
        List<DbWmSite> list = dbWmSiteService.selectDbWmSiteList(dbWmSite);
        ExcelUtil<DbWmSite> util = new ExcelUtil<DbWmSite>(DbWmSite.class);
        util.exportExcel(response, list, "水利局涵洞监测站点数据");
    }

    @Log(title = "水利局涵洞监测站点", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbWmSite> util = new ExcelUtil<DbWmSite>(DbWmSite.class);
        List<DbWmSite> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbWmSiteService.importDbWmSite(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbWmSite> util = new ExcelUtil<DbWmSite>(DbWmSite.class);
        util.exportTemplateExcel(response, "水利局涵洞监测站点数据");
    }

    /**
     * 获取水利局涵洞监测站点详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wmSiteConvert.toVO(dbWmSiteService.selectDbWmSiteById(id)));
    }

    /**
     * 新增水利局涵洞监测站点
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:add')")
    @Log(title = "水利局涵洞监测站点", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbWmSiteFormDTO wmSiteFormDTO)
    {
        DbWmSite dbWmSite = wmSiteConvert.toDO(wmSiteFormDTO);
        dbWmSite.setCreateBy(getUsername());
        if (dbWmSiteService.checkDbWmSite(dbWmSite)){
            return error("数据已存在！");
        }
        return toAjax(dbWmSiteService.insertDbWmSite(dbWmSite));
    }

    /**
     * 修改水利局涵洞监测站点
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:edit')")
    @Log(title = "水利局涵洞监测站点", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbWmSiteFormDTO wmSiteFormDTO)
    {
        DbWmSite dbWmSite = wmSiteConvert.toDO(wmSiteFormDTO);
        dbWmSite.setUpdateBy(getUsername());
        if (dbWmSiteService.checkDbWmSite(dbWmSite)){
            return error("数据已存在！");
        }
        return toAjax(dbWmSiteService.updateDbWmSite(dbWmSite));
    }

    /**
     * 删除水利局涵洞监测站点
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:wmSite:remove')")
    @Log(title = "水利局涵洞监测站点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbWmSiteService.deleteDbWmSiteByIds(ids));
    }
}
