package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 心灵有约对象 se_heart_promise
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SeHeartPromise extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    @Excel(name = "所属网格")
    private String gridName;

    /** 咨询问题 */
    @Excel(name = "咨询问题")
    private String feedbackIssue;

    /** 方便沟通时间 */
    @Excel(name = "方便沟通时间")
    private String communicateTime;

    /** 账户 */
    @Excel(name = "账户")
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String nickName;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 经纬度 */
    @Excel(name = "经纬度")
    private String lat;

    /**  */
    @Excel(name = "")
    private String lng;

    /** 省 */
    @Excel(name = "省")
    private String province;

    /** 市 */
    @Excel(name = "市")
    private String city;

    /** 县 */
    @Excel(name = "县")
    private String addrCounty;

    /** 街道 */
    @Excel(name = "街道")
    private String addrCountry;

    /** 地址 */
    @Excel(name = "地址")
    private String addr;

    /** 数据状态（0待处理 1已结束） */
    @Excel(name = "数据状态", readConverterExp = "0=待处理,1=已结束")
    private Integer status;

    /** 处理结果 */
    @Excel(name = "处理结果")
    private String handleResult;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setFeedbackIssue(String feedbackIssue) 
    {
        this.feedbackIssue = feedbackIssue;
    }

    public String getFeedbackIssue() 
    {
        return feedbackIssue;
    }
    public void setCommunicateTime(String communicateTime) 
    {
        this.communicateTime = communicateTime;
    }

    public String getCommunicateTime() 
    {
        return communicateTime;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setAddrCounty(String addrCounty) 
    {
        this.addrCounty = addrCounty;
    }

    public String getAddrCounty() 
    {
        return addrCounty;
    }
    public void setAddrCountry(String addrCountry) 
    {
        this.addrCountry = addrCountry;
    }

    public String getAddrCountry() 
    {
        return addrCountry;
    }
    public void setAddr(String addr) 
    {
        this.addr = addr;
    }

    public String getAddr() 
    {
        return addr;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setHandleResult(String handleResult) 
    {
        this.handleResult = handleResult;
    }

    public String getHandleResult() 
    {
        return handleResult;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("feedbackIssue", getFeedbackIssue())
            .append("communicateTime", getCommunicateTime())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("phone", getPhone())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("province", getProvince())
            .append("city", getCity())
            .append("addrCounty", getAddrCounty())
            .append("addrCountry", getAddrCountry())
            .append("addr", getAddr())
            .append("status", getStatus())
            .append("handleResult", getHandleResult())
            .append("createTime", getCreateTime())
            .toString();
    }
}
