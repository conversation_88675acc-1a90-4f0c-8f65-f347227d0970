package com.ruoyi.dataview.mapper;

import java.util.List;
import com.ruoyi.dataview.domain.po.DbSiteData;

/**
 * 水利局涵洞监测数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DbSiteDataMapper 
{
    /**
     * 查询水利局涵洞监测数据
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 水利局涵洞监测数据
     */
    public DbSiteData selectDbSiteDataById(String id);

    /**
     * 校验水利局涵洞监测数据是否唯一
     *
     * @param dbSiteData
     * @return 水利局涵洞监测数据
     */
    public DbSiteData checkDbSiteDataUnique(DbSiteData dbSiteData);

    /**
     * 查询水利局涵洞监测数据列表
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 水利局涵洞监测数据集合
     */
    public List<DbSiteData> selectDbSiteDataList(DbSiteData dbSiteData);

    /**
     * 新增水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    public int insertDbSiteData(DbSiteData dbSiteData);

    /**
     * 修改水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    public int updateDbSiteData(DbSiteData dbSiteData);

    /**
     * 删除水利局涵洞监测数据
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 结果
     */
    public int deleteDbSiteDataById(String id);

    /**
     * 批量删除水利局涵洞监测数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbSiteDataByIds(String[] ids);
}
