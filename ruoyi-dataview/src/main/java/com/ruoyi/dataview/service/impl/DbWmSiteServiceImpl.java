package com.ruoyi.dataview.service.impl;

import java.util.Collections;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbWmSiteMapper;
import com.ruoyi.dataview.domain.po.DbWmSite;
import com.ruoyi.dataview.service.IDbWmSiteService;

/**
 * 水利局涵洞监测站点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbWmSiteServiceImpl implements IDbWmSiteService 
{
    @Autowired
    private DbWmSiteMapper dbWmSiteMapper;

    /**
     * 查询水利局涵洞监测站点
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 水利局涵洞监测站点
     */
    @Override
    public DbWmSite selectDbWmSiteById(Long id)
    {
        return dbWmSiteMapper.selectDbWmSiteById(id);
    }

    /**
     * 校验水利局涵洞监测站点是否存在
     *
     * @param dbWmSite
     * @return boolean
     */
    @Override
    public boolean checkDbWmSite(DbWmSite dbWmSite){
        DbWmSite old = dbWmSiteMapper.checkDbWmSiteUnique(dbWmSite);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询水利局涵洞监测站点列表
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 水利局涵洞监测站点
     */
    @Override
    public List<DbWmSite> selectDbWmSiteList(DbWmSite dbWmSite)
    {
        return dbWmSiteMapper.selectDbWmSiteList(dbWmSite);
    }

    /**
     * 导入水利局涵洞监测站点
     *
     * @param infos       水利局涵洞监测站点列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbWmSite(List<DbWmSite> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入水利局涵洞监测站点数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbWmSite info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                DbWmSite old = dbWmSiteMapper.checkDbWmSiteUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbWmSiteMapper.insertDbWmSite(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbWmSiteMapper.updateDbWmSite(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    @Override
    public int insertDbWmSite(DbWmSite dbWmSite)
    {
        dbWmSite.setCreateTime(DateUtils.getNowDate());
        return dbWmSiteMapper.insertDbWmSite(dbWmSite);
    }

    /**
     * 修改水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    @Override
    public int updateDbWmSite(DbWmSite dbWmSite)
    {
        dbWmSite.setUpdateTime(DateUtils.getNowDate());
        return dbWmSiteMapper.updateDbWmSite(dbWmSite);
    }

    /**
     * 批量删除水利局涵洞监测站点
     * 
     * @param ids 需要删除的水利局涵洞监测站点主键
     * @return 结果
     */
    @Override
    public int deleteDbWmSiteByIds(Long[] ids)
    {
        return dbWmSiteMapper.deleteDbWmSiteByIds(ids);
    }

    /**
     * 删除水利局涵洞监测站点信息
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 结果
     */
    @Override
    public int deleteDbWmSiteById(Long id)
    {
        return dbWmSiteMapper.deleteDbWmSiteById(id);
    }

    @Override
    public List<CommonBaseCount> wdDataStatusCount() {
        List<CommonBaseCount> commonBaseCounts = dbWmSiteMapper.wdDataStatusCount();
        // 0 正常 1离线 2异常
        int online = 0;
        int offline = 0;
        if (commonBaseCounts != null) {
            for (CommonBaseCount item : commonBaseCounts) {
                if (item == null || item.getCount() == null || item.getStatus() == null) {
                    continue;
                }
                if (item.getStatus() == 0) {
                    online += item.getCount();
                } else {
                    offline += item.getCount();
                }
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(2);
        CommonBaseCount on = new CommonBaseCount();
        on.setLable("在线");
        on.setStatus(0);
        on.setCount(online);
        res.add(on);

        CommonBaseCount off = new CommonBaseCount();
        off.setLable("离线（非正常）");
        off.setStatus(1);
        off.setCount(offline);
        res.add(off);

        return res;
    }
}
