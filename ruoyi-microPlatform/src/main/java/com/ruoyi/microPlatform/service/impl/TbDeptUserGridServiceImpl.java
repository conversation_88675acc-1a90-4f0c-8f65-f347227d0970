package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbDeptUserGridMapper;
import com.ruoyi.microPlatform.domain.TbDeptUserGrid;
import com.ruoyi.microPlatform.service.ITbDeptUserGridService;

/**
 * 社区干部关联网格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbDeptUserGridServiceImpl implements ITbDeptUserGridService 
{
    @Autowired
    private TbDeptUserGridMapper tbDeptUserGridMapper;

    /**
     * 查询社区干部关联网格
     * 
     * @param id 社区干部关联网格主键
     * @return 社区干部关联网格
     */
    @Override
    public TbDeptUserGrid selectTbDeptUserGridById(Long id)
    {
        return tbDeptUserGridMapper.selectTbDeptUserGridById(id);
    }

    /**
     * 校验社区干部关联网格是否存在
     *
     * @param tbDeptUserGrid
     * @return boolean
     */
    @Override
    public boolean checkTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid){
        TbDeptUserGrid old = tbDeptUserGridMapper.checkTbDeptUserGridUnique(tbDeptUserGrid);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询社区干部关联网格列表
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 社区干部关联网格
     */
    @Override
    public List<TbDeptUserGrid> selectTbDeptUserGridList(TbDeptUserGrid tbDeptUserGrid)
    {
        return tbDeptUserGridMapper.selectTbDeptUserGridList(tbDeptUserGrid);
    }

    /**
     * 导入社区干部关联网格
     *
     * @param infos       社区干部关联网格列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbDeptUserGrid(List<TbDeptUserGrid> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入社区干部关联网格数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbDeptUserGrid info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbDeptUserGrid old = tbDeptUserGridMapper.checkTbDeptUserGridUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbDeptUserGridMapper.insertTbDeptUserGrid(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbDeptUserGridMapper.updateTbDeptUserGrid(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    @Override
    public int insertTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid)
    {
        return tbDeptUserGridMapper.insertTbDeptUserGrid(tbDeptUserGrid);
    }

    /**
     * 修改社区干部关联网格
     * 
     * @param tbDeptUserGrid 社区干部关联网格
     * @return 结果
     */
    @Override
    public int updateTbDeptUserGrid(TbDeptUserGrid tbDeptUserGrid)
    {
        return tbDeptUserGridMapper.updateTbDeptUserGrid(tbDeptUserGrid);
    }

    /**
     * 批量删除社区干部关联网格
     * 
     * @param ids 需要删除的社区干部关联网格主键
     * @return 结果
     */
    @Override
    public int deleteTbDeptUserGridByIds(Long[] ids)
    {
        return tbDeptUserGridMapper.deleteTbDeptUserGridByIds(ids);
    }

    /**
     * 删除社区干部关联网格信息
     * 
     * @param id 社区干部关联网格主键
     * @return 结果
     */
    @Override
    public int deleteTbDeptUserGridById(Long id)
    {
        return tbDeptUserGridMapper.deleteTbDeptUserGridById(id);
    }
}
