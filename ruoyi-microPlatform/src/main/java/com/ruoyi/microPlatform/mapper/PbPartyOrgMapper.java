package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyOrg;

import java.util.List;

/**
 * 基层党组织管理Mapper接口
 */
public interface PbPartyOrgMapper {
    /** 查询 */
    PbPartyOrg selectPbPartyOrgById(Long id);

    /** 唯一性校验（org_code唯一） */
    PbPartyOrg checkPbPartyOrgUnique(PbPartyOrg org);

    /** 列表 */
    List<PbPartyOrg> selectPbPartyOrgList(PbPartyOrg org);

    /** 新增 */
    int insertPbPartyOrg(PbPartyOrg org);

    /** 修改 */
    int updatePbPartyOrg(PbPartyOrg org);

    /** 删除 */
    int deletePbPartyOrgById(Long id);

    /** 批量删除 */
    int deletePbPartyOrgByIds(Long[] ids);

    List<CommonBaseCount> pbPartyOrgGroupOrgLevel();

    List<CommonBaseCount> pbPartyOrgGroupOrgType();
}
