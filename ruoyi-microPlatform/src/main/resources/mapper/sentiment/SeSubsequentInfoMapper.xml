<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeSubsequentInfoMapper">

    <resultMap type="SeSubsequentInfo" id="SeSubsequentInfoResult">
        <result property="id" column="id"/>
        <result property="formId" column="form_id"/>
        <result property="formType" column="form_type"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="createTime" column="create_time"/>
        <result property="parentId" column="parent_id"/>
        <result property="content" column="content"/>
        <result property="accessory" column="accessory"/>
        <association property="seSubsequentInfo" column="parent_id" javaType="SeSubsequentInfo" resultMap="ParentResult"/>
    </resultMap>

    <resultMap type="SeSubsequentInfo" id="ParentResult">
        <result property="id" column="p_id"/>
        <result property="formId" column="p_form_id"/>
        <result property="formType" column="p_form_type"/>
        <result property="userId" column="p_user_id"/>
        <result property="nickName" column="p_nick_name"/>
        <result property="createTime" column="p_create_time"/>
        <result property="parentId" column="p_parent_id"/>
        <result property="content" column="p_content"/>
        <result property="accessory" column="p_accessory"/>
    </resultMap>

    <sql id="selectSeSubsequentInfoVo">
        select s.id, s.form_id, s.form_type, s.user_id, s.nick_name, s.create_time, s.parent_id, s.content, s.accessory,
        p.id as p_id, p.form_id as p_form_id, p.form_type as p_form_type, p.user_id as p_user_id, p.nick_name as p_nick_name,
        p.create_time as p_create_time, p.parent_id as p_parent_id, p.content as p_content, p.accessory as p_accessory
        from se_subsequent_info s
        left join se_subsequent_info p on s.parent_id = p.id
    </sql>

    <select id="selectSeSubsequentInfoList" parameterType="SeSubsequentInfo" resultMap="SeSubsequentInfoResult">
        <include refid="selectSeSubsequentInfoVo"/>
        <where>
            <if test="formId != null ">and s.form_id = #{formId}</if>
            <if test="formType != null  and formType != ''">and s.form_type = #{formType}</if>
            <if test="userId != null ">and s.user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and s.nick_name like concat('%', #{nickName}, '%')</if>
            <if test="parentId != null ">and s.parent_id = #{parentId}</if>
            <if test="content != null  and content != ''">and s.content = #{content}</if>
        </where>
        order by create_time asc
    </select>

    <select id="selectSeSubsequentInfoById" parameterType="Long" resultMap="SeSubsequentInfoResult">
        <include refid="selectSeSubsequentInfoVo"/>
        where s.id = #{id}
    </select>

    <select id="checkSeSubsequentInfoUnique" parameterType="SeSubsequentInfo" resultMap="SeSubsequentInfoResult">
        select id from se_subsequent_info where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeSubsequentInfo" parameterType="SeSubsequentInfo" useGeneratedKeys="true" keyProperty="id">
        insert into se_subsequent_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="formId != null">form_id,</if>
            <if test="formType != null">form_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="content != null">content,</if>
            <if test="accessory != null">accessory,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="formId != null">#{formId},</if>
            <if test="formType != null">#{formType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="content != null">#{content},</if>
            <if test="accessory != null">#{accessory},</if>
        </trim>
    </insert>

    <update id="updateSeSubsequentInfo" parameterType="SeSubsequentInfo">
        update se_subsequent_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="formId != null">form_id = #{formId},</if>
            <if test="formType != null">form_type = #{formType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeSubsequentInfoById" parameterType="Long">
        delete from se_subsequent_info where id = #{id}
    </delete>

    <delete id="deleteSeSubsequentInfoByIds" parameterType="String">
        delete from se_subsequent_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDataCount" parameterType="SeSubsequentInfo" resultType="integer">
        select count(*) from se_subsequent_info where form_type = #{formType} and form_id = #{formId}
    </select>

    <delete id="deleteSeSubsequentInfoByPubIds">
        delete from se_subsequent_info where form_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and form_type = #{formType}
    </delete>
</mapper>