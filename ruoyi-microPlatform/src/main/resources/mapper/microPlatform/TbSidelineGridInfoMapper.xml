<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbSidelineGridInfoMapper">

    <resultMap type="TbSidelineGridInfo" id="TbSidelineGridInfoResult">
            <result property="id"    column="id"    />
            <result property="sideUserId"    column="side_user_id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="county"    column="county"    />
            <result property="country"    column="country"    />
            <result property="town"    column="town"    />
            <result property="gridName"    column="grid_name"    />
            <result property="gridId"    column="grid_id"    />
            <result property="gridRange"    column="grid_range"    />
    </resultMap>

    <sql id="selectTbSidelineGridInfoVo">
        select id, dept_id, side_user_id, county, country, town, grid_name, grid_id, grid_range from tb_sideline_grid_info
    </sql>

    <select id="selectTbSidelineGridInfoList" parameterType="TbSidelineGridInfo" resultMap="TbSidelineGridInfoResult">
        <include refid="selectTbSidelineGridInfoVo"/>
        <where>
                        <if test="sideUserId != null "> and side_user_id = #{sideUserId}</if>
                        <if test="deptId != null "> and dept_id = #{deptId}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="country != null  and country != ''"> and country = #{country}</if>
                        <if test="town != null  and town != ''"> and town = #{town}</if>
                        <if test="gridName != null  and gridName != ''"> and grid_name like concat('%', #{gridName}, '%')</if>
                        <if test="gridId != null "> and grid_id = #{gridId}</if>
                        <if test="gridRange != null  and gridRange != ''"> and grid_range = #{gridRange}</if>
        </where>
    </select>

    <select id="selectTbSidelineGridInfoById" parameterType="Long" resultMap="TbSidelineGridInfoResult">
            <include refid="selectTbSidelineGridInfoVo"/>
            where id = #{id}
    </select>

    <select id="checkTbSidelineGridInfoUnique" parameterType="TbSidelineGridInfo" resultMap="TbSidelineGridInfoResult">
        select id from tb_sideline_grid_info
        <where>
            <if test="id != null">
                and id != #{id}
            </if>
            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
            <if test="sideUserId != null">
                and side_user_id = #{sideUserId}
            </if>

        </where>
    </select>

    <insert id="insertTbSidelineGridInfo" parameterType="TbSidelineGridInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sideline_grid_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="sideUserId != null">side_user_id,</if>
                    <if test="deptId != null">dept_id,</if>
                    <if test="county != null">county,</if>
                    <if test="country != null">country,</if>
                    <if test="town != null">town,</if>
                    <if test="gridName != null">grid_name,</if>
                    <if test="gridId != null">grid_id,</if>
                    <if test="gridRange != null">grid_range,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="sideUserId != null">#{sideUserId},</if>
                    <if test="deptId != null">#{deptId},</if>
                    <if test="county != null">#{county},</if>
                    <if test="country != null">#{country},</if>
                    <if test="town != null">#{town},</if>
                    <if test="gridName != null">#{gridName},</if>
                    <if test="gridId != null">#{gridId},</if>
                    <if test="gridRange != null">#{gridRange},</if>
        </trim>
    </insert>

    <update id="updateTbSidelineGridInfo" parameterType="TbSidelineGridInfo">
        update tb_sideline_grid_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="sideUserId != null">side_user_id = #{sideUserId},</if>
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="country != null">country = #{country},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="gridName != null">grid_name = #{gridName},</if>
                    <if test="gridId != null">grid_id = #{gridId},</if>
                    <if test="gridRange != null">grid_range = #{gridRange},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbSidelineGridInfoById" parameterType="Long">
        delete from tb_sideline_grid_info where id = #{id}
    </delete>

    <delete id="deleteTbSidelineGridInfoByIds" parameterType="String">
        delete from tb_sideline_grid_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>