package com.ruoyi.framework.util;

import com.google.code.kaptcha.impl.FishEyeGimpy;

import java.awt.*;
import java.awt.image.BufferedImage;

public class AftFishEyeGimpy extends FishEyeGimpy {
    @Override
    public BufferedImage getDistortedImage(BufferedImage baseImage) {
        Graphics2D graph = (Graphics2D)baseImage.getGraphics();
        int imageHeight = baseImage.getHeight();
        int imageWidth = baseImage.getWidth();
        int horizontalLines = imageHeight / 7;
        int verticalLines = imageWidth / 7;
        int horizontalGaps = imageHeight / (horizontalLines + 1);
        int verticalGaps = imageWidth / (verticalLines + 1);
//
//        int i;
//        for(i = horizontalGaps; i < imageHeight; i += horizontalGaps) {
//            graph.setColor(Color.blue);
//            graph.drawLine(0, i, imageWidth, i);
//        }

//        for(i = verticalGaps; i < imageWidth; i += verticalGaps) {
//            graph.setColor(Color.red);
//            graph.drawLine(i, 0, i, imageHeight);
//        }

//        int[] pix = new int[imageHeight * imageWidth];
//        int j = 0;

//        for(int j1 = 0; j1 < imageWidth; ++j1) {
//            for(int k1 = 0; k1 < imageHeight; ++k1) {
//                pix[j] = baseImage.getRGB(j1, k1);
//                ++j;
//            }
//        }

        /*这里删除了默认鱼纹效果类的渲染代码*/

        return baseImage;
    }
}
