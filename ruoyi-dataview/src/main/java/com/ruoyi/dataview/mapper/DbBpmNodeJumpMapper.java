package com.ruoyi.dataview.mapper;

import java.util.List;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;

/**
 * 运管服案件流程流转记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DbBpmNodeJumpMapper 
{
    /**
     * 查询运管服案件流程流转记录
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 运管服案件流程流转记录
     */
    public DbBpmNodeJump selectDbBpmNodeJumpById(Long id);

    /**
     * 校验运管服案件流程流转记录是否唯一
     *
     * @param dbBpmNodeJump
     * @return 运管服案件流程流转记录
     */
    public DbBpmNodeJump checkDbBpmNodeJumpUnique(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 查询运管服案件流程流转记录列表
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 运管服案件流程流转记录集合
     */
    public List<DbBpmNodeJump> selectDbBpmNodeJumpList(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 新增运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    public int insertDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 修改运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    public int updateDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 删除运管服案件流程流转记录
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 结果
     */
    public int deleteDbBpmNodeJumpById(Long id);

    /**
     * 批量删除运管服案件流程流转记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbBpmNodeJumpByIds(Long[] ids);
}
