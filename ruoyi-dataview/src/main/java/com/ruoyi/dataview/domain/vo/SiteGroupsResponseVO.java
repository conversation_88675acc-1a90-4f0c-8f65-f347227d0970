package com.ruoyi.dataview.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.List;

@Data
public class SiteGroupsResponseVO {

    private int offLine;
    private int total;
    private List<DataItem> data;
    private int exceptionState;
    private String message;

    @Data
    public static class DataItem {
        private List<Object> dataInfos;
        private String stCode;
        private String stName;
        private int status;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private String sttTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private String ttTime;
    }
}
