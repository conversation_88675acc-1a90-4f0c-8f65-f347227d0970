package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 许邻e家事项统计Mapper
 */
@Mapper
public interface XuLinMatterMapper {
    
    /**
     * 获取居民吹哨统计数据
     * @return 已处理和未处理数量
     */
    Map<String, Object> countResidentWhistleStats(@Param("bigdataParam") BigdataParam bigdataParam);
    
    /**
     * 获取网格巡查问题统计数据
     * @return 已处理和未处理数量
     */
    Map<String, Object> countGridInspectionStats(@Param("bigdataParam") BigdataParam bigdataParam);
    
    /**
     * 获取住户走访问题统计数据
     * @return 已处理和未处理数量
     */
    Map<String, Object> countHouseholdVisitStats(@Param("bigdataParam") BigdataParam bigdataParam);
    
    /**
     * 获取门店巡查问题统计数据
     * @return 已处理和未处理数量
     */
    Map<String, Object> countStoreInspectionStats(@Param("bigdataParam") BigdataParam bigdataParam);
}
