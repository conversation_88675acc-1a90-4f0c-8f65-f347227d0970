<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeVolunteerRegisterMapper">

    <resultMap type="SeVolunteerRegister" id="SeVolunteerRegisterResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="idCard" column="id_card"/>
        <result property="nation" column="nation"/>
        <result property="sex" column="sex"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="addrCounty" column="addr_county"/>
        <result property="addrCountry" column="addr_country"/>
        <result property="addr" column="addr"/>
        <result property="skilledSkills" column="skilled_skills"/>
        <result property="remark" column="remark"/>
        <result property="phone" column="phone"/>
        <result property="status" column="status"/>
        <result property="handleResult" column="handle_result"/>
        <result property="createTime" column="create_time"/>
        <result property="addrId" column="addr_id"/>
    </resultMap>

    <sql id="selectSeVolunteerRegisterVo">
        select id, dept_id, county, country, town, grid_name, user_id, nick_name, id_card, nation, sex, political_status, province, city, addr_county, addr_country,
         addr, skilled_skills, remark, phone, status, handle_result, create_time, addr_id from se_volunteer_register
    </sql>

    <select id="selectSeVolunteerRegisterList" parameterType="SeVolunteerRegister"
            resultMap="SeVolunteerRegisterResult">
        <include refid="selectSeVolunteerRegisterVo"/>
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_volunteer_register.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="nation != null  and nation != ''">and nation = #{nation}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="politicalStatus != null  and politicalStatus != ''">and political_status = #{politicalStatus}</if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="addrCounty != null  and addrCounty != ''">and addr_county = #{addrCounty}</if>
            <if test="addrCountry != null  and addrCountry != ''">and addr_country = #{addrCountry}</if>
            <if test="addr != null  and addr != ''">and addr = #{addr}</if>
            <if test="skilledSkills != null  and skilledSkills != ''">and skilled_skills like concat('%', #{skilledSkills}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="handleResult != null  and handleResult != ''">and handle_result = #{handleResult}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSeVolunteerRegisterById" parameterType="Long" resultMap="SeVolunteerRegisterResult">
        <include refid="selectSeVolunteerRegisterVo"/>
        where id = #{id}
    </select>

    <select id="checkSeVolunteerRegisterUnique" parameterType="SeVolunteerRegister"
            resultMap="SeVolunteerRegisterResult">
        select id from se_volunteer_register where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeVolunteerRegister" parameterType="SeVolunteerRegister" useGeneratedKeys="true" keyProperty="id">
        insert into se_volunteer_register
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="nation != null">nation,</if>
            <if test="sex != null">sex,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="addrCounty != null">addr_county,</if>
            <if test="addrCountry != null">addr_country,</if>
            <if test="addr != null">addr,</if>
            <if test="skilledSkills != null">skilled_skills,</if>
            <if test="remark != null">remark,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="handleResult != null">handle_result,</if>
            <if test="createTime != null">create_time,</if>
            <if test="addrId != null">addr_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="nation != null">#{nation},</if>
            <if test="sex != null">#{sex},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="addrCounty != null">#{addrCounty},</if>
            <if test="addrCountry != null">#{addrCountry},</if>
            <if test="addr != null">#{addr},</if>
            <if test="skilledSkills != null">#{skilledSkills},</if>
            <if test="remark != null">#{remark},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="handleResult != null">#{handleResult},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="addrId != null">#{addrId},</if>
        </trim>
    </insert>

    <update id="updateSeVolunteerRegister" parameterType="SeVolunteerRegister">
        update se_volunteer_register
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="addrCounty != null">addr_county = #{addrCounty},</if>
            <if test="addrCountry != null">addr_country = #{addrCountry},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="skilledSkills != null">skilled_skills = #{skilledSkills},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handleResult != null">handle_result = #{handleResult},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="addrId != null">addr_id = #{addrId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeVolunteerRegisterById" parameterType="Long">
        delete from se_volunteer_register where id = #{id}
    </delete>

    <delete id="deleteSeVolunteerRegisterByIds" parameterType="String">
        delete from se_volunteer_register where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSubmitCount" parameterType="SeVolunteerRegister" resultType="map">
        select count(*) as submitCount, max(create_time) as submitTime from se_volunteer_register
        where user_id = #{userId}
    </select>

    <select id="selectSubmitCountByHome" resultType="int">
        SELECT COUNT(*) AS count
        FROM se_volunteer_register
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_volunteer_register.dept_id))</if>

            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(startTime == null or startTime == null) and time != null">
                <choose>
                    <when test="time == 'day'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="time == 'week'">
                        AND YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="time == 'month'">
                        AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    </when>
                    <otherwise>
                        AND DATE(create_time) = CURDATE()
                    </otherwise>
                </choose>
            </if>
        </where>

    </select>



    <select id="selectCountByBigData" resultType="int">
        SELECT COUNT(*) AS count
        FROM se_volunteer_register
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_volunteer_register.dept_id))</if>

        </where>

    </select>


</mapper>