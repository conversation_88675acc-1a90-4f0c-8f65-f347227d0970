package com.ruoyi.microPlatform.controller.bigdata.gov;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.microPlatform.controller.bigdata.gov.req.GovGetUnifiedManagementLetterListReq;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetLetterTypeModelResp;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetUnifiedManagementLetterListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class GovBiz {

    @Autowired
    private SendToGovRequest sendToGovRequest;
    @Resource
    private ObjectMapper objectMapper;


    private static final BizAuthDto bizAuthDto = new BizAuthDto();

    static {
        bizAuthDto.setThirdUrl("https://www.xuchang.gov.cn");
    }

    public String getToken() {
        try {
            String res = sendToGovRequest.sendRequest(GovApiEnum.GET_TOKEN, null, null, bizAuthDto);
            GovBaseReq govBaseReq = JSON.parseObject(res, GovBaseReq.class);
            return govBaseReq.getCustom();
        } catch (Exception e) {
            throw new RuntimeException("查询失败！");
        }
    }


    public GovGetUnifiedManagementLetterListResp getUnifiedManagementLetterList(GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq) {
        govGetUnifiedManagementLetterListReq.setToken(getToken());
        try {
            String res = sendToGovRequest.sendRequest(GovApiEnum.GET_UNIFIED_MANAGEMENT_LETTER_LIST, null, govGetUnifiedManagementLetterListReq, bizAuthDto);
            GovBaseReq govBaseReq = JSON.parseObject(res, GovBaseReq.class);

            return JSON.parseObject(govBaseReq.getCustom(), GovGetUnifiedManagementLetterListResp.class);
        } catch (Exception e) {
            throw new RuntimeException("查询失败！");
        }
    }

    public List<GovGetLetterTypeModelResp> getLetterTypeModel() {
        GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
        govGetUnifiedManagementLetterListReq.setToken(getToken());
        try {
            String res = sendToGovRequest.sendRequest(GovApiEnum.GET_LETTER_TYPE_MODEL, null, govGetUnifiedManagementLetterListReq, bizAuthDto);
            GovBaseReq govBaseReq = JSON.parseObject(res, GovBaseReq.class);
            return JSON.parseArray(govBaseReq.getCustom(), GovGetLetterTypeModelResp.class);
        } catch (Exception e) {
            throw new RuntimeException("查询失败！");
        }
    }

    public List<GovGetLetterTypeModelResp> getStatusModel() {
        GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
        govGetUnifiedManagementLetterListReq.setToken(getToken());
        try {
            String res = sendToGovRequest.sendRequest(GovApiEnum.GET_STATUS_MODEL, null, govGetUnifiedManagementLetterListReq, bizAuthDto);
            GovBaseReq govBaseReq = JSON.parseObject(res, GovBaseReq.class);
            return JSON.parseArray(govBaseReq.getCustom(), GovGetLetterTypeModelResp.class);
        } catch (Exception e) {
            throw new RuntimeException("查询失败！");
        }
    }
}
