package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;
import com.ruoyi.microPlatform.util.ExcelMainUtil;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;
import com.ruoyi.microPlatform.service.ITbMerchantInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 门店/商户信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/merchantInfo")
public class TbMerchantInfoController extends BaseController {
    @Autowired
    private ITbMerchantInfoService tbMerchantInfoService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询门店/商户信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbMerchantInfo tbMerchantInfo) {
        startPage();
        handle(tbMerchantInfo);
        List<TbMerchantInfo> list = tbMerchantInfoService.selectTbMerchantInfoList(tbMerchantInfo);
        return getDataTable(list);
    }

    public void handle(TbMerchantInfo tbMerchantInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbMerchantInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的门店商户数据
                if (ObjectUtil.isEmpty(tbMerchantInfo.getDeptId()) && ObjectUtil.isEmpty(tbMerchantInfo.getGridId())) {
                    tbMerchantInfo.setDeptId(getDataDeptId());
                }
            }
        }
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(TbMerchantInfo tbMerchantInfo) {
        handle(tbMerchantInfo);
        List<TbMerchantInfo> list = tbMerchantInfoService.selectTbMerchantInfoList(tbMerchantInfo);
        return success(list);
    }

    /**
     * 导出门店/商户信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:export')")
    @Log(title = "门店/商户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbMerchantInfo tbMerchantInfo) {
        handle(tbMerchantInfo);
        if (ObjectUtil.isNotEmpty(tbMerchantInfo.getIsExportAll()) && tbMerchantInfo.getIsExportAll() == 0) {
            startPage(pageNum, pageSize);
        }
        tbMerchantInfo.setIsExport(1);
        List<TbMerchantInfo> list = tbMerchantInfoService.selectTbMerchantInfoList(tbMerchantInfo);
        ExcelUtil<TbMerchantInfo> util = new ExcelUtil<TbMerchantInfo>(TbMerchantInfo.class);
        util.exportExcel(response, list, "门店商户信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbMerchantInfo tbMerchantInfo) {
        handle(tbMerchantInfo);
        if (ObjectUtil.isNotEmpty(tbMerchantInfo.getIsExportAll()) && tbMerchantInfo.getIsExportAll() == 0) {
            startPage();
        }
        tbMerchantInfo.setIsExport(1);
        Integer integer = tbMerchantInfoService.selectTbMerchantInfoCount(tbMerchantInfo);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbMerchantInfo);
    }

    @Log(title = "门店/商户信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelMainUtil<TbMerchantInfo, TbMerchantAssistant> util = new ExcelMainUtil<TbMerchantInfo, TbMerchantAssistant>(TbMerchantInfo.class, TbMerchantAssistant.class);
        List<TbMerchantInfo> infos = util.importExcel(file.getInputStream(), "门店店员信息");
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        if(ObjectUtil.isEmpty(userDept)){
            return error("获取当前用户部门信息失败！" + getDataDeptId());
        }
        String message = tbMerchantInfoService.importTbMerchantInfo(infos, updateSupport, operName, userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbMerchantInfo> util = new ExcelUtil<TbMerchantInfo>(TbMerchantInfo.class);
        util.exportTemplateExcel(response, "门店商户信息数据");
    }

    /**
     * 获取门店/商户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbMerchantInfoService.selectTbMerchantInfoById(id));
    }

    public void getDeptInfo(TbMerchantInfo tbMerchantInfo) {
        if (ObjectUtil.isEmpty(tbMerchantInfo.getDeptId())) {
            tbMerchantInfo.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbMerchantInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbMerchantInfo.setCounty(split[1]);
            if (split.length >= 3) {
                tbMerchantInfo.setCountry(split[2]);
                if (split.length >= 4) {
                    tbMerchantInfo.setTown(split[3]);
                    if (split.length >= 5) {
                        tbMerchantInfo.setGridName(split[4]);
                    }
                }
            }
        }

        Long gridId = SecurityUtils.getGridId();
        // 网格员账号
        if (ObjectUtil.isNotEmpty(gridId)){
            if (StringUtils.isBlank(tbMerchantInfo.getGridName()) && gridId.longValue() == tbMerchantInfo.getGridId().longValue()){
                tbMerchantInfo.setGridName(SecurityUtils.getLoginUser().getUser().getDept().getDeptName());
            }
        }

        if (StringUtils.isNotBlank(tbMerchantInfo.getGridName()) && NumberUtil.isNumber(tbMerchantInfo.getGridName())){
            tbMerchantInfo.setSort(Integer.parseInt(tbMerchantInfo.getGridName()));
        }
    }

    /**
     * 新增门店/商户信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:add')")
    @Log(title = "门店/商户信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbMerchantInfo tbMerchantInfo) {
        tbMerchantInfo.setCreateBy(getUsername());
        getDeptInfo(tbMerchantInfo);
        if (tbMerchantInfoService.checkTbMerchantInfo(tbMerchantInfo)) {
            return error("数据已存在！");
        }
        return toAjax(tbMerchantInfoService.insertTbMerchantInfo(tbMerchantInfo));
    }

    /**
     * 修改门店/商户信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:edit')")
    @Log(title = "门店/商户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbMerchantInfo tbMerchantInfo) {
        tbMerchantInfo.setUpdateBy(getUsername());
        getDeptInfo(tbMerchantInfo);
        if (tbMerchantInfoService.checkTbMerchantInfo(tbMerchantInfo)) {
            return error("数据已存在！");
        }
        return toAjax(tbMerchantInfoService.updateTbMerchantInfo(tbMerchantInfo));
    }

    /**
     * 删除门店/商户信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantInfo:remove')")
    @Log(title = "门店/商户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbMerchantInfoService.deleteTbMerchantInfoByIds(ids));
    }
}
