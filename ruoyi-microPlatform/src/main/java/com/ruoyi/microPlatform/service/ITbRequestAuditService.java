package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbRequestAudit;

/**
 * 导出请求审核过程Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public interface ITbRequestAuditService 
{
    /**
     * 查询导出请求审核过程
     * 
     * @param id 导出请求审核过程主键
     * @return 导出请求审核过程
     */
    public TbRequestAudit selectTbRequestAuditById(Long id);

    /**
     * 校验导出请求审核过程是否存在
     *
     * @param tbRequestAudit 导出请求审核过程
     * @return 导出请求审核过程
     */
    public boolean checkTbRequestAudit(TbRequestAudit tbRequestAudit);

    /**
     * 查询导出请求审核过程列表
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 导出请求审核过程集合
     */
    public List<TbRequestAudit> selectTbRequestAuditList(TbRequestAudit tbRequestAudit);

    /**
     * 导入导出请求审核过程
     *
     * @param infos       导出请求审核过程列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbRequestAudit(List<TbRequestAudit> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    public int insertTbRequestAudit(TbRequestAudit tbRequestAudit);

    /**
     * 修改导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    public int updateTbRequestAudit(TbRequestAudit tbRequestAudit);

    /**
     * 批量删除导出请求审核过程
     * 
     * @param ids 需要删除的导出请求审核过程主键集合
     * @return 结果
     */
    public int deleteTbRequestAuditByIds(Long[] ids);

    /**
     * 删除导出请求审核过程信息
     * 
     * @param id 导出请求审核过程主键
     * @return 结果
     */
    public int deleteTbRequestAuditById(Long id);
}
