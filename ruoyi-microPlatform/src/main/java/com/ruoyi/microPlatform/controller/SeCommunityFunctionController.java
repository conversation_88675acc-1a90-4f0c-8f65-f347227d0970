package com.ruoyi.microPlatform.controller;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeCommunityFunction;
import com.ruoyi.microPlatform.service.ISeCommunityFunctionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 社区功能室Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/communityFunction")
public class SeCommunityFunctionController extends BaseController
{
    @Autowired
    private ISeCommunityFunctionService seCommunityFunctionService;

    /**
     * 查询社区功能室列表
     */
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(SeCommunityFunction seCommunityFunction)
    {
        startPage();
        try {
            handle(seCommunityFunction);
        } catch (Exception e) {
            throw new ServiceException("请登录2", HttpStatus.UNAUTHORIZED);
        }
        List<SeCommunityFunction> list = seCommunityFunctionService.selectSeCommunityFunctionList(seCommunityFunction);
        return getDataTable(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(SeCommunityFunction seCommunityFunction)
    {
        handle(seCommunityFunction);
        List<SeCommunityFunction> list = seCommunityFunctionService.selectSeCommunityFunctionList(seCommunityFunction);
        return success(list);
    }

    public void handle(SeCommunityFunction seCommunityFunction){
        if (ObjectUtil.isEmpty(seCommunityFunction.getDeptId())){
            if (!SecurityUtils.isAdmin(getUserId())){
                    seCommunityFunction.setDeptId(getDataDeptId());
            }
        }
    }


    /**
     * 导出社区功能室列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:export')")
    @Log(title = "社区功能室", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeCommunityFunction seCommunityFunction)
    {
        try {
            handle(seCommunityFunction);
        } catch (Exception e) {
            throw new ServiceException("请登录2", HttpStatus.UNAUTHORIZED);
        }
        List<SeCommunityFunction> list = seCommunityFunctionService.selectSeCommunityFunctionList(seCommunityFunction);
        ExcelUtil<SeCommunityFunction> util = new ExcelUtil<SeCommunityFunction>(SeCommunityFunction.class);
        util.exportExcel(response, list, "社区功能室数据");
    }

    @Log(title = "社区功能室", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SeCommunityFunction> util = new ExcelUtil<SeCommunityFunction>(SeCommunityFunction.class);
        List<SeCommunityFunction> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seCommunityFunctionService.importSeCommunityFunction(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SeCommunityFunction> util = new ExcelUtil<SeCommunityFunction>(SeCommunityFunction.class);
        util.exportTemplateExcel(response, "社区功能室数据");
    }

    /**
     * 获取社区功能室详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(seCommunityFunctionService.selectSeCommunityFunctionById(id));
    }

    public void getDeptInfo(SeCommunityFunction seCommunityFunction){
        if (ObjectUtil.isEmpty(seCommunityFunction.getDeptId())){
            seCommunityFunction.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(seCommunityFunction.getDeptId());
        if (ObjectUtil.isNotEmpty(dept)){
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2){
                seCommunityFunction.setCounty(split[1]);
                if (split.length >= 3){
                    seCommunityFunction.setCountry(split[2]);
                    if (split.length >= 4){
                        seCommunityFunction.setTown(split[3]);
                    }
                }
            }
        }
    }

    /**
     * 新增社区功能室
     */
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:add')")
    @Log(title = "社区功能室", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeCommunityFunction seCommunityFunction)
    {
        seCommunityFunction.setCreateBy(getUsername());
        getDeptInfo(seCommunityFunction);
        return toAjax(seCommunityFunctionService.insertSeCommunityFunction(seCommunityFunction));
    }

    /**
     * 修改社区功能室
     */
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:edit')")
    @Log(title = "社区功能室", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeCommunityFunction seCommunityFunction)
    {
        seCommunityFunction.setUpdateBy(getUsername());
        getDeptInfo(seCommunityFunction);
        return toAjax(seCommunityFunctionService.updateSeCommunityFunction(seCommunityFunction));
    }

    /**
     * 删除社区功能室
     */
    @PreAuthorize("@ss.hasPermi('sentiment:communityFunction:remove')")
    @Log(title = "社区功能室", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(seCommunityFunctionService.deleteSeCommunityFunctionByIds(ids));
    }
}
