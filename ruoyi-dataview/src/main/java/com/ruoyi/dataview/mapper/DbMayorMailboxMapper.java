package com.ruoyi.dataview.mapper;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbMayorMailbox;

/**
 * 市长信箱主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DbMayorMailboxMapper 
{
    /**
     * 查询市长信箱主
     * 
     * @param id 市长信箱主主键
     * @return 市长信箱主
     */
    public DbMayorMailbox selectDbMayorMailboxById(Long id);

    /**
     * 校验市长信箱主是否唯一
     *
     * @param dbMayorMailbox
     * @return 市长信箱主
     */
    public DbMayorMailbox checkDbMayorMailboxUnique(DbMayorMailbox dbMayorMailbox);

    /**
     * 查询市长信箱主列表
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 市长信箱主集合
     */
    public List<DbMayorMailbox> selectDbMayorMailboxList(DbMayorMailbox dbMayorMailbox);

    /**
     * 新增市长信箱主
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    public int insertDbMayorMailbox(DbMayorMailbox dbMayorMailbox);

    /**
     * 修改市长信箱主
     * 
     * @param dbMayorMailbox 市长信箱主
     * @return 结果
     */
    public int updateDbMayorMailbox(DbMayorMailbox dbMayorMailbox);

    /**
     * 删除市长信箱主
     * 
     * @param id 市长信箱主主键
     * @return 结果
     */
    public int deleteDbMayorMailboxById(Long id);

    /**
     * 批量删除市长信箱主
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbMayorMailboxByIds(Long[] ids);

    Integer dbMayorMailboxAllCount();

    List<CommonBaseCount> dbMayorMailboxCountGroupByCategory();
}
