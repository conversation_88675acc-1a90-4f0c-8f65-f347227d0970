package com.ruoyi.common.config;

import com.ruoyi.common.constant.Constants;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取项目相关配置
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "ruoyi")
public class RuoYiConfig
{
    /** 项目名称 */
    private String name;

    /** 版本 */
    private String version;

    /** 版权年份 */
    private String copyrightYear;

    /** 上传路径 */
    private static String profile;

    private static String videoPath;

    /** 获取地址开关 */
    private static boolean addressEnabled;

    /** 验证码类型 */
    private static String captchaType;

    public String getName()
    {
        return name;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public String getVersion()
    {
        return version;
    }

    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getCopyrightYear()
    {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear)
    {
        this.copyrightYear = copyrightYear;
    }

    public static String getProfile()
    {
        return profile;
    }

    public void setProfile(String profile)
    {
        RuoYiConfig.profile = profile;
    }

    public static String getVideoPath() {
        return videoPath;
    }

    public void setVideoPath(String videoPath) {
        RuoYiConfig.videoPath = videoPath;
    }

    public static boolean isAddressEnabled()
    {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled)
    {
        RuoYiConfig.addressEnabled = addressEnabled;
    }

    public static String getCaptchaType() {
        return captchaType;
    }

    public void setCaptchaType(String captchaType) {
        RuoYiConfig.captchaType = captchaType;
    }

    /**
     * 获取导入上传路径
     */
    public static String getImportPath()
    {
        return getProfile() + "/import";
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath()
    {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath()
    {
        return getProfile() + "/download/";
    }
    public static String getVideoDownPath()
    {
        return getProfile() + "/videoTrans/";
    }



    /**
     * 获取上传路径
     */
    public static String getUploadPath()
    {
        return getProfile() + "/upload";
    }

    public static String getUploadNoticePath()
    {
        return getProfile() + "/notice";
    }

    public static String getDeptPath()
    {
        return getProfile() + "/deptOpinion/";
    }

    public static String getDeptUploadPath(){
        return Constants.RESOURCE_PREFIX+"/deptOpinion/";
    }

    public static String getTemplatePath()
    {
        return getProfile() + "/template/";
    }

    public static String getPrivateKey(){
        return "MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEApoJgBPwNzZUn_bTNzW15l-hJLPoQHnCPgsNStzgj6umEgohEVu6DgZ-d8kXu2y5Qys-GBWnfMfdBgXVXP1muzwIDAQABAkEAlSa5_h0N_DZ8wR1BMJ-8yJ8RVVGWOL4ZP0fZgzetrNEW6X1qyaG_w6WOd5WXAmh4y-Sqr8bCtDJO7SKoAeZXQQIhAOPqKWuiHMMx318gwr9g2FNFYvxQyy8obzzfVi0PsOdhAiEAuwcbXLPo8umRTi0uJ__HOveqdWQdChMYL2gEHTyPtC8CIFeulYLYGlKqFDJnyySPYIogrqU9e_3Hg4waq--oErnhAiBBXCd0yy7SjUuJbYKe2eVPNzQr2DPJzIh8MLaXXQ2_YwIhAN3tFcT_MIAVk1B0DHqJQA8bmez-Q2DrNYbE4cXpFsYf";

    }

    public static String getPublicKey(){
        return "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKaCYAT8Dc2VJ_20zc1teZfoSSz6EB5wj4LDUrc4I-rphIKIRFbug4GfnfJF7tsuUMrPhgVp3zH3QYF1Vz9Zrs8CAwEAAQ";
    }
}
