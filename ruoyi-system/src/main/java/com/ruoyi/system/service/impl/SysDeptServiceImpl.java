package com.ruoyi.system.service.impl;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl implements ISysDeptService {
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    //    @PostConstruct
    public void initDept() {
        List<SysDept> deptList = deptMapper.selectDeptList(new SysDept() {{
            setDeptLevel(6);
        }});
        for (SysDept sysDept : deptList) {
            List<SysDept> deptList1 = deptMapper.selectList(sysDept.getCode());
            deptList1.stream().forEach(item -> {
                this.insertDept(new SysDept() {{
                    setDeptName(item.getPhone());
                    setCode(item.getEmail());
                    setParentId(sysDept.getDeptId());
                    setCounty(sysDept.getCounty());
                    setDeptLevel(7);
                    setDeptType("村社区");
                }});
            });
        }
    }

    @PostConstruct
    public void init() throws UnknownHostException {
        String hostAddress = InetAddress.getLocalHost().getHostAddress();
        if (("**************").equals(hostAddress) || hostAddress.startsWith("172.168.10.")) {
            this.initThreeDept();
            this.initDeptAncestorsCache();
        }
    }


    @Async("threadPoolTaskExecutor")
    public void initThreeDept() {
        List<String> oneDeptList = new ArrayList<>();
        Map<String, List<String>> mapChild = new LinkedHashMap<>();
        Map<String, Object> deptMap = new HashMap<>();
        // 一级部门
        List<SysDept> depts = deptMapper.selectDeptList(new SysDept() {{
            setAncestors("0,1");
            setStatus("0");
        }});

        for (SysDept item : depts) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("level", 1);
            deptMap.put(item.getDeptName(), jsonObject);
            oneDeptList.add(item.getDeptName());

            //二级数组
            List<String> childList = new ArrayList<>();
            List<SysDept> deptList = deptMapper.selectChildrenDeptByParentId(item.getDeptId());
            for (SysDept itemSecond : deptList) {
                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("level", 2);
                jsonObject2.put("parent", item.getDeptName());
                deptMap.put(itemSecond.getDeptName(), jsonObject2);

                childList.add(itemSecond.getDeptName());

                List<String> threeChildList = new ArrayList<>();
                List<SysDept> deptThreeList = deptMapper.selectChildrenDeptByParentId(itemSecond.getDeptId());
                for (SysDept itemThree : deptThreeList) {
                    JSONObject jsonObject3 = new JSONObject();
                    jsonObject3.put("level", 3);
                    jsonObject3.put("parent", itemSecond.getDeptName());
                    jsonObject3.put("grandpa", item.getDeptName());
                    deptMap.put(itemThree.getDeptName(), jsonObject3);

                    threeChildList.add(itemThree.getDeptName());

//                    List<String> fourChildList = new ArrayList<>();
//                    List<SysDept> deptFourList = deptMapper.selectChildrenDeptByParentId(itemThree.getDeptId());
//                    for(SysDept itemFour : deptFourList){
//                        JSONObject jsonObject4 = new JSONObject();
//                        jsonObject4.put("level",4);
//                        jsonObject4.put("parent",itemThree.getDeptName());
//                        jsonObject4.put("grandpa",item.getDeptName());
//                        deptMap.put(itemFour.getDeptName(),jsonObject4);
//
//                        fourChildList.add(itemFour.getDeptName());
//                    }
//                    mapChild.put(itemThree.getDeptName(), fourChildList);
                }
                mapChild.put(itemSecond.getDeptName(), threeChildList);
            }
            mapChild.put(item.getDeptName(), childList);
        }
        redisCache.setCacheMap("THREE_DEPT_MAP", deptMap);
        redisCache.setCacheObject("ONE_DEPT_LIST", oneDeptList);
        redisCache.setCacheObject("THREE_DEPT_LIST", mapChild);
    }

    @Async("threadPoolTaskExecutor")
    public void initDeptAncestorsCache() {
        System.err.println("要更新缓存数据啦啦啦");
        // 清除旧的，新增新的
        Collection<String> keys = redisCache.keys(CacheConstants.DEPT_ANCESTORS_KEY + "*");
        redisCache.deleteObject(keys);
        keys.clear();
        keys = redisCache.keys(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "*");
        redisCache.deleteObject(keys);
        keys.clear();

        List<SysDept> deptList = deptMapper.selectDeptAncestorsCache();
        deptList.stream().forEach(dept -> {
            redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + dept.getDeptId(), dept);
            redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + dept.getAncestorsName(), dept);

        });
    }

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    @Override
    public List<SysDept> selectTreeList(SysDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    @Override
    public List<SysDept> selectTownList(SysDept dept) {
        return deptMapper.selectTownList(dept);
    }

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelect> selectDeptTreeList(SysDept dept) {
        List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
        List<TreeSelect> treeSelects = buildDeptTreeSelect(depts);
        if (ObjectUtil.isNotEmpty(dept.getLinshi()) && dept.getLinshi() == 0 && treeSelects.size() == 2 && treeSelects.get(1).getLabel().equals("临时部门")){
            treeSelects.remove(1);
        }
        return treeSelects;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        for (SysDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDept dept) {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()) && StringUtils.isNotNull(deptId)) {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = SpringUtils.getAopProxy(this).selectDeptList(dept);
            if (StringUtils.isEmpty(depts)) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDept(SysDept dept) {
        if (dept.getParentId() != 0l) {
            SysDept info = deptMapper.selectDeptById(dept.getParentId());
            // 如果父节点不为正常状态,则不允许新增子节点
            if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
                throw new ServiceException("部门停用，不允许新增");
            }
            if (info.getDeptLevel().intValue() > 6 && dept.getType() == 1){
                throw new ServiceException("部门新增级别上限，不允许用户新增下级");
            }
            dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
            dept.setDeptLevel(info.getDeptLevel() + 1);
            dept.setDeptType(dept.getDeptLevel() == 5 ? "县市区" : dept.getDeptLevel() == 6 ? "乡镇/街道" : dept.getDeptLevel() == 7 ? "村/社区" : "");
        } else {
            dept.setAncestors("0");
            dept.setDeptLevel(4);
            dept.setDeptType("市");
        }
        int i = deptMapper.insertDept(dept);
        if (i > 0) {
            if (ObjectUtil.isNotEmpty(dept.getDeptId()) && ObjectUtil.isNotEmpty(dept.getDeptLevel()) && dept.getDeptLevel().intValue() < 8) {
                SysDept sysDept = deptMapper.selectDeptAncestorsByDeptId(dept.getDeptId());
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + dept.getDeptId(), sysDept);
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + dept.getAncestorsName(), sysDept);
                this.initThreeDept();
            }
        }
        return i;
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept) {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            dept.setDeptLevel(newParentDept.getDeptLevel() + 1);
            dept.setDeptType(dept.getDeptLevel() == 5 ? "县市区" : dept.getDeptLevel() == 6 ? "乡镇/街道" : dept.getDeptLevel() == 7 ? "村/社区" : "");
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }

        if (result > 0 && dept.getDeptLevel().intValue() < 8) {
            this.initDeptAncestorsCache();
            this.initThreeDept();
        }

        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        deptMapper.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId) {
        SysDept sysDept = deptMapper.selectDeptAncestorsByDeptId(deptId);
        int i = deptMapper.deleteDeptById(deptId);
        if (i > 0) {
            redisCache.deleteObject(CacheConstants.DEPT_ANCESTORS_KEY + deptId);
            redisCache.deleteObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + sysDept.getAncestorsName());

        }
        return i;
    }

    @Override
    public int deleteDeptByGridIds(Long[] gridIds) {
        return deptMapper.deleteDeptByGridIds(gridIds);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return getChildList(list, t).size() > 0;
    }

    @Override
    public List<TreeSelect> deptUserList(SysDept dept) {
        dept.setStatus("0");
        List<SysDept> deptList = buildDeptTree(deptMapper.selectDeptList(dept));
        deptList = this.recursion2(this.recursion1(deptList));
        return deptList.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    //递归遍历
    private List<SysDept> recursion1(List<SysDept> deptList) {
        deptList.stream().forEach(item -> {
            List<SysDept> children = new ArrayList<>();
            sysUserMapper.selectUserByDeptId(item.getDeptId()).forEach(data -> {
                children.add(new SysDept() {{
                    setDeptId(data.getUserId());
                    setDeptName(data.getNickName());
                    setChildren(new ArrayList<>());
                    setDisabled(false);
                    setUserName(data.getUserName());
                }});
            });
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                this.recursion1(item.getChildren());
                item.getChildren().addAll(children);
            } else {
                item.setChildren(children);
            }
        });
        return deptList;
    }

    //递归遍历设置禁用
    private List<SysDept> recursion2(List<SysDept> deptList) {
        deptList.forEach(item -> {
            if (!CollectionUtils.isEmpty(item.getChildren())) {
                item.setDisabled(false);
                this.recursion2(item.getChildren());
            }
        });
        return deptList;
    }

    /**
     * //查询当前部门及其所有子部门 ID
     * @param deptId
     * @return
     */
    @Override
    public List<Long> selectChildDeptIds(Long deptId) {
        return deptMapper.selectChildDeptIds(deptId);
    }
}
