package com.ruoyi.microPlatform.controller.bigdata;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.service.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网格治理 大数据相关api
 */
@RestController
@RequestMapping("/bigData/gridGovernance")
public class GridGovernanceController extends BaseController {

    /**
     * 社区活动
     */
    @Resource
    private ISeCommunityActivityService seCommunityActivityService;
    /**
     * 功能室
     */
    @Resource
    private ISeCommunityFunctionService seCommunityFunctionService;
    /**
     * 功能室预约
     */
    @Resource
    private ISeFunctionReservationService seFunctionReservationService;
    /**
     * 便民服务
     */
    @Resource
    private ISeConvenientPhoneService seConvenientPhoneService;
    /**
     * 居民吹哨
     */
    @Resource
    private ISePublicSentimentService sePublicSentimentService;
    /**
     * 志愿者
     */
    @Resource
    private ISeVolunteerRegisterService seVolunteerRegisterService;


    /**
     * 权限控制逻辑：处理非管理员用户的数据范围
     */
    public void handle(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                bigdataParam.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                    bigdataParam.setDeptId(getDataDeptId());
                } else {
                    if (bigdataParam.getDeptId() == 1) {
                        bigdataParam.setDeptId(null);
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(bigdataParam.getDeptId())) {
            getDeptInfo1(bigdataParam);
            bigdataParam.setDeptId(null);
        }
    }

    public void getDeptInfo1(BigdataParam bigdataParam) {
        SysDept dept = getDept(bigdataParam.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            bigdataParam.setCounty(split[1]);
            if (split.length >= 3) {
                bigdataParam.setCountry(split[2]);
                if (split.length >= 4) {
                    bigdataParam.setTown(split[3]);
                    if (split.length >= 5) {
                        bigdataParam.setGridName(split[4]);
                    }
                }
            }
        }
    }

    /**
     * 查询社区活动列表
     */
    @GetMapping("/activity/listPage")
    public TableDataInfo activityListPage(SeCommunityActivity bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeCommunityActivity seCommunityActivity = new SeCommunityActivity();
        seCommunityActivity.setDeptId(bigdataParam.getDeptId());
        seCommunityActivity.setCounty(bigdataParam.getCounty());
        startPage();
        List<SeCommunityActivity> list = seCommunityActivityService.selectSeCommunityActivityList(seCommunityActivity);
        return getDataTable(list);
    }

    /**
     * 查询功能室分页列表
     */
    @GetMapping("/function/listPage")
    public TableDataInfo functionListPage(BigdataParam bigdataParam) {
        startPage();
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeCommunityFunction seCommunityFunction = new SeCommunityFunction();
        seCommunityFunction.setDeptId(bigdataParam.getDeptId());
        seCommunityFunction.setCounty(bigdataParam.getCounty());
        List<SeCommunityFunction> list = seCommunityFunctionService.selectSeCommunityFunctionList(seCommunityFunction);
        return getDataTable(list);
    }

    /**
     * 查询功能室列表
     */
    @GetMapping("/function/list")
    public AjaxResult functionList(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeCommunityFunction seCommunityFunction = new SeCommunityFunction();
        seCommunityFunction.setDeptId(bigdataParam.getDeptId());
        seCommunityFunction.setCounty(bigdataParam.getCounty());
        List<SeCommunityFunction> list = seCommunityFunctionService.selectSeCommunityFunctionList(seCommunityFunction);
        return success(list);
    }

    /**
     * 功能室数量统计
     */
    @GetMapping("/function/countStatistics")
    public AjaxResult functionCountStatistics(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(seCommunityFunctionService.getCommunityFunctionCount(bigdataParam));
    }


    /**
     * 查询功能室预约分页列表
     */
    @GetMapping("/seFunctionReservation/listPage")
    public TableDataInfo seVolunteerRegisterListPage(BigdataParam bigdataParam) {
        startPage();
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeFunctionReservation seFunctionReservation = new SeFunctionReservation();
        seFunctionReservation.setDeptId(bigdataParam.getDeptId());
        seFunctionReservation.setCounty(bigdataParam.getCounty());
        List<SeFunctionReservation> list = seFunctionReservationService.selectSeFunctionReservationList(seFunctionReservation);
        return getDataTable(list);
    }


    /**
     * 查询便民服务分页
     */
    @GetMapping("/convenientPhone/listPage")
    public TableDataInfo convenientPhoneListPage(BigdataParam bigdataParam) {
        startPage();
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeConvenientPhone seConvenientPhone = new SeConvenientPhone();
        seConvenientPhone.setDeptId(bigdataParam.getDeptId());
        seConvenientPhone.setCounty(bigdataParam.getCounty());
        List<SeConvenientPhone> list = seConvenientPhoneService.selectSeConvenientPhoneList(seConvenientPhone);
        return getDataTable(list);
    }

    /**
     * 查询便民服务列表
     */
    @GetMapping("/convenientPhone/list")
    public AjaxResult convenientPhoneList(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        SeConvenientPhone seConvenientPhone = new SeConvenientPhone();
        seConvenientPhone.setDeptId(bigdataParam.getDeptId());
        seConvenientPhone.setCounty(bigdataParam.getCounty());
        List<SeConvenientPhone> list = seConvenientPhoneService.selectSeConvenientPhoneList(seConvenientPhone);
        return success(list);
    }


    /**
     * 查询居民吹哨数量
     */
    @GetMapping("/sePublicSentiment/count")
    public AjaxResult sePublicSentimentCount(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(new HashMap<String, Integer>() {{
            put("total", sePublicSentimentService.getResidentWhistleCountByBigData(bigdataParam));
        }});
    }

    /**
     * 查询居民吹哨分页列表
     */
    @GetMapping("/sePublicSentiment/listPage")
    public TableDataInfo sePublicSentimentListPage(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        handle(bigdataParam);
        SePublicSentiment sePublicSentiment = new SePublicSentiment();
        sePublicSentiment.setDeptId(bigdataParam.getDeptId());
        sePublicSentiment.setCounty(bigdataParam.getCounty());
        startPage();
        List<SePublicSentiment> list = sePublicSentimentService.selectSePublicSentimentList(sePublicSentiment);
        return getDataTable(list);
    }

    @Resource
    private ILargeScreenService largeScreenService;

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 数量
     */
    @GetMapping("/tb/countMap")
    public AjaxResult getStatisticsByJurisdiction(BigdataParam bigdataParam) {
        handle(bigdataParam);
        Map<String, Integer> map = largeScreenService.getStatisticsByJurisdiction(bigdataParam);
        return success(map);
    }

    @Resource
    private ITbGridInspectService tbGridInspectService;
    @Resource
    private ITbMerchantInspectService tbMerchantInspectService;
    @Resource
    private ITbResidentContactService tbResidentContactService;


    /**
     * 网格巡查 数量
     */
    @GetMapping("/tb/gridInspectCount")
    public AjaxResult gridInspectCount(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbGridInspectService.gridInspectCount(bigdataParam));
    }

    /**
     * 网格巡查月度统计
     */
    @GetMapping("/tb/gridInspectGroupMonth")
    public AjaxResult gridInspectGroupMonth(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbGridInspectService.gridInspectGroupMonth(bigdataParam));
    }

    /**
     * 门店巡查月度数量
     */
    @GetMapping("/tb/tbMerchantInspectCount")
    public AjaxResult tbMerchantInspectCount(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbMerchantInspectService.tbMerchantInspectCount(bigdataParam));
    }

    /**
     * 门店巡查月度统计
     */
    @GetMapping("/tb/tbMerchantInspectGroupMonth")
    public AjaxResult tbMerchantInspectGroupMonth(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbMerchantInspectService.merchantInspectGroupMonth(bigdataParam));
    }

    /**
     * 入户走访数量
     */
    @GetMapping("/tb/residentContactCount")
    public AjaxResult residentContactCount(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbResidentContactService.residentContactCount(bigdataParam));
    }

    /**
     * 入户走访月度统计
     */
    @GetMapping("/tb/residentContactGroupMonth")
    public AjaxResult residentContactGroupMonth(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(tbResidentContactService.residentContactGroupMonth(bigdataParam));
    }


    /**
     * 志愿者数量
     */
    @GetMapping("/tb/iSeVolunteerRegisterCount")
    public AjaxResult iSeVolunteerRegisterCount(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(seVolunteerRegisterService.selectCount(bigdataParam));
    }


    /**
     * 志愿者数量 根据下级分组
     */
    @GetMapping("/tb/iSeVolunteerRegisterCountByDept")
    public AjaxResult iSeVolunteerRegisterCountByDept(BigdataParam bigdataParam) {
        if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
            bigdataParam.setDeptId(getDataDeptId());
        }
        return success(seVolunteerRegisterService.iSeVolunteerRegisterCountByDept(bigdataParam));
    }


    /**
     * 志愿者分页列表
     */
    @GetMapping("/tb/iSeVolunteerRegisterListPage")
    public TableDataInfo iSeVolunteerRegisterListPage(SeVolunteerRegister seVolunteerRegister) {
        if (ObjectUtil.isEmpty(seVolunteerRegister.getDeptId())) {
            seVolunteerRegister.setDeptId(getDataDeptId());
        }
        startPage();
        List<SeVolunteerRegister> list = seVolunteerRegisterService.selectSeVolunteerRegisterList(seVolunteerRegister);
        return getDataTable(list);
    }

}
