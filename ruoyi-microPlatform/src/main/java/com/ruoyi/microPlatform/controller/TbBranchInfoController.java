package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.service.ITbGridInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbBranchInfo;
import com.ruoyi.microPlatform.service.ITbBranchInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机关企事业单位（支部）Controller
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/microPlatform/branchInfo")
public class TbBranchInfoController extends BaseController
{
    @Autowired
    private ITbBranchInfoService tbBranchInfoService;


    @Autowired
    private ITbGridInfoService tbGridInfoService;

    /**
     * 查询机关企事业单位（支部）列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbBranchInfo tbBranchInfo)
    {
        startPage();
        List<TbBranchInfo> list = tbBranchInfoService.selectTbBranchInfoList(tbBranchInfo);
        return getDataTable(list);
    }

    /**
     * 查询机关企事业单位（支部）列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:list')")
    @GetMapping("/listByGrid")
    public TableDataInfo listByGrid(TbBranchInfo tbBranchInfo)
    {
        startPage();
        TbGridInfo tbGridInfo = new TbGridInfo();
        tbGridInfo.setDeptId(tbBranchInfo.getDeptId());
        tbGridInfo.setId(tbBranchInfo.getGridId());
        handle(tbGridInfo);
        tbGridInfo.setBranchName(tbBranchInfo.getBranchName());
        tbGridInfo.setSecretaryName(tbBranchInfo.getSecretaryName());
        tbGridInfo.setSecretaryPhone(tbBranchInfo.getPhone());
        List<TbGridInfo> gridInfos = tbGridInfoService.selectTbGridInfoList(tbGridInfo);
        List<TbGridInfo> list = tbBranchInfoService.selectTbBranchInfoListByGrid(tbBranchInfo,gridInfos);
        return getDataTable3(list, gridInfos);
    }

    public void handle(TbGridInfo tbGridInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbGridInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(tbGridInfo.getDeptId())) {
                    tbGridInfo.setDeptId(getDataDeptId());
                }
            }
        }
    }



    /**
     * 导出机关企事业单位（支部）列表
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:export')")
    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbBranchInfo tbBranchInfo)
    {
        List<TbBranchInfo> list = tbBranchInfoService.selectTbBranchInfoList(tbBranchInfo);
        ExcelUtil<TbBranchInfo> util = new ExcelUtil<TbBranchInfo>(TbBranchInfo.class);
        util.exportExcel(response, list, "机关企事业单位（支部）数据");
    }

    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbBranchInfo> util = new ExcelUtil<TbBranchInfo>(TbBranchInfo.class);
        List<TbBranchInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbBranchInfoService.importTbBranchInfo(infos, updateSupport, operName,userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbBranchInfo> util = new ExcelUtil<TbBranchInfo>(TbBranchInfo.class);
        util.exportTemplateExcel(response, "机关企事业单位（支部）数据");
    }

    /**
     * 获取机关企事业单位（支部）详细信息
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbBranchInfoService.selectTbBranchInfoById(id));
    }

    /**
     * 新增机关企事业单位（支部）
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:add')")
    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbBranchInfo tbBranchInfo)
    {
        tbBranchInfo.setCreateBy(getUsername());
        if (tbBranchInfoService.checkTbBranchInfo(tbBranchInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbBranchInfoService.insertTbBranchInfo(tbBranchInfo));
    }

    /**
     * 修改机关企事业单位（支部）
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:edit')")
    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbBranchInfo tbBranchInfo)
    {
        tbBranchInfo.setUpdateBy(getUsername());
        if (tbBranchInfoService.checkTbBranchInfo(tbBranchInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbBranchInfoService.updateTbBranchInfo(tbBranchInfo));
    }

    /**
     * 删除机关企事业单位（支部）
     */
    /*@PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:remove')")
    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbBranchInfoService.deleteTbBranchInfoByIds(ids));
    }*/



    /**
     * 删除机关企事业单位（支部）
     */
    @PreAuthorize("@ss.hasPermi('microPlatform:branchInfo:remove')")
    @Log(title = "机关企事业单位（支部）", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    @RepeatSubmit
    public AjaxResult remove(TbBranchGridInfo tbBranchGridInfo)
    {
        return toAjax(tbBranchInfoService.deleteTbBranchInfoById(tbBranchGridInfo));
    }

}
