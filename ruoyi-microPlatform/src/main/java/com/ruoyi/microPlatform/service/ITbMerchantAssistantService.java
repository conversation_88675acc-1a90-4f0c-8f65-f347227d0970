package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;

/**
 * 门店店员Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbMerchantAssistantService 
{
    /**
     * 查询门店店员
     * 
     * @param id 门店店员主键
     * @return 门店店员
     */
    public TbMerchantAssistant selectTbMerchantAssistantById(Long id);

    /**
     * 校验门店店员是否存在
     *
     * @param tbMerchantAssistant 门店店员
     * @return 门店店员
     */
    public boolean checkTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 查询门店店员列表
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 门店店员集合
     */
    public List<TbMerchantAssistant> selectTbMerchantAssistantList(TbMerchantAssistant tbMerchantAssistant);

    public Integer selectTbMerchantAssistantCount(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 导入门店店员
     *
     * @param infos       门店店员列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantAssistant(List<TbMerchantAssistant> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    public int insertTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 修改门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    public int updateTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 批量删除门店店员
     * 
     * @param ids 需要删除的门店店员主键集合
     * @return 结果
     */
    public int deleteTbMerchantAssistantByIds(Long[] ids);

    /**
     * 删除门店店员信息
     * 
     * @param id 门店店员主键
     * @return 结果
     */
    public int deleteTbMerchantAssistantById(Long id);
}
