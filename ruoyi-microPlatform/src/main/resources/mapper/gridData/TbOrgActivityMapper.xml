<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbOrgActivityMapper">

    <resultMap type="TbOrgActivity" id="TbOrgActivityResult">
            <result property="id"    column="id"    />
            <result property="orgId"    column="org_id"    />
            <result property="actDate"    column="act_date"    />
            <result property="actContent"    column="act_content"    />
            <result property="actNumber"    column="act_number"    />
    </resultMap>

    <sql id="selectTbOrgActivityVo">
        select id, org_id, act_date, act_content, act_number from tb_org_activity
    </sql>

    <select id="selectTbOrgActivityList" parameterType="TbOrgActivity" resultMap="TbOrgActivityResult">
        <include refid="selectTbOrgActivityVo"/>
        <where>
                        <if test="orgId != null "> and org_id = #{orgId}</if>
                        <if test="actDate != null "> and act_date = #{actDate}</if>
                        <if test="actContent != null  and actContent != ''"> and act_content = #{actContent}</if>
                        <if test="actNumber != null "> and act_number = #{actNumber}</if>
        </where>
    </select>

    <select id="selectTbOrgActivityById" parameterType="Long" resultMap="TbOrgActivityResult">
            <include refid="selectTbOrgActivityVo"/>
            where id = #{id}
    </select>

    <select id="checkTbOrgActivityUnique" parameterType="TbOrgActivity" resultMap="TbOrgActivityResult">
        select id from tb_org_activity where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbOrgActivity" parameterType="TbOrgActivity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_org_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="orgId != null">org_id,</if>
                    <if test="actDate != null">act_date,</if>
                    <if test="actContent != null">act_content,</if>
                    <if test="actNumber != null">act_number,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="orgId != null">#{orgId},</if>
                    <if test="actDate != null">#{actDate},</if>
                    <if test="actContent != null">#{actContent},</if>
                    <if test="actNumber != null">#{actNumber},</if>
        </trim>
    </insert>

    <update id="updateTbOrgActivity" parameterType="TbOrgActivity">
        update tb_org_activity
        <trim prefix="SET" suffixOverrides=",">
                    <if test="orgId != null">org_id = #{orgId},</if>
                    <if test="actDate != null">act_date = #{actDate},</if>
                    <if test="actContent != null">act_content = #{actContent},</if>
                    <if test="actNumber != null">act_number = #{actNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbOrgActivityById" parameterType="Long">
        delete from tb_org_activity where id = #{id}
    </delete>

    <delete id="deleteTbOrgActivityByIds" parameterType="String">
        delete from tb_org_activity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>