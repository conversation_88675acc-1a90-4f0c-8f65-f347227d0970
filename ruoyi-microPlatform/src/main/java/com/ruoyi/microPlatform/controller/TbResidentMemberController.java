package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 居民住户成员信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/residentMember")
public class TbResidentMemberController extends BaseController {
    @Autowired
    private ITbResidentMemberService tbResidentMemberService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询居民住户成员信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbResidentMember tbResidentMember,@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) {
        //startPage();

        // 2. 查询分页 id 列表（偏移量计算：从第几条开始）
        int offset = (pageNum - 1) * pageSize;
        tbResidentMember.setOffset(offset);
        tbResidentMember.setPageSize(pageSize);
        handle(tbResidentMember);

        return tbResidentMemberService.selectTbResidentMemberList(tbResidentMember);
    }

    public void handle(TbResidentMember tbResidentMember) {
        if (!SecurityUtils.isAdmin(getUserId()) && ObjectUtil.isEmpty(tbResidentMember.getResidentId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbResidentMember.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // // 只能查看相应部门下的小区
                if (ObjectUtil.isEmpty(tbResidentMember.getDeptId()) && ObjectUtil.isEmpty(tbResidentMember.getGridId())) {
                    tbResidentMember.setDeptId(getDataDeptId());
                }
            }
        }

        if (ObjectUtil.isNotEmpty(tbResidentMember.getDeptId())){
            getDeptInfo(tbResidentMember);
            tbResidentMember.setDeptId(null);
        }
    }

    public void getDeptInfo(TbResidentMember tbResidentMember) {
        SysDept dept = getDept(tbResidentMember.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            tbResidentMember.setCounty(split[1]);
            if (split.length >= 3){
                tbResidentMember.setCountry(split[2]);
                if (split.length >= 4){
                    tbResidentMember.setTown(split[3]);
                    if (split.length >= 5){
                        tbResidentMember.setGridName(split[4]);
                    }
                }
            }
        }
    }

    /**
     * 导出居民住户成员信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:export')")
    @Log(title = "居民住户成员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbResidentMember tbResidentMember) {
        handle(tbResidentMember);
        if (ObjectUtil.isNotEmpty(tbResidentMember.getIsExportAll()) && tbResidentMember.getIsExportAll() == 0) {
            //startPage(pageNum, pageSize);

            // 2. 查询分页 id 列表（偏移量计算：从第几条开始）
            int offset = (pageNum - 1) * pageSize;
            tbResidentMember.setOffset(offset);
            tbResidentMember.setPageSize(pageSize);
        }
        tbResidentMember.setIsExport(1);
        TableDataInfo tableDataInfo = tbResidentMemberService.selectTbResidentMemberList(tbResidentMember);
        List<TbResidentMember> list = (List<TbResidentMember>) tableDataInfo.getRows();
        ExcelUtil<TbResidentMember> util = new ExcelUtil<TbResidentMember>(TbResidentMember.class);
        util.exportExcel(response, list, "居民住户成员信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbResidentMember tbResidentMember) {
        handle(tbResidentMember);
        if (ObjectUtil.isNotEmpty(tbResidentMember.getIsExportAll()) && tbResidentMember.getIsExportAll() == 0) {
            startPage();
        }
        Integer integer = tbResidentMemberService.selectTbResidentMemberCount(tbResidentMember);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbResidentMember);
    }

    @Log(title = "居民住户成员信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbResidentMember> util = new ExcelUtil<TbResidentMember>(TbResidentMember.class);
        List<TbResidentMember> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbResidentMemberService.importTbResidentMember(infos, updateSupport, getDeptId() + "-" + operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbResidentMember> util = new ExcelUtil<TbResidentMember>(TbResidentMember.class);
        util.exportTemplateExcel(response, "居民住户成员信息数据");
    }

    /**
     * 获取居民住户成员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbResidentMemberService.selectTbResidentMemberById(id));
    }

    /**
     * 新增居民住户成员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:add')")
    @Log(title = "居民住户成员信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit(interval = 6000)
    public AjaxResult add(@RequestBody TbResidentMember tbResidentMember) {
        tbResidentMember.setCreateBy(getDeptId() + "-" + (StringUtils.isNotBlank(tbResidentMember.getCreateBy()) ? tbResidentMember.getCreateBy() : "") + getUsername());
        if (tbResidentMemberService.checkTbResidentMember(tbResidentMember)) {
            return error("数据已存在！");
        }
        return toAjax(tbResidentMemberService.insertTbResidentMember(tbResidentMember));
    }

    /**
     * 修改居民住户成员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:edit')")
    @Log(title = "居民住户成员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit(interval = 6000)
    public AjaxResult edit(@RequestBody TbResidentMember tbResidentMember) {
        tbResidentMember.setUpdateBy(getDeptId() + "-" + (StringUtils.isNotBlank(tbResidentMember.getUpdateBy()) ? tbResidentMember.getUpdateBy() : "") + getUsername());
        if (tbResidentMemberService.checkTbResidentMember(tbResidentMember)) {
            return error("数据已存在！");
        }
        return toAjax(tbResidentMemberService.updateTbResidentMember(tbResidentMember));
    }

    /**
     * 删除居民住户成员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:residentMember:remove')")
    @Log(title = "居民住户成员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbResidentMemberService.deleteTbResidentMemberByIds(ids));
    }
}
