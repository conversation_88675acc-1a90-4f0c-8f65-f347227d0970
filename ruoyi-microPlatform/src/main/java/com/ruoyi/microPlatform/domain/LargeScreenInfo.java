package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 大屏查询条件参数
 */
public class LargeScreenInfo extends BaseEntity {

    /**
     * 所属部门
     */
    private Long deptId;


    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）", needMerge = true, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /**
     * 所属街道/乡镇
     */
    @Excel(name = "所属街道/乡镇", needMerge = true, width = 18)
    private String country;

    /**
     * 所属村/社区
     */
    @Excel(name = "所属村/社区", needMerge = true, width = 18)
    private String town;

    /**
     * 所属网格ID
     */
//    @Excel(name = "所属网格ID", needMerge = true)
    private Long gridId;

    /**
     * 网格编码
     */
    private String gridName;

    //小区id
    private Long communityId;

    //小区名称
    private String communityName;

    //住户id
    private Long residentId;

    //人员标签
    private String label;

    /**
     *
     * 查询类型 ： 网格、小区、门店商户、住户、住户成员 等
     */
    private String jurisdicType;

    /**
     * 网格数据count
     */
    private Integer gridInfo;
    /**
     * 小区数据count
     */
    private Integer communityInfo;
    /**
     * 门店商户数据count
     */
    private Integer merchantInfo;
    /**
     * 住户数据count
     */
    private Integer communityResident;
    /**
     * 住户成员数据count
     */
    private Integer residentMember;


    //是否获取空间区域
    private Boolean space;

    //开始日期
    private String startDate;

    //结束日期
    private String endDate;


    public Long getCommunityId() {
        return communityId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }


    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public Long getResidentId() {
        return residentId;
    }

    public void setResidentId(Long residentId) {
        this.residentId = residentId;
    }


    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Boolean getSpace() {
        return space;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public void setSpace(Boolean space) {
        this.space = space;
    }

    public Integer getGridInfo() {
        return gridInfo;
    }

    public void setGridInfo(Integer gridInfo) {
        this.gridInfo = gridInfo;
    }

    public Integer getCommunityInfo() {
        return communityInfo;
    }

    public void setCommunityInfo(Integer communityInfo) {
        this.communityInfo = communityInfo;
    }

    public Integer getMerchantInfo() {
        return merchantInfo;
    }

    public void setMerchantInfo(Integer merchantInfo) {
        this.merchantInfo = merchantInfo;
    }

    public Integer getCommunityResident() {
        return communityResident;
    }

    public void setCommunityResident(Integer communityResident) {
        this.communityResident = communityResident;
    }

    public Integer getResidentMember() {
        return residentMember;
    }

    public void setResidentMember(Integer residentMember) {
        this.residentMember = residentMember;
    }

    public String getJurisdicType() {
        return jurisdicType;
    }

    public void setJurisdicType(String jurisdicType) {
        this.jurisdicType = jurisdicType;
    }
}

