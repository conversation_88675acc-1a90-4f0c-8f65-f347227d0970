package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbCommunityEstate;
import com.ruoyi.microPlatform.service.ITbCommunityEstateService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区物业员Controller
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@RestController
@RequestMapping("/gridData/communityEstate")
public class TbCommunityEstateController extends BaseController
{
    @Autowired
    private ITbCommunityEstateService tbCommunityEstateService;

    /**
     * 查询小区物业员列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbCommunityEstate tbCommunityEstate)
    {
        startPage();
        List<TbCommunityEstate> list = tbCommunityEstateService.selectTbCommunityEstateList(tbCommunityEstate);
        return getDataTable(list);
    }

    /**
     * 导出小区物业员列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:export')")
    @Log(title = "小区物业员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbCommunityEstate tbCommunityEstate)
    {
        List<TbCommunityEstate> list = tbCommunityEstateService.selectTbCommunityEstateList(tbCommunityEstate);
        ExcelUtil<TbCommunityEstate> util = new ExcelUtil<TbCommunityEstate>(TbCommunityEstate.class);
        util.exportExcel(response, list, "小区物业员数据");
    }

    @Log(title = "小区物业员", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbCommunityEstate> util = new ExcelUtil<TbCommunityEstate>(TbCommunityEstate.class);
        List<TbCommunityEstate> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbCommunityEstateService.importTbCommunityEstate(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbCommunityEstate> util = new ExcelUtil<TbCommunityEstate>(TbCommunityEstate.class);
        util.exportTemplateExcel(response, "小区物业员数据");
    }

    /**
     * 获取小区物业员详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbCommunityEstateService.selectTbCommunityEstateById(id));
    }

    /**
     * 新增小区物业员
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:add')")
    @Log(title = "小区物业员", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbCommunityEstate tbCommunityEstate)
    {
        tbCommunityEstate.setCreateBy(getUsername());
        if (tbCommunityEstateService.checkTbCommunityEstate(tbCommunityEstate)){
            return error("数据已存在！");
        }
        return toAjax(tbCommunityEstateService.insertTbCommunityEstate(tbCommunityEstate));
    }

    /**
     * 修改小区物业员
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:edit')")
    @Log(title = "小区物业员", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbCommunityEstate tbCommunityEstate)
    {
        tbCommunityEstate.setUpdateBy(getUsername());
        if (tbCommunityEstateService.checkTbCommunityEstate(tbCommunityEstate)){
            return error("数据已存在！");
        }
        return toAjax(tbCommunityEstateService.updateTbCommunityEstate(tbCommunityEstate));
    }

    /**
     * 删除小区物业员
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityEstate:remove')")
    @Log(title = "小区物业员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbCommunityEstateService.deleteTbCommunityEstateByIds(ids));
    }
}
