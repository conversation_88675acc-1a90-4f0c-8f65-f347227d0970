<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbGridUserMapper">

    <resultMap type="TbGridUser" id="TbGridUserResult">
        <result property="id" column="id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="idPhoto" column="id_photo"/>
        <result property="postDesc" column="post_desc"/>
        <result property="status" column="status"/>
        <result property="postDetail" column="post_detail"/>
        <result property="postStr" column="post_str"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="idCard" column="id_card"/>
        <result property="gridArr" column="grid_arr"/>
    </resultMap>

    <sql id="selectTbGridUserVo">
        select id, county, country, town, grid_name, grid_id, dept_id, user_id, name, phone, id_photo,grid_arr, post_desc,id_card, status, post_detail, post_str, create_by, create_time, update_by, update_time from tb_grid_user
    </sql>
    <sql id="queryTbGridUserVo">
        <where>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_user.dept_id))</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="idPhoto != null  and idPhoto != ''">and id_photo = #{idPhoto}</if>
            <if test="postDesc != null  and postDesc != ''">and post_desc = #{postDesc}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="postDetail != null  and postDetail != ''">and post_detail = #{postDetail}</if>
            <if test="postStr != null  and postStr != ''">and post_str = #{postStr}</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
        </where>
    </sql>

    <select id="selectTbGridUserList" parameterType="TbGridUser" resultMap="TbGridUserResult">
        <include refid="selectTbGridUserVo"/>
        <include refid="queryTbGridUserVo"/>
        order by grid_name asc, id desc
    </select>

    <select id="selectTbGridUserCount" parameterType="TbGridUser" resultType="Long">
        select id from tb_grid_user
        <include refid="queryTbGridUserVo"/>
    </select>

    <select id="selectTbGridUserById" parameterType="Long" resultMap="TbGridUserResult">
        <include refid="selectTbGridUserVo"/>
        where id = #{id}
    </select>

    <select id="checkTbGridUserUnique" parameterType="TbGridUser" resultMap="TbGridUserResult">
        select id, user_id from tb_grid_user where name = #{name} and status = '0'
        <if test="gridId != null">
            and grid_id = #{gridId}
        </if>
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <select id="checkGridUserBig" parameterType="TbGridUser" resultMap="TbGridUserResult">
        select id from tb_grid_user where post_str = '网格长' and grid_id = #{gridId}
        <if test="id != null">
            and id != #{id}
        </if>
        limit 1
    </select>

    <insert id="insertTbGridUser" parameterType="TbGridUser" useGeneratedKeys="true" keyProperty="id">
        insert into tb_grid_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="idPhoto != null">id_photo,</if>
            <if test="postDesc != null">post_desc,</if>
            <if test="status != null">status,</if>
            <if test="postDetail != null">post_detail,</if>
            <if test="postStr != null">post_str,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="idCard != null">id_card,</if>
            <if test="gridArr != null">grid_arr,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idPhoto != null">#{idPhoto},</if>
            <if test="postDesc != null">#{postDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="postDetail != null">#{postDetail},</if>
            <if test="postStr != null">#{postStr},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="gridArr != null">#{gridArr},</if>
        </trim>
    </insert>

    <update id="updateTbGridUser" parameterType="TbGridUser">
        update tb_grid_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idPhoto != null">id_photo = #{idPhoto},</if>
            <if test="postDesc != null">post_desc = #{postDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="postDetail != null">post_detail = #{postDetail},</if>
            <if test="postStr != null">post_str = #{postStr},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="gridArr != null">grid_arr = #{gridArr},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbGridUserById" parameterType="Long">
        delete from tb_grid_user where id = #{id}
    </delete>

    <delete id="deleteTbGridUserByIds" parameterType="String">
        delete from tb_grid_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbGridUserList1" parameterType="TbGridUser" resultMap="TbGridUserResult">
        <include refid="selectTbGridUserVo"/>
        <include refid="queryTbGridUserVo"/>
        ORDER BY
            CASE WHEN post_str = '网格长' THEN 1 -- 网格长排在最前
            ELSE 2 -- 其他网格员排在后面
            END asc
    </select>

    <select id="selectGridIdByPhone" parameterType="string" resultType="long">
        select distinct grid_id from tb_grid_user where phone = #{phone}
    </select>
    <select id="selectTbGridUserCountByDept" resultType="java.lang.Integer">
        select count(*) from tb_grid_user
        where 1 = 1
        <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_user.dept_id))</if>

    </select>
</mapper>