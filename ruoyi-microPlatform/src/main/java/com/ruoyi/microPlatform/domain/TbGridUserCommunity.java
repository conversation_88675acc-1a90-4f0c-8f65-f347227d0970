package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 网格员关联小区对象 tb_grid_user_community
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbGridUserCommunity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 网格员ID */
    @Excel(name = "网格员ID")
    private Long gridUserId;

    /** 管理网小区ID */
    @Excel(name = "管理网小区ID")
    private Long communityId;


    //批量新增id
    private List<Long> idList;



    //小区信息表
    private TbCommunityInfo tbCommunityInfo;

    //网格员信息对象
    private TbGridUser tbGridUser;



    public List<Long> getIdList() {
        return idList;
    }

    public void setIdList(List<Long> idList) {
        this.idList = idList;
    }

    public TbGridUser getTbGridUser() {
        return tbGridUser;
    }

    public void setTbGridUser(TbGridUser tbGridUser) {
        this.tbGridUser = tbGridUser;
    }

    public TbCommunityInfo getTbCommunityInfo() {
        return tbCommunityInfo;
    }

    public void setTbCommunityInfo(TbCommunityInfo tbCommunityInfo) {
        this.tbCommunityInfo = tbCommunityInfo;
    }



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setGridUserId(Long gridUserId) 
    {
        this.gridUserId = gridUserId;
    }

    public Long getGridUserId() 
    {
        return gridUserId;
    }
    public void setCommunityId(Long communityId) 
    {
        this.communityId = communityId;
    }

    public Long getCommunityId() 
    {
        return communityId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("gridUserId", getGridUserId())
            .append("communityId", getCommunityId())
            .toString();
    }
}
