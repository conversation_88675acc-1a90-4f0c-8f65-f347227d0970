<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.PbPartyMemberMapper">

    <resultMap id="PbPartyMemberMap" type="PbPartyMember">
        <result property="id" column="id"/>
        <result property="memberCode" column="member_code"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="gender" column="gender"/>
        <result property="nation" column="nation"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="educationLevel" column="education_level"/>
        <result property="politicalStatus" column="political_status"/>
        <result property="joinPartyDate" column="join_party_date"/>
        <result property="status" column="status"/>
        <result property="branchId" column="branch_id"/>
        <result property="branchName" column="branch_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="activityCount" column="activity_count"/>
        <result property="lastActivityTime" column="last_activity_time"/>
        <result property="phone" column="phone"/>
        <result property="address" column="address"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectVo">
        select id, member_code, name, id_card, gender, nation, birthday, age,
               education_level, political_status, join_party_date, status,
               branch_id, branch_name, dept_id, county, country, town,
               activity_count, last_activity_time, phone, address,
               create_by, create_time, update_by, update_time
        from pb_party_member
    </sql>

    <sql id="queryWhere">
        <where>
            <if test="memberCode != null and memberCode != ''">and member_code = #{memberCode}</if>
            <if test="name != null and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null and idCard != ''">and id_card = #{idCard}</if>
            <if test="gender != null">and gender = #{gender}</if>
            <if test="nation != null and nation != ''">and nation = #{nation}</if>
            <if test="birthday != null">and birthday = #{birthday}</if>
            <if test="educationLevel != null and educationLevel != ''">and education_level = #{educationLevel}</if>
            <if test="politicalStatus != null and politicalStatus != ''">and political_status = #{politicalStatus}</if>
            <if test="joinPartyDate != null">and join_party_date = #{joinPartyDate}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="branchId != null">and branch_id = #{branchId}</if>
            <if test="branchName != null and branchName != ''">and branch_name like concat('%', #{branchName}, '%')</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="county != null and county != ''">and county = #{county}</if>
            <if test="country != null and country != ''">and country = #{country}</if>
            <if test="town != null and town != ''">and town = #{town}</if>
            <if test="phone != null and phone != ''">and phone = #{phone}</if>
            <if test="params.beginTime != null and params.beginTime != '' and params.endTime != null and params.endTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </sql>

    <select id="selectPbPartyMemberList" parameterType="PbPartyMember" resultMap="PbPartyMemberMap">
        <include refid="selectVo"/>
        <include refid="queryWhere"/>
        order by id desc
    </select>

    <select id="selectPbPartyMemberById" parameterType="Long" resultMap="PbPartyMemberMap">
        <include refid="selectVo"/>
        where id = #{id}
    </select>

    <select id="checkPbPartyMemberUnique" parameterType="PbPartyMember" resultMap="PbPartyMemberMap">
        select id from pb_party_member where member_code = #{memberCode}
        <if test="id != null">and id != #{id}</if>
    </select>
    <select id="partyMemberCount" resultType="java.lang.Integer">
        select count(1) from pb_party_member
    </select>
    <select id="partyMemberGroupSexCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select gender as type, count(1) as count from pb_party_member
        group by gender
    </select>

    <select id="partyMemberGroupAgeCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            case
                when age <![CDATA[ <= ]]> 35 then 1
                when age between 36 and 60 then 2
                when age > 60 then 3
            end as type,
            count(1) as count
        from pb_party_member
        where age is not null
        group by
            case
                when age <![CDATA[ <= ]]> 35 then 1
                when age between 36 and 60 then 2
                when age > 60 then 3
            end
    </select>

    <select id="partyMemberGroupEducationCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            ifnull(education_level, '未知') as lable,
            count(1) as count
        from pb_party_member
        group by ifnull(education_level, '未知')
        order by count desc
    </select>

    <select id="partyMemberGroupPoliticalStatusCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            ifnull(political_status, '未知') as lable,
            count(1) as count
        from pb_party_member
        group by ifnull(political_status, '未知')
        order by count desc
    </select>

    <select id="partyMemberGroupByOrgType" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            o.org_type as lable,
            count(1) as count
        from pb_party_member m
        left join pb_party_org o on o.id = m.branch_id
        group by o.org_type
    </select>

    <insert id="insertPbPartyMember" parameterType="PbPartyMember" useGeneratedKeys="true" keyProperty="id">
        insert into pb_party_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberCode != null">member_code,</if>
            <if test="name != null">name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="gender != null">gender,</if>
            <if test="nation != null">nation,</if>
            <if test="birthday != null">birthday,</if>
            <if test="age != null">age,</if>
            <if test="educationLevel != null">education_level,</if>
            <if test="politicalStatus != null">political_status,</if>
            <if test="joinPartyDate != null">join_party_date,</if>
            <if test="status != null">status,</if>
            <if test="branchId != null">branch_id,</if>
            <if test="branchName != null">branch_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="activityCount != null">activity_count,</if>
            <if test="lastActivityTime != null">last_activity_time,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberCode != null">#{memberCode},</if>
            <if test="name != null">#{name},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="gender != null">#{gender},</if>
            <if test="nation != null">#{nation},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="age != null">#{age},</if>
            <if test="educationLevel != null">#{educationLevel},</if>
            <if test="politicalStatus != null">#{politicalStatus},</if>
            <if test="joinPartyDate != null">#{joinPartyDate},</if>
            <if test="status != null">#{status},</if>
            <if test="branchId != null">#{branchId},</if>
            <if test="branchName != null">#{branchName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="activityCount != null">#{activityCount},</if>
            <if test="lastActivityTime != null">#{lastActivityTime},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePbPartyMember" parameterType="PbPartyMember">
        update pb_party_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberCode != null">member_code = #{memberCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="age != null">age = #{age},</if>
            <if test="educationLevel != null">education_level = #{educationLevel},</if>
            <if test="politicalStatus != null">political_status = #{politicalStatus},</if>
            <if test="joinPartyDate != null">join_party_date = #{joinPartyDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="branchId != null">branch_id = #{branchId},</if>
            <if test="branchName != null">branch_name = #{branchName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="activityCount != null">activity_count = #{activityCount},</if>
            <if test="lastActivityTime != null">last_activity_time = #{lastActivityTime},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePbPartyMemberById" parameterType="Long">
        delete from pb_party_member where id = #{id}
    </delete>

    <delete id="deletePbPartyMemberByIds" parameterType="String">
        delete from pb_party_member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
