package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbCommunityEstateMapper;
import com.ruoyi.microPlatform.domain.TbCommunityEstate;
import com.ruoyi.microPlatform.service.ITbCommunityEstateService;

/**
 * 小区物业员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
@Service
public class TbCommunityEstateServiceImpl implements ITbCommunityEstateService 
{
    @Autowired
    private TbCommunityEstateMapper tbCommunityEstateMapper;

    /**
     * 查询小区物业员
     * 
     * @param id 小区物业员主键
     * @return 小区物业员
     */
    @Override
    public TbCommunityEstate selectTbCommunityEstateById(Long id)
    {
        return tbCommunityEstateMapper.selectTbCommunityEstateById(id);
    }

    /**
     * 校验小区物业员是否存在
     *
     * @param tbCommunityEstate
     * @return boolean
     */
    @Override
    public boolean checkTbCommunityEstate(TbCommunityEstate tbCommunityEstate){
        TbCommunityEstate old = tbCommunityEstateMapper.checkTbCommunityEstateUnique(tbCommunityEstate);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询小区物业员列表
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 小区物业员
     */
    @Override
    public List<TbCommunityEstate> selectTbCommunityEstateList(TbCommunityEstate tbCommunityEstate)
    {
        return tbCommunityEstateMapper.selectTbCommunityEstateList(tbCommunityEstate);
    }

    /**
     * 导入小区物业员
     *
     * @param infos       小区物业员列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbCommunityEstate(List<TbCommunityEstate> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入小区物业员数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbCommunityEstate info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbCommunityEstate old = tbCommunityEstateMapper.checkTbCommunityEstateUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbCommunityEstateMapper.insertTbCommunityEstate(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbCommunityEstateMapper.updateTbCommunityEstate(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    @Override
    public int insertTbCommunityEstate(TbCommunityEstate tbCommunityEstate)
    {
        tbCommunityEstate.setCreateTime(DateUtils.getNowDate());
        return tbCommunityEstateMapper.insertTbCommunityEstate(tbCommunityEstate);
    }

    /**
     * 修改小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    @Override
    public int updateTbCommunityEstate(TbCommunityEstate tbCommunityEstate)
    {
        tbCommunityEstate.setUpdateTime(DateUtils.getNowDate());
        return tbCommunityEstateMapper.updateTbCommunityEstate(tbCommunityEstate);
    }

    /**
     * 批量删除小区物业员
     * 
     * @param ids 需要删除的小区物业员主键
     * @return 结果
     */
    @Override
    public int deleteTbCommunityEstateByIds(Long[] ids)
    {
        return tbCommunityEstateMapper.deleteTbCommunityEstateByIds(ids);
    }

    /**
     * 删除小区物业员信息
     * 
     * @param id 小区物业员主键
     * @return 结果
     */
    @Override
    public int deleteTbCommunityEstateById(Long id)
    {
        return tbCommunityEstateMapper.deleteTbCommunityEstateById(id);
    }
}
