package com.ruoyi.common.core.domain.entity;

import java.util.Date;
import java.util.List;

public class PreventionFeverNational {
    private String code;

    private String cnName;

    private String pinyinName;

    private String enName;

    private String enShortCode;

    private String enLongCode;

    private String cnShortName;

    private String parentCode;

    private String lng;

    private String lat;

    private String level;

    private String orgName;

    private String orgCode;

    private String orgType;

    private String detailAddress;

    private String originalCode;

    private Date startDate;

    private Date endDate;

    private Date checkDate;

    private String contactor;

    private String licenceOrg;

    private String ceo;

    private String nation;

    private String dangYuan;

    private String xueLi;

    private String shuji;

    private Long groupNum;

    private Long familyNum;

    private Long townNum;

    private Date cdTime;

    private List<PreventionFeverNational> childrenList;

    public List<PreventionFeverNational> getChildrenList() {
        return childrenList;
    }

    public void setChildrenList(List<PreventionFeverNational> childrenList) {
        this.childrenList = childrenList;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code == null ? null : code.trim();
    }

    public String getCnName() {
        return cnName;
    }

    public void setCnName(String cnName) {
        this.cnName = cnName == null ? null : cnName.trim();
    }

    public String getPinyinName() {
        return pinyinName;
    }

    public void setPinyinName(String pinyinName) {
        this.pinyinName = pinyinName == null ? null : pinyinName.trim();
    }

    public String getEnName() {
        return enName;
    }

    public void setEnName(String enName) {
        this.enName = enName == null ? null : enName.trim();
    }

    public String getEnShortCode() {
        return enShortCode;
    }

    public void setEnShortCode(String enShortCode) {
        this.enShortCode = enShortCode == null ? null : enShortCode.trim();
    }

    public String getEnLongCode() {
        return enLongCode;
    }

    public void setEnLongCode(String enLongCode) {
        this.enLongCode = enLongCode == null ? null : enLongCode.trim();
    }

    public String getCnShortName() {
        return cnShortName;
    }

    public void setCnShortName(String cnShortName) {
        this.cnShortName = cnShortName == null ? null : cnShortName.trim();
    }

    public String getParentCode() {
        return parentCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode == null ? null : parentCode.trim();
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng == null ? null : lng.trim();
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat == null ? null : lat.trim();
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level == null ? null : level.trim();
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode == null ? null : orgCode.trim();
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType == null ? null : orgType.trim();
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress == null ? null : detailAddress.trim();
    }

    public String getOriginalCode() {
        return originalCode;
    }

    public void setOriginalCode(String originalCode) {
        this.originalCode = originalCode == null ? null : originalCode.trim();
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getContactor() {
        return contactor;
    }

    public void setContactor(String contactor) {
        this.contactor = contactor == null ? null : contactor.trim();
    }

    public String getLicenceOrg() {
        return licenceOrg;
    }

    public void setLicenceOrg(String licenceOrg) {
        this.licenceOrg = licenceOrg == null ? null : licenceOrg.trim();
    }

    public String getCeo() {
        return ceo;
    }

    public void setCeo(String ceo) {
        this.ceo = ceo == null ? null : ceo.trim();
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation == null ? null : nation.trim();
    }

    public String getDangYuan() {
        return dangYuan;
    }

    public void setDangYuan(String dangYuan) {
        this.dangYuan = dangYuan == null ? null : dangYuan.trim();
    }

    public String getXueLi() {
        return xueLi;
    }

    public void setXueLi(String xueLi) {
        this.xueLi = xueLi == null ? null : xueLi.trim();
    }

    public String getShuji() {
        return shuji;
    }

    public void setShuji(String shuji) {
        this.shuji = shuji == null ? null : shuji.trim();
    }

    public Long getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(Long groupNum) {
        this.groupNum = groupNum;
    }

    public Long getFamilyNum() {
        return familyNum;
    }

    public void setFamilyNum(Long familyNum) {
        this.familyNum = familyNum;
    }

    public Long getTownNum() {
        return townNum;
    }

    public void setTownNum(Long townNum) {
        this.townNum = townNum;
    }

    public Date getCdTime() {
        return cdTime;
    }

    public void setCdTime(Date cdTime) {
        this.cdTime = cdTime;
    }
}