package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 民情收集对象 se_public_sentiment
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SePublicSentiment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据所属部门/网格 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;
    /**
     * 所属网格ID
     */
    private Long gridId;

    /** 反馈人 */
    @Excel(name = "反馈人")
    private String nickName;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;
    /** 地址 */
    @Excel(name = "家庭地址")
    private String addr;

    /** 详细说明 */
    @Excel(name = "详细说明")
    private String descDetail;

    /** 相关照片 */
    @Excel(name = "相关照片")
    private String accessory;

    /** 语音留言包 */
    private String feedbackIssue;

    /** 所属网格 */
    @Excel(name = "处理网格")
    private String gridName;


    /** 其他 */
    private String otherIssue;

    /** 是否有最新回复*/
    private Integer isBackPhone;

    /** 账户 */
    private Long userId;


    /** 经纬度 */
    private String lat;

    /**  */
    private String lng;

    /** 省 */
    private String province;

    /** 市 */
    private String city;

    /** 县 */
    private String addrCounty;

    /** 街道 */
    private String addrCountry;


    /** 数据状态（0待处理 1已结束） */
    private Integer status;

    /** 处理结果 */
    private String handleResult;
    /**
     * 是否导出 用于判定如果导出的时候不进行某些操作
     */
    private Integer isExport = 0;
    /**
     * 后续动态数量
     */
    private Integer handleCount;

    //处理状态(首页传参：0)
    private Integer  handleStatus;
    /**
     * 填报用户的gridId
     */
    private Long userGridId;

    private Integer gridIdNull;
    /**
     * 语音时长
     */
    private Integer duration;

    //时间条件 日day 周week 月month
    private String time;

    /** 搜索开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 搜索结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    public void setAccessory(String accessory)
    {
        this.accessory = accessory;
        if (accessory != null && !this.accessory.equals("")){
            List<String> stringList = Arrays.asList(accessory.split(","));
            this.setFileList(stringList);
            if (stringList.size() > 0){
                this.setFirstFile(stringList.get(0));
            }
        }
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getAccessory()
    {
        return accessory;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getGridIdNull() {
        return gridIdNull;
    }

    public void setGridIdNull(Integer gridIdNull) {
        this.gridIdNull = gridIdNull;
    }

    private boolean self = false;

    public boolean isSelf() {
        return self;
    }

    public void setSelf(boolean self) {
        this.self = self;
    }

    public Long getUserGridId() {
        return userGridId;
    }

    public void setUserGridId(Long userGridId) {
        this.userGridId = userGridId;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public Integer getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    public Integer getHandleCount() {
        return handleCount;
    }

    public void setHandleCount(Integer handleCount) {
        this.handleCount = handleCount;
    }

    public Integer getIsExport() {
        return isExport;
    }

    public void setIsExport(Integer isExport) {
        this.isExport = isExport;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setFeedbackIssue(String feedbackIssue) 
    {
        this.feedbackIssue = feedbackIssue;
    }

    public String getFeedbackIssue() 
    {
        return feedbackIssue;
    }
    public void setOtherIssue(String otherIssue) 
    {
        this.otherIssue = otherIssue;
    }

    public String getOtherIssue() 
    {
        return otherIssue;
    }
    public void setIsBackPhone(Integer isBackPhone) 
    {
        this.isBackPhone = isBackPhone;
    }

    public Integer getIsBackPhone() 
    {
        return isBackPhone;
    }
    public void setDescDetail(String descDetail) 
    {
        this.descDetail = descDetail;
    }

    public String getDescDetail() 
    {
        return descDetail;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setAddrCounty(String addrCounty) 
    {
        this.addrCounty = addrCounty;
    }

    public String getAddrCounty() 
    {
        return addrCounty;
    }
    public void setAddrCountry(String addrCountry) 
    {
        this.addrCountry = addrCountry;
    }

    public String getAddrCountry() 
    {
        return addrCountry;
    }
    public void setAddr(String addr) 
    {
        this.addr = addr;
    }

    public String getAddr() 
    {
        return addr;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setHandleResult(String handleResult) 
    {
        this.handleResult = handleResult;
    }

    public String getHandleResult() 
    {
        return handleResult;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("feedbackIssue", getFeedbackIssue())
            .append("otherIssue", getOtherIssue())
            .append("isBackPhone", getIsBackPhone())
            .append("descDetail", getDescDetail())
            .append("accessory", getAccessory())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("phone", getPhone())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("province", getProvince())
            .append("city", getCity())
            .append("addrCounty", getAddrCounty())
            .append("addrCountry", getAddrCountry())
            .append("addr", getAddr())
            .append("status", getStatus())
            .append("handleResult", getHandleResult())
            .append("createTime", getCreateTime())
            .toString();
    }
}
