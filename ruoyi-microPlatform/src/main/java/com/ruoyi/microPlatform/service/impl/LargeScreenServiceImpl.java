package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.mapper.*;
import com.ruoyi.microPlatform.service.ILargeScreenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class LargeScreenServiceImpl implements ILargeScreenService {
    @Autowired
    private TbPersonTypeMapper tbPersonTypeMapper;

    @Autowired
    private LargeScreenMapper largeScreenMapper;

    @Autowired
    private SeCommunityFunctionMapper seCommunityFunctionMapper;

    @Autowired
    private TbGridInfoMapper tbGridInfoMapper;

    @Autowired
    private TbCommunityInfoMapper tbCommunityInfoMapper;

    @Autowired
    private TbMerchantInfoMapper tbMerchantInfoMapper;

    @Autowired
    private TbCommunityResidentMapper tbCommunityResidentMapper;

    @Autowired
    private TbResidentMemberMapper tbResidentMemberMapper;

    @Autowired
    private TbGridInspectMapper tbGridInspectMapper;

    @Autowired
    private TbMerchantInspectMapper tbMerchantInspectMapper;

    @Autowired
    private TbResidentContactMapper tbResidentContactMapper;

    @Autowired
    private SePublicSentimentMapper sePublicSentimentMapper;

    @Autowired
    private TbDeptUserMapper tbDeptUserMapper;

    @Autowired
    private TbIssueInfoMapper tbIssueInfoMapper;

    @Autowired
    @Qualifier("largeScreenExecutor")
    private ExecutorService executor;


    /**
     * 人员标签类型排名
     */
    @Override
    public Map<String, Integer> getLableRank(LargeScreenInfo largeScreenInfo) {
        List<TbPersonType> list = tbPersonTypeMapper.selectLableRank(largeScreenInfo);
        Map<String, Integer> resultMap = list.stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getLabel() != null && item.getValue() != null && item.getValue() != 0)
                .collect(Collectors.toMap(
                        TbPersonType::getLabel,
                        TbPersonType::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
        return resultMap;
    }

    /**
     * 社区功能室统计
     */
    @Override
    public Map<String, Integer> getCommunityFunctionCount(LargeScreenInfo largeScreenInfo) {

        List<SeCommunityFunction> communityFunctionList = seCommunityFunctionMapper.getCommunityFunctionCount(largeScreenInfo);
        Map<String, Integer> labelCountMap = communityFunctionList.stream()
                .filter(item -> item != null && item.getLabel() != null && item.getCount() != null)
                .collect(Collectors.toMap(
                        SeCommunityFunction::getLabel,
                        SeCommunityFunction::getCount,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        int total = labelCountMap.values().stream().mapToInt(Integer::intValue).sum();
        labelCountMap.put("total", total);

        return labelCountMap;
    }

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 数量
     */
    @Override
    public Map<String, Integer> getStatisticsByJurisdiction(LargeScreenInfo largeScreenInfo) {
        Map<String, Integer> resultMap = new LinkedHashMap<>();
        //LargeScreenInfo statisticsByJurisdiction = largeScreenMapper.getStatisticsByJurisdiction(largeScreenInfo);

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        System.out.println("开始并发查询...");

        // 并发任务：每个类型统计数量
        CompletableFuture<Integer> gridFuture = asyncCount("网格", () -> largeScreenMapper.countGridInfo(largeScreenInfo));
        CompletableFuture<Integer> communityFuture = asyncCount("小区", () -> largeScreenMapper.countCommunityInfo(largeScreenInfo));
        CompletableFuture<Integer> merchantFuture = asyncCount("门店商户", () -> largeScreenMapper.countMerchantInfo(largeScreenInfo));
        CompletableFuture<Integer> residentFuture = asyncCount("住户", () -> largeScreenMapper.countCommunityResident(largeScreenInfo));
        CompletableFuture<Integer> memberFuture = asyncCount("住户成员", () -> largeScreenMapper.countResidentMember(largeScreenInfo));

        // 等待所有任务完成
        CompletableFuture.allOf(gridFuture, communityFuture, merchantFuture, residentFuture, memberFuture).join();

        resultMap.put("网格", gridFuture.join());
        resultMap.put("小区", communityFuture.join());
        resultMap.put("门店商户", merchantFuture.join());
        resultMap.put("住户", residentFuture.join());
        resultMap.put("住户成员", memberFuture.join());

        // 记录结束时间并输出耗时（单位：秒）
        long endTime = System.currentTimeMillis();
        double seconds = (endTime - startTime) / 1000.0;
        System.out.println("并发查询结束，总耗时：" + seconds + " 秒");
        return resultMap;
    }


    /**
     * 通用封装：异步执行某个统计任务，出现异常返回默认值 0
     *
     * @param name     统计名称（用于日志）
     * @param supplier 查询逻辑（实际调用 Mapper 中对应方法）
     * @return CompletableFuture<Integer> 结果封装
     */
    private CompletableFuture<Integer> asyncCount(String name, Supplier<Integer> supplier) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                long start = System.currentTimeMillis();
                Integer count = supplier.get();
                long cost = System.currentTimeMillis() - start;
                System.out.println("统计【" + name + "】完成，耗时：" + cost + "ms");
                return Optional.ofNullable(count).orElse(0);
            } catch (Exception e) {
                System.err.println("统计【" + name + "】失败：" + e.getMessage());
                return 0; // 出现异常返回默认值
            }
        }, executor);
    }

    private Integer defaultZero(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 。空间区域经纬度
     */
    @Override
    public List<?> getSpaceByJurisdiction(LargeScreenInfo largeScreenInfo) {
        if (ObjectUtil.isEmpty(largeScreenInfo.getJurisdicType())) {
            largeScreenInfo.setJurisdicType("网格");
        }
        //获取存在空间区域的数据
        largeScreenInfo.setSpace(true);
        switch (largeScreenInfo.getJurisdicType()) {
            case "网格":
                return tbGridInfoMapper.selectTbGridInfoSpaceList(largeScreenInfo);
            case "小区":
                return tbCommunityInfoMapper.selectTbCommunityInfoSpaceList(largeScreenInfo);
            case "门店商户":
                return tbMerchantInfoMapper.selectTbMerchantInfoSpaceList(largeScreenInfo);
            default:
                throw new IllegalArgumentException("未知类型: " + largeScreenInfo.getJurisdicType());
        }
    }

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 具体数据
     */
    @Override
    public List<?> getListByJurisdiction(LargeScreenInfo largeScreenInfo) {
        if (ObjectUtil.isEmpty(largeScreenInfo.getJurisdicType())) {
            largeScreenInfo.setJurisdicType("网格");
        }
        switch (largeScreenInfo.getJurisdicType()) {
            case "网格":
                return tbGridInfoMapper.selectTbGridInfoSpaceList(largeScreenInfo);
            case "小区":
                return tbCommunityInfoMapper.selectTbCommunityInfoSpaceList(largeScreenInfo);
            case "门店商户":
                return tbMerchantInfoMapper.selectTbMerchantInfoSpaceList(largeScreenInfo);
            default:
                throw new IllegalArgumentException("未知类型: " + largeScreenInfo.getJurisdicType());
        }
    }


    /**
     * 巡查走访情况
     */
    @Override
    public Map<String, Map<String, Integer>> getpatrolVisitSituation(LargeScreenInfo largeScreenInfo) {

        Map<String, Map<String, Integer>> resultMap = new HashMap<>();

        // 分别查询三张表每月统计: 网格巡查\门店巡查\入户走访（居民联络）
        List<Map<String, Object>> gridData = tbGridInspectMapper.selectMonthlyStats(largeScreenInfo);//网格巡查
        List<Map<String, Object>> merchantData = tbMerchantInspectMapper.selectMonthlyStats(largeScreenInfo); // 门店巡查
        List<Map<String, Object>> residentData = tbResidentContactMapper.selectMonthlyStats(largeScreenInfo); // 入户走访（居民联络）

        // 2. 获取最近12周的自然周区间（如：7.08-7.14）
        //<String> weekKeys = buildRecent12WeekKeys();

        /*processStatData(resultMap, gridData, "网格巡查",weekKeys);
        processStatData(resultMap, merchantData, "门店巡查",weekKeys);
        processStatData(resultMap, residentData, "入户走访",weekKeys);
*/

        processStatData(resultMap, gridData, "网格巡查");
        processStatData(resultMap, merchantData, "门店巡查");
        processStatData(resultMap, residentData, "入户走访");
        return resultMap;
    }

    private void processStatData(Map<String, Map<String, Integer>> resultMap,
                                 List<Map<String, Object>> dataList,
                                 String typeName) {
        Map<String, Integer> monthMap = resultMap.computeIfAbsent(typeName, k -> new LinkedHashMap<>());
        int totalCount = 0;

        for (Map<String, Object> row : dataList) {
            if (row == null) continue;

            Object monthObj = row.get("month");
            Object countObj = row.get("total");

            // 跳过没有月份或统计值的行
            if (monthObj == null || countObj == null) continue;

            String month = String.valueOf(monthObj);
            int count;
            try {
                count = ((Number) countObj).intValue();
            } catch (ClassCastException e) {
                // 如果 total 字段不是数字，默认设置为 0，避免异常
                count = 0;
            }

            monthMap.put(month, count);
            //totalCount += count;
        }

        // 添加总次数（保证 key 不为 null）
        //monthMap.put("totalCount", totalCount);
    }


    /**
     * 处理每种巡查类型的数据（自动补全无数据的周为0，生成 totalCount）
     *
     * @param resultMap 最终返回的结构
     * @param dataList  查询出来的每周数据
     * @param typeName  类型名（如 网格巡查）
     * @param weekKeys  标准的12周区间 key 列表（如 7.08-7.14）
     */
    private void processStatData(Map<String, Map<String, Integer>> resultMap,
                                 List<Map<String, Object>> dataList,
                                 String typeName,
                                 List<String> weekKeys) {

        Map<String, Integer> weekMap = resultMap.computeIfAbsent(typeName, k -> new LinkedHashMap<>());

        // 将数据库中的 week -> count 映射出来
        Map<String, Integer> dbWeekMap = new HashMap<>();
        for (Map<String, Object> row : dataList) {
            String start = String.valueOf(row.get("week_start"));
            String end = String.valueOf(row.get("week_end"));
            String key = formatWeekKey(start, end); // 转为 7.08-7.14
            int count = ((Number) row.get("total")).intValue();
            dbWeekMap.put(key, count);
        }

        int totalCount = 0;
        for (String weekKey : weekKeys) {
            int count = dbWeekMap.getOrDefault(weekKey, 0);
            weekMap.put(weekKey, count);
            totalCount += count;
        }
        weekMap.put("totalCount", totalCount);


    }

    /**
     * 构建最近12周的自然周区间字符串列表，格式为：7.08-7.14
     */
    private List<String> buildRecent12WeekKeys() {
        List<String> weekKeys = new ArrayList<>();
        LocalDate now = LocalDate.now();

        // 找到当前周的周一
        LocalDate monday = now.with(DayOfWeek.MONDAY);

        for (int i = 11; i >= 0; i--) {
            // 每周的开始和结束
            LocalDate start = monday.minusWeeks(i);
            LocalDate end = start.plusDays(6);

            // 格式化为 7.08-7.14 形式
            String label = String.format("%d.%02d-%d.%02d",
                    start.getMonthValue(), start.getDayOfMonth(),
                    end.getMonthValue(), end.getDayOfMonth());

            weekKeys.add(label);
        }

        return weekKeys;
    }

    /**
     * 将 yyyy-MM-dd 转为 7.08-7.14 格式
     */
    private String formatWeekKey(String startDateStr, String endDateStr) {
        LocalDate start = LocalDate.parse(startDateStr);
        LocalDate end = LocalDate.parse(endDateStr);
        return String.format("%d.%02d-%d.%02d",
                start.getMonthValue(), start.getDayOfMonth(),
                end.getMonthValue(), end.getDayOfMonth());
    }


    /**
     * 居民吹哨（民情收集）
     */
    @Override
    public Map<String, Integer> getResidentWhistleCount(LargeScreenInfo largeScreenInfo) {
        Map<String, Integer> resultMap = new HashMap<>();

        Integer count = sePublicSentimentMapper.getResidentWhistleCount(largeScreenInfo);
        resultMap.put("居民吹哨", count != null ? count : 0);
        return resultMap;
    }

    /**
     * 干部岗位类型
     */
    @Override
    public Map<String, Integer> getDeptUserPostType(LargeScreenInfo largeScreenInfo) {

        // 获取字典类型
        List<SysDictData> deptUserType = DictUtils.getDictCache("dept_user_type");
        if (deptUserType == null){
            deptUserType = new ArrayList<>();
        }
        Set<String> allTypes = deptUserType.stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toSet());

        // 初始化统计 Map
        Map<String, Integer> typeCountMap = allTypes.stream()
                .collect(Collectors.toMap(Function.identity(), v -> 0, (a, b) -> b, LinkedHashMap::new));

        // 获取干部列表
        List<TbDeptUser> userList = tbDeptUserMapper.getDeptUserPostType(largeScreenInfo);

        // 流式统计
        userList.stream()
                .map(TbDeptUser::getPostStr)
                .filter(Objects::nonNull)
                .flatMap(postStr -> Arrays.stream(postStr.split("、")))
                .map(String::trim)
                .filter(allTypes::contains)
                .forEach(type -> typeCountMap.merge(type, 1, Integer::sum));

        // 构建最终结果 Map
        Map<String, Integer> resultMap = new LinkedHashMap<>();
        int total = 0;
        for (SysDictData dict : deptUserType) {
            int count = typeCountMap.getOrDefault(dict.getDictValue(), 0);
            resultMap.put(dict.getDictValue(), count);
            total += count;
        }
        resultMap.put("总数", total);

        return resultMap;
    }

    /**
     * 门店行业类型
     */
    @Override
    public Map<String, Integer> getMerchantInfoType(LargeScreenInfo largeScreenInfo) {

        // 1. 获取字典中所有门店类型（字典中的类型是你全量类型）
        List<SysDictData> dictCache = DictUtils.getDictCache("business_type");
        Set<String> allTypes = dictCache.stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toSet());

        // 2. 数据库按类型统计门店数量
        List<Map<String, Object>> merchantInfoTypeList = tbMerchantInfoMapper.getMerchantInfoByType(largeScreenInfo);

        // 3. 把数据库结果转换成Map<String,Integer>，key是store_type，value是数量
        Map<String, Integer> countMap = new HashMap<>();
        for (Map<String, Object> row : merchantInfoTypeList) {
            String type = (String) row.get("store_type");
            Number countNum = (Number) row.get("count");

            countMap.put(type, countNum == null ? 0 : countNum.intValue());
        }

        // 4. 构造最终结果Map，包含字典中所有类型，没数据的类型数量设为0
        Map<String, Integer> resultMap = new LinkedHashMap<>();
        int total = 0;
        for (SysDictData dict : dictCache) {
            String type = dict.getDictValue();
            int count = countMap.getOrDefault(type, 0);
            resultMap.put(type, count);
            total += count;
        }
        // 5. 添加总数
        //resultMap.put("总数", total);

        return resultMap;
    }

    /**
     * 根据网格编码获得 所属村/社区  网格范围   住户家庭数   人员数    门店商户数
     *
     * @param largeScreenInfo
     * @return
     */
    @Override
    public GridUnderInfoVO getGridUnderInfo(LargeScreenInfo largeScreenInfo) {
        GridUnderInfoVO resultVo = new GridUnderInfoVO();
        //获得网格信息
        TbGridInfo gridInfo = tbGridInfoMapper.getGridInfo(largeScreenInfo);
        resultVo.setTown(gridInfo.getTown());
        resultVo.setBoundary(gridInfo.getBoundary());
        //获得 住户家庭数   人员数    门店商户数
        largeScreenInfo.setGridId(gridInfo.getId());
        Integer countCommunityResident = largeScreenMapper.countCommunityResident(largeScreenInfo);
        Integer countResidentMember = largeScreenMapper.countResidentMember(largeScreenInfo);
        Integer countMerchantInfo = largeScreenMapper.countMerchantInfo(largeScreenInfo);

        resultVo.setCommunityResident(defaultZero(countCommunityResident));//住户家庭数
        resultVo.setResidentMember(defaultZero(countResidentMember)); //人员数
        resultVo.setMerchantInfo(defaultZero(countMerchantInfo)); //门店商户数
        return resultVo;
    }


    /**
     *
     * 根据小区id获得 小区名称   所属网格    面积   住户家庭数   住户成员数
     *
     * @param largeScreenInfo
     * @return
     */
    @Override
    public CommunityUnderInfoVO getCommunityUnderInfo(LargeScreenInfo largeScreenInfo) {
        CommunityUnderInfoVO resultVO = new CommunityUnderInfoVO();
        TbCommunityInfo communityInfo = tbCommunityInfoMapper.getCommunityInfo(largeScreenInfo);
        resultVO.setCommunityName(communityInfo.getCommunityName());
        resultVO.setGridName(communityInfo.getGridName());
        resultVO.setArea(communityInfo.getArea());
        //获得 住户家庭数   人员数
        Integer countCommunityResident = largeScreenMapper.countCommunityResident(largeScreenInfo);
        Integer countResidentMember = largeScreenMapper.countResidentMember(largeScreenInfo);

        resultVO.setCommunityResident(countCommunityResident);//住户家庭数
        resultVO.setResidentMember(countResidentMember); //人员数

        return resultVO;
    }


    @Override
    public Map<String, Integer> getIssueCount(LargeScreenInfo largeScreenInfo) {

        Map<String, Integer> map = tbIssueInfoMapper.getIssueCountByMdjfztdm(largeScreenInfo);
        return map;
    }

}
