package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbAssociateInfoMapper;
import com.ruoyi.microPlatform.domain.TbAssociateInfo;
import com.ruoyi.microPlatform.service.ITbAssociateInfoService;

/**
 * 行业协会商会信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@Service
public class TbAssociateInfoServiceImpl implements ITbAssociateInfoService 
{
    @Autowired
    private TbAssociateInfoMapper tbAssociateInfoMapper;

    /**
     * 查询行业协会商会信息
     * 
     * @param id 行业协会商会信息主键
     * @return 行业协会商会信息
     */
    @Override
    public TbAssociateInfo selectTbAssociateInfoById(Long id)
    {
        return tbAssociateInfoMapper.selectTbAssociateInfoById(id);
    }

    /**
     * 校验行业协会商会信息是否存在
     *
     * @param tbAssociateInfo
     * @return boolean
     */
    @Override
    public boolean checkTbAssociateInfo(TbAssociateInfo tbAssociateInfo){
        TbAssociateInfo old = tbAssociateInfoMapper.checkTbAssociateInfoUnique(tbAssociateInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询行业协会商会信息列表
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 行业协会商会信息
     */
    @Override
    public List<TbAssociateInfo> selectTbAssociateInfoList(TbAssociateInfo tbAssociateInfo)
    {
        return tbAssociateInfoMapper.selectTbAssociateInfoList(tbAssociateInfo);
    }

    /**
     * 导入行业协会商会信息
     *
     * @param infos       行业协会商会信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbAssociateInfo(List<TbAssociateInfo> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入行业协会商会信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbAssociateInfo info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbAssociateInfo old = tbAssociateInfoMapper.checkTbAssociateInfoUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbAssociateInfoMapper.insertTbAssociateInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbAssociateInfoMapper.updateTbAssociateInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    @Override
    public int insertTbAssociateInfo(TbAssociateInfo tbAssociateInfo)
    {
        tbAssociateInfo.setCreateTime(DateUtils.getNowDate());
        return tbAssociateInfoMapper.insertTbAssociateInfo(tbAssociateInfo);
    }

    /**
     * 修改行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    @Override
    public int updateTbAssociateInfo(TbAssociateInfo tbAssociateInfo)
    {
        tbAssociateInfo.setUpdateTime(DateUtils.getNowDate());
        return tbAssociateInfoMapper.updateTbAssociateInfo(tbAssociateInfo);
    }

    /**
     * 批量删除行业协会商会信息
     * 
     * @param ids 需要删除的行业协会商会信息主键
     * @return 结果
     */
    @Override
    public int deleteTbAssociateInfoByIds(Long[] ids)
    {
        return tbAssociateInfoMapper.deleteTbAssociateInfoByIds(ids);
    }

    /**
     * 删除行业协会商会信息信息
     * 
     * @param id 行业协会商会信息主键
     * @return 结果
     */
    @Override
    public int deleteTbAssociateInfoById(Long id)
    {
        return tbAssociateInfoMapper.deleteTbAssociateInfoById(id);
    }
}
