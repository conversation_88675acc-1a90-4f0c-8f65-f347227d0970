package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;
import com.ruoyi.microPlatform.mapper.SeFunctionReservationMapper;
import com.ruoyi.microPlatform.mapper.SePublicSentimentMapper;
import com.ruoyi.microPlatform.mapper.SeVolunteerRegisterMapper;
import com.ruoyi.microPlatform.service.IHomePageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class IHomePageServiceImpl implements IHomePageService {

    @Autowired
    private SeVolunteerRegisterMapper volunteerRegisterMapper;

    @Autowired
    private SeFunctionReservationMapper functionReservationMapper;

    @Autowired
    private SePublicSentimentMapper sePublicSentimentMapper;

    /**
     * 首页：志愿报名 se_volunteer_register、开放预约se_function_reservation 居民吹哨数se_public_sentiment  （当日、本周、当月、当年、时间范围）
     */
    @Override
    public Map<String, Integer> selectCount(HomePage homePage) {
        Map<String, Integer> map = new HashMap<>();
        //查询志愿报名数
        int volunteerCount = volunteerRegisterMapper.selectSubmitCountByHome(homePage);
        map.put("志愿报名",volunteerCount);
        //开放预约数
        int functionCount = functionReservationMapper.selectSubmitCountByHome(homePage);
        map.put("开放预约",functionCount);
        //居民吹哨数
        int publicCount = sePublicSentimentMapper.selectSubmitCountByHome(homePage);
        map.put("居民吹哨",publicCount);
        return map;
    }




}
