package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysInfoTest;
import com.ruoyi.system.service.ISysInfoTestService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 测试管理Controller
 * 
 * <AUTHOR>
 * @date 2023-01-10
 */
@RestController
@RequestMapping("/system/test")
public class SysInfoTestController extends BaseController
{
    @Autowired
    private ISysInfoTestService sysInfoTestService;

    /**
     * 查询测试管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:test:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysInfoTest sysInfoTest)
    {
        startPage();
        List<SysInfoTest> list = sysInfoTestService.selectSysInfoTestList(sysInfoTest);
        return getDataTable(list);
    }

    /**
     * 导出测试管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:test:export')")
    @Log(title = "测试管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysInfoTest sysInfoTest)
    {
        List<SysInfoTest> list = sysInfoTestService.selectSysInfoTestList(sysInfoTest);
        ExcelUtil<SysInfoTest> util = new ExcelUtil<SysInfoTest>(SysInfoTest.class);
        util.exportExcel(response, list, "测试管理数据");
    }

    /**
     * 获取测试管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:test:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(sysInfoTestService.selectSysInfoTestById(id));
    }

    /**
     * 新增测试管理
     */
    @PreAuthorize("@ss.hasPermi('system:test:add')")
    @Log(title = "测试管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysInfoTest sysInfoTest)
    {
        return toAjax(sysInfoTestService.insertSysInfoTest(sysInfoTest));
    }

    /**
     * 修改测试管理
     */
    @PreAuthorize("@ss.hasPermi('system:test:edit')")
    @Log(title = "测试管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysInfoTest sysInfoTest)
    {
        return toAjax(sysInfoTestService.updateSysInfoTest(sysInfoTest));
    }

    /**
     * 删除测试管理
     */
    @PreAuthorize("@ss.hasPermi('system:test:remove')")
    @Log(title = "测试管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(sysInfoTestService.deleteSysInfoTestByIds(ids));
    }
}
