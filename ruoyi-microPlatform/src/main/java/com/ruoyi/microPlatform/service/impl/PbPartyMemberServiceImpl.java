package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyMember;
import com.ruoyi.microPlatform.mapper.PbPartyMemberMapper;
import com.ruoyi.microPlatform.service.IPbPartyMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 党员队伍信息管理Service业务层处理
 */
@Service
public class PbPartyMemberServiceImpl implements IPbPartyMemberService {

    @Autowired
    private PbPartyMemberMapper mapper;

    @Override
    public PbPartyMember selectPbPartyMemberById(Long id) {
        return mapper.selectPbPartyMemberById(id);
    }

    @Override
    public boolean checkPbPartyMember(PbPartyMember info) {
        return mapper.checkPbPartyMemberUnique(info) != null;
    }

    @Override
    public List<PbPartyMember> selectPbPartyMemberList(PbPartyMember info) {
        return mapper.selectPbPartyMemberList(info);
    }

    @Override
    public int insertPbPartyMember(PbPartyMember info) {
        info.setCreateTime(DateUtils.getNowDate());
        return mapper.insertPbPartyMember(info);
    }

    @Override
    public int updatePbPartyMember(PbPartyMember info) {
        info.setUpdateTime(DateUtils.getNowDate());
        return mapper.updatePbPartyMember(info);
    }

    @Override
    public int deletePbPartyMemberByIds(Long[] ids) {
        return mapper.deletePbPartyMemberByIds(ids);
    }

    @Override
    public int deletePbPartyMemberById(Long id) {
        return mapper.deletePbPartyMemberById(id);
    }

    @Override
    public Integer partyMemberCount() {
        return mapper.partyMemberCount();
    }

    @Override
    public List<CommonBaseCount> partyMemberGroupSexCount() {
        List<CommonBaseCount> dbCounts = mapper.partyMemberGroupSexCount();
        // Map existing results by type for quick lookup
        Map<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) {
                    countMap.put(t, cnt == null ? 0 : cnt);
                }
            }
        }

        // Ensure types 0(男), 1(女), 2(未知) are all present
        List<CommonBaseCount> res = new ArrayList<>(3);

        CommonBaseCount male = new CommonBaseCount();
        male.setType(0);
        male.setLable("男");
        male.setCount(countMap.getOrDefault(0, 0));
        res.add(male);

        CommonBaseCount female = new CommonBaseCount();
        female.setType(1);
        female.setLable("女");
        female.setCount(countMap.getOrDefault(1, 0));
        res.add(female);

        CommonBaseCount unknown = new CommonBaseCount();
        unknown.setType(2);
        unknown.setLable("未知");
        unknown.setCount(countMap.getOrDefault(2, 0));
        res.add(unknown);

        return res;
    }

    @Override
    public List<CommonBaseCount> partyMemberGroupAgeCount() {
        List<CommonBaseCount> dbCounts = mapper.partyMemberGroupAgeCount();
        Map<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) {
                    countMap.put(t, cnt == null ? 0 : cnt);
                }
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(3);

        CommonBaseCount a1 = new CommonBaseCount();
        a1.setType(1);
        a1.setLable("35岁及以下");
        a1.setCount(countMap.getOrDefault(1, 0));
        res.add(a1);

        CommonBaseCount a2 = new CommonBaseCount();
        a2.setType(2);
        a2.setLable("36-60岁");
        a2.setCount(countMap.getOrDefault(2, 0));
        res.add(a2);

        CommonBaseCount a3 = new CommonBaseCount();
        a3.setType(3);
        a3.setLable("60岁以上");
        a3.setCount(countMap.getOrDefault(3, 0));
        res.add(a3);

        return res;
    }

    @Override
    public List<CommonBaseCount> partyMemberGroupEducationCount() {
        // 直接按学历分组统计，XML中处理了空值显示为“未知”
        return mapper.partyMemberGroupEducationCount();
    }

    @Override
    public List<CommonBaseCount> partyMemberGroupPoliticalStatusCount() {
        // 直接按政治面貌分组统计，XML中处理了空值显示为“未知”
        return mapper.partyMemberGroupPoliticalStatusCount();
    }

    @Override
    public List<CommonBaseCount> personnelDistribution() {
        // 党组织类型固定分类：机关事业单位、国有企业、非公企业、社会组织、网格
        String[] types = new String[]{"机关事业单位", "国有企业", "非公企业", "社会组织", "网格"};

        List<CommonBaseCount> dbCounts = mapper.partyMemberGroupByOrgType();
        Map<String, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                String label = c.getLable();
                Integer cnt = c.getCount();
                if (label != null) {
                    countMap.put(label, cnt == null ? 0 : cnt);
                }
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(types.length);
        for (String t : types) {
            CommonBaseCount item = new CommonBaseCount();
            item.setLable(t);
            item.setCount(countMap.getOrDefault(t, 0));
            res.add(item);
        }
        return res;
    }
}
