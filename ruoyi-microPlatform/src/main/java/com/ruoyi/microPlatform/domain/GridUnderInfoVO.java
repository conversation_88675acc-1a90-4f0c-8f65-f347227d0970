package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.annotation.Excel;

public class GridUnderInfoVO {

    //所属村镇/社区
    private  String town;

    //网格范围
    private String boundary;

    /**
     * 住户数据count
     */
    private Integer communityResident;

    /**
     * 住户成员数据count
     */
    private Integer residentMember;

    /**
     * 门店商户数据count
     */
    private Integer merchantInfo;


    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getBoundary() {
        return boundary;
    }

    public void setBoundary(String boundary) {
        this.boundary = boundary;
    }

    public Integer getCommunityResident() {
        return communityResident;
    }

    public void setCommunityResident(Integer communityResident) {
        this.communityResident = communityResident;
    }

    public Integer getResidentMember() {
        return residentMember;
    }

    public void setResidentMember(Integer residentMember) {
        this.residentMember = residentMember;
    }

    public Integer getMerchantInfo() {
        return merchantInfo;
    }

    public void setMerchantInfo(Integer merchantInfo) {
        this.merchantInfo = merchantInfo;
    }
}


