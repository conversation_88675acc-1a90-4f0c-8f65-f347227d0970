package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeConvenientPhoneMapper;
import com.ruoyi.microPlatform.domain.SeConvenientPhone;
import com.ruoyi.microPlatform.service.ISeConvenientPhoneService;

/**
 * 便民电话Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeConvenientPhoneServiceImpl implements ISeConvenientPhoneService 
{
    @Autowired
    private SeConvenientPhoneMapper seConvenientPhoneMapper;

    /**
     * 查询便民电话
     * 
     * @param id 便民电话主键
     * @return 便民电话
     */
    @Override
    public SeConvenientPhone selectSeConvenientPhoneById(Long id)
    {
        return seConvenientPhoneMapper.selectSeConvenientPhoneById(id);
    }

    /**
     * 校验便民电话是否存在
     *
     * @param seConvenientPhone
     * @return boolean
     */
    @Override
    public boolean checkSeConvenientPhone(SeConvenientPhone seConvenientPhone){
        SeConvenientPhone old = seConvenientPhoneMapper.checkSeConvenientPhoneUnique(seConvenientPhone);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询便民电话列表
     * 
     * @param seConvenientPhone 便民电话
     * @return 便民电话
     */
    @Override
    public List<SeConvenientPhone> selectSeConvenientPhoneList(SeConvenientPhone seConvenientPhone)
    {
        return seConvenientPhoneMapper.selectSeConvenientPhoneList(seConvenientPhone);
    }

    /**
     * 导入便民电话
     *
     * @param infos       便民电话列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeConvenientPhone(List<SeConvenientPhone> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入便民电话数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeConvenientPhone info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                SeConvenientPhone old = seConvenientPhoneMapper.checkSeConvenientPhoneUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seConvenientPhoneMapper.insertSeConvenientPhone(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seConvenientPhoneMapper.updateSeConvenientPhone(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getName() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    @Override
    public int insertSeConvenientPhone(SeConvenientPhone seConvenientPhone)
    {
        seConvenientPhone.setCreateTime(DateUtils.getNowDate());

        return seConvenientPhoneMapper.insertSeConvenientPhone(seConvenientPhone);
    }

    /**
     * 修改便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    @Override
    public int updateSeConvenientPhone(SeConvenientPhone seConvenientPhone)
    {
        seConvenientPhone.setUpdateTime(DateUtils.getNowDate());
        return seConvenientPhoneMapper.updateSeConvenientPhone(seConvenientPhone);
    }

    /**
     * 批量删除便民电话
     * 
     * @param ids 需要删除的便民电话主键
     * @return 结果
     */
    @Override
    public int deleteSeConvenientPhoneByIds(Long[] ids)
    {
        return seConvenientPhoneMapper.deleteSeConvenientPhoneByIds(ids);
    }

    /**
     * 删除便民电话信息
     * 
     * @param id 便民电话主键
     * @return 结果
     */
    @Override
    public int deleteSeConvenientPhoneById(Long id)
    {
        return seConvenientPhoneMapper.deleteSeConvenientPhoneById(id);
    }

    @Override
    public Map<String, Object> selectSubmitCount(SeConvenientPhone seConvenientPhone) {
        return seConvenientPhoneMapper.selectSubmitCount(seConvenientPhone);
    }
}
