package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbCityComponent;
import com.ruoyi.dataview.service.IDbCityComponentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运管服部件数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@RestController
@RequestMapping("/dataview/component")
public class DbCityComponentController extends BaseController
{
    @Autowired
    private IDbCityComponentService dbCityComponentService;

    /**
     * 查询运管服部件数据列表
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbCityComponent dbCityComponent)
    {
        startPage();
        List<DbCityComponent> list = dbCityComponentService.selectDbCityComponentList(dbCityComponent);
        return getDataTable(list);
    }

    /**
     * 导出运管服部件数据列表
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:export')")
    @Log(title = "运管服部件数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbCityComponent dbCityComponent)
    {
        List<DbCityComponent> list = dbCityComponentService.selectDbCityComponentList(dbCityComponent);
        ExcelUtil<DbCityComponent> util = new ExcelUtil<DbCityComponent>(DbCityComponent.class);
        util.exportExcel(response, list, "运管服部件数据数据");
    }

    @Log(title = "运管服部件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('dataview:component:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbCityComponent> util = new ExcelUtil<DbCityComponent>(DbCityComponent.class);
        List<DbCityComponent> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbCityComponentService.importDbCityComponent(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbCityComponent> util = new ExcelUtil<DbCityComponent>(DbCityComponent.class);
        util.exportTemplateExcel(response, "运管服部件数据数据");
    }

    /**
     * 获取运管服部件数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dbCityComponentService.selectDbCityComponentById(id));
    }

    /**
     * 新增运管服部件数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:add')")
    @Log(title = "运管服部件数据", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbCityComponent dbCityComponent)
    {
        dbCityComponent.setCreateBy(getUsername());
        if (dbCityComponentService.checkDbCityComponent(dbCityComponent)){
            return error("数据已存在！");
        }
        return toAjax(dbCityComponentService.insertDbCityComponent(dbCityComponent));
    }

    /**
     * 修改运管服部件数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:edit')")
    @Log(title = "运管服部件数据", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbCityComponent dbCityComponent)
    {
        dbCityComponent.setUpdateBy(getUsername());
        if (dbCityComponentService.checkDbCityComponent(dbCityComponent)){
            return error("数据已存在！");
        }
        return toAjax(dbCityComponentService.updateDbCityComponent(dbCityComponent));
    }

    /**
     * 删除运管服部件数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:component:remove')")
    @Log(title = "运管服部件数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbCityComponentService.deleteDbCityComponentByIds(ids));
    }
}
