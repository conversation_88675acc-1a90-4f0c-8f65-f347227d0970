package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 社区干部信息对象 tb_dept_user
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbDeptUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）",  width = 18, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true)
    private String county;

    /**
     * 所属街道/乡镇
     */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /**
     * 所属村/社区
     */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /**
     * 所属社区（部门）
     */
    private Long deptId;

    /**
     * 关联用户
     */
    private Long userId;

    /**
     * 干部姓名
     */
    @Excel(name = "干部姓名")
    private String name;

    @Excel(name = "职务")
    private String postDesc;


    @Excel(name = "证件号码", width = 20)
    private String idCard;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String phone;

    /**
     * 是否是党员（0否 1是）
     */
//    @Excel(name = "是否是党员", combo = {"是", "否"}, readConverterExp = "1=是,0=否")
    private String hasParty;

    /**
     * 是否有社会工作职业水平证书（0否 1是）
     */
//    @Excel(name = "是否有社会工作职业水平证书", combo = {"是", "否"}, readConverterExp = "1=是,0=否", width = 25)
    private String hasCertificate;

    /**
     * 社会工作职业水平证书
     */
//    @Excel(name = "社会工作职业水平证书")
    private String certificate;

    @Excel(name = "干部岗位类型", comboReadDict = true, dictType = "dept_user_type")
    private String postStr;

    /** 出生年月 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生年月", width = 20, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date birthday;


    /** 年龄 */
    @Excel(name = "年龄", type = Excel.Type.EXPORT)
    private Integer age;

    /**
     * 证件照
     */
    @Excel(name = "证件照", cellType = Excel.ColumnType.IMAGE, height = 40, type = Excel.Type.EXPORT)
    private String idPhoto;

    /**
     * 干部状态（0正常 1停用）
     */
    @Excel(name = "干部状态", combo = {"正常", "停用"}, readConverterExp = "0=正常,1=停用", type = Excel.Type.EXPORT)
    private String status;
    /**
     * 不属于该网格ID的人员查询参数
     */
    private Long notGrid;
    /**
     * 属于该网格id的人员查询参数
     */
    private Long gridId;

    private String postLabel;
    /**
     * 搜索
     */
    private List<String> postStrArr;

    public List<String> getPostStrArr() {
        return postStrArr;
    }

    public void setPostStrArr(List<String> postStrArr) {
        this.postStrArr = postStrArr;
    }

    public String getPostLabel() {
        return postLabel;
    }

    public void setPostLabel(String postLabel) {
        this.postLabel = postLabel;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getPostStr() {
        return postStr;
    }

    public void setPostStr(String postStr) {
        this.postStr = postStr;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public Long getNotGrid() {
        return notGrid;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getHasParty() {
        return hasParty;
    }

    public void setHasParty(String hasParty) {
        this.hasParty = hasParty;
    }

    public String getHasCertificate() {
        return hasCertificate;
    }

    public void setHasCertificate(String hasCertificate) {
        this.hasCertificate = hasCertificate;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public void setNotGrid(Long notGrid) {
        this.notGrid = notGrid;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCounty() {
        return county;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTown() {
        return town;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setIdPhoto(String idPhoto) {
        this.idPhoto = idPhoto;
    }

    public String getIdPhoto() {
        return idPhoto;
    }

    public void setPostDesc(String postDesc) {
        this.postDesc = postDesc;
    }

    public String getPostDesc() {
        return postDesc;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("deptId", getDeptId())
                .append("userId", getUserId())
                .append("name", getName())
                .append("phone", getPhone())
                .append("idPhoto", getIdPhoto())
                .append("postDesc", getPostDesc())
                .append("status", getStatus())
                .toString();
    }
}
