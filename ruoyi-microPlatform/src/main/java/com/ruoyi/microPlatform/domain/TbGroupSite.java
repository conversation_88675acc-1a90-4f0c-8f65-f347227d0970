package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 新就业群体站点对象 tb_group_site
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public class TbGroupSite extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县", readConverterExp = "市=、区")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区")
    private String town;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String siteName;

    /** 站点类型 （快递、外卖、网约、其他） */
    @Excel(name = "站点类型 ", readConverterExp = "快=递、外卖、网约、其他")
    private String siteType;

    /** 所属平台 （顺丰、中通、美团、饿了么、滴滴、其他） */
    @Excel(name = "所属平台 ", readConverterExp = "顺=丰、中通、美团、饿了么、滴滴、其他")
    private String sitePlatform;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String siteAddress;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String sitePhone;

    /** 总人数 */
    @Excel(name = "总人数")
    private String totalWorkers;

    /** 年龄结构 */
    @Excel(name = "年龄结构")
    private String ageDistribution;

    /** 党员数 */
    @Excel(name = "党员数")
    private Integer partyPeoples;

    /** 人员流动频率 */
    @Excel(name = "人员流动频率")
    private String turnoverRate;

    /** 报到人数 */
    @Excel(name = "报到人数")
    private Integer reportPeoples;

    /**  */
    @Excel(name = "")
    private String lat;

    /**  */
    @Excel(name = "")
    private String lng;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setSiteName(String siteName) 
    {
        this.siteName = siteName;
    }

    public String getSiteName() 
    {
        return siteName;
    }
    public void setSiteType(String siteType) 
    {
        this.siteType = siteType;
    }

    public String getSiteType() 
    {
        return siteType;
    }
    public void setSitePlatform(String sitePlatform) 
    {
        this.sitePlatform = sitePlatform;
    }

    public String getSitePlatform() 
    {
        return sitePlatform;
    }
    public void setSiteAddress(String siteAddress) 
    {
        this.siteAddress = siteAddress;
    }

    public String getSiteAddress() 
    {
        return siteAddress;
    }
    public void setSitePhone(String sitePhone) 
    {
        this.sitePhone = sitePhone;
    }

    public String getSitePhone() 
    {
        return sitePhone;
    }
    public void setTotalWorkers(String totalWorkers) 
    {
        this.totalWorkers = totalWorkers;
    }

    public String getTotalWorkers() 
    {
        return totalWorkers;
    }
    public void setAgeDistribution(String ageDistribution) 
    {
        this.ageDistribution = ageDistribution;
    }

    public String getAgeDistribution() 
    {
        return ageDistribution;
    }
    public void setPartyPeoples(Integer partyPeoples) 
    {
        this.partyPeoples = partyPeoples;
    }

    public Integer getPartyPeoples() 
    {
        return partyPeoples;
    }
    public void setTurnoverRate(String turnoverRate) 
    {
        this.turnoverRate = turnoverRate;
    }

    public String getTurnoverRate() 
    {
        return turnoverRate;
    }
    public void setReportPeoples(Integer reportPeoples) 
    {
        this.reportPeoples = reportPeoples;
    }

    public Integer getReportPeoples() 
    {
        return reportPeoples;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("siteName", getSiteName())
            .append("siteType", getSiteType())
            .append("sitePlatform", getSitePlatform())
            .append("siteAddress", getSiteAddress())
            .append("sitePhone", getSitePhone())
            .append("totalWorkers", getTotalWorkers())
            .append("ageDistribution", getAgeDistribution())
            .append("partyPeoples", getPartyPeoples())
            .append("turnoverRate", getTurnoverRate())
            .append("reportPeoples", getReportPeoples())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
