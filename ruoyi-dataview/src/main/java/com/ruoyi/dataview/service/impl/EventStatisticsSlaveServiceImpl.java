package com.ruoyi.dataview.service.impl;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import com.ruoyi.dataview.mapper.EventStatisticsSlaveMapper;
import com.ruoyi.dataview.service.IEventStatisticsSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 从库事项统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class EventStatisticsSlaveServiceImpl implements IEventStatisticsSlaveService {

    @Autowired
    private EventStatisticsSlaveMapper eventStatisticsSlaveMapper;

    @Override
    public Map<String, Object> getSlaveEventStatistics(DbBigdataParam dbBigdataParam) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取运管服事项统计
        Integer umEventTodayCount = eventStatisticsSlaveMapper.getUmEventTodayCount(dbBigdataParam);
        Integer umEventPendingCount = eventStatisticsSlaveMapper.getUmEventPendingCount(dbBigdataParam);
        Integer umEventTotalHandledCount = eventStatisticsSlaveMapper.getUmEventTotalHandledCount(dbBigdataParam);
        Integer umEventCompletedCount = eventStatisticsSlaveMapper.getUmEventCompletedCount(dbBigdataParam);
        
        // 获取12345热线统计
        Integer hotlineDetailTodayCount = eventStatisticsSlaveMapper.getHotlineDetailTodayCount(dbBigdataParam);
        Integer hotlineDetailPendingCount = eventStatisticsSlaveMapper.getHotlineDetailPendingCount(dbBigdataParam);
        Integer hotlineDetailTotalHandledCount = eventStatisticsSlaveMapper.getHotlineDetailTotalHandledCount(dbBigdataParam);
        Integer hotlineDetailCompletedCount = eventStatisticsSlaveMapper.getHotlineDetailCompletedCount(dbBigdataParam);
        Map<String, Object> hotlineDetailSatisfactionStats = eventStatisticsSlaveMapper.getHotlineDetailSatisfactionStats(dbBigdataParam);
        
        // 获取万人助企统计
        Integer enterpriseAppealTodayCount = eventStatisticsSlaveMapper.getEnterpriseAppealTodayCount(dbBigdataParam);
        Integer enterpriseAppealTotalCount = eventStatisticsSlaveMapper.getEnterpriseAppealTotalCount(dbBigdataParam);
        
        // 获取应急事项统计
        Integer emergencyEventTodayCount = eventStatisticsSlaveMapper.getEmergencyEventTodayCount(dbBigdataParam);
        Integer emergencyEventPendingCount = eventStatisticsSlaveMapper.getEmergencyEventPendingCount(dbBigdataParam);
        Integer emergencyEventTotalHandledCount = eventStatisticsSlaveMapper.getEmergencyEventTotalHandledCount(dbBigdataParam);
        Integer emergencyEventCompletedCount = eventStatisticsSlaveMapper.getEmergencyEventCompletedCount(dbBigdataParam);
        
        // 获取市长信箱统计
        Integer mayorMailboxTodayCount = eventStatisticsSlaveMapper.getMayorMailboxTodayCount(dbBigdataParam);
        Integer mayorMailboxTotalCount = eventStatisticsSlaveMapper.getMayorMailboxTotalCount(dbBigdataParam);
        
        // 获取信访统计
        Integer petitionInfoTodayCount = eventStatisticsSlaveMapper.getPetitionInfoTodayCount(dbBigdataParam);
        Integer petitionInfoTotalCount = eventStatisticsSlaveMapper.getPetitionInfoTotalCount(dbBigdataParam);
        Integer petitionInfoCompletedCount = eventStatisticsSlaveMapper.getPetitionInfoCompletedCount(dbBigdataParam);
        
        // 汇总数据
        result.put("umEventTodayCount", umEventTodayCount != null ? umEventTodayCount : 0);
        result.put("umEventPendingCount", umEventPendingCount != null ? umEventPendingCount : 0);
        result.put("umEventTotalHandledCount", umEventTotalHandledCount != null ? umEventTotalHandledCount : 0);
        result.put("umEventCompletedCount", umEventCompletedCount != null ? umEventCompletedCount : 0);
        
        result.put("hotlineDetailTodayCount", hotlineDetailTodayCount != null ? hotlineDetailTodayCount : 0);
        result.put("hotlineDetailPendingCount", hotlineDetailPendingCount != null ? hotlineDetailPendingCount : 0);
        result.put("hotlineDetailTotalHandledCount", hotlineDetailTotalHandledCount != null ? hotlineDetailTotalHandledCount : 0);
        result.put("hotlineDetailCompletedCount", hotlineDetailCompletedCount != null ? hotlineDetailCompletedCount : 0);
        result.put("hotlineDetailSatisfactionStats", hotlineDetailSatisfactionStats != null ? hotlineDetailSatisfactionStats : new HashMap<>());
        
        result.put("enterpriseAppealTodayCount", enterpriseAppealTodayCount != null ? enterpriseAppealTodayCount : 0);
        result.put("enterpriseAppealTotalCount", enterpriseAppealTotalCount != null ? enterpriseAppealTotalCount : 0);
        
        result.put("emergencyEventTodayCount", emergencyEventTodayCount != null ? emergencyEventTodayCount : 0);
        result.put("emergencyEventPendingCount", emergencyEventPendingCount != null ? emergencyEventPendingCount : 0);
        result.put("emergencyEventTotalHandledCount", emergencyEventTotalHandledCount != null ? emergencyEventTotalHandledCount : 0);
        result.put("emergencyEventCompletedCount", emergencyEventCompletedCount != null ? emergencyEventCompletedCount : 0);
        
        result.put("mayorMailboxTodayCount", mayorMailboxTodayCount != null ? mayorMailboxTodayCount : 0);
        result.put("mayorMailboxTotalCount", mayorMailboxTotalCount != null ? mayorMailboxTotalCount : 0);
        
        result.put("petitionInfoTodayCount", petitionInfoTodayCount != null ? petitionInfoTodayCount : 0);
        result.put("petitionInfoTotalCount", petitionInfoTotalCount != null ? petitionInfoTotalCount : 0);
        result.put("petitionInfoCompletedCount", petitionInfoCompletedCount != null ? petitionInfoCompletedCount : 0);
        
        return result;
    }
}
