package com.ruoyi.microPlatform.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.bigdata.enumDto.BigDataEnum;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeCommunityFunctionMapper;
import com.ruoyi.microPlatform.domain.SeCommunityFunction;
import com.ruoyi.microPlatform.service.ISeCommunityFunctionService;

import javax.annotation.Resource;

/**
 * 社区功能室Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeCommunityFunctionServiceImpl implements ISeCommunityFunctionService {
    @Autowired
    private SeCommunityFunctionMapper seCommunityFunctionMapper;

    /**
     * 查询社区功能室
     *
     * @param id 社区功能室主键
     * @return 社区功能室
     */
    @Override
    public SeCommunityFunction selectSeCommunityFunctionById(Long id) {
        return seCommunityFunctionMapper.selectSeCommunityFunctionById(id);
    }

    /**
     * 校验社区功能室是否存在
     *
     * @param seCommunityFunction
     * @return boolean
     */
    @Override
    public boolean checkSeCommunityFunction(SeCommunityFunction seCommunityFunction) {
        SeCommunityFunction old = seCommunityFunctionMapper.checkSeCommunityFunctionUnique(seCommunityFunction);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询社区功能室列表
     *
     * @param seCommunityFunction 社区功能室
     * @return 社区功能室
     */
    @Override
    public List<SeCommunityFunction> selectSeCommunityFunctionList(SeCommunityFunction seCommunityFunction) {
        return seCommunityFunctionMapper.selectSeCommunityFunctionList(seCommunityFunction);
    }

    /**
     * 导入社区功能室
     *
     * @param infos           社区功能室列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeCommunityFunction(List<SeCommunityFunction> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入社区功能室数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeCommunityFunction info : infos) {
            try {
                // 验证是否存在这个数据
                SeCommunityFunction old = seCommunityFunctionMapper.checkSeCommunityFunctionUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seCommunityFunctionMapper.insertSeCommunityFunction(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seCommunityFunctionMapper.updateSeCommunityFunction(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增社区功能室
     *
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    @Override
    public int insertSeCommunityFunction(SeCommunityFunction seCommunityFunction) {
        seCommunityFunction.setCreateTime(DateUtils.getNowDate());
        return seCommunityFunctionMapper.insertSeCommunityFunction(seCommunityFunction);
    }

    /**
     * 修改社区功能室
     *
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    @Override
    public int updateSeCommunityFunction(SeCommunityFunction seCommunityFunction) {
        seCommunityFunction.setUpdateTime(DateUtils.getNowDate());
        return seCommunityFunctionMapper.updateSeCommunityFunction(seCommunityFunction);
    }

    /**
     * 批量删除社区功能室
     *
     * @param ids 需要删除的社区功能室主键
     * @return 结果
     */
    @Override
    public int deleteSeCommunityFunctionByIds(Long[] ids) {
        return seCommunityFunctionMapper.deleteSeCommunityFunctionByIds(ids);
    }

    /**
     * 删除社区功能室信息
     *
     * @param id 社区功能室主键
     * @return 结果
     */
    @Override
    public int deleteSeCommunityFunctionById(Long id) {
        return seCommunityFunctionMapper.deleteSeCommunityFunctionById(id);
    }


    @Resource
    private ISysDictDataService sysDictDataService;

    @Override
    public Map<String, Integer> getCommunityFunctionCount(BigdataParam bigdataParam) {
        List<SeCommunityFunction> communityFunctionList = seCommunityFunctionMapper.getCommunityFunctionCountByBigData(bigdataParam);

        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType(BigDataEnum.FUNCTION_TYPE.getDictType());
        List<SysDictData> sysDictDataList = sysDictDataService.selectDictDataList(sysDictData);
        Map<String, Integer> labelCountMap = communityFunctionList.stream()
                .filter(item -> item != null && item.getLabel() != null && StringUtils.isNotEmpty(item.getLabel()) && item.getCount() != null)
                .collect(Collectors.toMap(
                        SeCommunityFunction::getLabel,
                        SeCommunityFunction::getCount,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));
        // 补充分组不到的数据
        sysDictDataList.forEach(e -> {
            if (!labelCountMap.containsKey(e.getDictLabel())) {
                labelCountMap.put(e.getDictValue(), 0);
            }
        });
        int total = labelCountMap.values().stream().mapToInt(Integer::intValue).sum();
        labelCountMap.put("total", total);
        return labelCountMap;
    }
}
