package com.ruoyi.dataview.service.impl;

import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.dataview.domain.dto.SiteParamsQueryDTO;
import com.ruoyi.dataview.domain.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbSiteDataMapper;
import com.ruoyi.dataview.domain.po.DbSiteData;
import com.ruoyi.dataview.service.IDbSiteDataService;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * 水利局涵洞监测数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.SLAVE)
public class DbSiteDataServiceImpl implements IDbSiteDataService 
{
    private final DbSiteDataMapper dbSiteDataMapper;

    private final RestTemplate restTemplate;
    /**
     * 查询水利局涵洞监测数据
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 水利局涵洞监测数据
     */
    @Override
    public DbSiteData selectDbSiteDataById(String id)
    {
        return dbSiteDataMapper.selectDbSiteDataById(id);
    }

    /**
     * 校验水利局涵洞监测数据是否存在
     *
     * @param dbSiteData
     * @return boolean
     */
    @Override
    public boolean checkDbSiteData(DbSiteData dbSiteData){
        DbSiteData old = dbSiteDataMapper.checkDbSiteDataUnique(dbSiteData);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询水利局涵洞监测数据列表
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 水利局涵洞监测数据
     */
    @Override
    public List<DbSiteData> selectDbSiteDataList(DbSiteData dbSiteData)
    {
        return dbSiteDataMapper.selectDbSiteDataList(dbSiteData);
    }

    /**
     * 导入水利局涵洞监测数据
     *
     * @param infos       水利局涵洞监测数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbSiteData(List<DbSiteData> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入水利局涵洞监测数据数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbSiteData info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                DbSiteData old = dbSiteDataMapper.checkDbSiteDataUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbSiteDataMapper.insertDbSiteData(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbSiteDataMapper.updateDbSiteData(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    @Override
    public int insertDbSiteData(DbSiteData dbSiteData)
    {
        dbSiteData.setCreateTime(DateUtils.getNowDate());
        return dbSiteDataMapper.insertDbSiteData(dbSiteData);
    }

    /**
     * 修改水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    @Override
    public int updateDbSiteData(DbSiteData dbSiteData)
    {
        return dbSiteDataMapper.updateDbSiteData(dbSiteData);
    }

    /**
     * 批量删除水利局涵洞监测数据
     * 
     * @param ids 需要删除的水利局涵洞监测数据主键
     * @return 结果
     */
    @Override
    public int deleteDbSiteDataByIds(String[] ids)
    {
        return dbSiteDataMapper.deleteDbSiteDataByIds(ids);
    }

    /**
     * 删除水利局涵洞监测数据信息
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 结果
     */
    @Override
    public int deleteDbSiteDataById(String id)
    {
        return dbSiteDataMapper.deleteDbSiteDataById(id);
    }

    // Nginx 代理的域名（运维提供）
    private final String PROXY_BASE_URL = "http://117.159.53.11:60631/";


    /**
     * 防汛视频登陆
     * @param login 账号
     * @param pwd   密码
     * @return cookie： NFSESSIONID
     */
    @Override
    public Map<String,String> siteSysLogin(String login, String pwd) {
        String url = PROXY_BASE_URL + "/xfServe/groupApi/sysLogin";
        Map<String,String> result = new HashMap<>();
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("login",login);
        requestBody.put("pwd",pwd);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> entity = new HttpEntity<Map<String, String>>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        if (response.getStatusCodeValue() == 200){
            // 从 Set-Cookie 里取出 NFSESSIONID
            List<String> cookies = response.getHeaders().get(HttpHeaders.SET_COOKIE);
            if (cookies != null) {
                for (String cookie : cookies) {
                    if (cookie.startsWith("NFSESSIONID")) {
                        result.put("NFSESSIONID", cookie.split(";")[0].split("=")[1]);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 代理请求：/xfServe/nfQuery/groups/selectSiteNoByGroupsNo
     * @param NFSESSIONID cookies
     * @return  涵洞监测站点
     */
    @Override
    public List<SiteGroupsResponseVO.DataItem> proxyRequestGetSiteGroups(String NFSESSIONID) {
        String path = "/xfServe/nfQuery/groups/selectSiteNoByGroupsNo";
        String url = PROXY_BASE_URL + path;

        // 拼接 GET 参数
        URI uri = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("platformCode", 11224)
                .queryParam("siteName", "")
                .build()
                .encode()
                .toUri();

        // 设置请求头，带上 Cookie
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.COOKIE, "NFSESSIONID=" + NFSESSIONID);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<SiteGroupsResponseVO> response = restTemplate.exchange(
                uri,
                HttpMethod.GET,
                entity,
                SiteGroupsResponseVO.class
        );

        List<SiteGroupsResponseVO.DataItem> body = response.getBody().getData();
        return body;
    }

    /**
     * 代理请求：/xfServe/nfQuery/data/selectSiteSiteManBySerial
     * @param stCode
     * @return  涵洞监测站点监测数据详情
     */
    @Override
    public SiteDetailsDataResponseVO proxyRequestgGetSiteDetailsByCode(String stCode, String NFSESSIONID) {
        String path = "/xfServe/nfQuery/data/selectSiteSiteManBySerial";
        String url = PROXY_BASE_URL + path;

        // 拼接 GET 参数
        URI uri = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("stCode", stCode)
                .build()
                .encode()
                .toUri();

        // 设置请求头，带上 Cookie
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.COOKIE, "NFSESSIONID=" + NFSESSIONID);
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        ResponseEntity<SiteDetailsDataResponseVO> response = restTemplate.exchange(
                uri,
                HttpMethod.GET,
                entity,
                SiteDetailsDataResponseVO.class
        );
        return response.getBody();
    }


    /**
     * 代理请求：/xfServe/nfQuery/data/selectSiteSiteManHistoryDataMongoBySerial
     * @param NFSESSIONID cookie
     * @param queryDTO 参数
     * @return  涵洞监测站点历史监测数据
     */
    @Override
    public TableDataInfo proxyRequestGetSiteHistoryDateList(SiteParamsQueryDTO queryDTO, String NFSESSIONID) {
        String path = "/xfServe/nfQuery/data/selectSiteSiteManHistoryDataMongoBySerial";
        String url = PROXY_BASE_URL + path;

        // 拼接 GET 参数
        URI uri = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("dName", queryDTO.getDName())
                .queryParam("rows", queryDTO.getRows())
                .queryParam("page", queryDTO.getPage())
                .queryParam("startTime", queryDTO.getStartTime())
                .queryParam("endTime", queryDTO.getEndTime())
                .queryParam("invalidValue", queryDTO.getInvalidValue())
                .build()
                .encode()
                .toUri();

        // 设置请求头，带上 Cookie
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.COOKIE, "NFSESSIONID=" + NFSESSIONID);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<SiteHistoryDataResponseVO> response = restTemplate.exchange(
                uri,
                HttpMethod.GET,
                entity,
                SiteHistoryDataResponseVO.class
        );
        Integer total = response.getBody().getTotal();
        List<SiteHistoryDataResponseVO.DataItem> body = response.getBody().getRows();

        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(body);
        tableDataInfo.setTotal(total);

        return tableDataInfo;
    }

    /**
     * 代理请求：/xfServe/nfQuery/data/siteDataEchat
     * @param queryDTO 参数
     * @param NFSESSIONID cookie
     * @return 涵洞监测站点历史监测数据图形
     */
    @Override
    public SiteEchartVO proxyRequestGetSiteEchart(SiteParamsQueryDTO queryDTO, String NFSESSIONID) {
        String path = "/xfServe/nfQuery/data/siteDataEchat";
        String url = PROXY_BASE_URL + path;

        // 拼接 GET 参数
        URI uri = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("dName", queryDTO.getDName())
                .queryParam("up", queryDTO.getUp())
                .queryParam("down", queryDTO.getDown())
                .queryParam("startTime", queryDTO.getStartTime())
                .queryParam("endTime", queryDTO.getEndTime())
                .queryParam("invalidValue", queryDTO.getInvalidValue())
                .build()
                .encode()
                .toUri();

        // 设置请求头，带上 Cookie
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.COOKIE, "NFSESSIONID=" + NFSESSIONID);
        HttpEntity<Void> entity = new HttpEntity<>(headers);
        ResponseEntity<SiteEchartVO> response = restTemplate.exchange(
                uri,
                HttpMethod.GET,
                entity,
                SiteEchartVO.class
        );
        SiteEchartVO body = response.getBody();
        return body;
    }

    @Override
    public List<SiteStatisticsVO.DataItem> proxyRequestGetSiteStatistics(SiteParamsQueryDTO queryDTO, String NFSESSIONID) {
        String path = "/xfServe/nfQuery/data/selectStationBaseStatisticsHistoryDataMongodb";
        String url = PROXY_BASE_URL + path;

        // 拼接 GET 参数
        // 请求体（把参数放进去）
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("endTime", queryDTO.getEndTime());
        requestBody.put("stCode", queryDTO.getStCode());
        requestBody.put("startTime", queryDTO.getStartTime());


        // 设置请求头，带上 Cookie
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add(HttpHeaders.COOKIE, "NFSESSIONID=" + NFSESSIONID);
        // 封装成 HttpEntity
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        // 发送 POST 请求
        ResponseEntity<SiteStatisticsVO> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                SiteStatisticsVO.class
        );
        List<SiteStatisticsVO.DataItem> body = response.getBody().getData();
        return body;
    }
}
