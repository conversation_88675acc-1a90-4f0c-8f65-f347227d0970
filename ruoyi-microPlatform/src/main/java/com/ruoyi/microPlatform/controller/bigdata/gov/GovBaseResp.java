package com.ruoyi.microPlatform.controller.bigdata.gov;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * VclustersBaseResp类是一个泛型类，用于封装API响应。
 * 它包含错误码、错误信息和数据三个字段，适用于各种类型的响应数据。
 * 通过泛型T，可以使得这个类能够适应不同类型的响应数据结构。
 */

/**
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GovBaseResp<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码，用于标识响应中的错误类型。
     * 0表示没有错误，其他值表示不同的错误类型。
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String reason;

    /**
     * 响应数据，使用泛型T表示。
     * 根据不同的请求，该字段可以包含不同类型的数据。
     */
    private T result;

    /**
     * 判断响应是否成功的方法。
     *
     * @return 如果响应成功（即错误码为0），返回true；否则返回false。
     */
    public boolean ok() {
        return 200 == code || 0 == code;
    }
}
