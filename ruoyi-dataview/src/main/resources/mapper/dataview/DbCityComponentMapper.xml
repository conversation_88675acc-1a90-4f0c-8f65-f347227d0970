<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbCityComponentMapper">

    <resultMap type="DbCityComponent" id="DbCityComponentResult">
            <result property="id"    column="id"    />
            <result property="majorCode"    column="major_code"    />
            <result property="majorName"    column="major_name"    />
            <result property="minorCode"    column="minor_code"    />
            <result property="minorName"    column="minor_name"    />
            <result property="quantity"    column="quantity"    />
            <result property="description"    column="description"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDbCityComponentVo">
        select id, major_code, major_name, minor_code, minor_name, quantity, description, create_time, update_time from db_city_component
    </sql>

    <select id="selectDbCityComponentList" parameterType="DbCityComponent" resultMap="DbCityComponentResult">
        <include refid="selectDbCityComponentVo"/>
        <where>
                        <if test="majorCode != null  and majorCode != ''"> and major_code = #{majorCode}</if>
                        <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
                        <if test="minorCode != null  and minorCode != ''"> and minor_code = #{minorCode}</if>
                        <if test="minorName != null  and minorName != ''"> and minor_name like concat('%', #{minorName}, '%')</if>
                        <if test="quantity != null "> and quantity = #{quantity}</if>
                        <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>

    <select id="selectDbCityComponentById" parameterType="Long" resultMap="DbCityComponentResult">
            <include refid="selectDbCityComponentVo"/>
            where id = #{id}
    </select>

    <select id="checkDbCityComponentUnique" parameterType="DbCityComponent" resultMap="DbCityComponentResult">
        select id from db_city_component where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertDbCityComponent" parameterType="DbCityComponent" useGeneratedKeys="true" keyProperty="id">
        insert into db_city_component
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="majorCode != null and majorCode != ''">major_code,</if>
                    <if test="majorName != null and majorName != ''">major_name,</if>
                    <if test="minorCode != null">minor_code,</if>
                    <if test="minorName != null">minor_name,</if>
                    <if test="quantity != null">quantity,</if>
                    <if test="description != null">description,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="majorCode != null and majorCode != ''">#{majorCode},</if>
                    <if test="majorName != null and majorName != ''">#{majorName},</if>
                    <if test="minorCode != null">#{minorCode},</if>
                    <if test="minorName != null">#{minorName},</if>
                    <if test="quantity != null">#{quantity},</if>
                    <if test="description != null">#{description},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDbCityComponent" parameterType="DbCityComponent">
        update db_city_component
        <trim prefix="SET" suffixOverrides=",">
                    <if test="majorCode != null and majorCode != ''">major_code = #{majorCode},</if>
                    <if test="majorName != null and majorName != ''">major_name = #{majorName},</if>
                    <if test="minorCode != null">minor_code = #{minorCode},</if>
                    <if test="minorName != null">minor_name = #{minorName},</if>
                    <if test="quantity != null">quantity = #{quantity},</if>
                    <if test="description != null">description = #{description},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbCityComponentById" parameterType="Long">
        delete from db_city_component where id = #{id}
    </delete>

    <delete id="deleteDbCityComponentByIds" parameterType="String">
        delete from db_city_component where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDbCityComponentCount" parameterType="DbCityComponent" resultType="integer">
        select count(*) from db_city_component
        <where>
            <if test="majorCode != null  and majorCode != ''"> and major_code = #{majorCode}</if>
            <if test="majorName != null  and majorName != ''"> and major_name like concat('%', #{majorName}, '%')</if>
            <if test="minorCode != null  and minorCode != ''"> and minor_code = #{minorCode}</if>
            <if test="minorName != null  and minorName != ''"> and minor_name like concat('%', #{minorName}, '%')</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
        </where>
    </select>
    <select id="selectCountGroupByMajorCode"
            resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        select major_name lable,count(*) count from db_city_component
        group by major_name
    </select>
</mapper>