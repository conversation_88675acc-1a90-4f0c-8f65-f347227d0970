package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbGridUser;

/**
 * 网格员信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbGridUserMapper 
{
    /**
     * 查询网格员信息
     * 
     * @param id 网格员信息主键
     * @return 网格员信息
     */
    public TbGridUser selectTbGridUserById(Long id);

    /**
     * 校验网格员信息是否唯一
     *
     * @param tbGridUser
     * @return 网格员信息
     */
    public TbGridUser checkTbGridUserUnique(TbGridUser tbGridUser);

    /**
     * 获取网格长
     * @param tbGridUser
     * @return
     */
    public TbGridUser checkGridUserBig(TbGridUser tbGridUser);

    /**
     * 查询网格员信息列表
     * 
     * @param tbGridUser 网格员信息
     * @return 网格员信息集合
     */
    public List<TbGridUser> selectTbGridUserList(TbGridUser tbGridUser);

    public List<Long> selectTbGridUserCount(TbGridUser tbGridUser);

    /**
     * 新增网格员信息
     * 
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    public int insertTbGridUser(TbGridUser tbGridUser);

    /**
     * 修改网格员信息
     * 
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    public int updateTbGridUser(TbGridUser tbGridUser);

    /**
     * 删除网格员信息
     * 
     * @param id 网格员信息主键
     * @return 结果
     */
    public int deleteTbGridUserById(Long id);

    /**
     * 批量删除网格员信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbGridUserByIds(Long[] ids);


    public List<TbGridUser> selectTbGridUserList1(TbGridUser tbGridUser);

    List<Long> selectGridIdByPhone(String phone);



    Integer selectTbGridUserCountByDept(TbGridUser tbGridUser);
}
