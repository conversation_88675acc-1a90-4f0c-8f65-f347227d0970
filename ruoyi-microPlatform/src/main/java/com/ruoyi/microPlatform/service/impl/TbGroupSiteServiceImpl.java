package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbGroupSiteMapper;
import com.ruoyi.microPlatform.domain.TbGroupSite;
import com.ruoyi.microPlatform.service.ITbGroupSiteService;

/**
 * 新就业群体站点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class TbGroupSiteServiceImpl implements ITbGroupSiteService 
{
    @Autowired
    private TbGroupSiteMapper tbGroupSiteMapper;

    /**
     * 查询新就业群体站点
     * 
     * @param id 新就业群体站点主键
     * @return 新就业群体站点
     */
    @Override
    public TbGroupSite selectTbGroupSiteById(Long id)
    {
        return tbGroupSiteMapper.selectTbGroupSiteById(id);
    }

    /**
     * 校验新就业群体站点是否存在
     *
     * @param tbGroupSite
     * @return boolean
     */
    @Override
    public boolean checkTbGroupSite(TbGroupSite tbGroupSite){
        TbGroupSite old = tbGroupSiteMapper.checkTbGroupSiteUnique(tbGroupSite);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询新就业群体站点列表
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 新就业群体站点
     */
    @Override
    public List<TbGroupSite> selectTbGroupSiteList(TbGroupSite tbGroupSite)
    {
        return tbGroupSiteMapper.selectTbGroupSiteList(tbGroupSite);
    }

    /**
     * 导入新就业群体站点
     *
     * @param infos       新就业群体站点列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGroupSite(List<TbGroupSite> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入新就业群体站点数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbGroupSite info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbGroupSite old = tbGroupSiteMapper.checkTbGroupSiteUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbGroupSiteMapper.insertTbGroupSite(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbGroupSiteMapper.updateTbGroupSite(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    @Override
    public int insertTbGroupSite(TbGroupSite tbGroupSite)
    {
        tbGroupSite.setCreateTime(DateUtils.getNowDate());
        return tbGroupSiteMapper.insertTbGroupSite(tbGroupSite);
    }

    /**
     * 修改新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    @Override
    public int updateTbGroupSite(TbGroupSite tbGroupSite)
    {
        tbGroupSite.setUpdateTime(DateUtils.getNowDate());
        return tbGroupSiteMapper.updateTbGroupSite(tbGroupSite);
    }

    /**
     * 批量删除新就业群体站点
     * 
     * @param ids 需要删除的新就业群体站点主键
     * @return 结果
     */
    @Override
    public int deleteTbGroupSiteByIds(Long[] ids)
    {
        return tbGroupSiteMapper.deleteTbGroupSiteByIds(ids);
    }

    /**
     * 删除新就业群体站点信息
     * 
     * @param id 新就业群体站点主键
     * @return 结果
     */
    @Override
    public int deleteTbGroupSiteById(Long id)
    {
        return tbGroupSiteMapper.deleteTbGroupSiteById(id);
    }
}
