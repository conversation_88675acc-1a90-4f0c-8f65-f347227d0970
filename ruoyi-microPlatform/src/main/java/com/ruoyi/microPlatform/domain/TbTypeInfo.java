package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.TreeEntity;

/**
 * 矛盾纠纷类别编码对象 tb_type_info
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public class TbTypeInfo extends TreeEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;

    /** 类别编码 */
    @Excel(name = "类别编码")
    private String oneCode;

    /** 类别名称 */
    @Excel(name = "类别名称")
    private String oneName;

    /** 父级编码 */
    @Excel(name = "父级编码")
    private String parentCode;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setOneCode(String oneCode) 
    {
        this.oneCode = oneCode;
    }

    public String getOneCode() 
    {
        return oneCode;
    }
    public void setOneName(String oneName) 
    {
        this.oneName = oneName;
    }

    public String getOneName() 
    {
        return oneName;
    }
    public void setParentCode(String parentCode) 
    {
        this.parentCode = parentCode;
    }

    public String getParentCode() 
    {
        return parentCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("oneCode", getOneCode())
            .append("oneName", getOneName())
            .append("parentCode", getParentCode())
            .append("parentName", getParentName())
            .toString();
    }
}
