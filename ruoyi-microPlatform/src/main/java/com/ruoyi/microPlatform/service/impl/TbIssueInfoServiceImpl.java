package com.ruoyi.microPlatform.service.impl;

import java.util.*;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.microPlatform.mapper.TbIssueInfoMapper;
import com.ruoyi.microPlatform.service.ITbIssueInfoService;

/**
 * 矛盾纠纷Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@Service
public class TbIssueInfoServiceImpl implements ITbIssueInfoService {
    @Autowired
    private TbIssueInfoMapper tbIssueInfoMapper;

    /**
     * 查询矛盾纠纷
     *
     * @param id 矛盾纠纷主键
     * @return 矛盾纠纷
     */
    @Override
    public TbIssueInfo selectTbIssueInfoById(Long id) {
        return tbIssueInfoMapper.selectTbIssueInfoById(id);
    }

    /**
     * 校验矛盾纠纷是否存在
     *
     * @param tbIssueInfo
     * @return boolean
     */
    @Override
    public boolean checkTbIssueInfo(TbIssueInfo tbIssueInfo) {
        TbIssueInfo old = tbIssueInfoMapper.checkTbIssueInfoUnique(tbIssueInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询矛盾纠纷列表
     *
     * @param tbIssueInfo 矛盾纠纷
     * @return 矛盾纠纷
     */
    @Override
    public List<TbIssueInfo> selectTbIssueInfoList(TbIssueInfo tbIssueInfo) {
        return tbIssueInfoMapper.selectTbIssueInfoList(tbIssueInfo);
    }

    /**
     * 导入矛盾纠纷
     *
     * @param infos           矛盾纠纷列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbIssueInfo(List<TbIssueInfo> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入矛盾纠纷数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbIssueInfo info : infos) {
            try {
                // 验证是否存在这个数据
                TbIssueInfo old = tbIssueInfoMapper.checkTbIssueInfoUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbIssueInfoMapper.insertTbIssueInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbIssueInfoMapper.updateTbIssueInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增矛盾纠纷
     *
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTbIssueInfo(TbIssueInfo tbIssueInfo) {
        tbIssueInfo.setCreateTime(DateUtils.getNowDate());
        tbIssueInfo.setMdjfdjsj(DateUtils.getNowDate());
        List<TbMainParty> tbMainPartyList = tbIssueInfo.getTbMainPartyList();
        if (CollUtil.isNotEmpty(tbMainPartyList)) {
            tbIssueInfo.setPersonNum(tbMainPartyList.size());

            if (tbIssueInfo.getFormId() != null && StringUtils.isNotBlank(tbIssueInfo.getDataFrom())) {
                StringBuilder dsrxms = new StringBuilder();
                for (TbMainParty party : tbMainPartyList) {
                    dsrxms.append(party.getDsrxm()).append("、");
                }

                // 去掉最后一个顿号
                if (dsrxms.length() > 0) {
                    dsrxms.deleteCharAt(dsrxms.length() - 1);
                }
                String title = tbIssueInfo.getMdjfmc() + dsrxms + "发生了" + tbIssueInfo.getMdjflbmc() + "纠纷";
                tbIssueInfo.setMdjfmc(title);
            }
        }
//        //查找字典赋值状态名称
//        List<SysDictData> mdjfStatus = DictUtils.getDictCache("mdjf_status");
//        Map<String, String > map = mdjfStatus.stream()
//                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
//        if (StringUtils.isNotBlank(tbIssueInfo.getMdjfztdm())){
//            tbIssueInfo.setMdjfztmc(map.get(tbIssueInfo.getMdjfztdm()));
//        }

        int rows = tbIssueInfoMapper.insertTbIssueInfo(tbIssueInfo);
        insertTbMainParty(tbIssueInfo);
        return rows;
    }

    /**
     * 修改矛盾纠纷
     *
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTbIssueInfo(TbIssueInfo tbIssueInfo) {
        tbIssueInfo.setUpdateTime(DateUtils.getNowDate());
        tbIssueInfoMapper.deleteTbMainPartyByIssueId(tbIssueInfo.getId());
        insertTbMainParty(tbIssueInfo);
        return tbIssueInfoMapper.updateTbIssueInfo(tbIssueInfo);
    }

    /**
     * 批量删除矛盾纠纷
     *
     * @param ids 需要删除的矛盾纠纷主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbIssueInfoByIds(Long[] ids) {
        tbIssueInfoMapper.deleteTbMainPartyByIssueIds(ids);
        return tbIssueInfoMapper.deleteTbIssueInfoByIds(ids);
    }

    /**
     * 删除矛盾纠纷信息
     *
     * @param id 矛盾纠纷主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbIssueInfoById(Long id) {
        tbIssueInfoMapper.deleteTbMainPartyByIssueId(id);
        return tbIssueInfoMapper.deleteTbIssueInfoById(id);
    }

    /**
     * 新增矛盾纠纷当事人信息信息
     *
     * @param tbIssueInfo 矛盾纠纷对象
     */
    public void insertTbMainParty(TbIssueInfo tbIssueInfo) {
        List<TbMainParty> tbMainPartyList = tbIssueInfo.getTbMainPartyList();
        if (ObjectUtil.isEmpty(tbMainPartyList)) {
            return;
        }

        List<SysDictData> sexType = DictUtils.getDictCache("issue_sex_type"); //当事人性别
        Map<String, String> sexMap = sexType.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<SysDictData> zjlbType = DictUtils.getDictCache("issue_zjlb_type"); //当事人证件种类
        Map<String, String> zjlbMap = zjlbType.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<SysDictData> hyzkType = DictUtils.getDictCache("issue_hyzk_type"); //婚姻状况(矛盾
        Map<String, String> hyzkMap = hyzkType.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<SysDictData> whcdType = DictUtils.getDictCache("issue_whcd_type"); //文化程度(矛盾)
        Map<String, String> whcdMap = whcdType.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));

        List<SysDictData> mzType = DictUtils.getDictCache("issue_mz_type"); //民族(矛盾)
        Map<String, String> mzMap = mzType.stream()
                .collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));


        Long id = tbIssueInfo.getId();
        if (ObjectUtil.isNotEmpty(tbMainPartyList)) {
            List<TbMainParty> list = new ArrayList<TbMainParty>();
            for (TbMainParty tbMainParty : tbMainPartyList) {
                tbMainParty.setIssueId(id);
                list.add(tbMainParty);
                if (tbMainParty.getXbdm() != null) {
                    tbMainParty.setXb(sexMap.get(tbMainParty.getXbdm()));
                }
                if (tbMainParty.getDsrzjzldm() != null) {
                    tbMainParty.setDsrzjzlmc(zjlbMap.get(tbMainParty.getDsrzjzldm()));
                }
                if (tbMainParty.getHyzkdm() != null) {
                    tbMainParty.setHyzk(hyzkMap.get(tbMainParty.getHyzkdm()));
                }
                if (tbMainParty.getWhcddm() != null) {
                    tbMainParty.setWhcd(whcdMap.get(tbMainParty.getWhcddm()));
                }
                if (tbMainParty.getMzdm() != null) {
                    tbMainParty.setMz(mzMap.get(tbMainParty.getMzdm()));
                }

            }
            if (list.size() > 0) {
                tbIssueInfoMapper.batchTbMainParty(list);
            }
        }
    }


    @Override
    public List<HandleStepVO> getHandleStepInfo(Long issueId) {

        return tbIssueInfoMapper.getHandleStepInfo(issueId);
    }

    @Override
    public TransferIssueInfoVO getTransferInfo(Long issueId) {
        return tbIssueInfoMapper.getTransferInfo(issueId);
    }

    @Override
    public List<CommonBaseCount> statusCount() {
        List<CommonBaseCount> commonBaseCounts = tbIssueInfoMapper.statusCount();
        // 归并为两类：已化解、未化解
        int resolved = 0;
        int unresolved = 0;

        if (commonBaseCounts != null) {
            for (CommonBaseCount item : commonBaseCounts) {
                String label = item.getLable();
                int c = item.getCount() == null ? 0 : item.getCount();
                // 以下状态视为“已化解”，其他（包括空）视为“未化解”
                if ("公安已化解".equals(label) || "综治已化解".equals(label) || "已化解".equals(label)) {
                    resolved += c;
                } else {
                    unresolved += c;
                }
            }
        }

        List<CommonBaseCount> res = new ArrayList<>();
        CommonBaseCount y = new CommonBaseCount();
        y.setLable("已化解");
        y.setCount(resolved);
        res.add(y);

        CommonBaseCount w = new CommonBaseCount();
        w.setLable("未化解");
        w.setCount(unresolved);
        res.add(w);

        return res;
    }
}
