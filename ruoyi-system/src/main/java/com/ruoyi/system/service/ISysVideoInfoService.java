package com.ruoyi.system.service;

import java.util.List;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysVideoInfo;

/**
 * 视频管理Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
public interface ISysVideoInfoService 
{
    /**
     * 查询视频管理
     * 
     * @param id 视频管理主键
     * @return 视频管理
     */
    public SysVideoInfo selectSysVideoInfoById(Long id);

    /**
     * 查询视频管理列表
     * 
     * @param sysVideoInfo 视频管理
     * @return 视频管理集合
     */
    public List<SysVideoInfo> selectSysVideoInfoList(SysVideoInfo sysVideoInfo);

    /**
     * 新增视频管理
     * 
     * @param sysVideoInfo 视频管理
     * @return 结果
     */
    public int insertSysVideoInfo(SysVideoInfo sysVideoInfo);

    /**
     * 修改视频管理
     * 
     * @param sysVideoInfo 视频管理
     * @return 结果
     */
    public int updateSysVideoInfo(SysVideoInfo sysVideoInfo);

    /**
     * 批量删除视频管理
     * 
     * @param ids 需要删除的视频管理主键集合
     * @return 结果
     */
    public int deleteSysVideoInfoByIds(Long[] ids);

    /**
     * 删除视频管理信息
     * 
     * @param id 视频管理主键
     * @return 结果
     */
    public int deleteSysVideoInfoById(Long id);
}
