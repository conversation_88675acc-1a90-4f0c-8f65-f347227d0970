<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMerchantAssistantMapper">

    <resultMap type="TbMerchantAssistant" id="TbMerchantAssistantResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="name" column="name"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="birthday" column="birthday" jdbcType="DATE"/>
        <result property="createTime" column="create_time"/>
        <result property="age" column="age"/>
    </resultMap>

    <sql id="selectTbMerchantAssistantVo">
        select m.id,m. merchant_id, m.name, m.id_card, m.phone, m.create_time, m.birthday,
        i.merchant_name, i.dept_id, i.county, i.country, i.town, i.grid_id, i.grid_name,
            IFNULL(TIMESTAMPDIFF(YEAR, m.birthday, CURDATE()), null) AS age from tb_merchant_assistant m
        left join tb_merchant_info i on m.merchant_id = i.id
    </sql>
    <sql id="queryTbMerchantAssistantVo">
        <where>
            <if test="merchantId != null ">and m.merchant_id = #{merchantId}</if>
            <if test="deptId != null ">and (i.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = i.dept_id))</if>
            <if test="gridId != null ">and i.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and i.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name != null  and name != ''">and m.name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''">and m.id_card = #{idCard}</if>
            <if test="phone != null  and phone != ''">and m.phone = #{phone}</if>
        </where>
    </sql>

    <select id="selectTbMerchantAssistantList" parameterType="TbMerchantAssistant"
            resultMap="TbMerchantAssistantResult">
        <include refid="selectTbMerchantAssistantVo"/>
        <include refid="queryTbMerchantAssistantVo"/>
        order by m.merchant_id desc, m.id desc
    </select>

    <select id="selectTbMerchantAssistantCount" parameterType="TbMerchantAssistant"
            resultType="Long">
        select distinct m.id from tb_merchant_assistant m
        left join tb_merchant_info i on m.merchant_id = i.id
        <include refid="queryTbMerchantAssistantVo"/>
    </select>

    <select id="selectTbMerchantAssistantById" parameterType="Long" resultMap="TbMerchantAssistantResult">
        <include refid="selectTbMerchantAssistantVo"/>
        where m.id = #{id}
    </select>

    <select id="checkTbMerchantAssistantUnique" parameterType="TbMerchantAssistant"
            resultMap="TbMerchantAssistantResult">
        select id from tb_merchant_assistant where merchant_id = #{merchantId} and id_card = #{idCard}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMerchantAssistant" parameterType="TbMerchantAssistant" useGeneratedKeys="true" keyProperty="id">
        insert into tb_merchant_assistant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">merchant_id,</if>
            <if test="name != null">name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="phone != null">phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="birthday != null">birthday,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">#{merchantId},</if>
            <if test="name != null">#{name},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="phone != null">#{phone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="birthday != null">#{birthday},</if>
        </trim>
    </insert>

    <update id="updateTbMerchantAssistant" parameterType="TbMerchantAssistant">
        update tb_merchant_assistant
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbMerchantAssistantById" parameterType="Long">
        delete from tb_merchant_assistant where id = #{id}
    </delete>

    <delete id="deleteTbMerchantAssistantByIds" parameterType="String">
        delete from tb_merchant_assistant where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbMerchantAssistantByMainIds" parameterType="String">
        delete from tb_merchant_assistant where merchant_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <insert id="batchTbMerchantAssistant">
        insert into tb_merchant_assistant(  merchant_id, name, id_card, phone, create_time, birthday) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{main.id}, #{item.name}, #{item.idCard}, #{item.phone}, #{item.createTime}, #{item.birthday})
        </foreach>
    </insert>
</mapper>