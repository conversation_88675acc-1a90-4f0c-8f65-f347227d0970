package com.ruoyi.common.utils.poi;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

@Component
public class RedisCacheUtil {

    public static RedisCacheUtil nationUtilUtil;

    @Autowired
    private RedisCache redisCache;

    @PostConstruct
    public void init(){
        nationUtilUtil = this;
        nationUtilUtil.redisCache = this.redisCache;
    }

    public Map<String, List<String>> getCountryMap(){
        return nationUtilUtil.redisCache.getCacheObject("NATION_XuChang_Child_List");
    }

    public Map<String, List<String>> getMapObject(String key){
        return nationUtilUtil.redisCache.getCacheObject(key);
    }

    public  List<String> getCacheList(String key){
        return nationUtilUtil.redisCache.getCacheObject(key);
    }

    public  String[] getCacheArray(String key){
        JSONArray objects = nationUtilUtil.redisCache.getCacheObject(key);
        String[] strings = new String[objects.size()];
        return objects.toArray(strings);
    }

    public Object checkRedisObject(String key){
        return nationUtilUtil.redisCache.getCacheObject(key);
    }
}
