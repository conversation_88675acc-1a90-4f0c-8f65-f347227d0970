package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.service.ITbIssueInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 矛盾纠纷Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@RestController
@RequestMapping("/issue/issueInfo")
public class TbIssueInfoController extends BaseController
{
    @Autowired
    private ITbIssueInfoService tbIssueInfoService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询矛盾纠纷列表
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbIssueInfo tbIssueInfo)
    {
        if (tbIssueInfo.getGridId() != null){
            SysDept sysDept = sysDeptMapper.selectByGridId(tbIssueInfo.getGridId());
            tbIssueInfo.setDeptId(sysDept.getParentId());
        }
        handle(tbIssueInfo);
        startPage();
        List<TbIssueInfo> list = tbIssueInfoService.selectTbIssueInfoList(tbIssueInfo);
        return getDataTable(list);
    }

    public void getDeptInfo(TbIssueInfo tbIssueInfo) {
        if (ObjectUtil.isEmpty(tbIssueInfo.getDeptId())){
            tbIssueInfo.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbIssueInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            tbIssueInfo.setCounty(split[1]);
            if (split.length >= 3){
                tbIssueInfo.setCountry(split[2]);
                if (split.length >= 4){
                    tbIssueInfo.setTown(split[3]);
                    if (split.length >= 5){
                        tbIssueInfo.setGridName(split[4]);
                    }
                }
            }
        }
    }

    /**
     * 权限控制逻辑：处理非管理员用户的数据范围
     */
    public void handle(TbIssueInfo tbIssueInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbIssueInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(tbIssueInfo.getDeptId())) {
                    tbIssueInfo.setDeptId(getDataDeptId());
                }else {
                    if (tbIssueInfo.getDeptId().longValue() == 1){
                        tbIssueInfo.setDeptId(null);
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(tbIssueInfo.getDeptId())){
            getDeptInfo1(tbIssueInfo);
            tbIssueInfo.setDeptId(null);
        }
    }

    public void getDeptInfo1(TbIssueInfo tbIssueInfo) {
        SysDept dept = getDept(tbIssueInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            tbIssueInfo.setCounty(split[1]);
            if (split.length >= 3){
                tbIssueInfo.setCountry(split[2]);
                if (split.length >= 4){
                    tbIssueInfo.setTown(split[3]);
                    if (split.length >= 5){
                        tbIssueInfo.setGridName(split[4]);
                    }
                }
            }
        }
    }


    /**
     * 导出矛盾纠纷列表
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:export')")
    @Log(title = "矛盾纠纷", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbIssueInfo tbIssueInfo)
    {
        getDeptInfo(tbIssueInfo);
        List<TbIssueInfo> list = tbIssueInfoService.selectTbIssueInfoList(tbIssueInfo);
        ExcelUtil<TbIssueInfo> util = new ExcelUtil<TbIssueInfo>(TbIssueInfo.class);
        util.exportExcel(response, list, "矛盾纠纷数据");
    }

    @Log(title = "矛盾纠纷", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbIssueInfo> util = new ExcelUtil<TbIssueInfo>(TbIssueInfo.class);
        List<TbIssueInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbIssueInfoService.importTbIssueInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbIssueInfo> util = new ExcelUtil<TbIssueInfo>(TbIssueInfo.class);
        util.exportTemplateExcel(response, "矛盾纠纷数据");
    }

    /**
     * 获取矛盾纠纷详细信息
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbIssueInfoService.selectTbIssueInfoById(id));
    }

    /**
     * 新增矛盾纠纷
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:add')")
    @Log(title = "矛盾纠纷", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbIssueInfo tbIssueInfo)
    {
        tbIssueInfo.setCreateBy(getUsername());
       /* if (tbIssueInfoService.checkTbIssueInfo(tbIssueInfo)){
            return error("数据已存在！");
        }*/
        if (ObjectUtil.isEmpty(tbIssueInfo.getDeptId())){
            tbIssueInfo.setDeptId(getDataDeptId());
        }
        getDeptInfo(tbIssueInfo);
        if (ObjectUtil.isEmpty(tbIssueInfo.getGridId())) {
            tbIssueInfo.setGridId(SecurityUtils.getGridId());
        }
        return toAjax(tbIssueInfoService.insertTbIssueInfo(tbIssueInfo));
    }

    /**
     * 修改矛盾纠纷
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:edit')")
    @Log(title = "矛盾纠纷", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbIssueInfo tbIssueInfo)
    {
        tbIssueInfo.setUpdateBy(getUsername());
        /*if (tbIssueInfoService.checkTbIssueInfo(tbIssueInfo)){
            return error("数据已存在！");
        }*/
        return toAjax(tbIssueInfoService.updateTbIssueInfo(tbIssueInfo));
    }

    /**
     * 删除矛盾纠纷
     */
    @PreAuthorize("@ss.hasPermi('issue:issueInfo:remove')")
    @Log(title = "矛盾纠纷", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbIssueInfoService.deleteTbIssueInfoByIds(ids));
    }


    /**
     *
     * 获得调处过程信息
     * @param issueId
     * @return
     */
    @GetMapping("/handle-step")
    public AjaxResult getHandleStepInfo(@RequestParam Long issueId){
        List<HandleStepVO> handleStepVOS = tbIssueInfoService.getHandleStepInfo(issueId);
        return success(handleStepVOS);
    }

    /**
     *
     * 获得移交综治数据
     * @param issueId
     * @return
     */
    @GetMapping("/transfer-info")
    public AjaxResult getTransferInfo(@RequestParam Long issueId){
        TransferIssueInfoVO handleStepVO = tbIssueInfoService.getTransferInfo(issueId);
        return success(handleStepVO);
    }




}
