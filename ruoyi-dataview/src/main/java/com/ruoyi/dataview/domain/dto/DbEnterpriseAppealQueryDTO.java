package com.ruoyi.dataview.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 万人助企诉求对象 db_enterprise_appeal
 *      DTO-->请求参数
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbEnterpriseAppealQueryDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 流水号 */
    private String serialNo;

    /** 企业所属县市区 */
    private String county;

    /** 企业名称 */
    private String enterpriseName;

    /** 企业联系人 */
    private String contactPerson;

    /** 联系人电话 */
    private String contactPhone;

    /** 问题分类 */
    private String problemCategory;

    /** 问题描述 */
    private String problemDesc;

    /** 解决建议 */
    private String solutionSuggestion;

    /** 问题收集日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date collectDate;

    /** 问题来源 */
    private String problemSource;

    /** 诉求标签 */
    private String appealTags;

    /** 诉求综合状态 */
    private String appealStatus;

    /** 联系领导 */
    private String leaderContact;

    /** 解决层级 */
    private String solutionLevel;

    /** 责办单位 */
    private String responsibleUnit;

    /** 办理情况 */
    private String handlingStatus;
}
