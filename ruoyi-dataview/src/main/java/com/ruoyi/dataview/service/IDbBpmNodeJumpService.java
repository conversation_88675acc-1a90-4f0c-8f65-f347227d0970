package com.ruoyi.dataview.service;

import java.util.List;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;

/**
 * 运管服案件流程流转记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IDbBpmNodeJumpService 
{
    /**
     * 查询运管服案件流程流转记录
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 运管服案件流程流转记录
     */
    public DbBpmNodeJump selectDbBpmNodeJumpById(Long id);

    /**
     * 校验运管服案件流程流转记录是否存在
     *
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 运管服案件流程流转记录
     */
    public boolean checkDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 查询运管服案件流程流转记录列表
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 运管服案件流程流转记录集合
     */
    public List<DbBpmNodeJump> selectDbBpmNodeJumpList(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 导入运管服案件流程流转记录
     *
     * @param infos       运管服案件流程流转记录列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbBpmNodeJump(List<DbBpmNodeJump> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    public int insertDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 修改运管服案件流程流转记录
     * 
     * @param dbBpmNodeJump 运管服案件流程流转记录
     * @return 结果
     */
    public int updateDbBpmNodeJump(DbBpmNodeJump dbBpmNodeJump);

    /**
     * 批量删除运管服案件流程流转记录
     * 
     * @param ids 需要删除的运管服案件流程流转记录主键集合
     * @return 结果
     */
    public int deleteDbBpmNodeJumpByIds(Long[] ids);

    /**
     * 删除运管服案件流程流转记录信息
     * 
     * @param id 运管服案件流程流转记录主键
     * @return 结果
     */
    public int deleteDbBpmNodeJumpById(Long id);
}
