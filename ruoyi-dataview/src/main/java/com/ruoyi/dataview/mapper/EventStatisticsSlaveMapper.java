package com.ruoyi.dataview.mapper;

import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 从库事项统计Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Mapper
public interface EventStatisticsSlaveMapper {

    /**
     * 获取运管服事项当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getUmEventTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取运管服事项待办数量
     * @param dbBigdataParam 查询参数
     * @return 待办数量
     */
    Integer getUmEventPendingCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取运管服事项累计办理数量
     * @param dbBigdataParam 查询参数
     * @return 累计办理数量
     */
    Integer getUmEventTotalHandledCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取运管服事项办结数量
     * @param dbBigdataParam 查询参数
     * @return 办结数量
     */
    Integer getUmEventCompletedCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取12345热线当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getHotlineDetailTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取12345热线待办数量
     * @param dbBigdataParam 查询参数
     * @return 待办数量
     */
    Integer getHotlineDetailPendingCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取12345热线累计办理数量
     * @param dbBigdataParam 查询参数
     * @return 累计办理数量
     */
    Integer getHotlineDetailTotalHandledCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取12345热线办结数量
     * @param dbBigdataParam 查询参数
     * @return 办结数量
     */
    Integer getHotlineDetailCompletedCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取12345热线满意度统计
     * @param dbBigdataParam 查询参数
     * @return 满意度统计
     */
    Map<String, Object> getHotlineDetailSatisfactionStats(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取万人助企当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getEnterpriseAppealTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取万人助企累计数量
     * @param dbBigdataParam 查询参数
     * @return 累计数量
     */
    Integer getEnterpriseAppealTotalCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取应急事项当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getEmergencyEventTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取应急事项待办数量
     * @param dbBigdataParam 查询参数
     * @return 待办数量
     */
    Integer getEmergencyEventPendingCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取应急事项累计办理数量
     * @param dbBigdataParam 查询参数
     * @return 累计办理数量
     */
    Integer getEmergencyEventTotalHandledCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取应急事项办结数量
     * @param dbBigdataParam 查询参数
     * @return 办结数量
     */
    Integer getEmergencyEventCompletedCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取市长信箱当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getMayorMailboxTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取市长信箱累计数量
     * @param dbBigdataParam 查询参数
     * @return 累计数量
     */
    Integer getMayorMailboxTotalCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取信访当日新增数量
     * @param dbBigdataParam 查询参数
     * @return 当日新增数量
     */
    Integer getPetitionInfoTodayCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取信访累计数量
     * @param dbBigdataParam 查询参数
     * @return 累计数量
     */
    Integer getPetitionInfoTotalCount(@Param("param") DbBigdataParam dbBigdataParam);

    /**
     * 获取信访办结数量
     * @param dbBigdataParam 查询参数
     * @return 办结数量
     */
    Integer getPetitionInfoCompletedCount(@Param("param") DbBigdataParam dbBigdataParam);
}
