<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeActivityUserMapper">

    <resultMap type="SeActivityUser" id="SeActivityUserResult">
            <result property="id"    column="id"    />
            <result property="activityId"    column="activity_id"    />
            <result property="userId"    column="user_id"    />
            <result property="nickName"    column="nick_name"    />
            <result property="phone"    column="phone"    />
            <result property="userAvatar"    column="user_avatar"    />
    </resultMap>

    <sql id="selectSeActivityUserVo">
        select id, activity_id, user_id, nick_name, phone,user_avatar from se_activity_user
    </sql>

    <select id="selectSeActivityUserList" parameterType="SeActivityUser" resultMap="SeActivityUserResult">
        <include refid="selectSeActivityUserVo"/>
        <where>
                        <if test="activityId != null "> and activity_id = #{activityId}</if>
                        <if test="userId != null "> and user_id = #{userId}</if>
                        <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
                        <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectSeActivityUserById" parameterType="Long" resultMap="SeActivityUserResult">
            <include refid="selectSeActivityUserVo"/>
            where id = #{id}
    </select>

    <select id="checkSeActivityUserUnique" parameterType="SeActivityUser" resultMap="SeActivityUserResult">
        select id from se_activity_user
        <where>
            <if test="id != null">
                 and id != #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
        </where>

    </select>

    <insert id="insertSeActivityUser" parameterType="SeActivityUser" useGeneratedKeys="true" keyProperty="id">
        insert into se_activity_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="activityId != null">activity_id,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="nickName != null">nick_name,</if>
                    <if test="phone != null">phone,</if>
                    <if test="userAvatar != null">user_avatar,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="activityId != null">#{activityId},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="nickName != null">#{nickName},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="userAvatar != null">#{userAvatar},</if>
        </trim>
    </insert>

    <update id="updateSeActivityUser" parameterType="SeActivityUser">
        update se_activity_user
        <trim prefix="SET" suffixOverrides=",">
                    <if test="activityId != null">activity_id = #{activityId},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="nickName != null">nick_name = #{nickName},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="userAvatar != null">user_avatar = #{userAvatar},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeActivityUserById" parameterType="Long">
        delete from se_activity_user where id = #{id}
    </delete>

    <delete id="deleteSeActivityUserByIds" parameterType="String">
        delete from se_activity_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>