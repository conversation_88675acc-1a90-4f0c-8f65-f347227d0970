package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysInfoTest;
import com.ruoyi.system.domain.SysInfoChild;

/**
 * 测试管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface SysInfoTestMapper 
{
    /**
     * 查询测试管理
     * 
     * @param id 测试管理主键
     * @return 测试管理
     */
    public SysInfoTest selectSysInfoTestById(Integer id);

    /**
     * 查询测试管理列表
     * 
     * @param sysInfoTest 测试管理
     * @return 测试管理集合
     */
    public List<SysInfoTest> selectSysInfoTestList(SysInfoTest sysInfoTest);

    /**
     * 新增测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    public int insertSysInfoTest(SysInfoTest sysInfoTest);

    /**
     * 修改测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    public int updateSysInfoTest(SysInfoTest sysInfoTest);

    /**
     * 删除测试管理
     * 
     * @param id 测试管理主键
     * @return 结果
     */
    public int deleteSysInfoTestById(Integer id);

    /**
     * 批量删除测试管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysInfoTestByIds(Integer[] ids);

    /**
     * 批量删除测试子
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysInfoChildByInfoIds(Integer[] ids);
    
    /**
     * 批量新增测试子
     * 
     * @param sysInfoChildList 测试子列表
     * @return 结果
     */
    public int batchSysInfoChild(List<SysInfoChild> sysInfoChildList);
    

    /**
     * 通过测试管理主键删除测试子信息
     * 
     * @param id 测试管理ID
     * @return 结果
     */
    public int deleteSysInfoChildByInfoId(Integer id);
}
