package com.ruoyi.dataview.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dataview.domain.po.DbPetitionInfo;
import com.ruoyi.dataview.service.IDbPetitionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 市信访局-上访信息数据Controller
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@RestController
@RequestMapping("/dataview/petitionInfo")
public class DbPetitionInfoController extends BaseController {
    @Autowired
    private IDbPetitionInfoService dbPetitionInfoService;

    /**
     * 查询市信访局-上访信息数据列表
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbPetitionInfo dbPetitionInfo) {
        startPage();
        List<DbPetitionInfo> list = dbPetitionInfoService.selectDbPetitionInfoList(dbPetitionInfo);
        return getDataTable(list);
    }

    /**
     * 导出市信访局-上访信息数据列表
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:export')")
    @Log(title = "市信访局-上访信息数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbPetitionInfo dbPetitionInfo) {
        List<DbPetitionInfo> list = dbPetitionInfoService.selectDbPetitionInfoList(dbPetitionInfo);
        ExcelUtil<DbPetitionInfo> util = new ExcelUtil<DbPetitionInfo>(DbPetitionInfo.class);
        util.exportExcel(response, list, "市信访局-上访信息数据数据");
    }

    @Log(title = "市信访局-上访信息数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:import')")
    @RepeatSubmit// 自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DbPetitionInfo> util = new ExcelUtil<DbPetitionInfo>(DbPetitionInfo.class);
        List<DbPetitionInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbPetitionInfoService.importDbPetitionInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<DbPetitionInfo> util = new ExcelUtil<DbPetitionInfo>(DbPetitionInfo.class);
        util.exportTemplateExcel(response, "市信访局-上访信息数据数据");
    }

    /**
     * 获取市信访局-上访信息数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dbPetitionInfoService.selectDbPetitionInfoById(id));
    }

    /**
     * 新增市信访局-上访信息数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:add')")
    @Log(title = "市信访局-上访信息数据", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbPetitionInfo dbPetitionInfo) {
        dbPetitionInfo.setCreateBy(getUsername());
        if (dbPetitionInfoService.checkDbPetitionInfo(dbPetitionInfo)) {
            return error("数据已存在！");
        }
        return toAjax(dbPetitionInfoService.insertDbPetitionInfo(dbPetitionInfo));
    }

    /**
     * 修改市信访局-上访信息数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:edit')")
    @Log(title = "市信访局-上访信息数据", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbPetitionInfo dbPetitionInfo) {
        dbPetitionInfo.setUpdateBy(getUsername());
        if (dbPetitionInfoService.checkDbPetitionInfo(dbPetitionInfo)) {
            return error("数据已存在！");
        }
        return toAjax(dbPetitionInfoService.updateDbPetitionInfo(dbPetitionInfo));
    }

    /**
     * 删除市信访局-上访信息数据
     */
    @PreAuthorize("@ss.hasPermi('dataview:petitionInfo:remove')")
    @Log(title = "市信访局-上访信息数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dbPetitionInfoService.deleteDbPetitionInfoByIds(ids));
    }

}