<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMsgTemplateMapper">

    <resultMap type="TbMsgTemplate" id="TbMsgTemplateResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="openId" column="open_id"/>
    </resultMap>

    <sql id="selectTbMsgTemplateVo">
        SELECT t.id, t.user_id, t.template_code, u.nick_name,
               u.open_id
        FROM tb_msg_template t
        LEFT JOIN sys_user u ON t.user_id = u.user_id
    </sql>

    <select id="selectTbMsgTemplateList" parameterType="TbMsgTemplate" resultMap="TbMsgTemplateResult">
        <include refid="selectTbMsgTemplateVo"/>
        <where>
            <if test="userId != null ">and t.user_id = #{userId}</if>
            <if test="status != null ">and t.status = #{status}</if>
            <if test="templateCode != null  and templateCode != ''">and t.template_code = #{templateCode}</if>
            <if test="deptId != null ">and u.dept_id = #{deptId}</if>
            <if test="roleId != null ">and u.user_id in (select user_id from sys_user_role where role_id = #{roleId} group by user_id)</if>
            <if test="notRoleId != null ">and u.user_id not in (select user_id from sys_user_role where role_id = #{notRoleId} group by user_id)</if>
        </where>
    </select>

    <select id="selectTbMsgTemplateById" parameterType="Long" resultMap="TbMsgTemplateResult">
        <include refid="selectTbMsgTemplateVo"/>
        where id = #{id}
    </select>

    <select id="checkTbMsgTemplateUnique" parameterType="TbMsgTemplate" resultMap="TbMsgTemplateResult">
        select id from tb_msg_template where user_id = #{userId} and template_code = #{templateCode}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMsgTemplate" parameterType="TbMsgTemplate">
        insert into tb_msg_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="templateCode != null">template_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="templateCode != null">#{templateCode},</if>
        </trim>
    </insert>

    <update id="updateTbMsgTemplate" parameterType="TbMsgTemplate">
        update tb_msg_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="templateCode != null">template_code = #{templateCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbMsgTemplateById" parameterType="Long">
        delete from tb_msg_template where id = #{id}
    </delete>

    <delete id="deleteTbMsgTemplateByIds" parameterType="String">
        delete from tb_msg_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>