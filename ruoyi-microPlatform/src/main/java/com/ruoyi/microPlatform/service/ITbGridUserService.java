package com.ruoyi.microPlatform.service;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbGridUser;

/**
 * 网格员信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbGridUserService 
{
    /**
     * 查询网格员信息
     * 
     * @param id 网格员信息主键
     * @return 网格员信息
     */
    public TbGridUser selectTbGridUserById(Long id);

    /**
     * 校验网格员信息是否存在
     *
     * @param tbGridUser 网格员信息
     * @return 网格员信息
     */
    public boolean checkTbGridUser(TbGridUser tbGridUser);

    public boolean checkTbGridUserUnique(TbGridUser tbGridUser);

    /**
     * 查询网格员信息列表
     * 
     * @param tbGridUser 网格员信息
     * @return 网格员信息集合
     */
    public List<TbGridUser> selectTbGridUserList(TbGridUser tbGridUser);

    public Integer selectTbGridUserCount(TbGridUser tbGridUser);

    /**
     * 导入网格员信息
     *
     * @param infos       网格员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridUser(List<TbGridUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增网格员信息
     * 
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    public int insertTbGridUser(TbGridUser tbGridUser) throws InvalidKeySpecException, NoSuchAlgorithmException;

    public List<TbGridUser> getMyGridUserList(LoginUser user);

    /**
     * 修改网格员信息
     * 
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    public int updateTbGridUser(TbGridUser tbGridUser);

    /**
     * 批量删除网格员信息
     * 
     * @param ids 需要删除的网格员信息主键集合
     * @return 结果
     */
    public int deleteTbGridUserByIds(Long[] ids);

    /**
     * 删除网格员信息信息
     * 
     * @param id 网格员信息主键
     * @return 结果
     */
    public int deleteTbGridUserById(Long id);

    public List<Long> selectGridIdByPhone(String phone);


    List<CommonBaseCount> gridUserCount();
}
