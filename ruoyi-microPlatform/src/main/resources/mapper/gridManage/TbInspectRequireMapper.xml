<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbInspectRequireMapper">

    <resultMap type="TbInspectRequire" id="TbInspectRequireResult">
        <result property="id" column="id"/>
        <result property="inspectType" column="inspect_type"/>
        <result property="inspectDesc" column="inspect_desc"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectTbInspectRequireVo">
        select id, inspect_type, inspect_desc, type, status from tb_inspect_require
    </sql>

    <select id="selectTbInspectRequireList" parameterType="TbInspectRequire" resultMap="TbInspectRequireResult">
        <include refid="selectTbInspectRequireVo"/>
        <where>
            <if test="inspectType != null  and inspectType != ''">and inspect_type = #{inspectType}</if>
            <if test="inspectDesc != null  and inspectDesc != ''">and inspect_desc = #{inspectDesc}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
    </select>

    <select id="selectTbInspectRequireById" parameterType="Integer" resultMap="TbInspectRequireResult">
        <include refid="selectTbInspectRequireVo"/>
        where id = #{id}
    </select>

    <select id="checkTbInspectRequireUnique" parameterType="TbInspectRequire" resultMap="TbInspectRequireResult">
        select id from tb_inspect_require where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbInspectRequire" parameterType="TbInspectRequire" useGeneratedKeys="true" keyProperty="id">
        insert into tb_inspect_require
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inspectType != null">inspect_type,</if>
            <if test="inspectDesc != null">inspect_desc,</if>
            <if test="type != null">type,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inspectType != null">#{inspectType},</if>
            <if test="inspectDesc != null">#{inspectDesc},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateTbInspectRequire" parameterType="TbInspectRequire">
        update tb_inspect_require
        <trim prefix="SET" suffixOverrides=",">
            <if test="inspectType != null">inspect_type = #{inspectType},</if>
            <if test="inspectDesc != null">inspect_desc = #{inspectDesc},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbInspectRequireById" parameterType="Integer">
        delete from tb_inspect_require where id = #{id}
    </delete>

    <delete id="deleteTbInspectRequireByIds" parameterType="String">
        delete from tb_inspect_require where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectTbInspectRequireListByType" resultMap="TbInspectRequireResult">
        <include refid="selectTbInspectRequireVo"/>
        where
        status = 0

    </select>
</mapper>