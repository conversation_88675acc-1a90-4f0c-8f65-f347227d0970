package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 志愿报名对象 se_volunteer_register
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SeVolunteerRegister extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 数据所属部门/网格 */
//    @Excel(name = "数据所属部门/网格")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
//    @Excel(name = "所属网格")
    private String gridName;

    /** 账户 */
//    @Excel(name = "账户")
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String nickName;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String idCard;

    /** 民族 */
    @Excel(name = "民族")
    private String nation;

    /** 性别 */
    @Excel(name = "性别")
    private String sex;

    /** 政治面貌 */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /** 省 */
    private String province;

    /** 市 */
    private String city;

    /** 县 */
    private String addrCounty;

    /** 街道 */
    private String addrCountry;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String addr;

//    @Excel(name = "家庭住址")
    private String familyAddr;

    /** 擅长技能 */
    @Excel(name = "擅长技能")
    private String skilledSkills;

    @Excel(name = "其他")
    private String remark;

    /** 报名电话 */
    @Excel(name = "报名电话")
    private String phone;

//    /** 数据状态（0待处理 1已结束） */
    private Integer status;

    /** 处理结果 */
    private String handleResult;

    private Long addrId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setNation(String nation) 
    {
        this.nation = nation;
    }

    public String getNation() 
    {
        return nation;
    }
    public void setSex(String sex) 
    {
        this.sex = sex;
    }

    public String getSex() 
    {
        return sex;
    }
    public void setPoliticalStatus(String politicalStatus) 
    {
        this.politicalStatus = politicalStatus;
    }

    public String getPoliticalStatus() 
    {
        return politicalStatus;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setAddrCounty(String addrCounty) 
    {
        this.addrCounty = addrCounty;
    }

    public String getAddrCounty() 
    {
        return addrCounty;
    }
    public void setAddrCountry(String addrCountry) 
    {
        this.addrCountry = addrCountry;
    }

    public String getAddrCountry() 
    {
        return addrCountry;
    }
    public void setAddr(String addr) 
    {
        this.addr = addr;
    }

    public String getAddr() 
    {
        return addr;
    }
    public void setSkilledSkills(String skilledSkills) 
    {
        this.skilledSkills = skilledSkills;
    }

    public String getSkilledSkills() 
    {
        return skilledSkills;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setHandleResult(String handleResult) 
    {
        this.handleResult = handleResult;
    }

    public String getHandleResult() 
    {
        return handleResult;
    }

    public String getFamilyAddr() {
        return province + city + (StringUtils.isNotBlank(addrCountry) ? addrCountry:"") +
                (StringUtils.isNotBlank(addrCounty) ? addrCounty:"") + (StringUtils.isNotBlank(addr) ? addr:"");
    }

    public void setFamilyAddr(String familyAddr) {
        this.familyAddr = province + city + (StringUtils.isNotBlank(addrCountry) ? addrCountry:"") +
                (StringUtils.isNotBlank(addrCounty) ? addrCounty:"") + (StringUtils.isNotBlank(addr) ? addr:"");
    }

    public Long getAddrId() {
        return addrId;
    }

    public void setAddrId(Long addrId) {
        this.addrId = addrId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("idCard", getIdCard())
            .append("nation", getNation())
            .append("sex", getSex())
            .append("politicalStatus", getPoliticalStatus())
            .append("province", getProvince())
            .append("city", getCity())
            .append("addrCounty", getAddrCounty())
            .append("addrCountry", getAddrCountry())
            .append("addr", getAddr())
            .append("skilledSkills", getSkilledSkills())
            .append("remark", getRemark())
            .append("phone", getPhone())
            .append("status", getStatus())
            .append("handleResult", getHandleResult())
            .append("createTime", getCreateTime())
            .toString();
    }
}
