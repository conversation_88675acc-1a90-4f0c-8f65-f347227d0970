package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 组织日常活动开展情况对象 tb_org_activity
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class TbOrgActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 关联组织id */
    @Excel(name = "关联组织id")
    private Long orgId;

    /** 活动日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "活动日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actDate;

    /** 活动内容 */
    @Excel(name = "活动内容")
    private String actContent;

    /** 参与人数 */
    @Excel(name = "参与人数")
    private Integer actNumber;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrgId(Long orgId) 
    {
        this.orgId = orgId;
    }

    public Long getOrgId() 
    {
        return orgId;
    }
    public void setActDate(Date actDate) 
    {
        this.actDate = actDate;
    }

    public Date getActDate() 
    {
        return actDate;
    }
    public void setActContent(String actContent) 
    {
        this.actContent = actContent;
    }

    public String getActContent() 
    {
        return actContent;
    }
    public void setActNumber(Integer actNumber) 
    {
        this.actNumber = actNumber;
    }

    public Integer getActNumber() 
    {
        return actNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orgId", getOrgId())
            .append("actDate", getActDate())
            .append("actContent", getActContent())
            .append("actNumber", getActNumber())
            .toString();
    }
}
