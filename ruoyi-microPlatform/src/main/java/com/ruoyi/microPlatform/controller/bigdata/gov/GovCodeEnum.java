package com.ruoyi.microPlatform.controller.bigdata.gov;

import lombok.Getter;

/**
 * Vclusters 错误码枚举类
 * 用于定义Vclusters系统中可能出现的错误码及其对应的消息描述
 */
@Getter
public enum GovCodeEnum {

    // 错误码：token无效或已过期
    // 描述：当用户提供的token无法验证或已过期时，返回此错误码
    TOKEN_INVALID_OR_EXPIRED(-9, "token无效或已过期"),

    // 错误码：操作成功
    // 描述：当操作成功完成时，返回此错误码
    SUCCESS(0, "Success OK");

    // 错误码
    private final Integer code;
    // 错误消息
    private final String msg;

    /**
     * 构造方法
     * @param code 错误码
     * @param msg 错误消息
     */
    GovCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
