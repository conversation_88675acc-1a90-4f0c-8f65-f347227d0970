package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 后续动态信息对象 se_subsequent_info
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SeSubsequentInfo extends BaseEntity implements Cloneable
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 关联数据记录主表 */
    @Excel(name = "关联数据记录主表")
    private Long formId;

    /** 数据记录类型 */
    @Excel(name = "数据记录类型")
    private String formType;

    /** 创建账户 */
    @Excel(name = "创建账户")
    private Long userId;

    /** 创建者名称 */
    @Excel(name = "创建者名称")
    private String nickName;

    /** 关联父级回复 */
    @Excel(name = "关联父级回复")
    private Long parentId;

    /** 发表内容 */
    @Excel(name = "发表内容")
    private String content;
    /**
     * 附件
     */
    private String accessory;


    SeSubsequentInfo seSubsequentInfo;

    public SeSubsequentInfo getSeSubsequentInfo() {
        return seSubsequentInfo;
    }

    public void setSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo) {
        this.seSubsequentInfo = seSubsequentInfo;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFormId(Long formId) 
    {
        this.formId = formId;
    }

    public Long getFormId() 
    {
        return formId;
    }
    public void setFormType(String formType) 
    {
        this.formType = formType;
    }

    public String getFormType() 
    {
        return formType;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    @Override
    public SeSubsequentInfo clone() throws CloneNotSupportedException {
        SeSubsequentInfo subsequentInfo = (SeSubsequentInfo) super.clone();

        return subsequentInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("formId", getFormId())
            .append("formType", getFormType())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("createTime", getCreateTime())
            .append("parentId", getParentId())
            .append("content", getContent())
            .toString();
    }
}
