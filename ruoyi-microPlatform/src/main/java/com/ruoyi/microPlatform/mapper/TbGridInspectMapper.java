package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbGridInspect;

import java.util.List;
import java.util.Map;

/**
 * 网格巡查Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbGridInspectMapper 
{
    /**
     * 查询网格巡查
     * 
     * @param id 网格巡查主键
     * @return 网格巡查
     */
    public TbGridInspect selectTbGridInspectById(Long id);

    /**
     * 校验网格巡查是否唯一
     *
     * @param tbGridInspect
     * @return 网格巡查
     */
    public TbGridInspect checkTbGridInspectUnique(TbGridInspect tbGridInspect);

    /**
     * 查询网格巡查列表
     * 
     * @param tbGridInspect 网格巡查
     * @return 网格巡查集合
     */
    public List<TbGridInspect> selectTbGridInspectList(TbGridInspect tbGridInspect);

    /**
     * 新增网格巡查
     * 
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    public int insertTbGridInspect(TbGridInspect tbGridInspect);

    /**
     * 修改网格巡查
     * 
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    public int updateTbGridInspect(TbGridInspect tbGridInspect);

    /**
     * 删除网格巡查
     * 
     * @param id 网格巡查主键
     * @return 结果
     */
    public int deleteTbGridInspectById(Long id);

    /**
     * 批量删除网格巡查
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbGridInspectByIds(Long[] ids);

    //查询当前网格ID下的所有巡查数据size 根据状态获得不同状态的数据量
    List<TbGridInspect> selectTbGridInspectByStatus(TbGridInspect tbGridInspect);

    //查询最新更新的数据
    TbGridInspect selectTbGridInspectByUpdateTime(TbGridInspect tbGridInspect);

    /**
     * 查询当前用户的提交总数 和最新提交日期
     * @param tbGridInspect
     * @return
     */
    Map<String, Object> selectSubmitCount(TbGridInspect tbGridInspect);

    //查询当前网格id下的数据size
    int selectTbGridInspectSize(TbGridInspect tbGridInspect);

    List<Map<String, Object>> selectMonthlyStats(LargeScreenInfo largeScreenInfo);

    int selectCount(LargeScreenInfo largeScreenInfo);

}
