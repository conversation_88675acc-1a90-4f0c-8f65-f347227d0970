<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMainPartyMapper">

    <resultMap type="TbMainParty" id="TbMainPartyResult">
            <result property="id"    column="id"    />
            <result property="issueId"    column="issue_id"    />
            <result property="mdjfdsrzj"    column="mdjfdsrzj"    />
            <result property="mdjfzj"    column="mdjfzj"    />
            <result property="dsrzjzldm"    column="dsrzjzldm"    />
            <result property="dsrzjhm"    column="dsrzjhm"    />
            <result property="dsrzjzlmc"    column="dsrzjzlmc"    />
            <result property="dsrxm"    column="dsrxm"    />
            <result property="lxdh"    column="lxdh"    />
            <result property="xbdm"    column="xbdm"    />
            <result property="xb"    column="xb"    />
            <result property="hyzkdm"    column="hyzkdm"    />
            <result property="hyzk"    column="hyzk"    />
            <result property="whcddm"    column="whcddm"    />
            <result property="whcd"    column="whcd"    />
            <result property="mzdm"    column="mzdm"    />
            <result property="mz"    column="mz"    />
            <result property="dsrhjdz"    column="dsrhjdz"    />
            <result property="dsrxzz"    column="dsrxzz"    />
            <result property="nl"    column="nl"    />
            <result property="csrq"    column="csrq"    />
            <result property="gzdw"    column="gzdw"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTbMainPartyVo">
        select id, issue_id, mdjfdsrzj, mdjfzj, dsrzjzldm, dsrzjhm, dsrzjzlmc, dsrxm, lxdh, xbdm, xb, hyzkdm, hyzk, whcddm, whcd, mzdm, mz, dsrhjdz, dsrxzz, nl, csrq, gzdw, create_time, create_by, update_time, update_by from tb_main_party
    </sql>

    <select id="selectTbMainPartyList" parameterType="TbMainParty" resultMap="TbMainPartyResult">
        <include refid="selectTbMainPartyVo"/>
        <where>
                        <if test="issueId != null "> and issue_id = #{issueId}</if>
                        <if test="mdjfdsrzj != null  and mdjfdsrzj != ''"> and mdjfdsrzj = #{mdjfdsrzj}</if>
                        <if test="mdjfzj != null  and mdjfzj != ''"> and mdjfzj = #{mdjfzj}</if>
                        <if test="dsrzjzldm != null  and dsrzjzldm != ''"> and dsrzjzldm = #{dsrzjzldm}</if>
                        <if test="dsrzjhm != null  and dsrzjhm != ''"> and dsrzjhm = #{dsrzjhm}</if>
                        <if test="dsrzjzlmc != null  and dsrzjzlmc != ''"> and dsrzjzlmc = #{dsrzjzlmc}</if>
                        <if test="dsrxm != null  and dsrxm != ''"> and dsrxm = #{dsrxm}</if>
                        <if test="lxdh != null  and lxdh != ''"> and lxdh = #{lxdh}</if>
                        <if test="xbdm != null  and xbdm != ''"> and xbdm = #{xbdm}</if>
                        <if test="xb != null  and xb != ''"> and xb = #{xb}</if>
                        <if test="hyzkdm != null  and hyzkdm != ''"> and hyzkdm = #{hyzkdm}</if>
                        <if test="hyzk != null  and hyzk != ''"> and hyzk = #{hyzk}</if>
                        <if test="whcddm != null  and whcddm != ''"> and whcddm = #{whcddm}</if>
                        <if test="whcd != null  and whcd != ''"> and whcd = #{whcd}</if>
                        <if test="mzdm != null  and mzdm != ''"> and mzdm = #{mzdm}</if>
                        <if test="mz != null  and mz != ''"> and mz = #{mz}</if>
                        <if test="dsrhjdz != null  and dsrhjdz != ''"> and dsrhjdz = #{dsrhjdz}</if>
                        <if test="dsrxzz != null  and dsrxzz != ''"> and dsrxzz = #{dsrxzz}</if>
                        <if test="nl != null  and nl != ''"> and nl = #{nl}</if>
                        <if test="csrq != null  and csrq != ''"> and csrq = #{csrq}</if>
                        <if test="gzdw != null  and gzdw != ''"> and gzdw = #{gzdw}</if>
        </where>
    </select>

    <select id="selectTbMainPartyById" parameterType="Long" resultMap="TbMainPartyResult">
            <include refid="selectTbMainPartyVo"/>
            where id = #{id}
    </select>

    <select id="checkTbMainPartyUnique" parameterType="TbMainParty" resultMap="TbMainPartyResult">
        select id from tb_main_party where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMainParty" parameterType="TbMainParty" useGeneratedKeys="true" keyProperty="id">
        insert into tb_main_party
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="issueId != null">issue_id,</if>
                    <if test="mdjfdsrzj != null">mdjfdsrzj,</if>
                    <if test="mdjfzj != null">mdjfzj,</if>
                    <if test="dsrzjzldm != null">dsrzjzldm,</if>
                    <if test="dsrzjhm != null">dsrzjhm,</if>
                    <if test="dsrzjzlmc != null">dsrzjzlmc,</if>
                    <if test="dsrxm != null and dsrxm != ''">dsrxm,</if>
                    <if test="lxdh != null">lxdh,</if>
                    <if test="xbdm != null">xbdm,</if>
                    <if test="xb != null">xb,</if>
                    <if test="hyzkdm != null">hyzkdm,</if>
                    <if test="hyzk != null">hyzk,</if>
                    <if test="whcddm != null">whcddm,</if>
                    <if test="whcd != null">whcd,</if>
                    <if test="mzdm != null">mzdm,</if>
                    <if test="mz != null">mz,</if>
                    <if test="dsrhjdz != null">dsrhjdz,</if>
                    <if test="dsrxzz != null">dsrxzz,</if>
                    <if test="nl != null">nl,</if>
                    <if test="csrq != null">csrq,</if>
                    <if test="gzdw != null">gzdw,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="issueId != null">#{issueId},</if>
                    <if test="mdjfdsrzj != null">#{mdjfdsrzj},</if>
                    <if test="mdjfzj != null">#{mdjfzj},</if>
                    <if test="dsrzjzldm != null">#{dsrzjzldm},</if>
                    <if test="dsrzjhm != null">#{dsrzjhm},</if>
                    <if test="dsrzjzlmc != null">#{dsrzjzlmc},</if>
                    <if test="dsrxm != null and dsrxm != ''">#{dsrxm},</if>
                    <if test="lxdh != null">#{lxdh},</if>
                    <if test="xbdm != null">#{xbdm},</if>
                    <if test="xb != null">#{xb},</if>
                    <if test="hyzkdm != null">#{hyzkdm},</if>
                    <if test="hyzk != null">#{hyzk},</if>
                    <if test="whcddm != null">#{whcddm},</if>
                    <if test="whcd != null">#{whcd},</if>
                    <if test="mzdm != null">#{mzdm},</if>
                    <if test="mz != null">#{mz},</if>
                    <if test="dsrhjdz != null">#{dsrhjdz},</if>
                    <if test="dsrxzz != null">#{dsrxzz},</if>
                    <if test="nl != null">#{nl},</if>
                    <if test="csrq != null">#{csrq},</if>
                    <if test="gzdw != null">#{gzdw},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateTbMainParty" parameterType="TbMainParty">
        update tb_main_party
        <trim prefix="SET" suffixOverrides=",">
                    <if test="issueId != null">issue_id = #{issueId},</if>
                    <if test="mdjfdsrzj != null">mdjfdsrzj = #{mdjfdsrzj},</if>
                    <if test="mdjfzj != null">mdjfzj = #{mdjfzj},</if>
                    <if test="dsrzjzldm != null">dsrzjzldm = #{dsrzjzldm},</if>
                    <if test="dsrzjhm != null">dsrzjhm = #{dsrzjhm},</if>
                    <if test="dsrzjzlmc != null">dsrzjzlmc = #{dsrzjzlmc},</if>
                    <if test="dsrxm != null and dsrxm != ''">dsrxm = #{dsrxm},</if>
                    <if test="lxdh != null">lxdh = #{lxdh},</if>
                    <if test="xbdm != null">xbdm = #{xbdm},</if>
                    <if test="xb != null">xb = #{xb},</if>
                    <if test="hyzkdm != null">hyzkdm = #{hyzkdm},</if>
                    <if test="hyzk != null">hyzk = #{hyzk},</if>
                    <if test="whcddm != null">whcddm = #{whcddm},</if>
                    <if test="whcd != null">whcd = #{whcd},</if>
                    <if test="mzdm != null">mzdm = #{mzdm},</if>
                    <if test="mz != null">mz = #{mz},</if>
                    <if test="dsrhjdz != null">dsrhjdz = #{dsrhjdz},</if>
                    <if test="dsrxzz != null">dsrxzz = #{dsrxzz},</if>
                    <if test="nl != null">nl = #{nl},</if>
                    <if test="csrq != null">csrq = #{csrq},</if>
                    <if test="gzdw != null">gzdw = #{gzdw},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbMainPartyById" parameterType="Long">
        delete from tb_main_party where id = #{id}
    </delete>

    <delete id="deleteTbMainPartyByIds" parameterType="String">
        delete from tb_main_party where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>