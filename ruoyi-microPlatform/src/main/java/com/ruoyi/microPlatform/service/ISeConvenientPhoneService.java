package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeConvenientPhone;

/**
 * 便民电话Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeConvenientPhoneService 
{
    /**
     * 查询便民电话
     * 
     * @param id 便民电话主键
     * @return 便民电话
     */
    public SeConvenientPhone selectSeConvenientPhoneById(Long id);

    /**
     * 校验便民电话是否存在
     *
     * @param seConvenientPhone 便民电话
     * @return 便民电话
     */
    public boolean checkSeConvenientPhone(SeConvenientPhone seConvenientPhone);

    /**
     * 查询便民电话列表
     * 
     * @param seConvenientPhone 便民电话
     * @return 便民电话集合
     */
    public List<SeConvenientPhone> selectSeConvenientPhoneList(SeConvenientPhone seConvenientPhone);

    /**
     * 导入便民电话
     *
     * @param infos       便民电话列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeConvenientPhone(List<SeConvenientPhone> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    public int insertSeConvenientPhone(SeConvenientPhone seConvenientPhone);

    /**
     * 修改便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    public int updateSeConvenientPhone(SeConvenientPhone seConvenientPhone);

    /**
     * 批量删除便民电话
     * 
     * @param ids 需要删除的便民电话主键集合
     * @return 结果
     */
    public int deleteSeConvenientPhoneByIds(Long[] ids);

    /**
     * 删除便民电话信息
     * 
     * @param id 便民电话主键
     * @return 结果
     */
    public int deleteSeConvenientPhoneById(Long id);


    public Map<String, Object> selectSubmitCount(SeConvenientPhone seConvenientPhone);
}
