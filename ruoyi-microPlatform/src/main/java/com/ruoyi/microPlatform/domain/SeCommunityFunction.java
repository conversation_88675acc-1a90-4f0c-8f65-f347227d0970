package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 社区功能室对象 se_community_function
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class SeCommunityFunction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    private String gridName;

    /** 功能室名称 */
    @Excel(name = "功能室名称")
    private String name;

    /** 使用状态 */
    @Excel(name = "使用状态")
    private String useStatus;

    /** 简介 */
    @Excel(name = "简介")
    private String introduction;

    /** 面积 */
    @Excel(name = "面积(㎡)")
    private String area;

    /** 容纳人数 */
    @Excel(name = "容纳人数")
    private Integer capacity;

    /** 标签 */
    @Excel(name = "功能室类型", comboReadDict = true,dictType = "function_type")
    private String label;

    /** 管理员联系方式 */
    @Excel(name = "管理员联系方式")
    private String phone;

    /** 功能室照片 */
    @Excel(name = "功能室照片",cellType = Excel.ColumnType.IMAGE)
    private String accessory;

    /** 经纬度 */
    private String lat;

    /**  */
    @Excel(name = "")
    private String lng;


    /** 状态（1可用，0待审核，2停用） */
    //    @Excel(name = "状态", readConverterExp = "1=可用，0待审核，2停用")
    private Integer status;

    //数量
    private Integer count;

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setUseStatus(String useStatus) 
    {
        this.useStatus = useStatus;
    }

    public String getUseStatus() 
    {
        return useStatus;
    }
    public void setLat(String lat)
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("name", getName())
            .append("useStatus", getUseStatus())
            .append("introduction", getIntroduction())
            .append("area", getArea())
            .append("capacity", getCapacity())
            .append("label", getLabel())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("phone", getPhone())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
