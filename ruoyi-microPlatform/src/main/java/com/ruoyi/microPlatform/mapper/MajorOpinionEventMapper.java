package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.MajorOpinionEvent;

/**
 * 重大舆情信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
public interface MajorOpinionEventMapper 
{
    /**
     * 查询重大舆情信息
     * 
     * @param id 重大舆情信息主键
     * @return 重大舆情信息
     */
    public MajorOpinionEvent selectMajorOpinionEventById(Long id);

    /**
     * 查询重大舆情信息列表
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 重大舆情信息集合
     */
    public List<MajorOpinionEvent> selectMajorOpinionEventList(MajorOpinionEvent majorOpinionEvent);

    /**
     * 新增重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    public int insertMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent);

    /**
     * 修改重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    public int updateMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent);

    /**
     * 删除重大舆情信息
     * 
     * @param id 重大舆情信息主键
     * @return 结果
     */
    public int deleteMajorOpinionEventById(Long id);

    /**
     * 批量删除重大舆情信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMajorOpinionEventByIds(Long[] ids);

    /**
     * 获取最新的舆情事项
     * @param limit 限制数量
     * @return 舆情事项列表
     */
    List<MajorOpinionEvent> selectLatestOpinionEvents(int limit);
}
