package com.ruoyi.system.domain;

import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 测试管理对象 sys_info_test
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
public class SysInfoTest extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Integer id;

    /**
     * 数据1
     */
    @Excel(name = "数据1", needMerge = true)
    private String spare1;

    /**
     * 数字类型（0男测试 1女测试 2未知测试）
     */
    @Excel(name = "数字类型", readConverterExp = "0=男测试,1=女测试,2=未知测试", needMerge = true)
    private Integer spare2;

    /**
     * 标签名
     */
    @Excel(name = "标签名", needMerge = true, color = IndexedColors.RED, backgroundColor = IndexedColors.WHITE)
    private String dictLabel;

    /**
     * 标签值
     */
    @Excel(name = "标签值", needMerge = true)
    private String dictValue;

    /**
     * 测试子信息
     */
    @Excel(name = "测试子信息", needMerge = true)
    private List<SysInfoChild> sysInfoChildList;

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setSpare1(String spare1) {
        this.spare1 = spare1;
    }

    public String getSpare1() {
        return spare1;
    }

    public void setSpare2(Integer spare2) {
        this.spare2 = spare2;
    }

    public Integer getSpare2() {
        return spare2;
    }

    public void setDictLabel(String dictLabel) {
        this.dictLabel = dictLabel;
    }

    public String getDictLabel() {
        return dictLabel;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    public String getDictValue() {
        return dictValue;
    }

    public List<SysInfoChild> getSysInfoChildList() {
        return sysInfoChildList;
    }

    public void setSysInfoChildList(List<SysInfoChild> sysInfoChildList) {
        this.sysInfoChildList = sysInfoChildList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("spare1", getSpare1())
                .append("spare2", getSpare2())
                .append("dictLabel", getDictLabel())
                .append("dictValue", getDictValue())
                .append("sysInfoChildList", getSysInfoChildList())
                .toString();
    }
}
