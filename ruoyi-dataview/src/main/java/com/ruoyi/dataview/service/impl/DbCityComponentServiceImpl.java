package com.ruoyi.dataview.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbCityComponent;
import com.ruoyi.dataview.mapper.DbCityComponentMapper;
import com.ruoyi.dataview.service.IDbCityComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 运管服部件数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbCityComponentServiceImpl implements IDbCityComponentService 
{
    @Autowired
    private DbCityComponentMapper dbCityComponentMapper;

    /**
     * 查询运管服部件数据
     * 
     * @param id 运管服部件数据主键
     * @return 运管服部件数据
     */
    @Override
    public DbCityComponent selectDbCityComponentById(Long id)
    {
        return dbCityComponentMapper.selectDbCityComponentById(id);
    }

    /**
     * 校验运管服部件数据是否存在
     *
     * @param dbCityComponent
     * @return boolean
     */
    @Override
    public boolean checkDbCityComponent(DbCityComponent dbCityComponent){
        DbCityComponent old = dbCityComponentMapper.checkDbCityComponentUnique(dbCityComponent);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询运管服部件数据列表
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 运管服部件数据
     */
    @Override
    public List<DbCityComponent> selectDbCityComponentList(DbCityComponent dbCityComponent)
    {
        return dbCityComponentMapper.selectDbCityComponentList(dbCityComponent);
    }

    /**
     * 导入运管服部件数据
     *
     * @param infos       运管服部件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbCityComponent(List<DbCityComponent> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入运管服部件数据数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbCityComponent info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                DbCityComponent old = dbCityComponentMapper.checkDbCityComponentUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbCityComponentMapper.insertDbCityComponent(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbCityComponentMapper.updateDbCityComponent(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增运管服部件数据
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    @Override
    public int insertDbCityComponent(DbCityComponent dbCityComponent)
    {
        dbCityComponent.setCreateTime(DateUtils.getNowDate());
        return dbCityComponentMapper.insertDbCityComponent(dbCityComponent);
    }

    /**
     * 修改运管服部件数据
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    @Override
    public int updateDbCityComponent(DbCityComponent dbCityComponent)
    {
        dbCityComponent.setUpdateTime(DateUtils.getNowDate());
        return dbCityComponentMapper.updateDbCityComponent(dbCityComponent);
    }

    /**
     * 批量删除运管服部件数据
     * 
     * @param ids 需要删除的运管服部件数据主键
     * @return 结果
     */
    @Override
    public int deleteDbCityComponentByIds(Long[] ids)
    {
        return dbCityComponentMapper.deleteDbCityComponentByIds(ids);
    }

    /**
     * 删除运管服部件数据信息
     * 
     * @param id 运管服部件数据主键
     * @return 结果
     */
    @Override
    public int deleteDbCityComponentById(Long id)
    {
        return dbCityComponentMapper.deleteDbCityComponentById(id);
    }

    @Override
    public Integer selectDbCityComponentCount(DbCityComponent dbCityComponent) {
        return dbCityComponentMapper.selectDbCityComponentCount(dbCityComponent);
    }

    @Override
    public List<CommonBaseCount> selectCountGroupByMajorCode() {
        // 没找到对应的字典 就不补0了
        return dbCityComponentMapper.selectCountGroupByMajorCode();
    }
}
