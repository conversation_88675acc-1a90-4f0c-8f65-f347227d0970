package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbTypeInfo;

/**
 * 矛盾纠纷类别编码Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface ITbTypeInfoService 
{
    /**
     * 查询矛盾纠纷类别编码
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 矛盾纠纷类别编码
     */
    public TbTypeInfo selectTbTypeInfoById(Integer id);

    /**
     * 校验矛盾纠纷类别编码是否存在
     *
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 矛盾纠纷类别编码
     */
    public boolean checkTbTypeInfo(TbTypeInfo tbTypeInfo);

    /**
     * 查询矛盾纠纷类别编码列表
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 矛盾纠纷类别编码集合
     */
    public List<TbTypeInfo> selectTbTypeInfoList(TbTypeInfo tbTypeInfo);

    public List<TbTypeInfo> selectTbTypeInfoList2(TbTypeInfo tbTypeInfo);

    /**
     * 导入矛盾纠纷类别编码
     *
     * @param infos       矛盾纠纷类别编码列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbTypeInfo(List<TbTypeInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    public int insertTbTypeInfo(TbTypeInfo tbTypeInfo);

    /**
     * 修改矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    public int updateTbTypeInfo(TbTypeInfo tbTypeInfo);

    /**
     * 批量删除矛盾纠纷类别编码
     * 
     * @param ids 需要删除的矛盾纠纷类别编码主键集合
     * @return 结果
     */
    public int deleteTbTypeInfoByIds(Integer[] ids);

    /**
     * 删除矛盾纠纷类别编码信息
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 结果
     */
    public int deleteTbTypeInfoById(Integer id);
}
