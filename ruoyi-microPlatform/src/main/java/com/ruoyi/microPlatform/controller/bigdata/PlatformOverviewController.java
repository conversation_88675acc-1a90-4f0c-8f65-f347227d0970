package com.ruoyi.microPlatform.controller.bigdata;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.controller.bigdata.gov.GovBiz;
import com.ruoyi.microPlatform.controller.bigdata.gov.req.GovGetUnifiedManagementLetterListReq;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetLetterTypeModelResp;
import com.ruoyi.microPlatform.controller.bigdata.gov.resp.GovGetUnifiedManagementLetterListResp;
import com.ruoyi.microPlatform.service.*;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 常态事项
 */
@RestController
@RequestMapping("/bigData/platform")
public class PlatformOverviewController extends BaseController {

    /**
     * 矛盾纠纷
     */
    @Resource
    private ITbIssueInfoService tbIssueInfoService;

    /**
     * 矛盾纠纷 数量
     */
    @GetMapping("/homePage/tbIssueInfoCount")
    public AjaxResult tbIssueInfoCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(tbIssueInfoService.statusCount());
    }

    @Resource
    private ITbGridUserService tbGridUserService;

    /**
     * 九个县网格员
     */
    @GetMapping("/homePage/gridUserCount")
    public AjaxResult gridUserCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(tbGridUserService.gridUserCount());
    }


    @Resource
    private IMajorOpinionEventService majorOpinionEventService;

    /**
     * 热门事项
     * 改为舆情事项
     * 取 major_opinion_events 最新10条
     */
    @GetMapping("/homePage/majorOpinionEvents")
    public AjaxResult majorOpinionEvents(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        return success(majorOpinionEventService.selectLatestOpinionEvents(10));
    }

    @Resource
    private GovBiz govBiz;

    /**
     * 信件总数
     */
    @GetMapping("/homePage/letterCount")
    public AjaxResult letterCount(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(new GovGetUnifiedManagementLetterListReq());
        return success(unifiedManagementLetterList.getTotal());
    }

    /**
     * 信件类别
     * 这个只能循环查询 这样会很慢
     */
    @GetMapping("/homePage/letterType")
    public AjaxResult letterType(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        List<GovGetLetterTypeModelResp> letterTypeModel = govBiz.getLetterTypeModel();
        List<CommonBaseCount> res = new ArrayList<>();
        letterTypeModel.forEach(e -> {

            CommonBaseCount commonBaseCount = new CommonBaseCount();
            GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
            govGetUnifiedManagementLetterListReq.setType2(e.getValue());
            GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
            commonBaseCount.setLable(e.getText());
            commonBaseCount.setCount(unifiedManagementLetterList.getTotal());
            res.add(commonBaseCount);
        });
        return success(res);
    }


    /**
     * 信件状态
     * 这个只能循环查询 这样会很慢
     */
    @GetMapping("/homePage/letterStatus")
    public AjaxResult letterStatus(BigdataParam bigdataParam) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(bigdataParam.getDeptId())) {
                bigdataParam.setDeptId(getDataDeptId());
            }
        }
        List<GovGetLetterTypeModelResp> letterTypeModel = govBiz.getStatusModel();
        List<CommonBaseCount> res = new ArrayList<>();
        letterTypeModel.forEach(e -> {
            CommonBaseCount commonBaseCount = new CommonBaseCount();
            GovGetUnifiedManagementLetterListReq govGetUnifiedManagementLetterListReq = new GovGetUnifiedManagementLetterListReq();
            govGetUnifiedManagementLetterListReq.setInfostatus(e.getValue());
            GovGetUnifiedManagementLetterListResp unifiedManagementLetterList = govBiz.getUnifiedManagementLetterList(govGetUnifiedManagementLetterListReq);
            commonBaseCount.setLable(e.getText());
            commonBaseCount.setCount(unifiedManagementLetterList.getTotal());
            res.add(commonBaseCount);
        });
        return success(res);
    }

    @Resource
    private IXuLinMatterService xuLinMatterService;

    /**
     * 许邻e家事项统计
     *
     * @return 统计结果
     */
    @GetMapping("/xulinMatterStats")
    public AjaxResult getXuLinMatterStats(BigdataParam bigdataParam) {
        if (bigdataParam.getYear() == null){
            bigdataParam.setYear(DateUtil.thisYear());
        }
        return success(xuLinMatterService.getXuLinMatterStats(bigdataParam));
    }
}
