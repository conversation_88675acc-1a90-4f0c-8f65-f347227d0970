package com.ruoyi.microPlatform.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 支部关联网格对象 tb_branch_grid_info
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public class TbBranchGridInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 支部id */
    @Excel(name = "支部id")
    private Long branchId;

    /** 网格id */
    @Excel(name = "网格id")
    private Long gridId;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 所属县区 */
    @Excel(name = "所属县区")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村镇/社区 */
    @Excel(name = "所属村镇/社区")
    private String town;

    /** 网格编码 */
    @Excel(name = "网格编码")
    private String gridName;

    private String gridRange;


    public String getGridRange() {
        return gridRange;
    }

    public void setGridRange(String gridRange) {
        this.gridRange = gridRange;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBranchId(Long branchId) 
    {
        this.branchId = branchId;
    }

    public Long getBranchId() 
    {
        return branchId;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("branchId", getBranchId())
            .append("gridId", getGridId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
