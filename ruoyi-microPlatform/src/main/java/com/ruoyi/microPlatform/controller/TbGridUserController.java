package com.ruoyi.microPlatform.controller;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbGridUser;
import com.ruoyi.microPlatform.service.ITbGridUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格员信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/gridUser")
public class TbGridUserController extends BaseController {
    @Autowired
    private ITbGridUserService tbGridUserService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询网格员信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbGridUser tbGridUser) {
        if (ObjectUtil.isNotEmpty(tbGridUser.getIsPage()) && tbGridUser.getIsPage() == 1) {
            startPage();
        }
        handle(tbGridUser);
        List<TbGridUser> list = tbGridUserService.selectTbGridUserList(tbGridUser);
        return getDataTable(list);
    }

    public void handle(TbGridUser tbGridUser) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                // 网格长用户
                tbGridUser.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格员数据
                if (ObjectUtil.isEmpty(tbGridUser.getDeptId())) {
                    tbGridUser.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出网格员信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:export')")
    @Log(title = "网格员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbGridUser tbGridUser) {
        handle(tbGridUser);
        if (ObjectUtil.isNotEmpty(tbGridUser.getIsExportAll()) && tbGridUser.getIsExportAll() == 0) {
            startPage(pageNum, pageSize);
        }
        List<TbGridUser> list = tbGridUserService.selectTbGridUserList(tbGridUser);
        ExcelUtil<TbGridUser> util = new ExcelUtil<TbGridUser>(TbGridUser.class);
        util.exportExcel(response, list, "网格员信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbGridUser tbGridUser) {
        handle(tbGridUser);
        if (ObjectUtil.isNotEmpty(tbGridUser.getIsExportAll()) && tbGridUser.getIsExportAll() == 0) {
            startPage();
        }
        Integer integer = tbGridUserService.selectTbGridUserCount(tbGridUser);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbGridUser);
    }

    @Log(title = "网格员信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbGridUser> util = new ExcelUtil<TbGridUser>(TbGridUser.class);
        List<TbGridUser> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbGridUserService.importTbGridUser(infos, updateSupport, operName, userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbGridUser> util = new ExcelUtil<TbGridUser>(TbGridUser.class);
        util.exportTemplateExcel(response, "网格员信息数据");
    }

    /**
     * 获取网格员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbGridUserService.selectTbGridUserById(id));
    }

    public void getDeptInfo(TbGridUser tbGridUser) {
        if (ObjectUtil.isEmpty(tbGridUser.getDeptId())) {
            tbGridUser.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbGridUser.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbGridUser.setCounty(split[1]);
            if (split.length >= 3) {
                tbGridUser.setCountry(split[2]);
                if (split.length >= 4) {
                    tbGridUser.setTown(split[3]);
                }
            }
        }

        if (tbGridUserService.checkTbGridUser(tbGridUser)) {
            throw new ServiceException("数据已存在！");
        }
        if (tbGridUser.getPostStr().equals("网格长")) {
            if (tbGridUserService.checkTbGridUserUnique(new TbGridUser() {{
                setGridId(tbGridUser.getGridId());
                setId(tbGridUser.getId());
            }})) {
                throw new ServiceException(tbGridUser.getGridName() + " 网格长已存在，" + tbGridUser.getName() + "不可再设置为网格长");
            }
        }

        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank( tbGridUser.getIdCard())){
                String s = tbGridUser.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbGridUser.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    tbGridUser.setBirthday(idCard);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 新增网格员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:add')")
    @Log(title = "网格员信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbGridUser tbGridUser) throws InvalidKeySpecException, NoSuchAlgorithmException {
        if (ObjectUtil.isEmpty(tbGridUser.getGridId())) {
            return error("关联网格不可为空！");
        }
        getDeptInfo(tbGridUser);
        tbGridUser.setCreateBy(getUsername());
        return toAjax(tbGridUserService.insertTbGridUser(tbGridUser));
    }

    @GetMapping("/getMyGridUserList")
    @Anonymous
    public AjaxResult getMyGridUserList(Long userId) {
        try {
            SysUser user = getLoginUser().getUser();
            if (StringUtils.isBlank(user.getPhonenumber())) {
                return error("当前账户无手机号！");
            }
            List<TbGridUser> list = tbGridUserService.getMyGridUserList(getLoginUser());
            return success(list);
        } catch (Exception e) {
            return success(new ArrayList<>());
        }

    }

    /**
     * 修改网格员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:edit')")
    @Log(title = "网格员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbGridUser tbGridUser) {
        tbGridUser.setUpdateBy(getUsername());
        if (ObjectUtil.isEmpty(tbGridUser.getGridId())) {
            return error("关联网格不可为空！");
        }
        getDeptInfo(tbGridUser);
        return toAjax(tbGridUserService.updateTbGridUser(tbGridUser));
    }

    /**
     * 删除网格员信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUser:remove')")
    @Log(title = "网格员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(tbGridUserService.deleteTbGridUserById(id));
    }
}
