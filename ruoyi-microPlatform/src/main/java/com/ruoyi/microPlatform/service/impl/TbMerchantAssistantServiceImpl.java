package com.ruoyi.microPlatform.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbMerchantAssistantMapper;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;
import com.ruoyi.microPlatform.service.ITbMerchantAssistantService;

/**
 * 门店店员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbMerchantAssistantServiceImpl implements ITbMerchantAssistantService 
{
    @Autowired
    private TbMerchantAssistantMapper tbMerchantAssistantMapper;

    /**
     * 查询门店店员
     * 
     * @param id 门店店员主键
     * @return 门店店员
     */
    @Override
    public TbMerchantAssistant selectTbMerchantAssistantById(Long id)
    {
        return tbMerchantAssistantMapper.selectTbMerchantAssistantById(id);
    }

    /**
     * 校验门店店员是否存在
     *
     * @param tbMerchantAssistant
     * @return boolean
     */
    @Override
    public boolean checkTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant){
        TbMerchantAssistant old = tbMerchantAssistantMapper.checkTbMerchantAssistantUnique(tbMerchantAssistant);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询门店店员列表
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 门店店员
     */
    @Override
    public List<TbMerchantAssistant> selectTbMerchantAssistantList(TbMerchantAssistant tbMerchantAssistant)
    {
        return tbMerchantAssistantMapper.selectTbMerchantAssistantList(tbMerchantAssistant);
    }

    @Override
    public Integer selectTbMerchantAssistantCount(TbMerchantAssistant tbMerchantAssistant) {
        List<Long> longs = tbMerchantAssistantMapper.selectTbMerchantAssistantCount(tbMerchantAssistant);
        return longs.size();
    }

    /**
     * 导入门店店员
     *
     * @param infos       门店店员列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantAssistant(List<TbMerchantAssistant> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入门店店员数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbMerchantAssistant info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbMerchantAssistant old = tbMerchantAssistantMapper.checkTbMerchantAssistantUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    this.insertTbMerchantAssistant(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    this.updateTbMerchantAssistant(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getName() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    @Override
    public int insertTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant)
    {
        tbMerchantAssistant.setCreateTime(DateUtils.getNowDate());
        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank( tbMerchantAssistant.getIdCard())){
                String s = tbMerchantAssistant.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbMerchantAssistant.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    tbMerchantAssistant.setBirthday(idCard);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("身份证号解析出生年月出错！");
        }
        return tbMerchantAssistantMapper.insertTbMerchantAssistant(tbMerchantAssistant);
    }

    /**
     * 修改门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    @Override
    public int updateTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant)
    {
        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank( tbMerchantAssistant.getIdCard())){
                String s = tbMerchantAssistant.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbMerchantAssistant.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    tbMerchantAssistant.setBirthday(idCard);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("身份证号解析出生年月出错！");
        }
        return tbMerchantAssistantMapper.updateTbMerchantAssistant(tbMerchantAssistant);
    }

    /**
     * 批量删除门店店员
     * 
     * @param ids 需要删除的门店店员主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantAssistantByIds(Long[] ids)
    {
        return tbMerchantAssistantMapper.deleteTbMerchantAssistantByIds(ids);
    }

    /**
     * 删除门店店员信息
     * 
     * @param id 门店店员主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantAssistantById(Long id)
    {
        return tbMerchantAssistantMapper.deleteTbMerchantAssistantById(id);
    }
}
