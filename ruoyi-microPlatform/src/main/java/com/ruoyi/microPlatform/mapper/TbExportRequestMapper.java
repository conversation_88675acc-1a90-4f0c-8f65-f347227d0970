package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbExportRequest;
import com.ruoyi.microPlatform.domain.TbRequestAudit;

/**
 * 数据导出审核Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public interface TbExportRequestMapper 
{
    /**
     * 查询数据导出审核
     * 
     * @param id 数据导出审核主键
     * @return 数据导出审核
     */
    public TbExportRequest selectTbExportRequestById(Long id);

    /**
     * 校验数据导出审核是否唯一
     *
     * @param tbExportRequest
     * @return 数据导出审核
     */
    public TbExportRequest checkTbExportRequestUnique(TbExportRequest tbExportRequest);

    /**
     * 查询数据导出审核列表
     * 
     * @param tbExportRequest 数据导出审核
     * @return 数据导出审核集合
     */
    public List<TbExportRequest> selectTbExportRequestList(TbExportRequest tbExportRequest);

    /**
     * 新增数据导出审核
     * 
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    public int insertTbExportRequest(TbExportRequest tbExportRequest);

    /**
     * 修改数据导出审核
     * 
     * @param tbExportRequest 数据导出审核
     * @return 结果
     */
    public int updateTbExportRequest(TbExportRequest tbExportRequest);

    /**
     * 删除数据导出审核
     * 
     * @param id 数据导出审核主键
     * @return 结果
     */
    public int deleteTbExportRequestById(Long id);

    /**
     * 批量删除数据导出审核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbExportRequestByIds(Long[] ids);

    /**
     * 批量删除导出请求审核过程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbRequestAuditByRequestIds(Long[] ids);
    
    /**
     * 批量新增导出请求审核过程
     * 
     * @param tbRequestAuditList 导出请求审核过程列表
     * @return 结果
     */
    public int batchTbRequestAudit(List<TbRequestAudit> tbRequestAuditList);
    

    /**
     * 通过数据导出审核主键删除导出请求审核过程信息
     * 
     * @param id 数据导出审核ID
     * @return 结果
     */
    public int deleteTbRequestAuditByRequestId(Long id);
}
