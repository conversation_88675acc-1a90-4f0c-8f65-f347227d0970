<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbGridInfoMapper">

    <resultMap type="TbGridInfo" id="TbGridInfoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridSort" column="grid_sort"/>
        <result property="gridCode" column="grid_code"/>
        <result property="establishStatus" column="establish_status"/>
        <result property="functionType" column="function_type"/>
        <result property="personPhone" column="person_phone"/>
        <result property="geometry" column="geometry"/>
        <result property="administrativePlan" column="administrative_plan"/>
        <result property="boundary" column="boundary"/>
        <result property="area" column="area"/>
        <result property="communityNum" column="community_num"/>
        <result property="remark" column="remark"/>
        <result property="accessory" column="accessory"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <resultMap type="TbGridInfo" id="TbGridInfoResult1">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridSort" column="grid_sort"/>
        <result property="gridCode" column="grid_code"/>
        <result property="establishStatus" column="establish_status"/>
        <result property="functionType" column="function_type"/>
        <result property="personPhone" column="person_phone"/>
        <result property="geometry" column="geometry"/>
        <result property="administrativePlan" column="administrative_plan"/>
        <result property="boundary" column="boundary"/>
        <result property="area" column="area"/>
        <result property="communityNum" column="community_num"/>
        <result property="remark" column="remark"/>
        <result property="accessory" column="accessory"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <collection property="deptUserList" javaType="java.util.List" resultMap="TbDeptUserResult"/>
        <collection property="gridUserList" javaType="java.util.List" resultMap="TbGridUserResult"/>
    </resultMap>

    <resultMap type="TbDeptUser" id="TbDeptUserResult">
        <result property="id" column="du_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="deptId" column="du_dept_id"/>
        <result property="userId" column="du_user_id"/>
        <result property="name" column="du_name"/>
        <result property="phone" column="du_phone"/>
        <result property="idPhoto" column="du_id_photo"/>
        <result property="postDesc" column="du_post_desc"/>
        <result property="status" column="du_status"/>
        <result property="idCard" column="du_id_card"/>
        <result property="postStr" column="du_post_str"/>
        <result property="postLabel" column="du_post_label"/>
        <result property="age" column="du_age"/>
    </resultMap>


    <resultMap type="TbGridUser" id="TbGridUserResult">
        <result property="id" column="gu_id"/>
        <result property="county" column="gu_county"/>
        <result property="country" column="gu_country"/>
        <result property="town" column="gu_town"/>
        <result property="gridName" column="gu_grid_name"/>
        <result property="gridId" column="gu_grid_id"/>
        <result property="deptId" column="gu_dept_id"/>
        <result property="userId" column="gu_user_id"/>
        <result property="name" column="gu_name"/>
        <result property="phone" column="gu_phone"/>
        <result property="idPhoto" column="gu_id_photo"/>
        <result property="postDesc" column="gu_post_desc"/>
        <result property="status" column="gu_status"/>
        <result property="postDetail" column="gu_post_detail"/>
        <result property="postStr" column="gu_post_str"/>
        <result property="idCard" column="gu_id_card"/>
        <result property="age" column="gu_age"/>
    </resultMap>

    <sql id="selectTbGridInfoVo">
        select id, dept_id, county, country, town, grid_name, grid_sort, grid_code,
               establish_status,function_type,person_phone,
               geometry, administrative_plan, boundary, area, community_num, remark, accessory, create_by, create_time, update_by, update_time, status from tb_grid_info
    </sql>
    <sql id="queryTbGridInfoVo">
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_info.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="id != null ">and id = #{id}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridArr != null  and gridArr != ''">and id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridSort != null  and gridSort != ''">and grid_sort = #{gridSort}</if>
            <if test="gridCode != null  and gridCode != ''">and grid_code = #{gridCode}</if>
            <if test="establishStatus != null  and establishStatus != ''">and establish_status = #{establishStatus}</if>
            <if test="functionType != null  and functionType != ''">and function_type = #{functionType}</if>
            <if test="personPhone != null  and personPhone != ''">and person_phone = #{personPhone}</if>
            <if test="geometry != null  and geometry != ''">and geometry = #{geometry}</if>
            <if test="administrativePlan != null  and administrativePlan != ''">and administrative_plan =
                #{administrativePlan}
            </if>
            <if test="boundary != null  and boundary != ''">and boundary = #{boundary}</if>
            <if test="area != null  and area != ''">and area = #{area}</if>
            <if test="communityNum != null ">and community_num = #{communityNum}</if>
            <if test="accessory != null  and accessory != ''">and accessory = #{accessory}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="name != null and name != ''">and id in
                (select gi.grid_id from tb_sideline_grid_user gu left join tb_sideline_grid_info gi on gu.id = gi.side_user_id where gu.name like concat ('%',#{name},'%'))</if>
            <if test="idCard != null and idCard != ''">and id in
                (select gi.grid_id from tb_sideline_grid_user gu left join tb_sideline_grid_info gi on gu.id = gi.side_user_id where gu.id_card = #{idCard})</if>
            <if test="phone != null and phone != ''">and id in
                (select gi.grid_id from tb_sideline_grid_user gu left join tb_sideline_grid_info gi on gu.id = gi.side_user_id where gu.phone = #{phone})</if>
            <if test="type != null and type != ''">and id in
                (select gi.grid_id from tb_sideline_grid_user gu left join tb_sideline_grid_info gi on gu.id = gi.side_user_id where gu.type = #{type})</if>

            <if test="branchName != null and branchName != ''">and id in
                (select gi.grid_id from tb_branch_info bi left join tb_branch_grid_info gi on bi.id = gi.branch_id where bi.branch_name = #{branchName})</if>
            <if test="secretaryName != null and secretaryName != ''">and id in
                (select gi.grid_id from tb_branch_info bi left join tb_branch_grid_info gi on bi.id = gi.branch_id where bi.secretary_name = #{secretaryName})</if>
            <if test="secretaryPhone != null and secretaryPhone != ''">and id in
                (select gi.grid_id from tb_branch_info bi left join tb_branch_grid_info gi on bi.id = gi.branch_id where bi.phone = #{secretaryPhone})</if>
        </where>
        order by grid_sort asc
    </sql>

    <select id="selectTbGridInfoList" parameterType="TbGridInfo" resultMap="TbGridInfoResult">
        <include refid="selectTbGridInfoVo"/>
        <include refid="queryTbGridInfoVo"/>
    </select>

    <select id="selectExportGridInfoCount" parameterType="TbGridInfo" resultType="Long">
        select id from tb_grid_info
        <include refid="queryTbGridInfoVo"/>
    </select>

    <select id="getWorkGrid" parameterType="TbGridInfo" resultMap="TbGridInfoResult">
       select g.id, g.grid_name, d.dept_id from tb_grid_info g left join sys_dept d on g.id = d.grid_id
       where
       <if test="gridArr != null and gridArr != ''">
           g.id in
           <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
               #{item}
           </foreach>
       </if>
       <if test="gridArr == null or gridArr == ''">
           g.id = 0
       </if>
    </select>

    <select id="selectTbGridInfoById" parameterType="Long" resultMap="TbGridInfoResult">
        <include refid="selectTbGridInfoVo"/>
        where id = #{id}
    </select>

    <select id="selectGridInfoById" parameterType="Long" resultMap="TbGridInfoResult">
        select g.id, g.dept_id, g.county, g.country, g.town, g.grid_name,g.grid_code,g.establish_status,
               g.function_type,g.person_phone,
               g.geometry, g.administrative_plan,
        g.boundary, g.area, g.remark, g.accessory, g.status,g.update_time
        from tb_grid_info g
        where g.id = #{id}
    </select>

<!--    LEFT JOIN sys_dict_data dd ON du.post_str = dd.dict_value AND dd.dict_type = 'dept_user_type'-->
    <select id="selectDeptUserByGridId" parameterType="Long" resultMap="TbDeptUserResult">
        SELECT
            du.id as du_id, du.dept_id as du_dept_id, du.name as du_name, du.phone as du_phone, du.id_photo as du_id_photo,
            TIMESTAMPDIFF(YEAR, du.birthday, CURDATE()) AS du_age,
            du.post_desc as du_post_desc, du.status as du_status, du.id_card as du_id_card, du.post_str as du_post_str
        FROM tb_dept_user du
        JOIN tb_dept_user_grid dug ON du.id = dug.dept_user_id
        WHERE dug.grid_id = #{gridId} AND du.status = '0'
    </select>
    <select id="selectGridUserByGridId" parameterType="Long" resultMap="TbGridUserResult">
        select
        gu.id as gu_id, gu.name as gu_name, gu.phone as gu_phone, gu.id_photo as gu_id_photo,
        gu.post_desc as gu_post_desc, gu.status as gu_status, gu.post_detail as gu_post_detail,
        TIMESTAMPDIFF(YEAR, gu.birthday, CURDATE()) AS gu_age,
        gu.post_str as gu_post_str, gu.id_card as gu_id_card
        from tb_grid_user gu where gu.grid_id = #{id} and gu.status = '0'
        ORDER BY
            CASE
                WHEN gu.post_str = '网格长' THEN 1 -- 网格长排在最前
                ELSE 2 -- 其他网格员排在后面
            END asc,
            gu.id desc -- 按网格员 ID 排序
    </select>
<!--    <select id="selectGridInfoById" parameterType="Long" resultMap="TbGridInfoResult1">-->
<!--        select g.id, g.dept_id, g.county, g.country, g.town, g.grid_name,g.grid_code,g.establish_status,-->
<!--               g.function_type,g.person_phone,-->
<!--               g.geometry, g.administrative_plan,-->
<!--        g.boundary, g.area, g.remark, g.accessory, g.status,g.update_time,-->
<!--        gu.id as gu_id, gu.name as gu_name, gu.phone as gu_phone, gu.id_photo as gu_id_photo, gu.post_desc as gu_post_desc, gu.status as gu_status, gu.post_detail as gu_post_detail, gu.post_str as gu_post_str, gu.id_card as gu_id_card,-->
<!--        du.id as du_id, du.dept_id as du_dept_id, du.name as du_name, du.phone as du_phone, du.id_photo as du_id_photo, du.post_desc as du_post_desc, du.status as du_status, du.id_card as du_id_card, du.post_str as du_post_str, dd.dict_label as du_post_label-->
<!--        from tb_grid_info g-->
<!--        left join tb_grid_user gu on gu.grid_id = g.id and gu.status = '0'-->
<!--        left join tb_dept_user_grid dug on dug.grid_id = g.id-->
<!--        left join tb_dept_user du on dug.dept_user_id = du.id and du.status = '0'-->
<!--        left join sys_dict_data dd on du.post_str = dd.dict_value on dd.dict_type = 'dept_user_type'-->
<!--        where g.id = #{id}-->
<!--        ORDER BY-->
<!--            CASE-->
<!--                WHEN gu.post_str = '网格长' THEN 1 &#45;&#45; 网格长排在最前-->
<!--                ELSE 2 &#45;&#45; 其他网格员排在后面-->
<!--            END asc,-->
<!--            gu.id desc; &#45;&#45; 按网格员 ID 排序-->
<!--    </select>-->

    <select id="checkTbGridInfoUnique" parameterType="TbGridInfo" resultMap="TbGridInfoResult">
        select id, grid_sort  from tb_grid_info where dept_id = #{deptId} and grid_name = #{gridName}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbGridInfo" parameterType="TbGridInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_grid_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridSort != null">grid_sort,</if>
            <if test="gridCode != null">grid_code,</if>
            <if test="establishStatus != null">establish_status,</if>
            <if test="functionType != null">function_type,</if>
            <if test="personPhone != null">person_phone,</if>
            <if test="geometry != null">geometry,</if>
            <if test="administrativePlan != null">administrative_plan,</if>
            <if test="boundary != null">boundary,</if>
            <if test="area != null">area,</if>
            <if test="communityNum != null">community_num,</if>
            <if test="remark != null">remark,</if>
            <if test="accessory != null">accessory,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridSort != null">#{gridSort},</if>
            <if test="gridCode != null">#{gridCode},</if>
            <if test="establishStatus != null">#{establishStatus},</if>
            <if test="functionType != null">#{functionType},</if>
            <if test="personPhone != null">#{personPhone},</if>
            <if test="geometry != null">#{geometry},</if>
            <if test="administrativePlan != null">#{administrativePlan},</if>
            <if test="boundary != null">#{boundary},</if>
            <if test="area != null">#{area},</if>
            <if test="communityNum != null">#{communityNum},</if>
            <if test="remark != null">#{remark},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateTbGridInfo" parameterType="TbGridInfo">
        update tb_grid_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null and county != ''">county = #{county},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="town != null and town != ''">town = #{town},</if>
            <if test="gridName != null and gridName != ''">grid_name = #{gridName},</if>
            <if test="gridSort != null">grid_sort = #{gridSort},</if>
            <if test="gridCode != null and gridCode != ''">grid_code =#{gridCode},</if>
            <if test="establishStatus != null">establish_status =#{establishStatus},</if>
            <if test="functionType != null and functionType != ''">function_type =#{functionType},</if>
            <if test="personPhone != null and personPhone != ''">person_phone =#{personPhone},</if>
            <if test="geometry != null and geometry != ''">geometry = #{geometry},</if>
            <if test="administrativePlan != null">administrative_plan = #{administrativePlan},</if>
            <if test="boundary != null and boundary != ''">boundary = #{boundary},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="communityNum != null">community_num = #{communityNum},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            <if test="accessory != null and accessory != ''">accessory = #{accessory},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbGridInfoById" parameterType="Long">
        delete from tb_grid_info where id = #{id}
    </delete>

    <delete id="deleteTbGridInfoByIds" parameterType="String">
        delete from tb_grid_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbGridInfoSpaceList" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbGridInfoResult">
        <include refid="selectTbGridInfoVo"/>
        <where>
            geometry IS NOT NULL
            and geometry != ''
            <if test="deptId != null ">
                and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_grid_info.dept_id))
            </if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and id = #{gridId}
            </if>

        </where>
        order by grid_sort asc
    </select>

    <select id="getGridInfo" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo"  resultMap="TbGridInfoResult">
        select
            id,town,boundary
        from
            tb_grid_info
        where
            grid_name = #{gridName}
    </select>
</mapper>