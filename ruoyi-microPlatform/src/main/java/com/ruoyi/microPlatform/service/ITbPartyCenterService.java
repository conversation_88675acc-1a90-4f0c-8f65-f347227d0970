package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbPartyCenter;

/**
 * 党群服务中心报到情况Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ITbPartyCenterService 
{
    /**
     * 查询党群服务中心报到情况
     * 
     * @param id 党群服务中心报到情况主键
     * @return 党群服务中心报到情况
     */
    public TbPartyCenter selectTbPartyCenterById(Long id);

    /**
     * 校验党群服务中心报到情况是否存在
     *
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 党群服务中心报到情况
     */
    public boolean checkTbPartyCenter(TbPartyCenter tbPartyCenter);

    /**
     * 查询党群服务中心报到情况列表
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 党群服务中心报到情况集合
     */
    public List<TbPartyCenter> selectTbPartyCenterList(TbPartyCenter tbPartyCenter);

    /**
     * 导入党群服务中心报到情况
     *
     * @param infos       党群服务中心报到情况列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbPartyCenter(List<TbPartyCenter> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    public int insertTbPartyCenter(TbPartyCenter tbPartyCenter);

    /**
     * 修改党群服务中心报到情况
     * 
     * @param tbPartyCenter 党群服务中心报到情况
     * @return 结果
     */
    public int updateTbPartyCenter(TbPartyCenter tbPartyCenter);

    /**
     * 批量删除党群服务中心报到情况
     * 
     * @param ids 需要删除的党群服务中心报到情况主键集合
     * @return 结果
     */
    public int deleteTbPartyCenterByIds(Long[] ids);

    /**
     * 删除党群服务中心报到情况信息
     * 
     * @param id 党群服务中心报到情况主键
     * @return 结果
     */
    public int deleteTbPartyCenterById(Long id);
}
