package com.ruoyi.microPlatform.service.impl;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.RSAUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import com.ruoyi.microPlatform.mapper.TbGridUserCommunityMapper;
import com.ruoyi.microPlatform.mapper.TbResidentMemberMapper;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbGridUserMapper;
import com.ruoyi.microPlatform.domain.TbGridUser;
import com.ruoyi.microPlatform.service.ITbGridUserService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 网格员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbGridUserServiceImpl implements ITbGridUserService {
    @Autowired
    private TbGridUserMapper tbGridUserMapper;
    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    @Autowired
    private RedisCache redisCache;
    //    @Autowired
//    private TbResidentMemberMapper memberMapper;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private TbGridUserCommunityMapper userCommunityMapper;
    @Autowired
    private SysUserRoleMapper userRoleMapper;

    /**
     * 查询网格员信息
     *
     * @param id 网格员信息主键
     * @return 网格员信息
     */
    @Override
    public TbGridUser selectTbGridUserById(Long id) {
        return tbGridUserMapper.selectTbGridUserById(id);
    }

    /**
     * 校验网格员信息是否存在
     *
     * @param tbGridUser
     * @return boolean
     */
    @Override
    public boolean checkTbGridUser(TbGridUser tbGridUser) {
        TbGridUser old = tbGridUserMapper.checkTbGridUserUnique(tbGridUser);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkTbGridUserUnique(TbGridUser tbGridUser) {
        TbGridUser old = tbGridUserMapper.checkGridUserBig(tbGridUser);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询网格员信息列表
     *
     * @param tbGridUser 网格员信息
     * @return 网格员信息
     */
    @Override
    public List<TbGridUser> selectTbGridUserList(TbGridUser tbGridUser) {
        return tbGridUserMapper.selectTbGridUserList(tbGridUser);
    }

    @Override
    public Integer selectTbGridUserCount(TbGridUser tbGridUser) {
        List<Long> longs = tbGridUserMapper.selectTbGridUserCount(tbGridUser);
        return longs.size();
    }

    /**
     * 导入网格员信息
     *
     * @param infos           网格员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridUser(List<TbGridUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入网格员信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int index = 0;
        int failureNum = 0;

        for (TbGridUser info : infos) {
            index++;
            info.setStatus("0");
            try {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getCounty())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属县（市、区） 不可为空" + "。数据条目：" + index);
                } else {
                    if (StringUtils.isBlank(info.getCountry())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属街道/乡镇 不可为空" + "。数据条目：" + index);
                    } else {
                        if (StringUtils.isBlank(info.getTown())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属村/社区 不可为空" + "。数据条目：" + index);
                        } else {
                            if (StringUtils.isBlank(info.getName())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据 网格员名称 不可为空" + "。数据条目：" + index);
                            } else {
                                if (StringUtils.isBlank(info.getGridName())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + "所属网格 不可为空" + "。数据条目：" + index);
                                } else {
                                    if (StringUtils.isBlank(info.getPostStr())) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 网格员角色 不可为空" + "。数据条目：" + index);
                                    } else {

                                        boolean flag = true;
                                        String msg = "";
                                        if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                            flag = false;
                                            msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getName() + "网格员数据" + "。数据条目：" + index);
                                        }

                                        if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                            flag = false;
                                            msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getName() + "网格员数据" + "。数据条目：" + index);
                                        }

                                        if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                            flag = false;
                                            msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getName() + "网格员数据" + "。数据条目：" + index);
                                        }

                                        if (!flag) {
                                            failureNum++;
                                            failureMsg.append(msg);
                                        } else {
                                            SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                            if (ObjectUtil.isEmpty(dept)) {
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格员数据所属部门未找到！" + "数据条目：" + index);
                                            } else {
                                                info.setDeptId(dept.getDeptId());
                                                TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                                    setDeptId(dept.getDeptId());
                                                    setGridName(info.getGridName());
                                                }});
                                                if (ObjectUtil.isEmpty(gridInfo)) {
                                                    failureNum++;
                                                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格员数据所属网格未找到！" + "数据条目：" + index);
                                                } else {
                                                    info.setGridId(gridInfo.getId());
                                                    // 验证是否存在这个数据
                                                    TbGridUser old = tbGridUserMapper.checkTbGridUserUnique(info);
                                                    flag = true;
                                                    if (info.getPostStr().equals("网格长")) {
                                                        // 校验是否已经存在网格长,只有当前用户是网格长的时候才需要校验
                                                        TbGridUser tbGridUser = tbGridUserMapper.checkGridUserBig(new TbGridUser() {{
                                                            setGridId(info.getGridId());
                                                            setId(ObjectUtil.isEmpty(old) ? null : old.getId());
                                                            setPostStr(info.getPostStr());
                                                        }});
                                                        if (ObjectUtil.isNotEmpty(tbGridUser)) {
                                                            flag = false;
                                                        }
                                                    }
                                                    if (!flag) {
                                                        failureNum++;
                                                        failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格长已存在，" + info.getName() + "不可再设置为网格长" + "。数据条目：" + index);
                                                    } else {
                                                        //根据身份证号获取生日信息
                                                        if (StringUtils.isNotBlank(info.getIdCard())) {
                                                            String s = info.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                                                            info.setIdCard(s);
                                                            Date idCard = DateUtils.getIdCard(s);
                                                            if (ObjectUtil.isNotEmpty(idCard)) {
                                                                info.setBirthday(idCard);
                                                            }
                                                        }
                                                        if (ObjectUtil.isEmpty(old)) {
                                                            info.setCreateTime(new Date());
                                                            info.setCreateBy(operName);
                                                            this.insertTbGridUser(info);
                                                            successNum++;
                                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 导入成功");
                                                        } else if (isUpdateSupport) {
                                                            info.setUpdateBy(operName);
                                                            info.setUpdateTime(new Date());
                                                            info.setId(old.getId());
                                                            info.setUserId(old.getUserId());
                                                            this.updateTbGridUser(info);
                                                            successNum++;
                                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 更新成功");
                                                        } else {
                                                            failureNum++;
                                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getName() + " 已存在" + "。数据条目：" + index);
                                                        }
                                                    }

                                                }
                                            }

                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 " + info.getName() + "导入失败：";
                failureMsg.append(msg + e.getMessage() + "。数据条目：" + index);
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增网格员信息
     *
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTbGridUser(TbGridUser tbGridUser) throws InvalidKeySpecException, NoSuchAlgorithmException {
        tbGridUser.setCreateTime(DateUtils.getNowDate());
        // 停用了
        if (tbGridUser.getStatus().equals("0")) {
            SysUser user = userMapper.checkPhoneUnique2(new SysUser() {{
                setPhonenumber(tbGridUser.getPhone());
            }});
            if (ObjectUtil.isEmpty(user)) {
                SysDept dept = deptMapper.selectByGridId(tbGridUser.getGridId());
                if (ObjectUtil.isNotEmpty(dept)) {
                    SysUser user1 = new SysUser();
                    user1.setUserName(tbGridUser.getPhone());
                    user1.setNickName(tbGridUser.getName());
                    user1.setDeptId(dept.getDeptId());
                    user1.setPhonenumber(tbGridUser.getPhone());
                    user1.setCreateBy(tbGridUser.getCreateBy());
                    if (ObjectUtil.isNotEmpty(tbGridUser.getBirthday())) {
                        user1.setSex(StringUtils.getSex(tbGridUser.getIdCard()));
                    }
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user1.setSpare(RSAUtils.publicEncrypt(password, RSAUtils.getPublicKey(RuoYiConfig.getPublicKey())));
                    user1.setPassword(SecurityUtils.encryptPassword(password));
                    user1.setRoleIds(new Long[]{3l});
                    user1.setDrillNo(tbGridUser.getGridId() + "");
                    user1.setCreateBy("网格员新增" + tbGridUser.getCreateBy());
                    user1.setCreateTime(new Date());
                    userService.insertUser(user1);
                    tbGridUser.setUserId(user1.getUserId());
                }
            } else {
                if (ObjectUtil.isNotEmpty(user.getDept())) {
                    if (ObjectUtil.isNotEmpty(user.getDept().getGridId())) {
                        // 是网格用户
                        tbGridUser.setUserId(user.getUserId());
                        String drillNo = user.getDrillNo();
                        if (StringUtils.isNotBlank(drillNo)) {
                            List<String> strings = Arrays.asList(drillNo.split(","));
                            if (!strings.contains(tbGridUser.getGridId() + "")) {
                                // 更新用户管理网格
                                userMapper.updateUser(new SysUser() {{
                                    setUserId(user.getUserId());
                                    setDrillNo(drillNo + "," + tbGridUser.getGridId());
                                }});
                            }
                        } else {
                            userMapper.updateUser(new SysUser() {{
                                setUserId(user.getUserId());
                                setDrillNo(tbGridUser.getGridId() + "");
                            }});
                        }
                    } else {
                        int i = userRoleMapper.selectUserRole(new SysUserRole() {{
                            setRoleId(2l);
                            setUserId(user.getUserId());
                        }});
                        if (i == 1) {
                            SysDept dept = deptMapper.selectByGridId(tbGridUser.getGridId());
                            // 改为网格用户
                            tbGridUser.setUserId(user.getUserId());
                            userMapper.updateUser(new SysUser() {{
                                setUserId(user.getUserId());
                                setDrillNo(tbGridUser.getGridId() + "");
                                setDeptId(dept.getDeptId());
                                setUpdateBy("网格员新增-变更为网格用户" + tbGridUser.getCreateBy());
                            }});
                            userRoleMapper.deleteUserRoleByUserId(user.getUserId());
                            userRoleMapper.insertUserRole(new SysUserRole() {{
                                setUserId(user.getUserId());
                                setRoleId(3l);
                            }});
                        }
                    }
                }
            }
        }
        int i = tbGridUserMapper.insertTbGridUser(tbGridUser);
        return i;
    }

    /**
     * 修改网格员信息
     *
     * @param tbGridUser 网格员信息
     * @return 结果
     */
    @Override
    public int updateTbGridUser(TbGridUser tbGridUser) {
        tbGridUser.setUpdateTime(DateUtils.getNowDate());
        TbGridUser old = tbGridUserMapper.selectTbGridUserById(tbGridUser.getId());

        int i = tbGridUserMapper.updateTbGridUser(tbGridUser);
//        this.updateUser(tbGridUser, old);
        return i;
    }

    @Override
    public List<TbGridUser> getMyGridUserList(LoginUser user) {
        Long gridId = null;
        if (ObjectUtil.isNotEmpty(user.getUser().getJobId())) {
            gridId = user.getUser().getJobId();
        } else {

            SysUser old = redisCache.getCacheObject(CacheConstants.USER_GRID_KEY + user.getUserId());
//            SysUser old = userMapper.selectUserGrid(user.getUserId());
            if (ObjectUtil.isNotEmpty(old) && ObjectUtil.isNotEmpty(old.getJobId())) {
                gridId = old.getJobId();

                user.setUser(userMapper.selectUserByUserName(user.getUsername()));
                System.err.println("啊啊啊 刷新缓存啦啦啦啦");
                tokenService.setLoginUser(user);
            }
        }
        Long finalGridId = gridId;
        if (ObjectUtil.isEmpty(finalGridId)) {
            return new ArrayList<>();
        }
        List<TbGridUser> tbGridUsers = tbGridUserMapper.selectTbGridUserList1(new TbGridUser() {{
            setGridId(finalGridId);
//            setPostStr("网格长");
        }});
        return tbGridUsers;
    }

    /**
     * 批量删除网格员信息
     *
     * @param ids 需要删除的网格员信息主键
     * @return 结果
     */
    @Override
    public int deleteTbGridUserByIds(Long[] ids) {
        return tbGridUserMapper.deleteTbGridUserByIds(ids);
    }

    /**
     * 删除网格员信息信息
     *
     * @param id 网格员信息主键
     * @return 结果
     */
    @Override
    public int deleteTbGridUserById(Long id) {
        TbGridUser user = tbGridUserMapper.selectTbGridUserById(id);
        int i = tbGridUserMapper.deleteTbGridUserById(id);
        userCommunityMapper.deleteTbGridUserCommunityByUserId(id);
        if (i > 0) {
            updateUser(user);
        }
        return i;
    }


    @Async("threadPoolTaskExecutor")
    public void updateUser(TbGridUser old) {
        // 已经关联过用户
        if (ObjectUtil.isNotEmpty(old.getUserId())) {
            SysUser user = userMapper.selectUserById(old.getUserId());
            if (ObjectUtil.isNotEmpty(user)) {
                String drillNo = user.getDrillNo();
                if (StringUtils.isNotBlank(drillNo)) {
                    List<String> strings = new ArrayList<>(Arrays.asList(drillNo.split(",")));
                    String s = old.getGridId() + "";
                    boolean remove = strings.remove(s);
                    if (strings.size() == 0) {
                        userMapper.updateUser(new SysUser() {{
                            setUserId(user.getUserId());
                            setDrillNo("");
                            setStatus("1");
                        }});
                        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + old.getUserId() + ":*");
                        for (String key : keys) {
                            System.err.println("剔除在线用户信息啦：");
                            redisCache.deleteObject(key);
                        }
                    } else {

                        String collect = strings.stream().map(Object::toString).collect(Collectors.joining(","));
                        if (StringUtils.isNotBlank(collect)) {
                            Long deptId = user.getDeptId();
                            if (user.getDept().getGridId().longValue() == old.getGridId()) {
                                // 用户所在部门刚好是删除的这个网格
                                SysDept dept = deptMapper.selectByGridId(Long.parseLong(strings.get(0)));
                                if (ObjectUtil.isNotEmpty(dept)) {
                                    user.setDept(dept);
                                    user.setDeptId(dept.getDeptId());
                                    deptId = dept.getDeptId();
                                }
                            }
                            user.setDrillNo(collect);
                            Long finalDeptId = deptId;
                            userMapper.updateUser(new SysUser() {{
                                setUserId(user.getUserId());
                                setDeptId(finalDeptId);
                                setDrillNo(collect);
                            }});
                        }
                        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + old.getUserId() + ":*");
                        for (String key : keys) {
                            LoginUser loginUser = redisCache.getCacheObject(key);
                            if (ObjectUtil.isNotEmpty(loginUser) && old.getUserId().longValue() == loginUser.getUserId().longValue()) {//更新这个人的信息
                                System.err.println("刷新用户信息啦：" + loginUser.getUser().getNickName());
                                System.err.println("刷新用户权限信息：" + loginUser.getUser().getNickName());
                                loginUser.setUser(user);
                                loginUser.setDeptId(user.getDeptId());
                                tokenService.setLoginUser(loginUser);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<Long> selectGridIdByPhone(String phone) {
        return tbGridUserMapper.selectGridIdByPhone(phone);
    }

    @Resource
    private ISysDeptService deptService;

    @Override
    public List<CommonBaseCount> gridUserCount() {
        SysDept sysDept = new SysDept();
        sysDept.setParentId(1L);
        List<SysDept> sysDepts = deptService.selectDeptList(sysDept);
        if (CollectionUtil.isEmpty(sysDepts)) {
            return new ArrayList<>();
        }
        List<CommonBaseCount> res = new ArrayList<>();
        sysDepts.forEach(e -> {
            TbGridUser tbGridUser = new TbGridUser();
            tbGridUser.setDeptId(e.getDeptId());
            int volunteerCount = tbGridUserMapper.selectTbGridUserCountByDept(tbGridUser);
            CommonBaseCount commonBaseCount = new CommonBaseCount();
            commonBaseCount.setLable(e.getDeptName());
            commonBaseCount.setCount(volunteerCount);
            res.add(commonBaseCount);
        });
        return res;
    }
}
