package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;
import com.ruoyi.microPlatform.service.ISeVolunteerRegisterService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 志愿报名Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/volunteerRegister")
public class SeVolunteerRegisterController extends BaseController {
    @Autowired
    private ISeVolunteerRegisterService seVolunteerRegisterService;

    /**
     * 查询志愿报名列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeVolunteerRegister seVolunteerRegister) {
        startPage();
        handle(seVolunteerRegister);
        List<SeVolunteerRegister> list = seVolunteerRegisterService.selectSeVolunteerRegisterList(seVolunteerRegister);
        return getDataTable(list);
    }


    public void handle(SeVolunteerRegister seVolunteerRegister) {
        if (!SecurityUtils.isAdmin(getUserId())){
            if (SecurityUtils.hasRole("common")) {
                // 普通用户
                seVolunteerRegister.setUserId(getUserId());
            } else {
                // 非普通用户
                if (ObjectUtil.isNotEmpty(seVolunteerRegister.getQueryType())){
                    if (seVolunteerRegister.getQueryType() == 2){
                        // 查询个人提交的
                        seVolunteerRegister.setUserId(getUserId());
                        return;
                    }
                    else if (seVolunteerRegister.getQueryType() == 1){
                        // 查询全部
                        seVolunteerRegister.setUserId(null);
                    }
                }

                if (ObjectUtil.isEmpty(seVolunteerRegister.getDeptId())) {
                    seVolunteerRegister.setDeptId(getDataDeptId());
                }
            }
        }
    }

    @GetMapping("/submitCount")
    public AjaxResult submitCount(SeVolunteerRegister seVolunteerRegister) {
        seVolunteerRegister.setUserId(getUserId());
        return success(seVolunteerRegisterService.selectSubmitCount(seVolunteerRegister));
    }

    /**
     * 导出志愿报名列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:export')")
    @Log(title = "志愿报名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeVolunteerRegister seVolunteerRegister) {
        handle(seVolunteerRegister);
        List<SeVolunteerRegister> list = seVolunteerRegisterService.selectSeVolunteerRegisterList(seVolunteerRegister);
        ExcelUtil<SeVolunteerRegister> util = new ExcelUtil<SeVolunteerRegister>(SeVolunteerRegister.class);
        util.exportExcel(response, list, "志愿报名数据");
    }

    @Log(title = "志愿报名", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SeVolunteerRegister> util = new ExcelUtil<SeVolunteerRegister>(SeVolunteerRegister.class);
        List<SeVolunteerRegister> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seVolunteerRegisterService.importSeVolunteerRegister(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SeVolunteerRegister> util = new ExcelUtil<SeVolunteerRegister>(SeVolunteerRegister.class);
        util.exportTemplateExcel(response, "志愿报名数据");
    }

    /**
     * 获取志愿报名详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(seVolunteerRegisterService.selectSeVolunteerRegisterById(id));
    }

    /**
     * 新增志愿报名
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:add')")
    @Log(title = "志愿报名", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeVolunteerRegister seVolunteerRegister) {
        seVolunteerRegister.setCreateBy(getUsername());

        if (ObjectUtil.isEmpty(seVolunteerRegister.getDeptId())) {
            seVolunteerRegister.setDeptId(getDataDeptId());
        }

        seVolunteerRegister.setUserId(getUserId());
        // 其他数据赋值
        SysDept dept = getDept(seVolunteerRegister.getDeptId());
        if (ObjectUtil.isNotEmpty(dept)) {
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2) {
                seVolunteerRegister.setCounty(split[1]);
                if (split.length >= 3) {
                    seVolunteerRegister.setCountry(split[2]);
                    if (split.length >= 4) {
                        seVolunteerRegister.setTown(split[3]);
                    }
                }
            }
        }
        return toAjax(seVolunteerRegisterService.insertSeVolunteerRegister(seVolunteerRegister));
    }

    /**
     * 修改志愿报名
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:edit')")
    @Log(title = "志愿报名", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeVolunteerRegister seVolunteerRegister) {
        seVolunteerRegister.setUpdateBy(getUsername());
        return toAjax(seVolunteerRegisterService.updateSeVolunteerRegister(seVolunteerRegister));
    }

    /**
     * 删除志愿报名
     */
    @PreAuthorize("@ss.hasPermi('sentiment:volunteerRegister:remove')")
    @Log(title = "志愿报名", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(seVolunteerRegisterService.deleteSeVolunteerRegisterByIds(ids));
    }
}
