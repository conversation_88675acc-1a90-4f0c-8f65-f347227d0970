<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbEnterpriseAppealMapper">

    <resultMap type="DbEnterpriseAppeal" id="DbEnterpriseAppealResult">
            <result property="id"    column="id"    />
            <result property="serialNo"    column="serial_no"    />
            <result property="county"    column="county"    />
            <result property="enterpriseName"    column="enterprise_name"    />
            <result property="contactPerson"    column="contact_person"    />
            <result property="contactPhone"    column="contact_phone"    />
            <result property="problemCategory"    column="problem_category"    />
            <result property="problemDesc"    column="problem_desc"    />
            <result property="solutionSuggestion"    column="solution_suggestion"    />
            <result property="collectDate"    column="collect_date"    />
            <result property="problemSource"    column="problem_source"    />
            <result property="appealTags"    column="appeal_tags"    />
            <result property="appealStatus"    column="appeal_status"    />
            <result property="leaderContact"    column="leader_contact"    />
            <result property="solutionLevel"    column="solution_level"    />
            <result property="responsibleUnit"    column="responsible_unit"    />
            <result property="handlingStatus"    column="handling_status"    />
            <result property="createTime"    column="create_time"    />
            <result property="createBy"    column="create_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectDbEnterpriseAppealVo">
        select id, serial_no, county, enterprise_name, contact_person, contact_phone, problem_category, problem_desc, solution_suggestion, collect_date, problem_source, appeal_tags, appeal_status, leader_contact, solution_level, responsible_unit, handling_status, create_time, create_by, update_time, update_by from db_enterprise_appeal
    </sql>

    <select id="selectDbEnterpriseAppealList" parameterType="DbEnterpriseAppeal" resultMap="DbEnterpriseAppealResult">
        <include refid="selectDbEnterpriseAppealVo"/>
        <where>
                        <if test="serialNo != null  and serialNo != ''"> and serial_no = #{serialNo}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="enterpriseName != null  and enterpriseName != ''"> and enterprise_name like concat('%', #{enterpriseName}, '%')</if>
                        <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
                        <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
                        <if test="problemCategory != null  and problemCategory != ''"> and problem_category = #{problemCategory}</if>
                        <if test="problemDesc != null  and problemDesc != ''"> and problem_desc = #{problemDesc}</if>
                        <if test="solutionSuggestion != null  and solutionSuggestion != ''"> and solution_suggestion = #{solutionSuggestion}</if>
                        <if test="collectDate != null "> and collect_date = #{collectDate}</if>
                        <if test="problemSource != null  and problemSource != ''"> and problem_source = #{problemSource}</if>
                        <if test="appealTags != null  and appealTags != ''"> and appeal_tags = #{appealTags}</if>
                        <if test="appealStatus != null  and appealStatus != ''"> and appeal_status = #{appealStatus}</if>
                        <if test="leaderContact != null  and leaderContact != ''"> and leader_contact = #{leaderContact}</if>
                        <if test="solutionLevel != null  and solutionLevel != ''"> and solution_level = #{solutionLevel}</if>
                        <if test="responsibleUnit != null  and responsibleUnit != ''"> and responsible_unit = #{responsibleUnit}</if>
                        <if test="handlingStatus != null  and handlingStatus != ''"> and handling_status = #{handlingStatus}</if>
        </where>
    </select>

    <select id="selectDbEnterpriseAppealById" parameterType="Long" resultMap="DbEnterpriseAppealResult">
            <include refid="selectDbEnterpriseAppealVo"/>
            where id = #{id}
    </select>

    <select id="checkDbEnterpriseAppealUnique" parameterType="DbEnterpriseAppeal" resultMap="DbEnterpriseAppealResult">
        select id from db_enterprise_appeal where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>
    <select id="enterpriseAppealGroup" resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        select id from db_enterprise_appeal
        group by
    </select>

    <insert id="insertDbEnterpriseAppeal" parameterType="DbEnterpriseAppeal" useGeneratedKeys="true" keyProperty="id">
        insert into db_enterprise_appeal
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="serialNo != null and serialNo != ''">serial_no,</if>
                    <if test="county != null">county,</if>
                    <if test="enterpriseName != null">enterprise_name,</if>
                    <if test="contactPerson != null">contact_person,</if>
                    <if test="contactPhone != null">contact_phone,</if>
                    <if test="problemCategory != null">problem_category,</if>
                    <if test="problemDesc != null">problem_desc,</if>
                    <if test="solutionSuggestion != null">solution_suggestion,</if>
                    <if test="collectDate != null">collect_date,</if>
                    <if test="problemSource != null">problem_source,</if>
                    <if test="appealTags != null">appeal_tags,</if>
                    <if test="appealStatus != null">appeal_status,</if>
                    <if test="leaderContact != null">leader_contact,</if>
                    <if test="solutionLevel != null">solution_level,</if>
                    <if test="responsibleUnit != null">responsible_unit,</if>
                    <if test="handlingStatus != null">handling_status,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="serialNo != null and serialNo != ''">#{serialNo},</if>
                    <if test="county != null">#{county},</if>
                    <if test="enterpriseName != null">#{enterpriseName},</if>
                    <if test="contactPerson != null">#{contactPerson},</if>
                    <if test="contactPhone != null">#{contactPhone},</if>
                    <if test="problemCategory != null">#{problemCategory},</if>
                    <if test="problemDesc != null">#{problemDesc},</if>
                    <if test="solutionSuggestion != null">#{solutionSuggestion},</if>
                    <if test="collectDate != null">#{collectDate},</if>
                    <if test="problemSource != null">#{problemSource},</if>
                    <if test="appealTags != null">#{appealTags},</if>
                    <if test="appealStatus != null">#{appealStatus},</if>
                    <if test="leaderContact != null">#{leaderContact},</if>
                    <if test="solutionLevel != null">#{solutionLevel},</if>
                    <if test="responsibleUnit != null">#{responsibleUnit},</if>
                    <if test="handlingStatus != null">#{handlingStatus},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateDbEnterpriseAppeal" parameterType="DbEnterpriseAppeal">
        update db_enterprise_appeal
        <trim prefix="SET" suffixOverrides=",">
                    <if test="serialNo != null and serialNo != ''">serial_no = #{serialNo},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="enterpriseName != null">enterprise_name = #{enterpriseName},</if>
                    <if test="contactPerson != null">contact_person = #{contactPerson},</if>
                    <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
                    <if test="problemCategory != null">problem_category = #{problemCategory},</if>
                    <if test="problemDesc != null">problem_desc = #{problemDesc},</if>
                    <if test="solutionSuggestion != null">solution_suggestion = #{solutionSuggestion},</if>
                    <if test="collectDate != null">collect_date = #{collectDate},</if>
                    <if test="problemSource != null">problem_source = #{problemSource},</if>
                    <if test="appealTags != null">appeal_tags = #{appealTags},</if>
                    <if test="appealStatus != null">appeal_status = #{appealStatus},</if>
                    <if test="leaderContact != null">leader_contact = #{leaderContact},</if>
                    <if test="solutionLevel != null">solution_level = #{solutionLevel},</if>
                    <if test="responsibleUnit != null">responsible_unit = #{responsibleUnit},</if>
                    <if test="handlingStatus != null">handling_status = #{handlingStatus},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbEnterpriseAppealById" parameterType="Long">
        delete from db_enterprise_appeal where id = #{id}
    </delete>

    <delete id="deleteDbEnterpriseAppealByIds" parameterType="String">
        delete from db_enterprise_appeal where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>