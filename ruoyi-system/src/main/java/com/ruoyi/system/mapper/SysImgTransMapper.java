package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SysImgTrans;

/**
 * 用户签名图片转换Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
public interface SysImgTransMapper 
{
    /**
     * 查询用户签名图片转换
     * 
     * @param id 用户签名图片转换主键
     * @return 用户签名图片转换
     */
    public SysImgTrans selectSysImgTransById(Long id);

    /**
     * 查询用户签名图片转换列表
     * 
     * @param sysImgTrans 用户签名图片转换
     * @return 用户签名图片转换集合
     */
    public List<SysImgTrans> selectSysImgTransList(SysImgTrans sysImgTrans);

    /**
     * 新增用户签名图片转换
     * 
     * @param sysImgTrans 用户签名图片转换
     * @return 结果
     */
    public int insertSysImgTrans(SysImgTrans sysImgTrans);

    /**
     * 修改用户签名图片转换
     * 
     * @param sysImgTrans 用户签名图片转换
     * @return 结果
     */
    public int updateSysImgTrans(SysImgTrans sysImgTrans);

    /**
     * 删除用户签名图片转换
     * 
     * @param id 用户签名图片转换主键
     * @return 结果
     */
    public int deleteSysImgTransById(Long id);

    /**
     * 批量删除用户签名图片转换
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysImgTransByIds(Long[] ids);
}
