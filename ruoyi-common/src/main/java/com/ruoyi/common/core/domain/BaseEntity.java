package com.ruoyi.common.core.domain;

import java.io.Serializable;
import java.util.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * Entity基类
 * 
 * <AUTHOR>
 */
public class BaseEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 搜索值 */
    @JsonIgnore
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 备注 */
    private String remark;

    /** 请求参数 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Map<String, Object> params;

    private String accessory;

    private List<String> fileList = new ArrayList<>();
    /**
     * 操作端：0PC，1移动端
     */
    private Integer optionType = 0;

    private Integer queryType = 0;

    private String firstFile;

    private List<String> fileList2 = new ArrayList<>();

    private String gridArr;

    private Integer isExportAll = 0;

    private Integer rowNum;

    public Integer getRowNum() {
        return rowNum;
    }

    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    public Integer getIsExportAll() {
        return isExportAll;
    }

    public void setIsExportAll(Integer isExportAll) {
        this.isExportAll = isExportAll;
    }

    public String getGridArr() {
        return gridArr;
    }

    public void setGridArr(String gridArr) {
        this.gridArr = gridArr;
    }


    public List<String> getFileList2() {
        return fileList2;
    }

    public void setFileList2(List<String> fileList2) {
        this.fileList2 = fileList2;
    }

    public Integer getQueryType() {
        return queryType;
    }

    public void setQueryType(Integer queryType) {
        this.queryType = queryType;
    }

    public String getSearchValue()
    {
        return searchValue;
    }

    public void setSearchValue(String searchValue)
    {
        this.searchValue = searchValue;
    }

    public String getCreateBy()
    {
        return createBy;
    }

    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }

    public String getUpdateBy()
    {
        return updateBy;
    }

    public void setUpdateBy(String updateBy)
    {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime()
    {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime)
    {
        this.updateTime = updateTime;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public Map<String, Object> getParams()
    {
        if (params == null)
        {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params)
    {
        this.params = params;
    }

    public void setAccessory(String accessory)
    {
        this.accessory = accessory;
        if (accessory != null && !this.accessory.equals("")){
            List<String> stringList = Arrays.asList(accessory.split(","));
            this.setFileList(stringList);
            if (stringList.size() > 0){
                this.setFirstFile(stringList.get(0));
            }
        }
    }

    public String getAccessory()
    {
        return accessory;
    }


    public List<String> getFileList() {
        return fileList;
    }


    public void setFileList(List<String> fileList) {
        this.fileList = fileList;
    }

    public Integer getOptionType() {
        return optionType;
    }

    public void setOptionType(Integer optionType) {
        this.optionType = optionType;
    }

    public String getFirstFile() {
        return firstFile;
    }

    public void setFirstFile(String firstFile) {
        this.firstFile = firstFile;
    }
}
