package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.microPlatform.domain.ProvidentFundRequest;
import com.ruoyi.microPlatform.service.IProvidentFundService;
import com.ruoyi.microPlatform.util.HttpClientUtil;
import org.springframework.stereotype.Service;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class ProvidentFundServiceImpl implements IProvidentFundService {

    /**
     * 查询公积金通用接口
     * @param providentFundRequest 参数
     * @return
     */
    @Override
    public  Map<String, String> getUnitBusinessDetails(ProvidentFundRequest providentFundRequest) {
        try {

            String apiName = providentFundRequest.getApiName();
            if (ObjectUtil.isEmpty(providentFundRequest.getParams())){
                throw new RuntimeException("参数不可为空");
            }

            // 1. 构建请求参数（1PMZ004 接口 + zzjgdm 参数）
            Map<String, String> params = providentFundRequest.getParams();
            List<Map<String, String>> paramList = buildParam(apiName, params);

            // 请求 URL
//            String url = "http://*************:8081/api/name3/"+apiName;
            String url = "http://*************:8083/api/name3/"+apiName;

            // 2. 发起 POST 请求
            AjaxResult response = HttpClientUtil.post(url, paramList);

            if (!response.isSuccess()) {
                throw new RuntimeException("调用失败:" + response.get("msg"));
            }

            // 3. 获取返回 XML 字符串
            String xmlString = String.valueOf(response.get("data"));

            // 4. 从返回结果中提取 <data> 节点内容
            String dataXml = extractDataNode(xmlString); // 使用工具类解析 XML

            // 5. 将 <data> XML 转为 JSON 返回给前端
            Map<String, String> dataMap = xmlToMap(dataXml);

            return dataMap;
        }catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("调用失败");
        }
    }




    /**
     * 提取返回 XML 中的 <data> 节点
     */
    public static String extractDataNode(String xml) throws Exception {
        Document document = DocumentHelper.parseText(xml);
        Element root = document.getRootElement();
        // soap:Envelope -> soap:Body -> ns2:doTraderResponse -> return -> data
        Element body = root.element("Body");
        Element response = body.elements().get(0); // 第一个子节点 ns2:doTraderResponse
        Element returnElement = response.element("return");
        Element data = returnElement.element("data");

        return data.asXML();
    }

    /**
     * 将 <data> XML 节点内容转换为 Map（用于转 JSON）
     */
    public static Map<String, String> xmlToMap(String dataXml) throws Exception {
        Document document = DocumentHelper.parseText(dataXml);
        Element root = document.getRootElement();

        Map<String, String> map = new HashMap<>();
        for (Iterator<Element> it = root.elementIterator(); it.hasNext(); ) {
            Element e = it.next();
            map.put(e.getName(), e.getTextTrim());
        }

        return map;
    }




    private static final String TXCHANNEL = "10";
    private static final String FORGCODE = "64ce2e4ecfb34a5e1235";
    private static final String TORGCODE = "5030gjj";
    private static final String CERTCODE = "afb0c14873a525bc3315";


    /**
     * 构造接口调用参数，支持多个动态字段
     * @param txcode 接口编号（如 1PMZ004）
     * @param dynamicFields 动态字段键值对（如 zzjgdm->xxx, zjhm->xxx）
     * @return 用于 POST 请求体的参数列表
     */
    public List<Map<String, String>> buildParam(String txcode, Map<String, String> dynamicFields) {
        if (txcode == null || dynamicFields == null || dynamicFields.isEmpty()) {
            throw new IllegalArgumentException("txcode 和 dynamicFields 不能为空");
        }
        String txdate = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String txtime = new SimpleDateFormat("HHmmss").format(new Date());

        StringBuilder xml = new StringBuilder();
        xml.append("<data>");
        xml.append("<txcode>").append(txcode).append("</txcode>");
        xml.append("<txdate>").append("20180910").append("</txdate>");
        xml.append("<txtime>").append("161120").append("</txtime>");
        xml.append("<txchannel>").append(TXCHANNEL).append("</txchannel>");
        xml.append("<forgcode>").append(FORGCODE).append("</forgcode>");
        xml.append("<torgcode>").append(TORGCODE).append("</torgcode>");
        xml.append("<certcode>").append(CERTCODE).append("</certcode>");
        xml.append("<reqident>").append("nt_ident").append("1538982819841").append("</reqident>");

        // 添加动态字段
        for (Map.Entry<String, String> entry : dynamicFields.entrySet()) {

            String key = entry.getKey().toLowerCase(); // 字段名转小写
            String value = entry.getValue();
            xml.append("<").append(key).append(">")
                    .append(value)
                    .append("</").append(key).append(">");
        }

        xml.append("</data>");

        String paramValue = "<![CDATA[" + xml + "]]>";

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("paramName", "paramXml");
        paramMap.put("paramValue", paramValue);

        List<Map<String, String>> paramList = new ArrayList<>();
        paramList.add(paramMap);
        return paramList;
    }

}
