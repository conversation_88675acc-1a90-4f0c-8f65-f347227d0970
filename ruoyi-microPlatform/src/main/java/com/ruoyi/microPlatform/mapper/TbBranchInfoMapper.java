package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbBranchInfo;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;

/**
 * 机关企事业单位（支部）Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface TbBranchInfoMapper 
{
    /**
     * 查询机关企事业单位（支部）
     * 
     * @param id 机关企事业单位（支部）主键
     * @return 机关企事业单位（支部）
     */
    public TbBranchInfo selectTbBranchInfoById(Long id);

    /**
     * 校验机关企事业单位（支部）是否唯一
     *
     * @param tbBranchInfo
     * @return 机关企事业单位（支部）
     */
    public TbBranchInfo checkTbBranchInfoUnique(TbBranchInfo tbBranchInfo);

    /**
     * 查询机关企事业单位（支部）列表
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 机关企事业单位（支部）集合
     */
    public List<TbBranchInfo> selectTbBranchInfoList(TbBranchInfo tbBranchInfo);

    /**
     * 新增机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    public int insertTbBranchInfo(TbBranchInfo tbBranchInfo);

    /**
     * 修改机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    public int updateTbBranchInfo(TbBranchInfo tbBranchInfo);

    /**
     * 删除机关企事业单位（支部）
     * 
     * @param id 机关企事业单位（支部）主键
     * @return 结果
     */
    public int deleteTbBranchInfoById(Long id);

    /**
     * 批量删除机关企事业单位（支部）
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbBranchInfoByIds(Long[] ids);

    /**
     * 批量删除支部关联网格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbBranchGridInfoByBranchIds(Long[] ids);
    
    /**
     * 批量新增支部关联网格
     * 
     * @param tbBranchGridInfoList 支部关联网格列表
     * @return 结果
     */
    public int batchTbBranchGridInfo(List<TbBranchGridInfo> tbBranchGridInfoList);
    

    /**
     * 通过机关企事业单位（支部）主键删除支部关联网格信息
     * 
     * @param id 机关企事业单位（支部）ID
     * @return 结果
     */
    public int deleteTbBranchGridInfoByBranchId(TbBranchGridInfo tbBranchGridInfo);

    List<TbBranchInfo> selectTbBranchInfoListByGrid(TbBranchInfo tbBranchInfo);

}
