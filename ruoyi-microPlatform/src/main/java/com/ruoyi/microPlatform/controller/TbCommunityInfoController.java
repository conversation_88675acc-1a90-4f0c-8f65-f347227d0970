package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbCommunityInfo;
import com.ruoyi.microPlatform.service.ITbCommunityInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/communityInfo")
public class TbCommunityInfoController extends BaseController {
    @Autowired
    private ITbCommunityInfoService tbCommunityInfoService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询小区信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbCommunityInfo tbCommunityInfo) {
        startPage();
        handle(tbCommunityInfo);
        List<TbCommunityInfo> list = tbCommunityInfoService.selectTbCommunityInfoList(tbCommunityInfo);
        return getDataTable(list);
    }

    public void handle(TbCommunityInfo tbCommunityInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbCommunityInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // // 只能查看相应部门下的小区
                if (ObjectUtil.isEmpty(tbCommunityInfo.getDeptId()) && ObjectUtil.isEmpty(tbCommunityInfo.getGridId())) {
                    tbCommunityInfo.setDeptId(getDataDeptId());
                }
            }
        }
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(TbCommunityInfo tbCommunityInfo) {
        handle(tbCommunityInfo);
        List<TbCommunityInfo> list = tbCommunityInfoService.selectTbCommunityInfoList(tbCommunityInfo);
        return success(list);
    }

    /**
     * 导出小区信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:export')")
    @Log(title = "小区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbCommunityInfo tbCommunityInfo) {
        if (ObjectUtil.isNotEmpty(tbCommunityInfo.getIsExportAll()) && tbCommunityInfo.getIsExportAll() == 0){
            startPage(pageNum, pageSize);
        }
        handle(tbCommunityInfo);
        List<TbCommunityInfo> list = tbCommunityInfoService.selectTbCommunityInfoList(tbCommunityInfo);
        ExcelUtil<TbCommunityInfo> util = new ExcelUtil<TbCommunityInfo>(TbCommunityInfo.class);
        util.exportExcel(response, list, "小区信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbCommunityInfo tbCommunityInfo) {
        if (ObjectUtil.isNotEmpty(tbCommunityInfo.getIsExportAll()) && tbCommunityInfo.getIsExportAll() == 0){
            startPage();
        }
        handle(tbCommunityInfo);
        Integer integer = tbCommunityInfoService.selectTbCommunityInfoCount(tbCommunityInfo);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbCommunityInfo);
    }

    @Log(title = "小区信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbCommunityInfo> util = new ExcelUtil<TbCommunityInfo>(TbCommunityInfo.class);
        List<TbCommunityInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbCommunityInfoService.importTbCommunityInfo(infos, updateSupport, operName, userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbCommunityInfo> util = new ExcelUtil<TbCommunityInfo>(TbCommunityInfo.class);
        util.exportTemplateExcel(response, "小区信息数据");
    }

    /**
     * 获取小区信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbCommunityInfoService.selectTbCommunityInfoById(id));
    }

    public void getDeptInfo(TbCommunityInfo tbCommunityInfo) {
        if (ObjectUtil.isEmpty(tbCommunityInfo.getDeptId())) {
            tbCommunityInfo.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbCommunityInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbCommunityInfo.setCounty(split[1]);
            if (split.length >= 3) {
                tbCommunityInfo.setCountry(split[2]);
                if (split.length >= 4) {
                    tbCommunityInfo.setTown(split[3]);
                }
            }
        }

        if (StringUtils.isNotBlank(tbCommunityInfo.getGridName()) && NumberUtil.isNumber(tbCommunityInfo.getGridName())){
            tbCommunityInfo.setCommunitySort(Integer.parseInt(tbCommunityInfo.getGridName()));
        }
    }

    /**
     * 新增小区信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:add')")
    @Log(title = "小区信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbCommunityInfo tbCommunityInfo) {
        tbCommunityInfo.setCreateBy(getUsername());
        if (ObjectUtil.isEmpty(tbCommunityInfo.getGridId())) {
            return error("所属网格不可为空！");
        }
        getDeptInfo(tbCommunityInfo);
        if (tbCommunityInfoService.checkTbCommunityInfo(tbCommunityInfo)) {
            return error("数据已存在！");
        }
        return toAjax(tbCommunityInfoService.insertTbCommunityInfo(tbCommunityInfo));
    }

    /**
     * 修改小区信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:edit')")
    @Log(title = "小区信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbCommunityInfo tbCommunityInfo) {
        tbCommunityInfo.setUpdateBy(getUsername());
        getDeptInfo(tbCommunityInfo);
        if (tbCommunityInfoService.checkTbCommunityInfo(tbCommunityInfo)) {
            return error("数据已存在！");
        }
        return toAjax(tbCommunityInfoService.updateTbCommunityInfo(tbCommunityInfo));
    }

    /**
     * 删除小区信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:communityInfo:remove')")
    @Log(title = "小区信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbCommunityInfoService.deleteTbCommunityInfoByIds(ids));
    }


}
