package com.ruoyi.microPlatform.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.service.ILargeScreenService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 大屏展示controller
 */
@RestController
@RequestMapping("/large/screen")
public class LargeScreenController extends BaseController {

    @Autowired
    private ILargeScreenService largeScreenService;

    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    @Qualifier("largeScreenExecutor")
    private ExecutorService executor;


    /**
     * 大屏数据聚合接口
     */
    @GetMapping("/overview")
    public AjaxResult overview(LargeScreenInfo info) {
        handle(info); // 权限控制

        // 异步任务：分别查询各个模块的数据，出现异常时记录日志并返回默认值
        CompletableFuture<Map<String, Integer>> labelRankFuture =
                safeAsync("人员标签类型排名labelRank", () -> largeScreenService.getLableRank(info), executor, Collections.emptyMap());

        CompletableFuture<Map<String, Integer>> communityFunctionFuture =
                safeAsync("社区功能室统计communityFunction", () -> largeScreenService.getCommunityFunctionCount(info), executor, Collections.emptyMap());

        CompletableFuture<Map<String, Integer>> jurisdictionFuture =
                safeAsync("辖区下各类实体数量统计jurisdiction", () -> largeScreenService.getStatisticsByJurisdiction(info), executor, Collections.emptyMap());

        CompletableFuture<List<?>> jurisdictionListFuture =
                safeAsync("辖区下具体实体列表jurisdictionList", () -> largeScreenService.getListByJurisdiction(info), executor, Collections.emptyList());

        CompletableFuture<Map<String, Map<String, Integer>>> patrolVisitFuture =
                safeAsync("巡查走访情况patrolVisit", () -> largeScreenService.getpatrolVisitSituation(info), executor, Collections.emptyMap());

        CompletableFuture<Map<String, Integer>> whistleFuture =
                safeAsync("居民吹哨residentWhistle", () -> largeScreenService.getResidentWhistleCount(info), executor, Collections.emptyMap());

        CompletableFuture<Map<String, Integer>> postTypeFuture =
                safeAsync("干部岗位类型postType", () -> largeScreenService.getDeptUserPostType(info), executor, Collections.emptyMap());

        CompletableFuture<Map<String, Integer>> merchantTypeFuture =
                safeAsync("门店行业类型merchantType", () -> largeScreenService.getMerchantInfoType(info), executor, Collections.emptyMap());

        // 等待所有异步任务完成
        CompletableFuture.allOf(
                labelRankFuture, communityFunctionFuture, jurisdictionFuture,
                jurisdictionListFuture, patrolVisitFuture,
                whistleFuture, postTypeFuture, merchantTypeFuture
        ).join();

        // 聚合所有结果到 DTO 对象
        LargeScreenOverviewDTO dto = new LargeScreenOverviewDTO();
        dto.setLabelRank(cleanMap(labelRankFuture.join()));
        dto.setCommunityFunction(cleanMap(communityFunctionFuture.join()));
        dto.setJurisdiction(cleanMap(jurisdictionFuture.join()));
        dto.setJurisdictionList(jurisdictionListFuture.join());   // List，不处理
        dto.setPatrolVisit(cleanNestedMap(patrolVisitFuture.join()));
        dto.setResidentWhistle(cleanMap(whistleFuture.join()));
        dto.setPostType(cleanMap(postTypeFuture.join()));
        dto.setMerchantType(cleanMap(merchantTypeFuture.join()));
        return success(dto);
    }

    /**
     * 处理 Map<String, Integer> 类型字段
     * @param map
     * @return
     */
    private Map<String, Integer> cleanMap(Map<String, Integer> map) {
        if (map == null) return Collections.emptyMap();
        return map.entrySet().stream()
                .filter(entry -> entry.getKey() != null) // 去除 key 为 null
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue() == null ? 0 : e.getValue(), // value 为 null 转为 0
                        (a, b) -> b,
                        LinkedHashMap::new
                ));
    }
    /**
     * 处理 Map<String, Map<String, Integer>> 类型字段
     */
    private Map<String, Map<String, Integer>> cleanNestedMap(Map<String, Map<String, Integer>> map) {
        if (map == null) return Collections.emptyMap();

        Map<String, Map<String, Integer>> cleaned = new LinkedHashMap<>();
        for (Map.Entry<String, Map<String, Integer>> outerEntry : map.entrySet()) {
            String outerKey = outerEntry.getKey();
            if (outerKey == null) continue;

            Map<String, Integer> innerMap = outerEntry.getValue();
            if (innerMap == null) {
                cleaned.put(outerKey, Collections.emptyMap());
                continue;
            }

            Map<String, Integer> cleanedInner = innerMap.entrySet().stream()
                    .filter(e -> e.getKey() != null)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> e.getValue() == null ? 0 : e.getValue(),
                            (a, b) -> b,
                            LinkedHashMap::new
                    ));
            cleaned.put(outerKey, cleanedInner);
        }
        return cleaned;
    }


    /**
     * 人员标签类型排名
     */
    @GetMapping("/lableRank")
    public AjaxResult labelRank(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String, Integer> map =  largeScreenService.getLableRank(largeScreenInfo);
        return success(map);
    }



    /**
     * 社区功能室统计
     */
    @GetMapping("/communityFunction")
    public AjaxResult communityFunctionCount(LargeScreenInfo largeScreenInfo){
        if (largeScreenInfo.getGridId() != null){
            SysDept sysDept = sysDeptMapper.selectByGridId(largeScreenInfo.getGridId());
            largeScreenInfo.setDeptId(sysDept.getParentId());
        }
        handle(largeScreenInfo);
        Map<String, Integer> map =  largeScreenService.getCommunityFunctionCount(largeScreenInfo);
        return success(map);
    }

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 数量
     */
    @GetMapping("/jurisdiction")
    public AjaxResult getStatisticsByJurisdiction(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);

        Map<String, Integer> map =  largeScreenService.getStatisticsByJurisdiction(largeScreenInfo);
        return success(map);
    }


    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 。空间区域经纬度
     */
    @GetMapping("/jurisdictionSpace")
    public AjaxResult getSpaceByJurisdiction(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        List<?> map =  largeScreenService.getSpaceByJurisdiction(largeScreenInfo);
        return success(map);
    }

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 具体数据
     */
    @GetMapping("/jurisdictionList")
    public AjaxResult getListByJurisdiction(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        List<?> list = largeScreenService.getListByJurisdiction(largeScreenInfo);
        return success(list);
    }

    /**
     * 巡查走访情况
     * @param largeScreenInfo
     */
    @GetMapping("/patrolVisit")
    public AjaxResult getpatrolVisitSituation(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String,Map<String,Integer>> map = largeScreenService.getpatrolVisitSituation(largeScreenInfo);
        return success(map);
    }

    /**
     * 居民吹哨
     */
    @GetMapping("/residentWhistle")
    public AjaxResult getResidentWhistleCount(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String,Integer> map = largeScreenService.getResidentWhistleCount(largeScreenInfo);
        return success(map);
    }


    /**
     * 干部岗位类型
     */
    @GetMapping("/postType")
    public AjaxResult getDeptUserPostType(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String,Integer> map = largeScreenService.getDeptUserPostType(largeScreenInfo);
        return success(map);
    }

    /**
     * 门店行业类型
     */
    @GetMapping("/merchantType")
    public AjaxResult getMerchantInfoType(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String,Integer> map = largeScreenService.getMerchantInfoType(largeScreenInfo);
        return success(map);
    }

    /**
     * 矛盾纠纷 已化解数 未化解数 总数
     *      已化解 包含：公安已化解 、综治已化解
            未化解 包含：除了 已化解的
     */
    @GetMapping("/issueCount")
    public AjaxResult getIssueCount(LargeScreenInfo largeScreenInfo){
        handle(largeScreenInfo);
        Map<String,Integer> map = largeScreenService.getIssueCount(largeScreenInfo);
        return success(map);
    }

    /**
     *  根据网格编码获得 所属村/社区    网格范围   住户家庭数   人员数    门店商户数
     * @return
     */
    @GetMapping("/gridUnderInfo")
    public AjaxResult getGridUnderInfo(LargeScreenInfo largeScreenInfo){
        return success(largeScreenService.getGridUnderInfo(largeScreenInfo));
    }

    /**
     *  根据小区id获得 小区名称   所属网格    面积   住户家庭数   住户成员数
     * @return
     */
    @GetMapping("/communityUnderInfo")
    public AjaxResult getCommunityUnderInfo(LargeScreenInfo largeScreenInfo){
        return success(largeScreenService.getCommunityUnderInfo(largeScreenInfo));
    }



    /**
     * 权限控制逻辑：处理非管理员用户的数据范围
     */
    public void handle(LargeScreenInfo largeScreenInfo) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                largeScreenInfo.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的网格
                if (ObjectUtil.isEmpty(largeScreenInfo.getDeptId())) {
                    largeScreenInfo.setDeptId(getDataDeptId());
                }else {
                    if (largeScreenInfo.getDeptId().longValue() == 1){
                        largeScreenInfo.setDeptId(null);
                    }
                }
            }
        }

        if (ObjectUtil.isNotEmpty(largeScreenInfo.getDeptId())){
            getDeptInfo1(largeScreenInfo);
            largeScreenInfo.setDeptId(null);
        }
    }

    public void getDeptInfo1(LargeScreenInfo largeScreenInfo) {
        SysDept dept = getDept(largeScreenInfo.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            largeScreenInfo.setCounty(split[1]);
            if (split.length >= 3){
                largeScreenInfo.setCountry(split[2]);
                if (split.length >= 4){
                    largeScreenInfo.setTown(split[3]);
                    if (split.length >= 5){
                        largeScreenInfo.setGridName(split[4]);
                    }
                }
            }
        }
    }

    /**
     * 通用安全异步执行器：防止异常导致线程池崩溃，返回默认值
     */
    private <T> CompletableFuture<T> safeAsync(String name, Supplier<T> supplier, Executor executor, T defaultValue) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return supplier.get();
            } catch (Exception e) {
                logger.error("【大屏模块】{} 执行失败：{}", name, e.getMessage(), e);
                return defaultValue;
            }
        }, executor);
    }



}
