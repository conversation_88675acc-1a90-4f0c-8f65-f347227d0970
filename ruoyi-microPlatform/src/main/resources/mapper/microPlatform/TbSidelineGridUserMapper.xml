<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbSidelineGridUserMapper">

    <resultMap type="TbSidelineGridUser" id="TbSidelineGridUserResult">
            <result property="id"    column="id"    />
            <result property="userId"    column="user_id"    />
            <result property="deptId"    column="dept_id"    />
            <result property="type"    column="type"    />
            <result property="name"    column="name"    />
            <result property="idPhoto"    column="id_photo"    />
            <result property="idCard"    column="id_card"    />
            <result property="phone"    column="phone"    />
            <result property="status"    column="status"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
    </resultMap>

        <resultMap id="TbSidelineGridUserTbSidelineGridInfoResult" type="TbSidelineGridUser" extends="TbSidelineGridUserResult">
            <collection property="tbSidelineGridInfoList" notNullColumn="sub_id" javaType="java.util.List" resultMap="TbSidelineGridInfoResult" />
        </resultMap>

        <resultMap type="TbSidelineGridInfo" id="TbSidelineGridInfoResult">
                <result property="id"    column="sub_id"    />
                <result property="sideUserId"    column="sub_side_user_id"    />
                <result property="deptId"    column="sub_dept_id"    />
                <result property="county"    column="sub_county"    />
                <result property="country"    column="sub_country"    />
                <result property="town"    column="sub_town"    />
                <result property="gridName"    column="sub_grid_name"    />
                <result property="gridRange"    column="sub_grid_range"    />

                <result property="gridId"    column="sub_grid_id"    />
        </resultMap>

    <sql id="selectTbSidelineGridUserVo">
        select gu.id, gu.user_id, gu.dept_id, gu.type, gu.name, gu.id_photo, gu.id_card, gu.phone, gu.status,
         gu.create_by, gu.create_time, gu.update_by, gu.update_time from tb_sideline_grid_user gu left join sys_dict_data dd on gu.type = dd.dict_value and dd.dict_type = 'grid_user_type'
    </sql>
    <sql id="queryTbSidelineGridUserVo">
        <where>
            <if test="type != null  and type != ''"> and gu.type = #{type}</if>
            <if test="name != null  and name != ''"> and gu.name like concat('%', #{name}, '%')</if>
            <if test="idPhoto != null  and idPhoto != ''"> and gu.id_photo = #{idPhoto}</if>
            <if test="idCard != null  and idCard != ''"> and gu.id_card = #{idCard}</if>
            <if test="phone != null  and phone != ''"> and gu.phone = #{phone}</if>
            <if test="status != null "> and gu.status = #{status}</if>
            <if test="gridId != null "> and gu.id in (select side_user_id from tb_sideline_grid_info where grid_id = #{gridId})</if>
            <if test="deptId != null ">and gu.id in (select side_user_id from tb_sideline_grid_info where dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_sideline_grid_info.dept_id))</if>
        </where>
    </sql>

    <select id="selectTbSidelineGridUserList" parameterType="TbSidelineGridUser" resultMap="TbSidelineGridUserResult">
        <include refid="selectTbSidelineGridUserVo"/>
        <include refid="queryTbSidelineGridUserVo"/>
        order by dd.dict_sort asc, gu.id desc
    </select>

    <select id="selectTbSidelineGridUserCount" parameterType="TbSidelineGridUser" resultMap="TbSidelineGridUserResult">
        select id from tb_sideline_grid_user
        <include refid="queryTbSidelineGridUserVo"/>
    </select>

    <select id="selectTbSidelineGridUserById" parameterType="Long" resultMap="TbSidelineGridUserTbSidelineGridInfoResult">
            select a.id, a.user_id, a.dept_id, a.type, a.name, a.id_photo, a.id_card, a.phone, a.status, a.create_by, a.create_time, a.update_by, a.update_time,
 b.id as sub_id, b.side_user_id as sub_side_user_id,b.dept_id as sub_dept_id, b.county as sub_county, b.country as sub_country, b.town as sub_town, b.grid_name as sub_grid_name, b.grid_id as sub_grid_id, b.grid_range as sub_grid_range
            from tb_sideline_grid_user a
            left join tb_sideline_grid_info b on b.side_user_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkTbSidelineGridUserUnique" parameterType="TbSidelineGridUser" resultMap="TbSidelineGridUserResult">
        select id,user_id from tb_sideline_grid_user
        <where>
            status = 0
            <if test="id != null">
                and id != #{id}
            </if>
            <if test="name != null">
                and name = #{name}
            </if>
            <if test="phone != null">
                and phone = #{phone}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
        </where>
        limit 1
    </select>

    <insert id="insertTbSidelineGridUser" parameterType="TbSidelineGridUser" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sideline_grid_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="userId != null">user_id,</if>
                    <if test="deptId != null">dept_id,</if>
                    <if test="type != null">type,</if>
                    <if test="name != null">name,</if>
                    <if test="idPhoto != null">id_photo,</if>
                    <if test="idCard != null">id_card,</if>
                    <if test="phone != null">phone,</if>
                    <if test="status != null">status,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="userId != null">#{userId},</if>
                    <if test="deptId != null">#{deptId},</if>
                    <if test="type != null">#{type},</if>
                    <if test="name != null">#{name},</if>
                    <if test="idPhoto != null">#{idPhoto},</if>
                    <if test="idCard != null">#{idCard},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="status != null">#{status},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbSidelineGridUser" parameterType="TbSidelineGridUser">
        update tb_sideline_grid_user
        <trim prefix="SET" suffixOverrides=",">
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="idPhoto != null">id_photo = #{idPhoto},</if>
                    <if test="idCard != null">id_card = #{idCard},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbSidelineGridUserById" parameterType="Long">
        delete from tb_sideline_grid_user where id = #{id}
    </delete>

    <delete id="deleteTbSidelineGridUserByIds" parameterType="String">
        delete from tb_sideline_grid_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

        <delete id="deleteTbSidelineGridInfoBySideUserIds" parameterType="String">
            delete from tb_sideline_grid_info where side_user_id in
            <foreach item="sideUserId" collection="array" open="(" separator="," close=")">
                #{sideUserId}
            </foreach>
        </delete>

        <delete id="deleteTbSidelineGridInfoBySideUserId" parameterType="com.ruoyi.microPlatform.domain.TbSidelineGridInfo">
        delete from tb_sideline_grid_info where side_user_id = #{sideUserId} and grid_id = #{gridId}
        </delete>

        <insert id="batchTbSidelineGridInfo">
            insert into tb_sideline_grid_info( id, side_user_id, county, country, town, grid_name, grid_id, grid_range) values
            <foreach item="item" index="index" collection="list" separator=",">
                ( #{item.id}, #{item.sideUserId}, #{item.county}, #{item.country}, #{item.town}, #{item.gridName}, #{item.gridId}, #{item.gridRange})
            </foreach>
        </insert>

    <select id="selectTbSidelineGridUserList2" resultMap="TbSidelineGridUserResult">
        select a.id, a.user_id, a.dept_id, a.type, a.name, a.id_photo, a.id_card, a.phone, a.status, a.create_by, a.create_time, a.update_by, a.update_time
        from tb_sideline_grid_user a
        left join tb_sideline_grid_info b on b.side_user_id = a.id
        <where>
            b.grid_id = #{gridId}
            <if test="type != null  and type != ''"> and a.type = #{type}</if>
            <if test="gridId != null "> and a.id in (select side_user_id from tb_sideline_grid_info where grid_id = #{gridId})</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="idPhoto != null  and idPhoto != ''"> and a.id_photo = #{idPhoto}</if>
            <if test="idCard != null  and idCard != ''"> and a.id_card = #{idCard}</if>
            <if test="phone != null  and phone != ''"> and a.phone = #{phone}</if>
            <if test="status != null "> and a.status = #{status}</if>
            <if test="deptId != null ">and a.id in (select side_user_id from tb_sideline_grid_info where dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_sideline_grid_info.dept_id))</if>
        </where>
        order by a.id desc
    </select>

</mapper>