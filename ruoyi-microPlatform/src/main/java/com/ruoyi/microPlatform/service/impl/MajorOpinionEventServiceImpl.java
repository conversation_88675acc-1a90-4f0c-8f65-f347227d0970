package com.ruoyi.microPlatform.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.MajorOpinionEventMapper;
import com.ruoyi.microPlatform.domain.MajorOpinionEvent;
import com.ruoyi.microPlatform.service.IMajorOpinionEventService;

/**
 * 重大舆情信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-10
 */
@Service
public class MajorOpinionEventServiceImpl implements IMajorOpinionEventService 
{
    @Autowired
    private MajorOpinionEventMapper majorOpinionEventMapper;

    /**
     * 查询重大舆情信息
     * 
     * @param id 重大舆情信息主键
     * @return 重大舆情信息
     */
    @Override
    public MajorOpinionEvent selectMajorOpinionEventById(Long id)
    {
        return majorOpinionEventMapper.selectMajorOpinionEventById(id);
    }

    /**
     * 查询重大舆情信息列表
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 重大舆情信息
     */
    @Override
    public List<MajorOpinionEvent> selectMajorOpinionEventList(MajorOpinionEvent majorOpinionEvent)
    {
        return majorOpinionEventMapper.selectMajorOpinionEventList(majorOpinionEvent);
    }

    /**
     * 新增重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    @Override
    public int insertMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent)
    {
        return majorOpinionEventMapper.insertMajorOpinionEvent(majorOpinionEvent);
    }

    /**
     * 修改重大舆情信息
     * 
     * @param majorOpinionEvent 重大舆情信息
     * @return 结果
     */
    @Override
    public int updateMajorOpinionEvent(MajorOpinionEvent majorOpinionEvent)
    {
        return majorOpinionEventMapper.updateMajorOpinionEvent(majorOpinionEvent);
    }

    /**
     * 批量删除重大舆情信息
     * 
     * @param ids 需要删除的重大舆情信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorOpinionEventByIds(Long[] ids)
    {
        return majorOpinionEventMapper.deleteMajorOpinionEventByIds(ids);
    }

    /**
     * 删除重大舆情信息信息
     * 
     * @param id 重大舆情信息主键
     * @return 结果
     */
    @Override
    public int deleteMajorOpinionEventById(Long id)
    {
        return majorOpinionEventMapper.deleteMajorOpinionEventById(id);
    }

    /**
     * 获取最新的舆情事项
     * @param limit 限制数量
     * @return 舆情事项列表
     */
    @Override
    public List<MajorOpinionEvent> selectLatestOpinionEvents(int limit) {
        return majorOpinionEventMapper.selectLatestOpinionEvents(limit);
    }
}
