package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.BizDailySubmission;

import java.util.List;

/**
 * 每日要情 Service 接口
 */
public interface IBizDailySubmissionService {

    /**
     * 根据主键查询
     */
    BizDailySubmission selectBizDailySubmissionById(Long id);

    /**
     * 条件查询列表
     */
    List<BizDailySubmission> selectBizDailySubmissionList(BizDailySubmission bizDailySubmission);

    /**
     * 新增
     */
    int insertBizDailySubmission(BizDailySubmission bizDailySubmission);

    /**
     * 修改
     */
    int updateBizDailySubmission(BizDailySubmission bizDailySubmission);

    /**
     * 批量删除
     */
    int deleteBizDailySubmissionByIds(Long[] ids);

    /**
     * 根据主键删除
     */
    int deleteBizDailySubmissionById(Long id);
}
