package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;

/**
 * 兼职网格员所属网格信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ITbSidelineGridInfoService 
{
    /**
     * 查询兼职网格员所属网格信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 兼职网格员所属网格信息
     */
    public TbSidelineGridInfo selectTbSidelineGridInfoById(Long id);

    /**
     * 校验兼职网格员所属网格信息是否存在
     *
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 兼职网格员所属网格信息
     */
    public boolean checkTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 查询兼职网格员所属网格信息列表
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 兼职网格员所属网格信息集合
     */
    public List<TbSidelineGridInfo> selectTbSidelineGridInfoList(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 导入兼职网格员所属网格信息
     *
     * @param infos       兼职网格员所属网格信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSidelineGridInfo(List<TbSidelineGridInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    public int insertTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 修改兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    public int updateTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 批量删除兼职网格员所属网格信息
     * 
     * @param ids 需要删除的兼职网格员所属网格信息主键集合
     * @return 结果
     */
    public int deleteTbSidelineGridInfoByIds(Long[] ids);

    /**
     * 删除兼职网格员所属网格信息信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 结果
     */
    public int deleteTbSidelineGridInfoById(Long id);
}
