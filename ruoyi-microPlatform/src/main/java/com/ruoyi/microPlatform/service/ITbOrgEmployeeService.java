package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbOrgEmployee;

/**
 * 组织情况员工信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ITbOrgEmployeeService 
{
    /**
     * 查询组织情况员工信息
     * 
     * @param id 组织情况员工信息主键
     * @return 组织情况员工信息
     */
    public TbOrgEmployee selectTbOrgEmployeeById(Long id);

    /**
     * 校验组织情况员工信息是否存在
     *
     * @param tbOrgEmployee 组织情况员工信息
     * @return 组织情况员工信息
     */
    public boolean checkTbOrgEmployee(TbOrgEmployee tbOrgEmployee);

    /**
     * 查询组织情况员工信息列表
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 组织情况员工信息集合
     */
    public List<TbOrgEmployee> selectTbOrgEmployeeList(TbOrgEmployee tbOrgEmployee);

    /**
     * 导入组织情况员工信息
     *
     * @param infos       组织情况员工信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbOrgEmployee(List<TbOrgEmployee> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    public int insertTbOrgEmployee(TbOrgEmployee tbOrgEmployee);

    /**
     * 修改组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    public int updateTbOrgEmployee(TbOrgEmployee tbOrgEmployee);

    /**
     * 批量删除组织情况员工信息
     * 
     * @param ids 需要删除的组织情况员工信息主键集合
     * @return 结果
     */
    public int deleteTbOrgEmployeeByIds(Long[] ids);

    /**
     * 删除组织情况员工信息信息
     * 
     * @param id 组织情况员工信息主键
     * @return 结果
     */
    public int deleteTbOrgEmployeeById(Long id);
}
