package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeSubsequentInfo;

/**
 * 后续动态信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeSubsequentInfoService 
{
    /**
     * 查询后续动态信息
     * 
     * @param id 后续动态信息主键
     * @return 后续动态信息
     */
    public SeSubsequentInfo selectSeSubsequentInfoById(Long id);

    /**
     * 校验后续动态信息是否存在
     *
     * @param seSubsequentInfo 后续动态信息
     * @return 后续动态信息
     */
    public boolean checkSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo);

    /**
     * 查询后续动态信息列表
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 后续动态信息集合
     */
    public List<SeSubsequentInfo> selectSeSubsequentInfoList(SeSubsequentInfo seSubsequentInfo);

    /**
     * 导入后续动态信息
     *
     * @param infos       后续动态信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeSubsequentInfo(List<SeSubsequentInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增后续动态信息
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    public int insertSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo, Long gridId);

    /**
     * 修改后续动态信息
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    public int updateSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo);

    /**
     * 批量删除后续动态信息
     * 
     * @param ids 需要删除的后续动态信息主键集合
     * @return 结果
     */
    public int deleteSeSubsequentInfoByIds(Long[] ids);

    /**
     * 删除后续动态信息信息
     * 
     * @param id 后续动态信息主键
     * @return 结果
     */
    public int deleteSeSubsequentInfoById(Long id);

    /**
     * 查询后续动态信息列表,当某条数据有 parent_id时，则找到被回复的数据，放到当前数据中
     */
    List<SeSubsequentInfo> selectSeSubsequentInfoListParent(SeSubsequentInfo seSubsequentInfo) throws CloneNotSupportedException;

    /**
     * 查询后续动态信息列表数量 根据form_id和form_type
     */
    Map<String,Integer> selectSeSubsequentInfoListSize(SeSubsequentInfo seSubsequentInfo);

}
