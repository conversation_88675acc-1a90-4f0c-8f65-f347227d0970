package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbResidentMember;

/**
 * 居民住户成员信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbResidentMemberService 
{
    /**
     * 查询居民住户成员信息
     * 
     * @param id 居民住户成员信息主键
     * @return 居民住户成员信息
     */
    public TbResidentMember selectTbResidentMemberById(Long id);

    /**
     * 校验居民住户成员信息是否存在
     *
     * @param tbResidentMember 居民住户成员信息
     * @return 居民住户成员信息
     */
    public boolean checkTbResidentMember(TbResidentMember tbResidentMember);

    /**
     * 查询居民住户成员信息列表
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 居民住户成员信息集合
     */
    public TableDataInfo selectTbResidentMemberList(TbResidentMember tbResidentMember);

    public Integer selectTbResidentMemberCount(TbResidentMember tbResidentMember);

    /**
     * 导入居民住户成员信息
     *
     * @param infos       居民住户成员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentMember(List<TbResidentMember> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增居民住户成员信息
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    public int insertTbResidentMember(TbResidentMember tbResidentMember);

    /**
     * 修改居民住户成员信息
     * 
     * @param tbResidentMember 居民住户成员信息
     * @return 结果
     */
    public int updateTbResidentMember(TbResidentMember tbResidentMember);

    /**
     * 批量删除居民住户成员信息
     * 
     * @param ids 需要删除的居民住户成员信息主键集合
     * @return 结果
     */
    public int deleteTbResidentMemberByIds(Long[] ids);

    public void updatePersonLabel();

    public void updatePersonAge();

    /**
     * 删除居民住户成员信息信息
     * 
     * @param id 居民住户成员信息主键
     * @return 结果
     */
    public int deleteTbResidentMemberById(Long id);

    List<TbResidentMember>  selectTbResidentMemberListByMainIds(TbResidentMember member);

    /**
     * 重点人群总数
     */
    int keyPopulationsCount(TbResidentMember tbResidentMember);

    List<CommonBaseCount> keyPopulationsCountGroupByType(TbResidentMember tbResidentMember);
}
