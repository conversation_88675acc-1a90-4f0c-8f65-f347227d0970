package com.ruoyi.microPlatform.util;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * HTTP 工具类（封装 RestTemplate 的 POST 调用）
 */
@Component
public class HttpClientUtil {
    // 注入 RestTemplate 实例（通过配置类）
    private static RestTemplate restTemplate;

    @Autowired
    public HttpClientUtil(RestTemplate restTemplate) {
        HttpClientUtil.restTemplate = restTemplate;
    }


    /**
     * 发送 POST 请求，发送参数为 JSON 格式 List<Map<String, String>> 格式
     *
     * @param url        请求地址
     * @param paramList  请求参数（JSON 数组结构）
     * @return AjaxResult 包含响应状态和响应内容
     */
    public static AjaxResult post(String url, List<Map<String, String>> paramList) {
        try {
            // 设置请求头（指定发送 JSON 数据）
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            // 2. 构建表单字段参数（key-value）
            MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
            formData.add("param", JSON.toJSONString(paramList));  // JSON字符串
            formData.add("ipAddr", "*************");    // IP地址  //测试用
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);

            // 发起 POST 请求，返回 ResponseEntity<String> 响应对象
            ResponseEntity<String> response = restTemplate.exchange(
                    url,                      // 请求地址
                    HttpMethod.POST,          // 请求方法 POST
                    requestEntity,            // 请求实体（包含参数与头）
                    String.class              // 返回类型
            );
            // 4. 判断 HTTP 状态码是否为 200
            if (response.getStatusCode() == HttpStatus.OK) {
                // 5. 获取响应体内容
                return AjaxResult.success("调用成功", response.getBody()); // 可按需提取 data 字段
            } else {
                // HTTP 状态不是 200，视为请求失败
                return AjaxResult.error("请求失败，HTTP状态码：" + response.getStatusCode());
            }
        } catch (Exception e) {
            // 捕获异常并返回错误信息
            return AjaxResult.error("HTTP 请求异常：" + e.getMessage());
        }
    }
}
