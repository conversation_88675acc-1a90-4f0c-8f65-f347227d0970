package com.ruoyi.microPlatform.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbResidentContact;
import com.ruoyi.microPlatform.service.ITbResidentContactService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 居民住户入户走访信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridManage/residentContact")
public class TbResidentContactController extends BaseController
{
    @Autowired
    private ITbResidentContactService tbResidentContactService;

    /**
     * 查询居民住户入户走访信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbResidentContact tbResidentContact)
    {
        startPage();
        handle(tbResidentContact);
        List<TbResidentContact> list = tbResidentContactService.selectTbResidentContactList(tbResidentContact);
        return getDataTable(list);
    }

    public void handle(TbResidentContact tbResidentContact){
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isNotEmpty(tbResidentContact.getQueryType())) {
                if (tbResidentContact.getQueryType() == 2) {
                    // 查询个人提交的
                    tbResidentContact.setUserId(getUserId());
                    return;
                } else if (tbResidentContact.getQueryType() == 1) {
                    // 查询全部
                    tbResidentContact.setUserId(null);
                }
            }
            if (ObjectUtil.isEmpty(tbResidentContact.getGridId())) {
                // 网格为空， 查询个人权限内
                Long gridId = SecurityUtils.getGridId();
                if (ObjectUtil.isNotEmpty(gridId)) {
                    tbResidentContact.setGridArr(getLoginUser().getUser().getDrillNo());
                } else {
                    // // 只能查看相应部门下的入户走访数据
                    if (ObjectUtil.isEmpty(tbResidentContact.getDeptId()) && ObjectUtil.isEmpty(tbResidentContact.getResidentId())) {
                        tbResidentContact.setDeptId(getDataDeptId());
                    }
                }
            }
        }
    }

    /**
     * 导出居民住户入户走访信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:export')")
    @Log(title = "居民住户入户走访信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbResidentContact tbResidentContact)
    {
        handle(tbResidentContact);
        List<TbResidentContact> list = tbResidentContactService.selectTbResidentContactList(tbResidentContact);
        ExcelUtil<TbResidentContact> util = new ExcelUtil<TbResidentContact>(TbResidentContact.class);
        util.exportExcel(response, list, "居民住户入户走访信息数据");
    }

    @Log(title = "居民住户入户走访信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbResidentContact> util = new ExcelUtil<TbResidentContact>(TbResidentContact.class);
        List<TbResidentContact> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbResidentContactService.importTbResidentContact(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbResidentContact> util = new ExcelUtil<TbResidentContact>(TbResidentContact.class);
        util.exportTemplateExcel(response, "居民住户入户走访信息数据");
    }

    /**
     * 获取居民住户入户走访信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbResidentContactService.selectTbResidentContactById(id));
    }

    public void getDeptInfo(TbResidentContact tbResidentContact) {
        if (ObjectUtil.isEmpty(tbResidentContact.getDeptId())) {
            tbResidentContact.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbResidentContact.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            tbResidentContact.setCounty(split[1]);
            if (split.length >= 3) {
                tbResidentContact.setCountry(split[2]);
                if (split.length >= 4) {
                    tbResidentContact.setTown(split[3]);
                }
            }
        }
    }

    /**
     * 新增居民住户入户走访信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:add')")
    @Log(title = "居民住户入户走访信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbResidentContact tbResidentContact)
    {
        if (ObjectUtil.isEmpty(tbResidentContact.getResidentId())){
            return error("关联住户家庭不可为空！");
        }
        tbResidentContact.setCreateBy(getNickName());
        tbResidentContact.setUserId(getUserId());
        Long Id = tbResidentContactService.insertTbResidentContact(tbResidentContact);
        AjaxResult ajaxResult = toAjax(1);
        ajaxResult.put("formId",Id);
        return ajaxResult;
    }

    /**
     * 修改居民住户入户走访信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:edit')")
    @Log(title = "居民住户入户走访信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbResidentContact tbResidentContact)
    {
        tbResidentContact.setUpdateBy(getNickName());
        getDeptInfo(tbResidentContact);
        return toAjax(tbResidentContactService.updateTbResidentContact(tbResidentContact));
    }

    /**
     * 删除居民住户入户走访信息
     */
    @PreAuthorize("@ss.hasPermi('gridManage:residentContact:remove')")
    @Log(title = "居民住户入户走访信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbResidentContactService.deleteTbResidentContactByIds(ids));
    }


    /**
     * 查询居民住户入户走访信息 根据resident_id 累计次数 和本月次数
     */
    @GetMapping("/listCount")
    public AjaxResult listCount(TbResidentContact tbResidentContact)
    {

        Map<String,Integer> map = tbResidentContactService.selectTbResidentContactListCount(tbResidentContact);

        return success(map);
    }


    /**
     * 查询居民住户入户走访信息 根据创建者id 查询提交的总次数  和 最新提交的数据日期
     */
    @GetMapping("/submitCount")
    public AjaxResult submitCount(TbResidentContact tbResidentContact)
    {
        tbResidentContact.setUserId(getUserId());
        return success(tbResidentContactService.selectSubmitCount(tbResidentContact));
    }



}
