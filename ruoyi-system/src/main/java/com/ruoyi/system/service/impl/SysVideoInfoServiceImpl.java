package com.ruoyi.system.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysVideoInfoMapper;
import com.ruoyi.common.core.domain.entity.SysVideoInfo;
import com.ruoyi.system.service.ISysVideoInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 视频管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
@Service
public class SysVideoInfoServiceImpl implements ISysVideoInfoService 
{
    @Autowired
    private SysVideoInfoMapper sysVideoInfoMapper;
    @Autowired
    RedisCache redisCache;

    /**
     * 查询视频管理
     * 
     * @param id 视频管理主键
     * @return 视频管理
     */
    @Override
    public SysVideoInfo selectSysVideoInfoById(Long id)
    {
        return sysVideoInfoMapper.selectSysVideoInfoById(id);
    }

    /**
     * 查询视频管理列表
     * 
     * @param sysVideoInfo 视频管理
     * @return 视频管理
     */
    @Override
    public List<SysVideoInfo> selectSysVideoInfoList(SysVideoInfo sysVideoInfo)
    {
        return sysVideoInfoMapper.selectSysVideoInfoList(sysVideoInfo);
    }

    /**
     * 新增视频管理
     * 
     * @param sysVideoInfo 视频管理
     * @return 结果
     */
    @Override
    public int insertSysVideoInfo(SysVideoInfo sysVideoInfo)
    {
        return sysVideoInfoMapper.insertSysVideoInfo(sysVideoInfo);
    }

    /**
     * 修改视频管理
     * 
     * @param sysVideoInfo 视频管理
     * @return 结果
     */
    @Override
    public int updateSysVideoInfo(SysVideoInfo sysVideoInfo)
    {
        return sysVideoInfoMapper.updateSysVideoInfo(sysVideoInfo);
    }

    /**
     * 批量删除视频管理
     * 
     * @param ids 需要删除的视频管理主键
     * @return 结果
     */
    @Override
    public int deleteSysVideoInfoByIds(Long[] ids)
    {
        return sysVideoInfoMapper.deleteSysVideoInfoByIds(ids);
    }

    /**
     * 删除视频管理信息
     * 
     * @param id 视频管理主键
     * @return 结果
     */
    @Override
    public int deleteSysVideoInfoById(Long id)
    {
        return sysVideoInfoMapper.deleteSysVideoInfoById(id);
    }


}
