package com.ruoyi.microPlatform.controller;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbMsgTemplate;
import com.ruoyi.microPlatform.mapper.TbMsgTemplateMapper;
import com.ruoyi.microPlatform.service.WeChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信授权
 */
@Controller
@Slf4j
@RequestMapping("/wx")
public class APIWxController extends BaseController {
    @Autowired
    WeChatService weChatService;
    @Autowired
    private RedisCache redisCache;

    // xv2ovY6feuLuSOjHHII5AmZIHg5J99ACkj8nLkfXTNE   消息加密米亚

    // 与接口配置信息中的Token要一致
    private static String WECHAT_TOKEN = "aasr6323fghijklmnopqrstuvwxyz";

    @RequestMapping("/checkToken")
    public void get(HttpServletRequest request, HttpServletResponse response) throws Exception {

        log.error("========checkToken Controller========= ");

        boolean isGet = request.getMethod().toLowerCase().equals("get");
        PrintWriter print;
        if (isGet) {
            // 微信加密签名
            String signature = request.getParameter("signature");
            // 时间戳
            String timestamp = request.getParameter("timestamp");
            // 随机数
            String nonce = request.getParameter("nonce");
            // 随机字符串
            String echostr = request.getParameter("echostr");

            System.err.println("signature" + signature);
            System.err.println("timestamp" + timestamp);
            System.err.println("nonce" + nonce);
            System.err.println("echostr" + echostr);
            // 通过检验signature对请求进行校验，若校验成功则原样返回echostr，表示接入成功，否则接入失败
            if (signature != null && checkSignature(signature, timestamp, nonce)) {
                try {
                    print = response.getWriter();
                    print.write(echostr);
                    print.flush();
                    log.info("========checkToken success ========= ");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                log.error("========checkToken failed========= ");
            }
        } else {
            log.error("========checkTokenPost");
        }
    }



    // 发送订阅消息
    @GetMapping("/sendSubscribeMessage")
    public AjaxResult sendSubscribeMessage() {
        // 消息内容，与模板对应
        Map<String, Object> data = new HashMap<>();

        data.put("thing2", newMap("您有一条新的居民吹哨信息！"));
        data.put("time3", newMap("2025-04-01 12:00"));
        data.put("thing4", newMap("提示信息"));
        data.put("thing1", newMap("魏武社区"));

        String accessToken = redisCache.getCacheObject("WEIXIN_TOKEN");
        if (StringUtils.isBlank(accessToken)){
            weChatService.getAccess_token();
            accessToken = redisCache.getCacheObject("WEIXIN_TOKEN");
        }
        String url = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=" + accessToken;

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("touser", "oZ9Jh7DebLqw1xVggEaoP8Dakt1E");
        requestBody.put("template_id", "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs");
        requestBody.put("page", "page/index/home");
        requestBody.put("data", data);
        String s = JSON.toJSONString(requestBody);

        // 发送请求
        String body = HttpUtil.createPost(url).body(s).execute().body();

        // 打印结果
        System.out.println("发送结果：" + body);
        return AjaxResult.success(body);
    }

    private static Map<String, Object> newMap(Object o) {
        Map<String, Object> map = new HashMap<>();
        map.put("value", o);
        return map;
    }

    /**
     * 验证签名
     *
     * @param signature
     * @param timestamp
     * @param nonce
     * @return
     */
    public static boolean checkSignature(String signature, String timestamp, String nonce) {
        String[] arr = new String[]{WECHAT_TOKEN, timestamp, nonce};
        // 将token、timestamp、nonce三个参数进行字典序排序
        // Arrays.sort(arr);
        sort(arr);
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < arr.length; i++) {
            content.append(arr[i]);
        }
        MessageDigest md = null;
        String tmpStr = null;

        try {
            md = MessageDigest.getInstance("SHA-1");
            // 将三个参数字符串拼接成一个字符串进行sha1加密
            byte[] digest = md.digest(content.toString().getBytes());
            tmpStr = byteToStr(digest);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        content = null;
        // 将sha1加密后的字符串可与signature对比，标识该请求来源于微信
        return tmpStr != null ? tmpStr.equals(signature.toUpperCase()) : false;
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param byteArray
     * @return
     */
    private static String byteToStr(byte[] byteArray) {
        String strDigest = "";
        for (int i = 0; i < byteArray.length; i++) {
            strDigest += byteToHexStr(byteArray[i]);
        }
        return strDigest;
    }

    /**
     * 将字节转换为十六进制字符串
     *
     * @param mByte
     * @return
     */
    private static String byteToHexStr(byte mByte) {
        char[] Digit = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        char[] tempArr = new char[2];
        tempArr[0] = Digit[(mByte >>> 4) & 0X0F];
        tempArr[1] = Digit[mByte & 0X0F];
        String s = new String(tempArr);
        return s;
    }

    public static void sort(String a[]) {
        for (int i = 0; i < a.length - 1; i++) {
            for (int j = i + 1; j < a.length; j++) {
                if (a[j].compareTo(a[i]) < 0) {
                    String temp = a[i];
                    a[i] = a[j];
                    a[j] = temp;
                }
            }
        }
    }

}

