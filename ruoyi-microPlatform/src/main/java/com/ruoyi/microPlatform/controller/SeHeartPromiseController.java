package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeHeartPromise;
import com.ruoyi.microPlatform.service.ISeHeartPromiseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 心灵有约Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/heartPromise")
public class SeHeartPromiseController extends BaseController
{
    @Autowired
    private ISeHeartPromiseService seHeartPromiseService;

    /**
     * 查询心灵有约列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeHeartPromise seHeartPromise)
    {
        startPage();
        List<SeHeartPromise> list = seHeartPromiseService.selectSeHeartPromiseList(seHeartPromise);
        return getDataTable(list);
    }

    /**
     * 导出心灵有约列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:export')")
    @Log(title = "心灵有约", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeHeartPromise seHeartPromise)
    {
        List<SeHeartPromise> list = seHeartPromiseService.selectSeHeartPromiseList(seHeartPromise);
        ExcelUtil<SeHeartPromise> util = new ExcelUtil<SeHeartPromise>(SeHeartPromise.class);
        util.exportExcel(response, list, "心灵有约数据");
    }

    @Log(title = "心灵有约", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SeHeartPromise> util = new ExcelUtil<SeHeartPromise>(SeHeartPromise.class);
        List<SeHeartPromise> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seHeartPromiseService.importSeHeartPromise(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SeHeartPromise> util = new ExcelUtil<SeHeartPromise>(SeHeartPromise.class);
        util.exportTemplateExcel(response, "心灵有约数据");
    }

    /**
     * 获取心灵有约详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(seHeartPromiseService.selectSeHeartPromiseById(id));
    }

    public void getDeptInfo(SeHeartPromise seHeartPromise){
        if (ObjectUtil.isEmpty(seHeartPromise.getDeptId())){
            seHeartPromise.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(seHeartPromise.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2){
            seHeartPromise.setCounty(split[1]);
            if (split.length >= 3){
                seHeartPromise.setCountry(split[2]);
                if (split.length >= 4){
                    seHeartPromise.setTown(split[3]);
                }
            }
        }
    }
    /**
     * 新增心灵有约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:add')")
    @Log(title = "心灵有约", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeHeartPromise seHeartPromise)
    {
        seHeartPromise.setCreateBy(getUsername());
        if (seHeartPromiseService.checkSeHeartPromise(seHeartPromise)){
            return error("数据已存在！");
        }

        getDeptInfo(seHeartPromise);
        seHeartPromise.setUserId(getUserId());
        return toAjax(seHeartPromiseService.insertSeHeartPromise(seHeartPromise));
    }

    /**
     * 修改心灵有约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:edit')")
    @Log(title = "心灵有约", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeHeartPromise seHeartPromise)
    {
        seHeartPromise.setUpdateBy(getUsername());
        if (seHeartPromiseService.checkSeHeartPromise(seHeartPromise)){
            return error("数据已存在！");
        }
        getDeptInfo(seHeartPromise);
        return toAjax(seHeartPromiseService.updateSeHeartPromise(seHeartPromise));
    }

    /**
     * 删除心灵有约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:heartPromise:remove')")
    @Log(title = "心灵有约", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(seHeartPromiseService.deleteSeHeartPromiseByIds(ids));
    }
}
