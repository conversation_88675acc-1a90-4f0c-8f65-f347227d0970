<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbBpmNodeJumpMapper">

    <resultMap type="DbBpmNodeJump" id="DbBpmNodeJumpResult">
            <result property="id"    column="id"    />
            <result property="procDefId"    column="proc_def_id"    />
            <result property="procInstId"    column="proc_inst_id"    />
            <result property="evtId"    column="evt_id"    />
            <result property="nodeName"    column="node_name"    />
            <result property="actDefId"    column="act_def_id"    />
            <result property="actInstId"    column="act_inst_id"    />
            <result property="fromActInstId"    column="from_act_inst_id"    />
            <result property="arrivalTime"    column="arrival_time"    />
            <result property="completeTime"    column="complete_time"    />
            <result property="extUsedMin"    column="ext_used_min"    />
            <result property="extExcludeMin"    column="ext_exclude_min"    />
            <result property="extRealUsedMin"    column="ext_real_used_min"    />
            <result property="extCumulativeMin"    column="ext_cumulative_min"    />
            <result property="receiveTypeId"    column="receive_type_id"    />
            <result property="ownerId"    column="owner_id"    />
            <result property="ownerName"    column="owner_name"    />
            <result property="handlerId"    column="handler_id"    />
            <result property="handlerName"    column="handler_name"    />
            <result property="deptId"    column="dept_id"    />
            <result property="deptName"    column="dept_name"    />
            <result property="extLimit"    column="ext_limit"    />
            <result property="fastExpirationTime"    column="fast_expiration_time"    />
            <result property="shouldFinishedTime"    column="should_finished_time"    />
            <result property="checkStatus"    column="check_status"    />
            <result property="jumpType"    column="jump_type"    />
            <result property="remark"    column="remark"    />
            <result property="createId"    column="create_id"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateId"    column="update_id"    />
            <result property="updateTime"    column="update_time"    />
            <result property="isTimeout"    column="is_timeout"    />
            <result property="updateContent"    column="update_content"    />
            <result property="isOnAccount"    column="is_on_account"    />
            <result property="dockingStatus"    column="docking_status"    />
            <result property="partitionId"    column="partition_id"    />
    </resultMap>

    <sql id="selectDbBpmNodeJumpVo">
        select id, proc_def_id, proc_inst_id, evt_id, node_name, act_def_id, act_inst_id, from_act_inst_id, arrival_time, complete_time, ext_used_min, ext_exclude_min, ext_real_used_min, ext_cumulative_min, receive_type_id, owner_id, owner_name, handler_id, handler_name, dept_id, dept_name, ext_limit, fast_expiration_time, should_finished_time, check_status, jump_type, remark, create_id, create_time, update_id, update_time, is_timeout, update_content, is_on_account, docking_status, partition_id from db_bpm_node_jump
    </sql>

    <select id="selectDbBpmNodeJumpList" parameterType="DbBpmNodeJump" resultMap="DbBpmNodeJumpResult">
        <include refid="selectDbBpmNodeJumpVo"/>
        <where>
                        <if test="procDefId != null  and procDefId != ''"> and proc_def_id = #{procDefId}</if>
                        <if test="procInstId != null "> and proc_inst_id = #{procInstId}</if>
                        <if test="evtId != null "> and evt_id = #{evtId}</if>
                        <if test="nodeName != null  and nodeName != ''"> and node_name like concat('%', #{nodeName}, '%')</if>
                        <if test="actDefId != null  and actDefId != ''"> and act_def_id = #{actDefId}</if>
                        <if test="actInstId != null "> and act_inst_id = #{actInstId}</if>
                        <if test="fromActInstId != null "> and from_act_inst_id = #{fromActInstId}</if>
                        <if test="arrivalTime != null "> and arrival_time = #{arrivalTime}</if>
                        <if test="completeTime != null "> and complete_time = #{completeTime}</if>
                        <if test="extUsedMin != null "> and ext_used_min = #{extUsedMin}</if>
                        <if test="extExcludeMin != null "> and ext_exclude_min = #{extExcludeMin}</if>
                        <if test="extRealUsedMin != null "> and ext_real_used_min = #{extRealUsedMin}</if>
                        <if test="extCumulativeMin != null "> and ext_cumulative_min = #{extCumulativeMin}</if>
                        <if test="receiveTypeId != null "> and receive_type_id = #{receiveTypeId}</if>
                        <if test="ownerId != null "> and owner_id = #{ownerId}</if>
                        <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
                        <if test="handlerId != null "> and handler_id = #{handlerId}</if>
                        <if test="handlerName != null  and handlerName != ''"> and handler_name like concat('%', #{handlerName}, '%')</if>
                        <if test="deptId != null "> and dept_id = #{deptId}</if>
                        <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
                        <if test="extLimit != null "> and ext_limit = #{extLimit}</if>
                        <if test="fastExpirationTime != null "> and fast_expiration_time = #{fastExpirationTime}</if>
                        <if test="shouldFinishedTime != null "> and should_finished_time = #{shouldFinishedTime}</if>
                        <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
                        <if test="jumpType != null  and jumpType != ''"> and jump_type = #{jumpType}</if>
                        <if test="createId != null "> and create_id = #{createId}</if>
                        <if test="updateId != null "> and update_id = #{updateId}</if>
                        <if test="isTimeout != null "> and is_timeout = #{isTimeout}</if>
                        <if test="updateContent != null  and updateContent != ''"> and update_content = #{updateContent}</if>
                        <if test="isOnAccount != null "> and is_on_account = #{isOnAccount}</if>
                        <if test="dockingStatus != null "> and docking_status = #{dockingStatus}</if>
        </where>
    </select>

    <select id="selectDbBpmNodeJumpById" parameterType="Long" resultMap="DbBpmNodeJumpResult">
            <include refid="selectDbBpmNodeJumpVo"/>
            where id = #{id}
    </select>

    <select id="checkDbBpmNodeJumpUnique" parameterType="DbBpmNodeJump" resultMap="DbBpmNodeJumpResult">
        select id from db_bpm_node_jump where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertDbBpmNodeJump" parameterType="DbBpmNodeJump">
        insert into db_bpm_node_jump
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="procDefId != null">proc_def_id,</if>
                    <if test="procInstId != null">proc_inst_id,</if>
                    <if test="evtId != null">evt_id,</if>
                    <if test="nodeName != null">node_name,</if>
                    <if test="actDefId != null">act_def_id,</if>
                    <if test="actInstId != null">act_inst_id,</if>
                    <if test="fromActInstId != null">from_act_inst_id,</if>
                    <if test="arrivalTime != null">arrival_time,</if>
                    <if test="completeTime != null">complete_time,</if>
                    <if test="extUsedMin != null">ext_used_min,</if>
                    <if test="extExcludeMin != null">ext_exclude_min,</if>
                    <if test="extRealUsedMin != null">ext_real_used_min,</if>
                    <if test="extCumulativeMin != null">ext_cumulative_min,</if>
                    <if test="receiveTypeId != null">receive_type_id,</if>
                    <if test="ownerId != null">owner_id,</if>
                    <if test="ownerName != null">owner_name,</if>
                    <if test="handlerId != null">handler_id,</if>
                    <if test="handlerName != null">handler_name,</if>
                    <if test="deptId != null">dept_id,</if>
                    <if test="deptName != null">dept_name,</if>
                    <if test="extLimit != null">ext_limit,</if>
                    <if test="fastExpirationTime != null">fast_expiration_time,</if>
                    <if test="shouldFinishedTime != null">should_finished_time,</if>
                    <if test="checkStatus != null">check_status,</if>
                    <if test="jumpType != null">jump_type,</if>
                    <if test="remark != null">remark,</if>
                    <if test="createId != null">create_id,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateId != null">update_id,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="isTimeout != null">is_timeout,</if>
                    <if test="updateContent != null">update_content,</if>
                    <if test="isOnAccount != null">is_on_account,</if>
                    <if test="dockingStatus != null">docking_status,</if>
                    <if test="partitionId != null">partition_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="procDefId != null">#{procDefId},</if>
                    <if test="procInstId != null">#{procInstId},</if>
                    <if test="evtId != null">#{evtId},</if>
                    <if test="nodeName != null">#{nodeName},</if>
                    <if test="actDefId != null">#{actDefId},</if>
                    <if test="actInstId != null">#{actInstId},</if>
                    <if test="fromActInstId != null">#{fromActInstId},</if>
                    <if test="arrivalTime != null">#{arrivalTime},</if>
                    <if test="completeTime != null">#{completeTime},</if>
                    <if test="extUsedMin != null">#{extUsedMin},</if>
                    <if test="extExcludeMin != null">#{extExcludeMin},</if>
                    <if test="extRealUsedMin != null">#{extRealUsedMin},</if>
                    <if test="extCumulativeMin != null">#{extCumulativeMin},</if>
                    <if test="receiveTypeId != null">#{receiveTypeId},</if>
                    <if test="ownerId != null">#{ownerId},</if>
                    <if test="ownerName != null">#{ownerName},</if>
                    <if test="handlerId != null">#{handlerId},</if>
                    <if test="handlerName != null">#{handlerName},</if>
                    <if test="deptId != null">#{deptId},</if>
                    <if test="deptName != null">#{deptName},</if>
                    <if test="extLimit != null">#{extLimit},</if>
                    <if test="fastExpirationTime != null">#{fastExpirationTime},</if>
                    <if test="shouldFinishedTime != null">#{shouldFinishedTime},</if>
                    <if test="checkStatus != null">#{checkStatus},</if>
                    <if test="jumpType != null">#{jumpType},</if>
                    <if test="remark != null">#{remark},</if>
                    <if test="createId != null">#{createId},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateId != null">#{updateId},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="isTimeout != null">#{isTimeout},</if>
                    <if test="updateContent != null">#{updateContent},</if>
                    <if test="isOnAccount != null">#{isOnAccount},</if>
                    <if test="dockingStatus != null">#{dockingStatus},</if>
                    <if test="partitionId != null">#{partitionId},</if>
        </trim>
    </insert>

    <update id="updateDbBpmNodeJump" parameterType="DbBpmNodeJump">
        update db_bpm_node_jump
        <trim prefix="SET" suffixOverrides=",">
                    <if test="procDefId != null">proc_def_id = #{procDefId},</if>
                    <if test="procInstId != null">proc_inst_id = #{procInstId},</if>
                    <if test="evtId != null">evt_id = #{evtId},</if>
                    <if test="nodeName != null">node_name = #{nodeName},</if>
                    <if test="actDefId != null">act_def_id = #{actDefId},</if>
                    <if test="actInstId != null">act_inst_id = #{actInstId},</if>
                    <if test="fromActInstId != null">from_act_inst_id = #{fromActInstId},</if>
                    <if test="arrivalTime != null">arrival_time = #{arrivalTime},</if>
                    <if test="completeTime != null">complete_time = #{completeTime},</if>
                    <if test="extUsedMin != null">ext_used_min = #{extUsedMin},</if>
                    <if test="extExcludeMin != null">ext_exclude_min = #{extExcludeMin},</if>
                    <if test="extRealUsedMin != null">ext_real_used_min = #{extRealUsedMin},</if>
                    <if test="extCumulativeMin != null">ext_cumulative_min = #{extCumulativeMin},</if>
                    <if test="receiveTypeId != null">receive_type_id = #{receiveTypeId},</if>
                    <if test="ownerId != null">owner_id = #{ownerId},</if>
                    <if test="ownerName != null">owner_name = #{ownerName},</if>
                    <if test="handlerId != null">handler_id = #{handlerId},</if>
                    <if test="handlerName != null">handler_name = #{handlerName},</if>
                    <if test="deptId != null">dept_id = #{deptId},</if>
                    <if test="deptName != null">dept_name = #{deptName},</if>
                    <if test="extLimit != null">ext_limit = #{extLimit},</if>
                    <if test="fastExpirationTime != null">fast_expiration_time = #{fastExpirationTime},</if>
                    <if test="shouldFinishedTime != null">should_finished_time = #{shouldFinishedTime},</if>
                    <if test="checkStatus != null">check_status = #{checkStatus},</if>
                    <if test="jumpType != null">jump_type = #{jumpType},</if>
                    <if test="remark != null">remark = #{remark},</if>
                    <if test="createId != null">create_id = #{createId},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateId != null">update_id = #{updateId},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="isTimeout != null">is_timeout = #{isTimeout},</if>
                    <if test="updateContent != null">update_content = #{updateContent},</if>
                    <if test="isOnAccount != null">is_on_account = #{isOnAccount},</if>
                    <if test="dockingStatus != null">docking_status = #{dockingStatus},</if>
                    <if test="partitionId != null">partition_id = #{partitionId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbBpmNodeJumpById" parameterType="Long">
        delete from db_bpm_node_jump where id = #{id}
    </delete>

    <delete id="deleteDbBpmNodeJumpByIds" parameterType="String">
        delete from db_bpm_node_jump where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>