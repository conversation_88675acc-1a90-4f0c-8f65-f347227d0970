package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbRequestAudit;

/**
 * 导出请求审核过程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public interface TbRequestAuditMapper 
{
    /**
     * 查询导出请求审核过程
     * 
     * @param id 导出请求审核过程主键
     * @return 导出请求审核过程
     */
    public TbRequestAudit selectTbRequestAuditById(Long id);

    /**
     * 校验导出请求审核过程是否唯一
     *
     * @param tbRequestAudit
     * @return 导出请求审核过程
     */
    public TbRequestAudit checkTbRequestAuditUnique(TbRequestAudit tbRequestAudit);

    /**
     * 查询导出请求审核过程列表
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 导出请求审核过程集合
     */
    public List<TbRequestAudit> selectTbRequestAuditList(TbRequestAudit tbRequestAudit);

    /**
     * 新增导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    public int insertTbRequestAudit(TbRequestAudit tbRequestAudit);

    /**
     * 修改导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    public int updateTbRequestAudit(TbRequestAudit tbRequestAudit);

    /**
     * 删除导出请求审核过程
     * 
     * @param id 导出请求审核过程主键
     * @return 结果
     */
    public int deleteTbRequestAuditById(Long id);

    /**
     * 批量删除导出请求审核过程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbRequestAuditByIds(Long[] ids);
}
