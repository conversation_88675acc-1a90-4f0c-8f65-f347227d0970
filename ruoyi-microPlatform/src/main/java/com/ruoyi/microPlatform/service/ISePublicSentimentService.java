package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.SePublicSentiment;

/**
 * 居民吹哨Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISePublicSentimentService 
{
    /**
     * 查询居民吹哨
     * 
     * @param id 居民吹哨主键
     * @return 居民吹哨
     */
    public SePublicSentiment selectSePublicSentimentById(Long id);

    /**
     * 校验居民吹哨是否存在
     *
     * @param sePublicSentiment 居民吹哨
     * @return 居民吹哨
     */
    public boolean checkSePublicSentiment(SePublicSentiment sePublicSentiment);

    /**
     * 查询居民吹哨列表
     * 
     * @param sePublicSentiment 居民吹哨
     * @return 居民吹哨集合
     */
    public List<SePublicSentiment> selectSePublicSentimentList(SePublicSentiment sePublicSentiment);

    /**
     * 导入居民吹哨
     *
     * @param infos       居民吹哨列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSePublicSentiment(List<SePublicSentiment> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增居民吹哨
     * 
     * @param sePublicSentiment 居民吹哨
     * @return 结果
     */
    public int insertSePublicSentiment(SePublicSentiment sePublicSentiment);

    /**
     * 修改居民吹哨
     * 
     * @param sePublicSentiment 居民吹哨
     * @return 结果
     */
    public int updateSePublicSentiment(SePublicSentiment sePublicSentiment);

    /**
     * 批量删除居民吹哨
     * 
     * @param ids 需要删除的居民吹哨主键集合
     * @return 结果
     */
    public int deleteSePublicSentimentByIds(Long[] ids);

    /**
     * 删除居民吹哨信息
     * 
     * @param id 居民吹哨主键
     * @return 结果
     */
    public int deleteSePublicSentimentById(Long id);

    public Map<String, Object> selectSubmitCount(SePublicSentiment sePublicSentiment);


    Integer getResidentWhistleCountByBigData(LargeScreenInfo largeScreenInfo);
}
