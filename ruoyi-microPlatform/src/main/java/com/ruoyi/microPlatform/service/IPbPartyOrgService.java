package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyOrg;

import java.util.List;

/**
 * 基层党组织管理Service接口
 */
public interface IPbPartyOrgService {
    /** 查询 */
    PbPartyOrg selectPbPartyOrgById(Long id);

    /** 唯一性校验（orgCode） */
    boolean checkPbPartyOrg(PbPartyOrg org);

    /** 列表 */
    List<PbPartyOrg> selectPbPartyOrgList(PbPartyOrg org);

    /** 新增 */
    int insertPbPartyOrg(PbPartyOrg org);

    /** 修改 */
    int updatePbPartyOrg(PbPartyOrg org);

    /** 批量删除 */
    int deletePbPartyOrgByIds(Long[] ids);

    /** 删除 */
    int deletePbPartyOrgById(Long id);

    List<CommonBaseCount> pbPartyOrgGroupOrgLevel();

    List<CommonBaseCount> pbPartyOrgGroupOrgType();
}
