package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbInspectRequire;

/**
 * 门店基础检查项目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbInspectRequireMapper 
{
    /**
     * 查询门店基础检查项目
     * 
     * @param id 门店基础检查项目主键
     * @return 门店基础检查项目
     */
    public TbInspectRequire selectTbInspectRequireById(Integer id);

    /**
     * 校验门店基础检查项目是否唯一
     *
     * @param tbInspectRequire
     * @return 门店基础检查项目
     */
    public TbInspectRequire checkTbInspectRequireUnique(TbInspectRequire tbInspectRequire);

    /**
     * 查询门店基础检查项目列表
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 门店基础检查项目集合
     */
    public List<TbInspectRequire> selectTbInspectRequireList(TbInspectRequire tbInspectRequire);

    /**
     * 新增门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    public int insertTbInspectRequire(TbInspectRequire tbInspectRequire);

    /**
     * 修改门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    public int updateTbInspectRequire(TbInspectRequire tbInspectRequire);

    /**
     * 删除门店基础检查项目
     * 
     * @param id 门店基础检查项目主键
     * @return 结果
     */
    public int deleteTbInspectRequireById(Integer id);

    /**
     * 批量删除门店基础检查项目
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbInspectRequireByIds(Integer[] ids);

    List<TbInspectRequire> selectTbInspectRequireListByType(TbInspectRequire tbInspectRequire);
}
