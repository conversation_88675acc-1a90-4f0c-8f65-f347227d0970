<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbResidentContactMapper">

    <resultMap type="TbResidentContact" id="TbResidentContactResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="residentId" column="resident_id"/>
        <result property="contactDesc" column="contact_desc"/>
        <result property="accessory" column="accessory"/>
        <result property="residentialSituation" column="residential_situation"/>
        <result property="otherSituation" column="other_situation"/>
        <result property="contractSituation" column="contract_situation"/>
        <result property="userId" column="user_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="handleCount" column="handle_count"/>
        <result property="communityName" column="community_name"/>
        <result property="householder" column="householder"/>
        <result property="roomNum" column="room_num"/>
        <result property="isIssue" column="is_issue"/>

        <!-- 一对一映射 -->
        <association property="tbCommunityResident" javaType="TbCommunityResident">
            <id property="communityName" column="community_name"/>
            <result property="buildingNum" column="building_num"/>
            <result property="unitNum" column="unit_num"/>
            <result property="floorNum" column="floor_num"/>
            <result property="householder" column="householder"/>
        </association>
    </resultMap>

    <sql id="selectTbResidentContactVo">
        select id, dept_id, county, country, town, grid_name, grid_id, resident_id, contact_desc, accessory, residential_situation,
         other_situation, contract_situation, user_id, create_by, create_time, update_by, update_time,is_issue from tb_resident_contact
    </sql>

    <select id="selectTbResidentContactList" parameterType="TbResidentContact" resultMap="TbResidentContactResult">
        select rc.id, rc.dept_id, rc.county, rc.country, rc.town, rc.grid_name, rc.grid_id, rc.resident_id, rc.contact_desc, rc.accessory,rc.residential_situation, rc.other_situation, rc.contract_situation,
        rc.user_id, rc.create_by, rc.create_time, rc.update_by, rc.update_time,rc.is_issue,
        cr.community_name,cr.householder,cr.room_num,
        COALESCE(ssi.handle_count, 0) AS handle_count
        from tb_resident_contact rc
        LEFT JOIN (
        SELECT form_id, COUNT(*) AS handle_count
        FROM se_subsequent_info
        WHERE form_type = '入户走访'
        GROUP BY form_id
        ) ssi ON rc.id = ssi.form_id
        left JOIN tb_community_resident cr on cr.id = rc.resident_id
        <where>
            <if test="deptId != null ">and (rc.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = rc.dept_id))</if>
            <if test="county != null  and county != ''">and rc.county = #{county}</if>
            <if test="country != null  and country != ''">and rc.country = #{country}</if>
            <if test="town != null  and town != ''">and rc.town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and rc.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="householder != null  and householder != ''">and cr.householder like concat('%', #{householder}, '%')</if>
            <if test="gridId != null ">and rc.grid_id = #{gridId}</if>
            <if test="roomNum != null and roomNum != ''">and cr.room_num = #{roomNum}</if>
            <if test="communityName != null and communityName != ''">and cr.community_name like concat('%', #{communityName}, '%')</if>
            <if test="gridArr != null  and gridArr != ''">and rc.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="residentId != null ">and rc.resident_id = #{residentId}</if>
            <if test="isCurrentMonth == true ">and DATE_FORMAT(rc.create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')</if>
            <if test="contactDesc != null  and contactDesc != ''">and rc.contact_desc = #{contactDesc}</if>
            <if test="accessory != null  and accessory != ''">and rc.accessory = #{accessory}</if>
            <if test="residentialSituation != null  and residentialSituation != ''">and rc.residential_situation =
                #{residentialSituation}
            </if>
            <if test="otherSituation != null  and otherSituation != ''">and rc.other_situation = #{otherSituation}</if>
            <if test="contractSituation != null  and contractSituation != ''">and rc.contract_situation =
                #{contractSituation}
            </if>
            <if test="userId != null ">and rc.user_id = #{userId}</if>
            <if test="isIssue != null ">and rc.is_issue = #{isIssue}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and rc.create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectTbResidentContactById" parameterType="Long" resultMap="TbResidentContactResult">
        SELECT
            a.id,
            a.dept_id,
            a.county,
            a.country,
            a.town,
            a.grid_name,
            a.grid_id,
            a.resident_id,
            a.contact_desc,
            a.accessory,
            a.residential_situation,
            a.other_situation,
            a.contract_situation,
            a.user_id,
            a.create_by,
            a.create_time,
            a.update_by,
            a.update_time,
            a.is_issue,

            b.community_name,
            b.building_num,
            b.unit_num,
            b.floor_num,
            b.room_num,
            b.householder
        FROM
            tb_resident_contact a
            left join tb_community_resident b on a.resident_id = b.id
        where a.id = #{id}
    </select>

    <select id="checkTbResidentContactUnique" parameterType="TbResidentContact" resultMap="TbResidentContactResult">
        select id from tb_resident_contact where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbResidentContact" parameterType="TbResidentContact" useGeneratedKeys="true" keyProperty="id">
        insert into tb_resident_contact
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="residentId != null">resident_id,</if>
            <if test="contactDesc != null">contact_desc,</if>
            <if test="accessory != null">accessory,</if>
            <if test="residentialSituation != null">residential_situation,</if>
            <if test="otherSituation != null">other_situation,</if>
            <if test="contractSituation != null">contract_situation,</if>
            <if test="userId != null">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isIssue != null">is_issue,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="residentId != null">#{residentId},</if>
            <if test="contactDesc != null">#{contactDesc},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="residentialSituation != null">#{residentialSituation},</if>
            <if test="otherSituation != null">#{otherSituation},</if>
            <if test="contractSituation != null">#{contractSituation},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isIssue != null">#{isIssue},</if>
        </trim>
    </insert>

    <update id="updateTbResidentContact" parameterType="TbResidentContact">
        update tb_resident_contact
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="residentId != null">resident_id = #{residentId},</if>
            <if test="contactDesc != null">contact_desc = #{contactDesc},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="residentialSituation != null">residential_situation = #{residentialSituation},</if>
            <if test="otherSituation != null">other_situation = #{otherSituation},</if>
            <if test="contractSituation != null">contract_situation = #{contractSituation},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isIssue != null">is_issue = #{isIssue},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbResidentContactById" parameterType="Long">
        delete from tb_resident_contact where id = #{id}
    </delete>

    <delete id="deleteTbResidentContactByIds" parameterType="String">
        delete from tb_resident_contact where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTbResidentContactCountById" resultType="int">
        SELECT COUNT(*) AS total_count
        FROM tb_resident_contact
        where resident_id = #{residentId}
    </select>

    <select id="selectTbResidentContactCountByIdTime" resultType="int">
        SELECT COUNT(*) AS current_month_count
        FROM tb_resident_contact
        WHERE resident_id = #{residentId}
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
    </select>

    <select id="selectSubmitCount" resultType="java.util.Map">
        SELECT
            COUNT(*) AS submitCount,
            DATE_FORMAT(MAX(create_time), '%m-%d %H:%i:%s') AS submitTime
        FROM tb_resident_contact
        WHERE user_id = #{userId}
        <if test="residentId != null">
            and resident_id = #{residentId}
        </if>
    </select>

    <select id="selectMonthlyStats" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.util.Map">
        SELECT DATE_FORMAT(create_time, '%Y%m') AS month, COUNT(*) AS total
        FROM tb_resident_contact
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_resident_contact.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>

            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
        GROUP BY month
        ORDER BY month
    </select>


    <select id="selectCount" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="int">
        SELECT COUNT(*) AS total
        FROM tb_resident_contact
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_resident_contact.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>

            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
    </select>
</mapper>