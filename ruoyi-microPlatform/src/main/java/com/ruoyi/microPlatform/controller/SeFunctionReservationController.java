package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeFunctionReservation;
import com.ruoyi.microPlatform.service.ISeFunctionReservationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 开放预约Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/sentiment/functionReservation")
public class SeFunctionReservationController extends BaseController {
    @Autowired
    private ISeFunctionReservationService seFunctionReservationService;

    /**
     * 查询开放预约列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeFunctionReservation seFunctionReservation) {
        startPage();
        handle(seFunctionReservation);
        List<SeFunctionReservation> list = seFunctionReservationService.selectSeFunctionReservationList(seFunctionReservation);
        return getDataTable(list);
    }

    public void handle(SeFunctionReservation seFunctionReservation) {
        // PC端查看
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (SecurityUtils.hasRole("common")) {
                // 普通用户，只查看个人的
                seFunctionReservation.setUserId(getUserId());
            } else {
                if (ObjectUtil.isNotEmpty(seFunctionReservation.getQueryType())) {
                    if (seFunctionReservation.getQueryType() == 2) {
                        // 查询个人提交的
                        seFunctionReservation.setUserId(getUserId());
                        return;
                    } else if (seFunctionReservation.getQueryType() == 1) {
                        // 查询全部
                        seFunctionReservation.setUserId(null);
                    }
                }
                if (ObjectUtil.isEmpty(seFunctionReservation.getDeptId())) {
                    seFunctionReservation.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出开放预约列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:export')")
    @Log(title = "开放预约", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeFunctionReservation seFunctionReservation) {
        handle(seFunctionReservation);
        List<SeFunctionReservation> list = seFunctionReservationService.selectSeFunctionReservationList(seFunctionReservation);
        ExcelUtil<SeFunctionReservation> util = new ExcelUtil<SeFunctionReservation>(SeFunctionReservation.class);
        util.exportExcel(response, list, "开放预约数据");
    }

    @Log(title = "开放预约", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SeFunctionReservation> util = new ExcelUtil<SeFunctionReservation>(SeFunctionReservation.class);
        List<SeFunctionReservation> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seFunctionReservationService.importSeFunctionReservation(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SeFunctionReservation> util = new ExcelUtil<SeFunctionReservation>(SeFunctionReservation.class);
        util.exportTemplateExcel(response, "开放预约数据");
    }

    @GetMapping("/submitCount")
    public AjaxResult submitCount(SeFunctionReservation seFunctionReservation) {
        seFunctionReservation.setUserId(getUserId());
        return success(seFunctionReservationService.selectSubmitCount(seFunctionReservation));
    }

    /**
     * 获取开放预约详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(seFunctionReservationService.selectSeFunctionReservationById(id));
    }

    public void getDeptInfo(SeFunctionReservation seFunctionReservation) {
        if (ObjectUtil.isEmpty(seFunctionReservation.getDeptId())) {
            seFunctionReservation.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(seFunctionReservation.getDeptId());
        String[] split = dept.getAncestorsName().split(",");
        if (split.length >= 2) {
            seFunctionReservation.setCounty(split[1]);
            if (split.length >= 3) {
                seFunctionReservation.setCountry(split[2]);
                if (split.length >= 4) {
                    seFunctionReservation.setTown(split[3]);
                }
            }
        }
    }

    /**
     * 新增开放预约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:add')")
    @Log(title = "开放预约", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeFunctionReservation seFunctionReservation) {
        seFunctionReservation.setCreateBy(getUsername());
        if (ObjectUtil.isEmpty(seFunctionReservation.getFunctionId())) {
            return error("预约功能室不可为空！");
        }
        getDeptInfo(seFunctionReservation);
        seFunctionReservation.setUserId(getUserId());
        return toAjax(seFunctionReservationService.insertSeFunctionReservation(seFunctionReservation));
    }

    /**
     * 修改开放预约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:edit')")
    @Log(title = "开放预约", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeFunctionReservation seFunctionReservation) {
        seFunctionReservation.setUpdateBy(getUsername());
//        if (seFunctionReservationService.checkSeFunctionReservation(seFunctionReservation)){
//            return error("数据已存在！");
//        }
        getDeptInfo(seFunctionReservation);
        return toAjax(seFunctionReservationService.updateSeFunctionReservation(seFunctionReservation));
    }

    /**
     * 修改开放预约 进行 审核操作
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:audit')")
    @Log(title = "开放预约(审核)", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @RepeatSubmit
    public AjaxResult audit(@RequestBody SeFunctionReservation seFunctionReservation) {
        seFunctionReservation.setUpdateBy(getUsername());
        //设置审批人姓名
        seFunctionReservation.setAuditUser(getLoginUser().getUser().getNickName());
        //设置审批时间
        seFunctionReservation.setAuditTime(DateUtils.getNowDate());
        seFunctionReservation.setStatus(1);
        seFunctionReservation.setMsg(1);
        // TODO: 增加开放预约审核消息通知
        return toAjax(seFunctionReservationService.updateSeFunctionReservation(seFunctionReservation));
    }

    /**
     * 删除开放预约
     */
    @PreAuthorize("@ss.hasPermi('sentiment:functionReservation:remove')")
    @Log(title = "开放预约", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(seFunctionReservationService.deleteSeFunctionReservationByIds(ids));
    }
}
