package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbGroupSite;

/**
 * 新就业群体站点Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ITbGroupSiteService 
{
    /**
     * 查询新就业群体站点
     * 
     * @param id 新就业群体站点主键
     * @return 新就业群体站点
     */
    public TbGroupSite selectTbGroupSiteById(Long id);

    /**
     * 校验新就业群体站点是否存在
     *
     * @param tbGroupSite 新就业群体站点
     * @return 新就业群体站点
     */
    public boolean checkTbGroupSite(TbGroupSite tbGroupSite);

    /**
     * 查询新就业群体站点列表
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 新就业群体站点集合
     */
    public List<TbGroupSite> selectTbGroupSiteList(TbGroupSite tbGroupSite);

    /**
     * 导入新就业群体站点
     *
     * @param infos       新就业群体站点列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGroupSite(List<TbGroupSite> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    public int insertTbGroupSite(TbGroupSite tbGroupSite);

    /**
     * 修改新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    public int updateTbGroupSite(TbGroupSite tbGroupSite);

    /**
     * 批量删除新就业群体站点
     * 
     * @param ids 需要删除的新就业群体站点主键集合
     * @return 结果
     */
    public int deleteTbGroupSiteByIds(Long[] ids);

    /**
     * 删除新就业群体站点信息
     * 
     * @param id 新就业群体站点主键
     * @return 结果
     */
    public int deleteTbGroupSiteById(Long id);
}
