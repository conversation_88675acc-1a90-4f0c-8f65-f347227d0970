package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMainParty;

/**
 * 矛盾纠纷当事人信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface ITbMainPartyService 
{
    /**
     * 查询矛盾纠纷当事人信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 矛盾纠纷当事人信息
     */
    public TbMainParty selectTbMainPartyById(Long id);

    /**
     * 校验矛盾纠纷当事人信息是否存在
     *
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 矛盾纠纷当事人信息
     */
    public boolean checkTbMainParty(TbMainParty tbMainParty);

    /**
     * 查询矛盾纠纷当事人信息列表
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 矛盾纠纷当事人信息集合
     */
    public List<TbMainParty> selectTbMainPartyList(TbMainParty tbMainParty);

    /**
     * 导入矛盾纠纷当事人信息
     *
     * @param infos       矛盾纠纷当事人信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMainParty(List<TbMainParty> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    public int insertTbMainParty(TbMainParty tbMainParty);

    /**
     * 修改矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    public int updateTbMainParty(TbMainParty tbMainParty);

    /**
     * 批量删除矛盾纠纷当事人信息
     * 
     * @param ids 需要删除的矛盾纠纷当事人信息主键集合
     * @return 结果
     */
    public int deleteTbMainPartyByIds(Long[] ids);

    /**
     * 删除矛盾纠纷当事人信息信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 结果
     */
    public int deleteTbMainPartyById(Long id);
}
