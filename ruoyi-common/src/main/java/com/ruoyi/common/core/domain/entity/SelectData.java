package com.ruoyi.common.core.domain.entity;


import java.util.ArrayList;

/**
 * 多级选择框所用对象
 */
public class SelectData {
    private Long value;

    private String label;

    private Long id;

    private Long parentId;

    private String parentName;

    private String serialNumber;

    private String valueStr;

    private String ruleType;

    private String weekTimes;

    private Integer weekMode;

    private String rcpWeekDay;

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public String getWeekTimes() {
        return weekTimes;
    }

    public void setWeekTimes(String weekTimes) {
        this.weekTimes = weekTimes;
    }

    public Integer getWeekMode() {
        return weekMode;
    }

    public void setWeekMode(Integer weekMode) {
        this.weekMode = weekMode;
    }

    public String getRcpWeekDay() {
        return rcpWeekDay;
    }

    public void setRcpWeekDay(String rcpWeekDay) {
        this.rcpWeekDay = rcpWeekDay;
    }

    public Long getValue() {
        return value;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getValueStr() {
        return valueStr;
    }

    public void setValueStr(String valueStr) {
        this.valueStr = valueStr;
    }

    private ArrayList<SelectData> children;

    public void setValue(Long value) {
        this.value = value;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ArrayList<SelectData> getChildren() {
        return children;
    }

    public void setChildren(ArrayList<SelectData> children) {
        this.children = children;
    }
}
