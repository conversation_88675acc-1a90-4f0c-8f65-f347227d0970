package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.service.*;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.web.util.ThrowableAnalyzer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AsyncUpdateServiceImpl implements AsyncUpdateService {

    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ITbResidentMemberService residentMemberService;
    @Autowired
    private ITbResidentContactService residentContactService;
    @Autowired
    private ITbMerchantInspectService merchantInspectService;
    @Autowired
    private ITbMerchantInfoService merchantInfoService;
    @Autowired
    private ITbGridUserService gridUserService;
    @Autowired
    private ITbGridInspectService gridInspectService;
    @Autowired
    private ITbCommunityResidentService communityResidentService;
    @Autowired
    private ITbCommunityInfoService communityInfoService;
    @Autowired
    private ITbCommunityEstateService communityEstateService;

    /**
     * 当网格信息表更新时，同时异步更新关联的其他表
     * @param tbGridInfo
     */
    @Override
    @Transactional
    @Async("asyncExecutor")
    public void AsyncUpdateByGridInfo(TbGridInfo tbGridInfo) {
        try {
            //修改部门表
            int i1 = deptService.updateDept(new SysDept() {{
                setParentId(tbGridInfo.getDeptId());
                setDeptName(tbGridInfo.getGridName());
                setOrderNum((tbGridInfo.getGridSort()));
                setDeptLevel(8);
                setDeptType("网格");
                setUpdateBy(tbGridInfo.getUpdateBy());
                setUpdateTime(tbGridInfo.getUpdateTime());
            }});
            //修改tb_resident_member 居民住户成员信息
            residentMemberService.updateTbResidentMember(new TbResidentMember(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_resident_contact 居民住户联络沟通信息
            residentContactService.updateTbResidentContact(new TbResidentContact(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_merchant_inspect 门店巡查
            merchantInspectService.updateTbMerchantInspect(new TbMerchantInspect(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_merchant_info 门店/商户信息
            merchantInfoService.updateTbMerchantInfo(new TbMerchantInfo(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_grid_user 网格员信息
            gridUserService.updateTbGridUser(new TbGridUser(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_grid_inspect 网格巡查
            gridInspectService.updateTbGridInspect(new TbGridInspect(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_community_resident 小区住户居民信息
            communityResidentService.updateTbCommunityResident(new TbCommunityResident(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_community_info 小区信息
            communityInfoService.updateTbCommunityInfo(new TbCommunityInfo(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改tb_community_estate 小区物业
            communityEstateService.updateTbCommunityEstate(new TbCommunityEstate(){{
                setGridId(tbGridInfo.getId()); //存入网格id
                setGridName(tbGridInfo.getGridName());
            }});
            //修改se_volunteer_register 志愿报名
            /*volunteerRegisterService.updateSeVolunteerRegister(new SeVolunteerRegister(){{

            }});
            //修改se_public_sentiment  民情收集
            publicSentimentService.updateSePublicSentiment(new SePublicSentiment(){{

            }});
            //修改se_heart_promise 心灵有约
            heartPromiseService.updateSeHeartPromise(new SeHeartPromise(){{

            }});
            //修改se_function_reservation 社区功能室服务预约
            functionReservationService.updateSeFunctionReservation(new SeFunctionReservation(){{

            }});
            //修改se_convenient_phone 便民电话
            convenientPhoneService.updateSeConvenientPhone(new SeConvenientPhone(){{

            }});
            //修改se_community_function  社区功能室
            communityFunctionService.updateSeCommunityFunction(new SeCommunityFunction(){{

            }});*/
        } catch (Exception e) {
            // 异步操作的异常处理
            throw new ServiceException("网格信息所关联的数据表更新数据失败！");
        }

    }
}
