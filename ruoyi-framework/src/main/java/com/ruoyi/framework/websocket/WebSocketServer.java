package com.ruoyi.framework.websocket;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * websocket 消息处理
 *
 * <AUTHOR>
 */
@Component
@ServerEndpoint("/websocket/{userTokenId}")
public class WebSocketServer {

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    /**
     * 接收tokenUser
     */
    private String tokenUser = "";

    public static ConcurrentHashMap<String, WebSocketServer> getWebSocketMap() {
        return webSocketMap;
    }

    /**
     * WebSocketServer 日志控制器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketServer.class);

    /**
     * 默认最多允许同时在线人数100
     */
    public static int socketMaxOnlineCount = 1000;

    private static Semaphore socketSemaphore = new Semaphore(socketMaxOnlineCount);

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userTokenId") String userTokenId) throws Exception {
        boolean semaphoreFlag = false;
        // 尝试获取信号量
        semaphoreFlag = SemaphoreUtils.tryAcquire(socketSemaphore);
        if (!semaphoreFlag) {
            // 未获取到信号量
            LOGGER.error("\n 当前在线人数超过限制数- {}", socketMaxOnlineCount);
//            WebSocketUsers.sendMessageToUserByText(session, "当前在线人数超过限制数：" + socketMaxOnlineCount);
            session.close();
        } else {
            // 添加用户
            WebSocketUsers.put(userTokenId, session);
            LOGGER.info("\n 建立连接 - {}", session);
            LOGGER.info("\n 当前人数 - {}", WebSocketUsers.getUsers().size());
            AjaxResult success = AjaxResult.success("ConnectSuccess");
            success.put("connectionID", userTokenId);
            success.put("workerID", userTokenId);
            WebSocketUsers.sendMessageToUserByText(session, JSONObject.toJSONString(success));
        }
    }

    /**
     * 连接关闭时处理
     */
    @OnClose
    public void onClose(Session session, @PathParam("userTokenId") String userTokenId) {
        LOGGER.info("\n 关闭连接 - {}", session, userTokenId);
        // 移除用户
        WebSocketUsers.remove(userTokenId);
        // 获取到信号量则需释放
        SemaphoreUtils.release(socketSemaphore);
    }

    /**
     * 抛出异常时处理
     */
    @OnError
    public void onError(Session session, Throwable exception) throws Exception {
        if (session != null) {
            if (session.isOpen()) {
                // 关闭连接
                session.close();
            }
            String sessionId = session.getId();
            LOGGER.info("\n 连接异常 - {}", sessionId);
            LOGGER.info("\n 异常信息 - {}", exception);
            // 移出用户
            WebSocketUsers.remove(sessionId);
            // 获取到信号量则需释放
            SemaphoreUtils.release(socketSemaphore);
        } else {
            exception.printStackTrace();
            System.err.println("session时空" + exception.getMessage());
        }

    }

    /**
     * 服务器接收到客户端消息时调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("userTokenId") String userTokenId) throws IOException {
//        LOGGER.info("\n 服务器{}收到信息 - {}", userTokenId, message);
        if (StringUtils.isNotBlank(message)) {
            Map messageMap = JSONObject.parseObject(message, Map.class);
            if (ObjectUtil.isNotEmpty(messageMap)) {
                String messageType = (String) messageMap.get("messageType");
                if (ObjectUtil.isNotEmpty(messageType)) {
                    if (messageType.equals("StartModbus")) {
                        Long drillingId = Long.parseLong(((Integer) messageMap.get("drillingId")).toString());
                        if (ObjectUtil.isNotEmpty(drillingId)) {
                            // 当前用户开启的modbus
                            System.err.println("启动设备监听啦" + userTokenId + drillingId);
//                            SpringUtils.getBean(RedisCache.class).setCacheObject(CacheConstants.SYS_STARTMODBUS_KEY + userTokenId, drillingId);
                            WebSocketUsers.sendMessageToUserByText(session, JSONObject.toJSONString(AjaxResult.success("StartModbusSuccess")));
                        }
                    } else if (messageType.equals("DeleteModbus")) {
                        // 当前用户移除modbus数据监听连接
                        System.err.println("关闭设备监听啦" + userTokenId);
//                        SpringUtils.getBean(RedisCache.class).deleteObject(CacheConstants.SYS_STARTMODBUS_KEY + userTokenId);
                        WebSocketUsers.sendMessageToUserByText(session, JSONObject.toJSONString(AjaxResult.success("EndModbusSuccess")));
                    }
                }

            }
        }
    }

    /**
     * 实现服务器主动推送(通知消息)
     */
    public static void sendMessage(String userTokenId) throws IOException {
        Session session = WebSocketUsers.get(userTokenId);
        if (ObjectUtil.isEmpty(session)) {
            return;
        }
        System.err.println("向客户端发送消息通知啦////////");
        AjaxResult success = AjaxResult.success("UserLogout");
        WebSocketUsers.sendMessageToUserByText(session, JSONObject.toJSONString(success));
    }

}

