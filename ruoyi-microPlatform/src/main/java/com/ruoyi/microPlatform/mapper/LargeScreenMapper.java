package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 大屏展示Mapper
 */
public interface LargeScreenMapper {

    //网格数量
    Integer countGridInfo(LargeScreenInfo largeScreenInfo);

    //小区数量
    Integer countCommunityInfo(LargeScreenInfo largeScreenInfo);

    //门店商户数量
    Integer countMerchantInfo(LargeScreenInfo largeScreenInfo);

    //住户数量
    Integer countCommunityResident(LargeScreenInfo largeScreenInfo);

    //住户成员数量
    Integer countResidentMember(LargeScreenInfo largeScreenInfo);

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 数量
     */
    LargeScreenInfo getStatisticsByJurisdiction(LargeScreenInfo largeScreenInfo);

    /**
     * 网格下的 :住户家庭数   人员数    门店商户数
     * @param largeScreenInfo
     * @return
     */
    LargeScreenInfo getGridUnderCount(LargeScreenInfo largeScreenInfo);

    /**
     * 小区下的 住户家庭数   人员数
     * @param largeScreenInfo
     * @return
     */
    LargeScreenInfo getCommunityUnderCount(LargeScreenInfo largeScreenInfo);

}
