package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbGridInspect;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import com.ruoyi.microPlatform.mapper.TbGridInspectMapper;
import com.ruoyi.microPlatform.service.ITbGridInspectService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 网格巡查Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbGridInspectServiceImpl implements ITbGridInspectService {
    @Autowired
    private TbGridInspectMapper tbGridInspectMapper;
    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询网格巡查
     *
     * @param id 网格巡查主键
     * @return 网格巡查
     */
    @Override
    public TbGridInspect selectTbGridInspectById(Long id) {
        return tbGridInspectMapper.selectTbGridInspectById(id);
    }

    /**
     * 校验网格巡查是否存在
     *
     * @param tbGridInspect
     * @return boolean
     */
    @Override
    public boolean checkTbGridInspect(TbGridInspect tbGridInspect) {
        TbGridInspect old = tbGridInspectMapper.checkTbGridInspectUnique(tbGridInspect);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询网格巡查列表
     *
     * @param tbGridInspect 网格巡查
     * @return 网格巡查
     */
    @Override
    public List<TbGridInspect> selectTbGridInspectList(TbGridInspect tbGridInspect) {
        return tbGridInspectMapper.selectTbGridInspectList(tbGridInspect);
    }

    /**
     * 导入网格巡查
     *
     * @param infos           网格巡查列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridInspect(List<TbGridInspect> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入网格巡查数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbGridInspect info : infos) {
            try {
                // 验证是否存在这个数据
                TbGridInspect old = tbGridInspectMapper.checkTbGridInspectUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbGridInspectMapper.insertTbGridInspect(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbGridInspectMapper.updateTbGridInspect(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增网格巡查
     *
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTbGridInspect(TbGridInspect tbGridInspect) {
        tbGridInspect.setCreateTime(DateUtils.getNowDate());

        TbGridInfo gridInfo = gridInfoMapper.selectTbGridInfoById(tbGridInspect.getGridId());
        tbGridInspect.setDeptId(gridInfo.getDeptId());
        tbGridInspect.setCountry(gridInfo.getCountry());
        tbGridInspect.setCounty(gridInfo.getCounty());
        tbGridInspect.setTown(gridInfo.getTown());
        tbGridInspect.setGridName(gridInfo.getGridName());
        int i = tbGridInspectMapper.insertTbGridInspect(tbGridInspect);
        // 更改网格状态（直接取对应网格巡查的巡查结果）
        gridInfoMapper.updateTbGridInfo(new TbGridInfo() {{
            setId(tbGridInspect.getGridId());
            setStatus(tbGridInspect.getInspectResult());
        }});
        if (i > 0) {
            handleSend(tbGridInspect, SecurityUtils.getLoginUser());
        }
        return i;
    }

    @Async("threadPoolTaskExecutor")
    public void handleSend(TbGridInspect tbGridInspect, LoginUser loginUser) {

        // 新增
        Long deptId = tbGridInspect.getDeptId();
        if (StringUtils.isBlank(tbGridInspect.getTown()) && ObjectUtil.isNotEmpty(tbGridInspect.getDeptId())) {
            SysDept sysDept = deptMapper.selectDeptAncestorsByDeptId(tbGridInspect.getDeptId());
            if (ObjectUtil.isNotEmpty(sysDept)) {
                System.err.println("竟然在新增的时候没有获取到社区信息");
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + sysDept.getDeptId(), sysDept);
                redisCache.setCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + sysDept.getAncestorsName(), sysDept);

                String[] split = sysDept.getAncestorsName().split(",");
                if (split.length >= 2) {
                    TbGridInspect newData = new TbGridInspect() {{
                        setId(tbGridInspect.getId());
                    }};
                    newData.setCounty(split[1]);
                    if (split.length >= 3) {
                        newData.setCountry(split[2]);
                        if (split.length >= 4) {
                            newData.setTown(split[3]);
                        }
                    }
                    tbGridInspectMapper.updateTbGridInspect(newData);
                }
            }
        }
        //TODO: 发送消息通知
        weChatService.insertMsg(deptId, "网格巡查",
                "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/patrolGrid/detail?gridInspectId="  + tbGridInspect.getId(),
                tbGridInspect.getTown(), "提交了网格巡查记录，巡查结果为" + (tbGridInspect.getInspectResult() == 1 ? "正常" : "异常"));
    }

    /**
     * 修改网格巡查
     *
     * @param tbGridInspect 网格巡查
     * @return 结果
     */
    @Override
    public int updateTbGridInspect(TbGridInspect tbGridInspect) {
        //TODO: 网格巡查--网格状态修改 异常改为这正常
        return tbGridInspectMapper.updateTbGridInspect(tbGridInspect);
    }

    /**
     * 批量删除网格巡查
     *
     * @param ids 需要删除的网格巡查主键
     * @return 结果
     */
    @Override
    public int deleteTbGridInspectByIds(Long[] ids) {
        return tbGridInspectMapper.deleteTbGridInspectByIds(ids);
    }

    /**
     * 删除网格巡查信息
     *
     * @param id 网格巡查主键
     * @return 结果
     */
    @Override
    public int deleteTbGridInspectById(Long id) {
        return tbGridInspectMapper.deleteTbGridInspectById(id);
    }


    @Override
    public Map<Object, Object> selectTbGridInspectListByStatus(TbGridInspect tbGridInspect) {

        Map<Object, Object> map = new HashMap<>();

        //查询当前网格ID下的所有巡查数据 根据状态获得不同状态的数据量
        List<TbGridInspect> tbGridInspects = tbGridInspectMapper.selectTbGridInspectByStatus(tbGridInspect);

        //把状态码 和 当前转态码的数据量 存入map中
        for (TbGridInspect gridInspect : tbGridInspects) {
            map.put(gridInspect.getStatus(), gridInspect.getStatusCount());
        }

        //查询最新更新的数据
        TbGridInspect LatestUpdateGridInspects = tbGridInspectMapper.selectTbGridInspectByUpdateTime(tbGridInspect);

        //查询当前网格id下的数据size
        int listSize = tbGridInspects.size() > 0 ? tbGridInspects.stream().mapToInt(TbGridInspect::getStatusCount).reduce(0, Integer::sum) : 0;
        //存入map
        map.put("总数", listSize);

        //存入最新数据
        map.put("最新数据", LatestUpdateGridInspects);

        return map;
    }

    /**
     * 查询当前用户的提交总数 和最新提交日期
     *
     * @param tbGridInspect
     * @return
     */
    @Override
    public Map<String, Object> selectSubmitCount(TbGridInspect tbGridInspect) {
        return tbGridInspectMapper.selectSubmitCount(tbGridInspect);
    }

    @Override
    public List<Map<String, Object>> gridInspectGroupMonth(LargeScreenInfo largeScreenInfo) {
        List<Map<String, Object>> gridData = tbGridInspectMapper.selectMonthlyStats(largeScreenInfo);//网格巡查

        // 结果用 LinkedHashMap 保持有序
        Map<String, Integer> monthCountMap = new HashMap<>();
        for (Map<String, Object> row : gridData) {
            monthCountMap.put((String) row.get("month"), ((Number) row.get("total")).intValue());
        }
        List<Map<String, Object>> finalResult = new ArrayList<>();

        // 当前月
        DateTime end = DateUtil.endOfMonth(new Date());
        // 往前推 11 个月
        DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(end, -11));
        // 按月遍历
        DateUtil.rangeToList(start, end, DateField.MONTH).forEach(date -> {
            String monthKey = DateUtil.format(date, "yyyyMM");
            Map<String, Object> item = new HashMap<>();
            item.put("month", monthKey);
            item.put("total", monthCountMap.getOrDefault(monthKey, 0));
            finalResult.add(item);
        });
        return finalResult;
    }

    @Override
    public Integer gridInspectCount(LargeScreenInfo largeScreenInfo) {
        return tbGridInspectMapper.selectCount(largeScreenInfo);
    }
}
