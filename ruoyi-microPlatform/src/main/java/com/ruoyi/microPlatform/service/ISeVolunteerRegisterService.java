package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.SeVolunteerRegister;

import java.util.List;
import java.util.Map;

/**
 * 志愿报名Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ISeVolunteerRegisterService 
{
    /**
     * 查询志愿报名
     * 
     * @param id 志愿报名主键
     * @return 志愿报名
     */
    public SeVolunteerRegister selectSeVolunteerRegisterById(Long id);

    /**
     * 校验志愿报名是否存在
     *
     * @param seVolunteerRegister 志愿报名
     * @return 志愿报名
     */
    public boolean checkSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister);

    /**
     * 查询志愿报名列表
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 志愿报名集合
     */
    public List<SeVolunteerRegister> selectSeVolunteerRegisterList(SeVolunteerRegister seVolunteerRegister);

    /**
     * 导入志愿报名
     *
     * @param infos       志愿报名列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeVolunteerRegister(List<SeVolunteerRegister> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增志愿报名
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    public int insertSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister);

    /**
     * 修改志愿报名
     * 
     * @param seVolunteerRegister 志愿报名
     * @return 结果
     */
    public int updateSeVolunteerRegister(SeVolunteerRegister seVolunteerRegister);

    /**
     * 批量删除志愿报名
     * 
     * @param ids 需要删除的志愿报名主键集合
     * @return 结果
     */
    public int deleteSeVolunteerRegisterByIds(Long[] ids);

    /**
     * 删除志愿报名信息
     * 
     * @param id 志愿报名主键
     * @return 结果
     */
    public int deleteSeVolunteerRegisterById(Long id);

    public Map<String, Object> selectSubmitCount(SeVolunteerRegister seVolunteerRegister);


    int selectCount(BigdataParam bigdataParam);

    List<CommonBaseCount> iSeVolunteerRegisterCountByDept(BigdataParam bigdataParam);
}
