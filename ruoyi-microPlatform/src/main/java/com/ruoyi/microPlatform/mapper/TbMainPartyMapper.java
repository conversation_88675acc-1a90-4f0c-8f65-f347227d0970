package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMainParty;

/**
 * 矛盾纠纷当事人信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface TbMainPartyMapper 
{
    /**
     * 查询矛盾纠纷当事人信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 矛盾纠纷当事人信息
     */
    public TbMainParty selectTbMainPartyById(Long id);

    /**
     * 校验矛盾纠纷当事人信息是否唯一
     *
     * @param tbMainParty
     * @return 矛盾纠纷当事人信息
     */
    public TbMainParty checkTbMainPartyUnique(TbMainParty tbMainParty);

    /**
     * 查询矛盾纠纷当事人信息列表
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 矛盾纠纷当事人信息集合
     */
    public List<TbMainParty> selectTbMainPartyList(TbMainParty tbMainParty);

    /**
     * 新增矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    public int insertTbMainParty(TbMainParty tbMainParty);

    /**
     * 修改矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    public int updateTbMainParty(TbMainParty tbMainParty);

    /**
     * 删除矛盾纠纷当事人信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 结果
     */
    public int deleteTbMainPartyById(Long id);

    /**
     * 批量删除矛盾纠纷当事人信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMainPartyByIds(Long[] ids);
}
