package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbSituationOrg;
import com.ruoyi.microPlatform.service.ITbSituationOrgService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 组织情况（非公经济组织/新社会组织）Controller
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/gridData/situationOrg")
public class TbSituationOrgController extends BaseController
{
    @Autowired
    private ITbSituationOrgService tbSituationOrgService;

    /**
     * 查询组织情况（非公经济组织/新社会组织）列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbSituationOrg tbSituationOrg)
    {
        startPage();
        List<TbSituationOrg> list = tbSituationOrgService.selectTbSituationOrgList(tbSituationOrg);
        return getDataTable(list);
    }

    /**
     * 导出组织情况（非公经济组织/新社会组织）列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:export')")
    @Log(title = "组织情况（非公经济组织/新社会组织）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbSituationOrg tbSituationOrg)
    {
        List<TbSituationOrg> list = tbSituationOrgService.selectTbSituationOrgList(tbSituationOrg);
        ExcelUtil<TbSituationOrg> util = new ExcelUtil<TbSituationOrg>(TbSituationOrg.class);
        util.exportExcel(response, list, "组织情况（非公经济组织/新社会组织）数据");
    }

    @Log(title = "组织情况（非公经济组织/新社会组织）", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbSituationOrg> util = new ExcelUtil<TbSituationOrg>(TbSituationOrg.class);
        List<TbSituationOrg> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbSituationOrgService.importTbSituationOrg(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbSituationOrg> util = new ExcelUtil<TbSituationOrg>(TbSituationOrg.class);
        util.exportTemplateExcel(response, "组织情况（非公经济组织/新社会组织）数据");
    }

    /**
     * 获取组织情况（非公经济组织/新社会组织）详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbSituationOrgService.selectTbSituationOrgById(id));
    }

    /**
     * 新增组织情况（非公经济组织/新社会组织）
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:add')")
    @Log(title = "组织情况（非公经济组织/新社会组织）", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbSituationOrg tbSituationOrg)
    {
        tbSituationOrg.setCreateBy(getUsername());
        if (tbSituationOrgService.checkTbSituationOrg(tbSituationOrg)){
            return error("数据已存在！");
        }
        return toAjax(tbSituationOrgService.insertTbSituationOrg(tbSituationOrg));
    }

    /**
     * 修改组织情况（非公经济组织/新社会组织）
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:edit')")
    @Log(title = "组织情况（非公经济组织/新社会组织）", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbSituationOrg tbSituationOrg)
    {
        tbSituationOrg.setUpdateBy(getUsername());
        if (tbSituationOrgService.checkTbSituationOrg(tbSituationOrg)){
            return error("数据已存在！");
        }
        return toAjax(tbSituationOrgService.updateTbSituationOrg(tbSituationOrg));
    }

    /**
     * 删除组织情况（非公经济组织/新社会组织）
     */
    @PreAuthorize("@ss.hasPermi('gridData:situationOrg:remove')")
    @Log(title = "组织情况（非公经济组织/新社会组织）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbSituationOrgService.deleteTbSituationOrgByIds(ids));
    }
}
