package com.ruoyi.microPlatform.mapper;

import java.util.List;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.SeCommunityFunction;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;

/**
 * 社区功能室Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeCommunityFunctionMapper 
{
    /**
     * 查询社区功能室
     * 
     * @param id 社区功能室主键
     * @return 社区功能室
     */
    public SeCommunityFunction selectSeCommunityFunctionById(Long id);

    /**
     * 校验社区功能室是否唯一
     *
     * @param seCommunityFunction
     * @return 社区功能室
     */
    public SeCommunityFunction checkSeCommunityFunctionUnique(SeCommunityFunction seCommunityFunction);

    /**
     * 查询社区功能室列表
     * 
     * @param seCommunityFunction 社区功能室
     * @return 社区功能室集合
     */
    public List<SeCommunityFunction> selectSeCommunityFunctionList(SeCommunityFunction seCommunityFunction);

    /**
     * 新增社区功能室
     * 
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    public int insertSeCommunityFunction(SeCommunityFunction seCommunityFunction);

    /**
     * 修改社区功能室
     * 
     * @param seCommunityFunction 社区功能室
     * @return 结果
     */
    public int updateSeCommunityFunction(SeCommunityFunction seCommunityFunction);

    /**
     * 删除社区功能室
     * 
     * @param id 社区功能室主键
     * @return 结果
     */
    public int deleteSeCommunityFunctionById(Long id);

    /**
     * 批量删除社区功能室
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeCommunityFunctionByIds(Long[] ids);

    /**
     * 查询不同类型的工作室数量
     * @param largeScreenInfo
     * @return
     */
    List<SeCommunityFunction> getCommunityFunctionCount(LargeScreenInfo largeScreenInfo);

    /**
     * 查询不同类型的工作室数量
     */
    List<SeCommunityFunction> getCommunityFunctionCountByBigData(BigdataParam largeScreenInfo);

}
