package com.ruoyi.dataview.domain.bigData.param;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class DbBigdataParam {


    /**
     * 所属部门
     */
    private Long deptId;


    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）", needMerge = true, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    private Integer year;

    /**
     * 搜索时间区间 时间戳
     */
    private Long startTimeLong;
    private Long endTimeLong;
}
