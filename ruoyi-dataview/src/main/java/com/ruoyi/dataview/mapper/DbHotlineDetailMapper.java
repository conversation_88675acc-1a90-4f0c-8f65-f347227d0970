package com.ruoyi.dataview.mapper;

import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbHotlineDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 12345热线中心综合明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface DbHotlineDetailMapper {
    /**
     * 查询12345热线中心综合明细
     *
     * @param id 12345热线中心综合明细主键
     * @return 12345热线中心综合明细
     */
    public DbHotlineDetail selectDbHotlineDetailById(Long id);

    /**
     * 校验12345热线中心综合明细是否唯一
     *
     * @param dbHotlineDetail
     * @return 12345热线中心综合明细
     */
    public DbHotlineDetail checkDbHotlineDetailUnique(DbHotlineDetail dbHotlineDetail);

    /**
     * 查询12345热线中心综合明细列表
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 12345热线中心综合明细集合
     */
    public List<DbHotlineDetail> selectDbHotlineDetailList(DbHotlineDetail dbHotlineDetail);

    int selectDbHotlineDetailCount(DbHotlineDetail dbHotlineDetail);

    /**
     * 新增12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    public int insertDbHotlineDetail(DbHotlineDetail dbHotlineDetail);

    /**
     * 修改12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    public int updateDbHotlineDetail(DbHotlineDetail dbHotlineDetail);

    /**
     * 删除12345热线中心综合明细
     *
     * @param id 12345热线中心综合明细主键
     * @return 结果
     */
    public int deleteDbHotlineDetailById(Long id);

    /**
     * 批量删除12345热线中心综合明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbHotlineDetailByIds(Long[] ids);

    List<CommonBaseCount> hotlineDetailGroupRec(@Param("bigdataParam") DbBigdataParam bigdataParam);
}
