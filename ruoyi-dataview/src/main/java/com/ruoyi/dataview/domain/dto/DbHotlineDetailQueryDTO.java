package com.ruoyi.dataview.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 12345热线中心综合明细对象 db_hotline_detail
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbHotlineDetailQueryDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 工单标识 */
    private String recId;

    /** 地址描述 */
    private String address;

    /** 工单描述 */
    private String eventDesc;

    /** 诉求来源 */
    private String eventSrcName;

    /** 工单类型 */
    private String recTypeName;

    /** 一级归口类型 */
    private String eventTypeName;

    /** 二级归口类型 */
    private String mainTypeName;

    /** 三级归口类型 */
    private String subTypeName;

    /** 所属区域 */
    private String districtName;

    /** 所属街道 */
    private String streetName;

    /** 一级承办部门 */
    private String firstUnitName;

    /** 四级归口 */
    private String thirdTypeName;

    /** 诉求目的 */
    private String caseGoal;

    /** 紧急程度 */
    private String urgencyLevel;

    /** 工单状态 */
    @Excel(name = "工单状态")
    private String eventStatus;

    /** 办理开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleStartTime;

    /** 办理截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleDeadline;

    /** 第三方平台截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date thirdPartyDeadline;

    /** 诉求人 */
    private String appealerName;

    /** 联系方式 */
    private String appealerContact;

    /** 是否回访(0否,1是) */
    private Integer isReturnVisit;

    /** 办理意见 */
    private String handleOpinion;

    /** 办结意见 */
    private String finishOpinion;

    /** 回访满意度 */
    private String returnVisitScore;

    /** 回访内容 */
    private String returnVisitContent;

    /** 话务员 */
    private String operatorName;

    /** 办理结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleEndTime;

}
