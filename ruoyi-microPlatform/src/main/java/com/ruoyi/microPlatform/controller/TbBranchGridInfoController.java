package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;
import com.ruoyi.microPlatform.service.ITbBranchGridInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 支部关联网格Controller
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/microPlatform/banchGridInfo")
public class TbBranchGridInfoController extends BaseController
{
    @Autowired
    private ITbBranchGridInfoService tbBranchGridInfoService;

    /**
     * 查询支部关联网格列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbBranchGridInfo tbBranchGridInfo)
    {
        startPage();
        List<TbBranchGridInfo> list = tbBranchGridInfoService.selectTbBranchGridInfoList(tbBranchGridInfo);
        return getDataTable(list);
    }

    /**
     * 导出支部关联网格列表
     */
    @Log(title = "支部关联网格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbBranchGridInfo tbBranchGridInfo)
    {
        List<TbBranchGridInfo> list = tbBranchGridInfoService.selectTbBranchGridInfoList(tbBranchGridInfo);
        ExcelUtil<TbBranchGridInfo> util = new ExcelUtil<TbBranchGridInfo>(TbBranchGridInfo.class);
        util.exportExcel(response, list, "支部关联网格数据");
    }

    @Log(title = "支部关联网格", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbBranchGridInfo> util = new ExcelUtil<TbBranchGridInfo>(TbBranchGridInfo.class);
        List<TbBranchGridInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbBranchGridInfoService.importTbBranchGridInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbBranchGridInfo> util = new ExcelUtil<TbBranchGridInfo>(TbBranchGridInfo.class);
        util.exportTemplateExcel(response, "支部关联网格数据");
    }

    /**
     * 获取支部关联网格详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbBranchGridInfoService.selectTbBranchGridInfoById(id));
    }

    /**
     * 新增支部关联网格
     */
    @Log(title = "支部关联网格", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbBranchGridInfo tbBranchGridInfo)
    {
        tbBranchGridInfo.setCreateBy(getUsername());
        if (tbBranchGridInfoService.checkTbBranchGridInfo(tbBranchGridInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbBranchGridInfoService.insertTbBranchGridInfo(tbBranchGridInfo));
    }

    /**
     * 修改支部关联网格
     */
    @Log(title = "支部关联网格", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbBranchGridInfo tbBranchGridInfo)
    {
        tbBranchGridInfo.setUpdateBy(getUsername());
        if (tbBranchGridInfoService.checkTbBranchGridInfo(tbBranchGridInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbBranchGridInfoService.updateTbBranchGridInfo(tbBranchGridInfo));
    }

    /**
     * 删除支部关联网格
     */
    @Log(title = "支部关联网格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbBranchGridInfoService.deleteTbBranchGridInfoByIds(ids));
    }
}
