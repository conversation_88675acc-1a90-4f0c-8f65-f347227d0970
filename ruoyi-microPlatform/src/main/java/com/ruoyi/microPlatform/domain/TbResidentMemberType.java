package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 成员标签配置对象 tb_resident_member_type
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
public class TbResidentMemberType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 人员id */
    @Excel(name = "人员id")
    private Long memberId;

    private Integer typeId;

    /** 标签value */
    @Excel(name = "标签value")
    private String typeValue;

    /** 标签名称 */
    @Excel(name = "标签名称")
    private String typeLabel;

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setTypeValue(String typeValue) 
    {
        this.typeValue = typeValue;
    }

    public String getTypeValue() 
    {
        return typeValue;
    }
    public void setTypeLabel(String typeLabel) 
    {
        this.typeLabel = typeLabel;
    }

    public String getTypeLabel() 
    {
        return typeLabel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("memberId", getMemberId())
            .append("typeValue", getTypeValue())
            .append("typeLabel", getTypeLabel())
            .toString();
    }
}
