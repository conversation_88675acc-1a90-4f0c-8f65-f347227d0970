package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbOrgActivity;

/**
 * 组织日常活动开展情况Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbOrgActivityMapper 
{
    /**
     * 查询组织日常活动开展情况
     * 
     * @param id 组织日常活动开展情况主键
     * @return 组织日常活动开展情况
     */
    public TbOrgActivity selectTbOrgActivityById(Long id);

    /**
     * 校验组织日常活动开展情况是否唯一
     *
     * @param tbOrgActivity
     * @return 组织日常活动开展情况
     */
    public TbOrgActivity checkTbOrgActivityUnique(TbOrgActivity tbOrgActivity);

    /**
     * 查询组织日常活动开展情况列表
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 组织日常活动开展情况集合
     */
    public List<TbOrgActivity> selectTbOrgActivityList(TbOrgActivity tbOrgActivity);

    /**
     * 新增组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    public int insertTbOrgActivity(TbOrgActivity tbOrgActivity);

    /**
     * 修改组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    public int updateTbOrgActivity(TbOrgActivity tbOrgActivity);

    /**
     * 删除组织日常活动开展情况
     * 
     * @param id 组织日常活动开展情况主键
     * @return 结果
     */
    public int deleteTbOrgActivityById(Long id);

    /**
     * 批量删除组织日常活动开展情况
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbOrgActivityByIds(Long[] ids);
}
