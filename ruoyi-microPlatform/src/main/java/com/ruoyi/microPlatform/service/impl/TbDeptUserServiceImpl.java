package com.ruoyi.microPlatform.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbDeptUserMapper;
import com.ruoyi.microPlatform.domain.TbDeptUser;
import com.ruoyi.microPlatform.service.ITbDeptUserService;

/**
 * 社区干部信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbDeptUserServiceImpl implements ITbDeptUserService {
    @Autowired
    private TbDeptUserMapper tbDeptUserMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询社区干部信息
     *
     * @param id 社区干部信息主键
     * @return 社区干部信息
     */
    @Override
    public TbDeptUser selectTbDeptUserById(Long id) {
        return tbDeptUserMapper.selectTbDeptUserById(id);
    }

    /**
     * 校验社区干部信息是否存在
     *
     * @param tbDeptUser
     * @return boolean
     */
    @Override
    public boolean checkTbDeptUser(TbDeptUser tbDeptUser) {
        TbDeptUser old = tbDeptUserMapper.checkTbDeptUserUnique(tbDeptUser);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询社区干部信息列表
     *
     * @param tbDeptUser 社区干部信息
     * @return 社区干部信息
     */
    @Override
    public List<TbDeptUser> selectTbDeptUserList(TbDeptUser tbDeptUser) {
        return tbDeptUserMapper.selectTbDeptUserList(tbDeptUser);
    }

    @Override
    public Integer selectExportDeptUserCount(TbDeptUser tbDeptUser) {
        List<Long> longs = tbDeptUserMapper.selectExportDeptUserCount(tbDeptUser);
        return longs.size();
    }

    /**
     * 导入社区干部信息
     *
     * @param infos           社区干部信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbDeptUser(List<TbDeptUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入社区干部信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int index = 2;
        int failureNum = 0;

        for (TbDeptUser info : infos) {
            index++;
            try {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getCounty())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属县（市、区） 不可为空"+ "。数据条目：" + index);
                } else {
                    if (StringUtils.isBlank(info.getCountry())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属街道/乡镇 不可为空"+ "。数据条目：" + index);
                    } else {
                        if (StringUtils.isBlank(info.getTown())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属村/社区 不可为空"+ "。数据条目：" + index);
                        } else {
                            if (StringUtils.isBlank(info.getName())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据 干部姓名 不可为空"+ "。数据条目：" + index);
                            } else {
                                if (StringUtils.isBlank(info.getPhone())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 联系方式 不可为空"+ "。数据条目：" + index);
                                } else {
                                    boolean flag = true;
                                    String msg = "";
                                    if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getName() + "社区干部数据"+ "。数据条目：" + index);
                                    }

                                    if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getName() + "社区干部数据"+ "。数据条目：" + index);
                                    }

                                    if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getName() + "社区干部数据"+ "。数据条目：" + index);
                                    }
                                    if (!flag) {
                                        failureNum++;
                                        failureMsg.append(msg);
                                    } else {
                                        SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                        if (ObjectUtil.isEmpty(dept)) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getName() + " 社区干部所属部门未找到！"+ "数据条目：" + index);
                                        } else {
                                            info.setDeptId(dept.getDeptId());
                                            //根据身份证号获取生日信息
                                            if (StringUtils.isNotBlank( info.getIdCard())){
                                                String s = info.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                                                info.setIdCard(s);
                                                Date idCard = DateUtils.getIdCard(s);
                                                if (ObjectUtil.isNotEmpty(idCard)) {
                                                    info.setBirthday(idCard);
                                                }
                                            }

                                            // 验证是否存在这个数据
                                            TbDeptUser old = tbDeptUserMapper.checkTbDeptUserUnique(info);
                                            if (ObjectUtil.isEmpty(old)) {
                                                info.setCreateTime(new Date());
                                                info.setCreateBy(operName);
                                                this.insertTbDeptUser(info);
                                                successNum++;
                                                successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 导入成功");
                                            } else if (isUpdateSupport) {
                                                info.setUpdateBy(operName);
                                                info.setUpdateTime(new Date());
                                                info.setId(old.getId());
                                                this.updateTbDeptUser(info);
                                                successNum++;
                                                successMsg.append("<br/>" + successNum + "、数据 " + info.getName() + " 更新成功");
                                            } else {
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据 " + info.getName() + " 已存在 "+ "。数据条目：" + index);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage() + "。数据条目：" + index);
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增社区干部信息
     *
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    @Override
    public int insertTbDeptUser(TbDeptUser tbDeptUser) {
        return tbDeptUserMapper.insertTbDeptUser(tbDeptUser);
    }

    /**
     * 修改社区干部信息
     *
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    @Override
    public int updateTbDeptUser(TbDeptUser tbDeptUser) {
        return tbDeptUserMapper.updateTbDeptUser(tbDeptUser);
    }

    /**
     * 批量删除社区干部信息
     *
     * @param ids 需要删除的社区干部信息主键
     * @return 结果
     */
    @Override
    public int deleteTbDeptUserByIds(Long[] ids) {
        return tbDeptUserMapper.deleteTbDeptUserByIds(ids);
    }

    /**
     * 删除社区干部信息信息
     *
     * @param id 社区干部信息主键
     * @return 结果
     */
    @Override
    public int deleteTbDeptUserById(Long id) {
        return tbDeptUserMapper.deleteTbDeptUserById(id);
    }

    @Override
    public Integer partyMemberCount() {
        return tbDeptUserMapper.partyMemberCount();
    }

    @Override
    public List<CommonBaseCount> partyMemberGroupSexCount() {
        List<CommonBaseCount> dbCounts = tbDeptUserMapper.partyMemberGroupSexCount();
        HashMap<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) countMap.put(t, cnt == null ? 0 : cnt);
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(3);

        CommonBaseCount male = new CommonBaseCount();
        male.setType(0);
        male.setLable("男");
        male.setCount(countMap.getOrDefault(0, 0));
        res.add(male);

        CommonBaseCount female = new CommonBaseCount();
        female.setType(1);
        female.setLable("女");
        female.setCount(countMap.getOrDefault(1, 0));
        res.add(female);

        CommonBaseCount unknown = new CommonBaseCount();
        unknown.setType(2);
        unknown.setLable("未知");
        unknown.setCount(countMap.getOrDefault(2, 0));
        res.add(unknown);

        return res;
    }

    @Override
    public List<CommonBaseCount> secretaryGroupSexCount() {
        List<CommonBaseCount> dbCounts = tbDeptUserMapper.secretaryGroupSexCount();
        HashMap<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) countMap.put(t, cnt == null ? 0 : cnt);
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(3);

        CommonBaseCount male = new CommonBaseCount();
        male.setType(0);
        male.setLable("男");
        male.setCount(countMap.getOrDefault(0, 0));
        res.add(male);

        CommonBaseCount female = new CommonBaseCount();
        female.setType(1);
        female.setLable("女");
        female.setCount(countMap.getOrDefault(1, 0));
        res.add(female);

        CommonBaseCount unknown = new CommonBaseCount();
        unknown.setType(2);
        unknown.setLable("未知");
        unknown.setCount(countMap.getOrDefault(2, 0));
        res.add(unknown);

        return res;
    }

    @Override
    public List<CommonBaseCount> secretaryGroupPartyCount() {
        List<CommonBaseCount> dbCounts = tbDeptUserMapper.secretaryGroupPartyCount();
        HashMap<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) countMap.put(t, cnt == null ? 0 : cnt);
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(2);

        CommonBaseCount yes = new CommonBaseCount();
        yes.setType(1);
        yes.setLable("党员");
        yes.setCount(countMap.getOrDefault(1, 0));
        res.add(yes);

        CommonBaseCount no = new CommonBaseCount();
        no.setType(0);
        no.setLable("非党员");
        no.setCount(countMap.getOrDefault(0, 0));
        res.add(no);

        return res;
    }

    @Override
    public List<CommonBaseCount> secretaryGroupAgeCount() {
        List<CommonBaseCount> dbCounts = tbDeptUserMapper.secretaryGroupAgeCount();
        HashMap<Integer, Integer> countMap = new HashMap<>();
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if (c == null) continue;
                Integer t = c.getType();
                Integer cnt = c.getCount();
                if (t != null) countMap.put(t, cnt == null ? 0 : cnt);
            }
        }

        List<CommonBaseCount> res = new ArrayList<>(3);

        CommonBaseCount a1 = new CommonBaseCount();
        a1.setType(1);
        a1.setLable("34岁及以下");
        a1.setCount(countMap.getOrDefault(1, 0));
        res.add(a1);

        CommonBaseCount a2 = new CommonBaseCount();
        a2.setType(2);
        a2.setLable("35-60岁");
        a2.setCount(countMap.getOrDefault(2, 0));
        res.add(a2);

        CommonBaseCount a3 = new CommonBaseCount();
        a3.setType(3);
        a3.setLable("60岁以上");
        a3.setCount(countMap.getOrDefault(3, 0));
        res.add(a3);

        return res;
    }

    @Override
    public List<CommonBaseCount> secretaryGroupEducationCount() {
        // Education categories may vary; rely on DB grouping, which outputs lable and count, including '未知' via ifnull
        return tbDeptUserMapper.secretaryGroupEducationCount();
    }
}
