package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbOrgEmployee;

/**
 * 组织情况员工信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface TbOrgEmployeeMapper 
{
    /**
     * 查询组织情况员工信息
     * 
     * @param id 组织情况员工信息主键
     * @return 组织情况员工信息
     */
    public TbOrgEmployee selectTbOrgEmployeeById(Long id);

    /**
     * 校验组织情况员工信息是否唯一
     *
     * @param tbOrgEmployee
     * @return 组织情况员工信息
     */
    public TbOrgEmployee checkTbOrgEmployeeUnique(TbOrgEmployee tbOrgEmployee);

    /**
     * 查询组织情况员工信息列表
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 组织情况员工信息集合
     */
    public List<TbOrgEmployee> selectTbOrgEmployeeList(TbOrgEmployee tbOrgEmployee);

    /**
     * 新增组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    public int insertTbOrgEmployee(TbOrgEmployee tbOrgEmployee);

    /**
     * 修改组织情况员工信息
     * 
     * @param tbOrgEmployee 组织情况员工信息
     * @return 结果
     */
    public int updateTbOrgEmployee(TbOrgEmployee tbOrgEmployee);

    /**
     * 删除组织情况员工信息
     * 
     * @param id 组织情况员工信息主键
     * @return 结果
     */
    public int deleteTbOrgEmployeeById(Long id);

    /**
     * 批量删除组织情况员工信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbOrgEmployeeByIds(Long[] ids);
}
