package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;
import com.ruoyi.microPlatform.service.ITbMerchantAssistantService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 门店店员Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/merchantAssistant")
public class TbMerchantAssistantController extends BaseController {
    @Autowired
    private ITbMerchantAssistantService tbMerchantAssistantService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询门店店员列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbMerchantAssistant tbMerchantAssistant) {
        startPage();
        handle(tbMerchantAssistant);
        List<TbMerchantAssistant> list = tbMerchantAssistantService.selectTbMerchantAssistantList(tbMerchantAssistant);
        return getDataTable(list);
    }

    public void handle(TbMerchantAssistant tbMerchantAssistant) {
        if (!SecurityUtils.isAdmin(getUserId()) && ObjectUtil.isEmpty(tbMerchantAssistant.getMerchantId())) {
            Long gridId = SecurityUtils.getGridId();
            if (ObjectUtil.isNotEmpty(gridId)) {
                tbMerchantAssistant.setGridArr(getLoginUser().getUser().getDrillNo());
            } else {
                // 只能查看相应部门下的小区
                if (ObjectUtil.isEmpty(tbMerchantAssistant.getDeptId()) && ObjectUtil.isEmpty(tbMerchantAssistant.getGridId())) {
                    tbMerchantAssistant.setDeptId(getDataDeptId());
                }
            }
        }
    }

    /**
     * 导出门店店员列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:export')")
    @Log(title = "门店店员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbMerchantAssistant tbMerchantAssistant) {
        if (ObjectUtil.isNotEmpty(tbMerchantAssistant.getIsExportAll()) && tbMerchantAssistant.getIsExportAll() == 0) {
            startPage(pageNum, pageSize);
        }
        handle(tbMerchantAssistant);
        List<TbMerchantAssistant> list = tbMerchantAssistantService.selectTbMerchantAssistantList(tbMerchantAssistant);
        ExcelUtil<TbMerchantAssistant> util = new ExcelUtil<TbMerchantAssistant>(TbMerchantAssistant.class);
        util.exportExcel(response, list, "门店店员数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(HttpServletResponse response, TbMerchantAssistant tbMerchantAssistant) {
        if (ObjectUtil.isNotEmpty(tbMerchantAssistant.getIsExportAll()) && tbMerchantAssistant.getIsExportAll() == 0) {
            startPage();
        }
        handle(tbMerchantAssistant);
        Integer integer = tbMerchantAssistantService.selectTbMerchantAssistantCount(tbMerchantAssistant);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbMerchantAssistant);
    }

    @Log(title = "门店店员", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbMerchantAssistant> util = new ExcelUtil<TbMerchantAssistant>(TbMerchantAssistant.class);
        List<TbMerchantAssistant> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbMerchantAssistantService.importTbMerchantAssistant(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbMerchantAssistant> util = new ExcelUtil<TbMerchantAssistant>(TbMerchantAssistant.class);
        util.exportTemplateExcel(response, "门店店员数据");
    }

    /**
     * 获取门店店员详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbMerchantAssistantService.selectTbMerchantAssistantById(id));
    }

    /**
     * 新增门店店员
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:add')")
    @Log(title = "门店店员", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbMerchantAssistant tbMerchantAssistant) {
        tbMerchantAssistant.setCreateBy(getUsername());
        if (tbMerchantAssistantService.checkTbMerchantAssistant(tbMerchantAssistant)) {
            return error("数据已存在！");
        }
        return toAjax(tbMerchantAssistantService.insertTbMerchantAssistant(tbMerchantAssistant));
    }

    /**
     * 修改门店店员
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:edit')")
    @Log(title = "门店店员", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbMerchantAssistant tbMerchantAssistant) {
        tbMerchantAssistant.setUpdateBy(getUsername());
        if (tbMerchantAssistantService.checkTbMerchantAssistant(tbMerchantAssistant)) {
            return error("数据已存在！");
        }
        return toAjax(tbMerchantAssistantService.updateTbMerchantAssistant(tbMerchantAssistant));
    }

    /**
     * 删除门店店员
     */
    @PreAuthorize("@ss.hasPermi('gridData:merchantAssistant:remove')")
    @Log(title = "门店店员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbMerchantAssistantService.deleteTbMerchantAssistantByIds(ids));
    }
}
