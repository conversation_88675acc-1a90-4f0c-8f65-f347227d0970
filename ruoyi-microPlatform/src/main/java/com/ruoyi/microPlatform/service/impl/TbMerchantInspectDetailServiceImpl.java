package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbMerchantInspectDetailMapper;
import com.ruoyi.microPlatform.domain.TbMerchantInspectDetail;
import com.ruoyi.microPlatform.service.ITbMerchantInspectDetailService;

/**
 * 门店巡查基础检查项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbMerchantInspectDetailServiceImpl implements ITbMerchantInspectDetailService 
{
    @Autowired
    private TbMerchantInspectDetailMapper tbMerchantInspectDetailMapper;

    /**
     * 查询门店巡查基础检查项目
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 门店巡查基础检查项目
     */
    @Override
    public TbMerchantInspectDetail selectTbMerchantInspectDetailById(Integer id)
    {
        return tbMerchantInspectDetailMapper.selectTbMerchantInspectDetailById(id);
    }

    /**
     * 校验门店巡查基础检查项目是否存在
     *
     * @param tbMerchantInspectDetail
     * @return boolean
     */
    @Override
    public boolean checkTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail){
        TbMerchantInspectDetail old = tbMerchantInspectDetailMapper.checkTbMerchantInspectDetailUnique(tbMerchantInspectDetail);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询门店巡查基础检查项目列表
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 门店巡查基础检查项目
     */
    @Override
    public List<TbMerchantInspectDetail> selectTbMerchantInspectDetailList(TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        return tbMerchantInspectDetailMapper.selectTbMerchantInspectDetailList(tbMerchantInspectDetail);
    }

    /**
     * 导入门店巡查基础检查项目
     *
     * @param infos       门店巡查基础检查项目列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantInspectDetail(List<TbMerchantInspectDetail> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入门店巡查基础检查项目数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbMerchantInspectDetail info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbMerchantInspectDetail old = tbMerchantInspectDetailMapper.checkTbMerchantInspectDetailUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbMerchantInspectDetailMapper.insertTbMerchantInspectDetail(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbMerchantInspectDetailMapper.updateTbMerchantInspectDetail(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    @Override
    public int insertTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        return tbMerchantInspectDetailMapper.insertTbMerchantInspectDetail(tbMerchantInspectDetail);
    }

    /**
     * 修改门店巡查基础检查项目
     * 
     * @param tbMerchantInspectDetail 门店巡查基础检查项目
     * @return 结果
     */
    @Override
    public int updateTbMerchantInspectDetail(TbMerchantInspectDetail tbMerchantInspectDetail)
    {
        return tbMerchantInspectDetailMapper.updateTbMerchantInspectDetail(tbMerchantInspectDetail);
    }

    /**
     * 批量删除门店巡查基础检查项目
     * 
     * @param ids 需要删除的门店巡查基础检查项目主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantInspectDetailByIds(Integer[] ids)
    {
        return tbMerchantInspectDetailMapper.deleteTbMerchantInspectDetailByIds(ids);
    }

    /**
     * 删除门店巡查基础检查项目信息
     * 
     * @param id 门店巡查基础检查项目主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantInspectDetailById(Integer id)
    {
        return tbMerchantInspectDetailMapper.deleteTbMerchantInspectDetailById(id);
    }


}
