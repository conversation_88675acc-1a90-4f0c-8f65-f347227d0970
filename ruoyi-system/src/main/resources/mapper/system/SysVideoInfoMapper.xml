<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysVideoInfoMapper">
    
    <resultMap type="SysVideoInfo" id="SysVideoInfoResult">
        <result property="id"    column="id"    />
        <result property="dataId"    column="data_id"    />
        <result property="webAddr"    column="web_addr"    />
        <result property="localAddr"    column="local_addr"    />
        <result property="dateTime"    column="date_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysVideoInfoVo">
        select id, data_id, web_addr, local_addr, date_time, remark from sys_video_info
    </sql>

    <select id="selectSysVideoInfoList" parameterType="SysVideoInfo" resultMap="SysVideoInfoResult">
        <include refid="selectSysVideoInfoVo"/>
        <where>  
            <if test="dataId != null "> and data_id = #{dataId}</if>
            <if test="webAddr != null  and webAddr != ''"> and web_addr = #{webAddr}</if>
            <if test="localAddr != null  and localAddr != ''"> and local_addr = #{localAddr}</if>
            <if test="dateTime != null "> and date_time = #{dateTime}</if>
        </where>
    </select>
    
    <select id="selectSysVideoInfoById" parameterType="Long" resultMap="SysVideoInfoResult">
        <include refid="selectSysVideoInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysVideoInfo" parameterType="SysVideoInfo" useGeneratedKeys="true" keyProperty="id">
        insert into sys_video_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataId != null">data_id,</if>
            <if test="webAddr != null">web_addr,</if>
            <if test="localAddr != null">local_addr,</if>
            <if test="dateTime != null">date_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataId != null">#{dataId},</if>
            <if test="webAddr != null">#{webAddr},</if>
            <if test="localAddr != null">#{localAddr},</if>
            <if test="dateTime != null">#{dateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysVideoInfo" parameterType="SysVideoInfo">
        update sys_video_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataId != null">data_id = #{dataId},</if>
            <if test="webAddr != null">web_addr = #{webAddr},</if>
            <if test="localAddr != null">local_addr = #{localAddr},</if>
            <if test="dateTime != null">date_time = #{dateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysVideoInfoById" parameterType="Long">
        delete from sys_video_info where id = #{id}
    </delete>

    <delete id="deleteSysVideoInfoByIds" parameterType="String">
        delete from sys_video_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>