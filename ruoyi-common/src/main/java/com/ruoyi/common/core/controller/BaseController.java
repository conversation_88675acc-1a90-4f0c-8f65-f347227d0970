package com.ruoyi.common.core.controller;

import java.beans.PropertyEditorSupport;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.sql.SqlUtil;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    @InitBinder
    public void setAllowedFields(WebDataBinder dataBinder) {
        String[] abd = new String[]{"class.*", "Class.*", "*.class.*", "*.Class.*"};
        dataBinder.setDisallowedFields(abd);
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageUtils.startPage();
    }

    protected void startPage(Integer pageNum, Integer pageSize) {
        PageUtils.startPage(pageNum, pageSize).setReasonable(ServletUtils.getParameterToBool("reasonable"));
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 清理分页的线程变量
     */
    protected void clearPage() {
        PageUtils.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    protected TableDataInfo getDataTable3(List<?> list, List<?> list2) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list2).getTotal());
        return rspData;
    }

    protected TableDataInfo getDataTable2(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setData(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(Object data) {
        return AjaxResult.success(data);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 返回警告消息
     */
    public AjaxResult warn(String message) {
        return AjaxResult.warn(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }

    /**
     * 获取用户缓存信息
     */
    public LoginUser getLoginUser() {
        return SecurityUtils.getLoginUser();
    }

    /**
     * 获取登录用户id
     */
    public Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取登录部门id
     */
    public Long getDeptId() {
        return getLoginUser().getDeptId();
    }

    /**
     * 查看数据权限部门
     *
     * @return
     */
    public Long getDataDeptId() {
        if (getLoginUser().getUser().getDept().getDeptLevel() == 8) {
            return getLoginUser().getUser().getDept().getParentId();
        } else {
            return getLoginUser().getDeptId();
        }
    }

    public String getDataDeptName() {
        if (getLoginUser().getUser().getDept().getDeptLevel() == 8) {
            // 网格获取上一级名称
            Long parentId = getLoginUser().getUser().getDept().getParentId();
            SysDept cacheObject = SpringUtils.getBean(RedisCache.class).getCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + parentId);
            if (ObjectUtil.isNotEmpty(cacheObject)){
                return cacheObject.getDeptName() + "+" + cacheObject.getPhone()+ "+" + parentId;
            } else {
                return "";
            }
        } else {
            return getLoginUser().getUser().getDept().getDeptName() + "+" + getLoginUser().getUser().getDept().getPhone() + "+" + getLoginUser().getDeptId();
        }
    }

    protected AjaxResult exportDataMethod(Integer integer, String exportData, Object object){
        if (ObjectUtil.isEmpty(integer) || integer.intValue() == 0) {
            return success(true);
        } else {
            // 当前用户层级
            Integer deptLevel = getLoginUser().getUser().getDept().getDeptLevel();
            if (deptLevel != 4) {
                exportData = StringUtils.isBlank(exportData) ? "0" : exportData;
                if (integer.intValue() <= Integer.parseInt(exportData)) {
                    return success(true);
                } else {
                    AjaxResult success = success(false);
                    success.put("msg", "用户允许导出数据超过限制，请向上级部门提交导出申请！");
                    success.put("count", integer);
                    success.put("object", object);
                    return success;
                }
            }
            return success(true);
        }
    }

    /**
     * 获取登录用户名
     */
    public String getUsername() {
        return getLoginUser().getUsername();
    }

    public String getNickName() {
        return getLoginUser().getUser().getNickName();
    }

    public SysDept getDept(Long deptId) {
        SysDept cacheObject = SpringUtils.getBean(RedisCache.class).getCacheObject(CacheConstants.DEPT_ANCESTORS_KEY + deptId);
        return cacheObject;
    }
}
