package com.ruoyi.common.constant;

/**
 * 缓存的key 常量
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";
    /**
     * 部门的组级名称
     */
    public static final String DEPT_ANCESTORS_KEY = "dept_ancestors:";
    /**
     * 根据组级名称+本级名称，获取部门ID
     */
    public static final String DEPT_ANCESTORS_NAME_KEY = "dept_ancestors_name:";

    public static final String PERSON_TYPE_NAME_KEY = "person_type_name:";
    /**
     * 人员我的网格信息
     */
    public static final String USER_GRID_KEY = "user_grid_key:";
}
