<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.PbPartyOrgMapper">

    <resultMap id="PbPartyOrgResult" type="PbPartyOrg">
        <result property="id" column="id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="orgType" column="org_type"/>
        <result property="orgLevel" column="org_level"/>
        <result property="parentOrgId" column="parent_org_id"/>
        <result property="parentPath" column="parent_path"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="establishDate" column="establish_date"/>
        <result property="approveUnit" column="approve_unit"/>
        <result property="activityCenter" column="activity_center"/>
        <result property="memberCount" column="member_count"/>
        <result property="leaderName" column="leader_name"/>
        <result property="leaderPhone" column="leader_phone"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPbPartyOrgVo">
        select id, org_code, org_name, org_type, org_level, parent_org_id, parent_path,
               dept_id, county, country, town, establish_date, approve_unit, activity_center,
               member_count, leader_name, leader_phone, status, remark, create_by, create_time, update_by, update_time
        from pb_party_org
    </sql>

    <sql id="queryPbPartyOrgVo">
        <where>
            <if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
            <if test="orgName != null and orgName != ''">and org_name like concat('%', #{orgName}, '%')</if>
            <if test="orgType != null and orgType != ''">and org_type = #{orgType}</if>
            <if test="orgLevel != null">and org_level = #{orgLevel}</if>
            <if test="parentOrgId != null">and parent_org_id = #{parentOrgId}</if>
            <if test="deptId != null">and dept_id = #{deptId}</if>
            <if test="county != null and county != ''">and county = #{county}</if>
            <if test="country != null and country != ''">and country = #{country}</if>
            <if test="town != null and town != ''">and town = #{town}</if>
            <if test="leaderName != null and leaderName != ''">and leader_name like concat('%', #{leaderName}, '%')</if>
            <if test="leaderPhone != null and leaderPhone != ''">and leader_phone = #{leaderPhone}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="beginTime != null and beginTime != ''">and date_format(create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')</if>
            <if test="endTime != null and endTime != ''">and date_format(create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')</if>
        </where>
    </sql>

    <select id="selectPbPartyOrgList" parameterType="PbPartyOrg" resultMap="PbPartyOrgResult">
        <include refid="selectPbPartyOrgVo"/>
        <include refid="queryPbPartyOrgVo"/>
        order by id desc
    </select>

    <select id="selectPbPartyOrgById" parameterType="Long" resultMap="PbPartyOrgResult">
        <include refid="selectPbPartyOrgVo"/>
        where id = #{id}
    </select>

    <select id="checkPbPartyOrgUnique" parameterType="PbPartyOrg" resultMap="PbPartyOrgResult">
        select id from pb_party_org where org_code = #{orgCode}
        <if test="id != null">and id != #{id}</if>
    </select>
    <select id="pbPartyOrgGroupOrgLevel" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select org_level type, count(*) as count from pb_party_org group by org_level
    </select>
    <select id="pbPartyOrgGroupOrgType" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select org_type lable, count(*) as count from pb_party_org group by org_type
    </select>

    <insert id="insertPbPartyOrg" parameterType="PbPartyOrg" useGeneratedKeys="true" keyProperty="id">
        insert into pb_party_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">org_code,</if>
            <if test="orgName != null">org_name,</if>
            <if test="orgType != null">org_type,</if>
            <if test="orgLevel != null">org_level,</if>
            <if test="parentOrgId != null">parent_org_id,</if>
            <if test="parentPath != null">parent_path,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="establishDate != null">establish_date,</if>
            <if test="approveUnit != null">approve_unit,</if>
            <if test="activityCenter != null">activity_center,</if>
            <if test="memberCount != null">member_count,</if>
            <if test="leaderName != null">leader_name,</if>
            <if test="leaderPhone != null">leader_phone,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">#{orgCode},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="orgType != null">#{orgType},</if>
            <if test="orgLevel != null">#{orgLevel},</if>
            <if test="parentOrgId != null">#{parentOrgId},</if>
            <if test="parentPath != null">#{parentPath},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="establishDate != null">#{establishDate},</if>
            <if test="approveUnit != null">#{approveUnit},</if>
            <if test="activityCenter != null">#{activityCenter},</if>
            <if test="memberCount != null">#{memberCount},</if>
            <if test="leaderName != null">#{leaderName},</if>
            <if test="leaderPhone != null">#{leaderPhone},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePbPartyOrg" parameterType="PbPartyOrg">
        update pb_party_org
        <trim prefix="SET" suffixOverrides=",">
            <if test="orgCode != null">org_code = #{orgCode},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="orgType != null">org_type = #{orgType},</if>
            <if test="orgLevel != null">org_level = #{orgLevel},</if>
            <if test="parentOrgId != null">parent_org_id = #{parentOrgId},</if>
            <if test="parentPath != null">parent_path = #{parentPath},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="establishDate != null">establish_date = #{establishDate},</if>
            <if test="approveUnit != null">approve_unit = #{approveUnit},</if>
            <if test="activityCenter != null">activity_center = #{activityCenter},</if>
            <if test="memberCount != null">member_count = #{memberCount},</if>
            <if test="leaderName != null">leader_name = #{leaderName},</if>
            <if test="leaderPhone != null">leader_phone = #{leaderPhone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePbPartyOrgById" parameterType="Long">
        delete from pb_party_org where id = #{id}
    </delete>

    <delete id="deletePbPartyOrgByIds" parameterType="String">
        delete from pb_party_org where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
