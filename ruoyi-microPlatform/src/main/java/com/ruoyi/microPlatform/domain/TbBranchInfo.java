package com.ruoyi.microPlatform.domain;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 机关企事业单位（支部）对象 tb_branch_info
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */

public class TbBranchInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 支部名称 */
    @Excel(name = "支部名称")
    private String branchName;

    /** 支部书记 */
    @Excel(name = "支部书记")
    private String secretaryName;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    //姓名
    private String name;



    /** 支部关联网格信息 */
    private List<TbBranchGridInfo> tbBranchGridInfoList;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;



    /** 所属县区 */
    @Excel(name = "县（市、区）", type = Excel.Type.IMPORT)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "乡镇（街道）", type = Excel.Type.IMPORT)
    private String country;

    /** 所属村镇/社区 */
    @Excel(name = "社区", type = Excel.Type.IMPORT)
    private String town;

    /** 网格编码 */
    @Excel(name = "网格编码", type = Excel.Type.IMPORT)
    private String gridName;

    /** 所属网格ID */
    @Excel(name = "所属网格ID", type = Excel.Type.IMPORT)
    private Long gridId;

    /** 网格范围 */
    @Excel(name = "网格范围", type = Excel.Type.IMPORT)
    private String gridRange;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public String getGridRange() {
        return gridRange;
    }

    public void setGridRange(String gridRange) {
        this.gridRange = gridRange;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBranchName(String branchName) 
    {
        this.branchName = branchName;
    }

    public String getBranchName() 
    {
        return branchName;
    }
    public void setSecretaryName(String secretaryName) 
    {
        this.secretaryName = secretaryName;
    }

    public String getSecretaryName() 
    {
        return secretaryName;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    public List<TbBranchGridInfo> getTbBranchGridInfoList()
    {
        return tbBranchGridInfoList;
    }

    public void setTbBranchGridInfoList(List<TbBranchGridInfo> tbBranchGridInfoList)
    {
        this.tbBranchGridInfoList = tbBranchGridInfoList;

    }








    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("branchName", getBranchName())
            .append("secretaryName", getSecretaryName())
            .append("phone", getPhone())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("tbBranchGridInfoList", getTbBranchGridInfoList())
            .toString();
    }
}
