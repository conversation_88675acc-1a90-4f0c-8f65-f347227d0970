<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeHeartPromiseMapper">

    <resultMap type="SeHeartPromise" id="SeHeartPromiseResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="feedbackIssue" column="feedback_issue"/>
        <result property="communicateTime" column="communicate_time"/>
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="phone" column="phone"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="addrCounty" column="addr_county"/>
        <result property="addrCountry" column="addr_country"/>
        <result property="addr" column="addr"/>
        <result property="status" column="status"/>
        <result property="handleResult" column="handle_result"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectSeHeartPromiseVo">
        select id, dept_id, county, country, town, grid_name, feedback_issue, communicate_time, user_id, nick_name, phone, lat, lng,
        province, city, addr_county, addr_country, addr, status, handle_result, create_time from se_heart_promise
    </sql>

    <select id="selectSeHeartPromiseList" parameterType="SeHeartPromise" resultMap="SeHeartPromiseResult">
        <include refid="selectSeHeartPromiseVo"/>
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_heart_promise.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="feedbackIssue != null  and feedbackIssue != ''">and feedback_issue = #{feedbackIssue}</if>
            <if test="communicateTime != null  and communicateTime != ''">and communicate_time = #{communicateTime}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="lat != null  and lat != ''">and lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and lng = #{lng}</if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="addrCounty != null  and addrCounty != ''">and addr_county = #{addrCounty}</if>
            <if test="addrCountry != null  and addrCountry != ''">and addr_country = #{addrCountry}</if>
            <if test="addr != null  and addr != ''">and addr = #{addr}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="handleResult != null  and handleResult != ''">and handle_result = #{handleResult}</if>
        </where>
    </select>

    <select id="selectSeHeartPromiseById" parameterType="Long" resultMap="SeHeartPromiseResult">
        <include refid="selectSeHeartPromiseVo"/>
        where id = #{id}
    </select>

    <select id="checkSeHeartPromiseUnique" parameterType="SeHeartPromise" resultMap="SeHeartPromiseResult">
        select id from se_heart_promise where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeHeartPromise" parameterType="SeHeartPromise" useGeneratedKeys="true" keyProperty="id">
        insert into se_heart_promise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="feedbackIssue != null">feedback_issue,</if>
            <if test="communicateTime != null">communicate_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="phone != null">phone,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="addrCounty != null">addr_county,</if>
            <if test="addrCountry != null">addr_country,</if>
            <if test="addr != null">addr,</if>
            <if test="status != null">status,</if>
            <if test="handleResult != null">handle_result,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="feedbackIssue != null">#{feedbackIssue},</if>
            <if test="communicateTime != null">#{communicateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="phone != null">#{phone},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="addrCounty != null">#{addrCounty},</if>
            <if test="addrCountry != null">#{addrCountry},</if>
            <if test="addr != null">#{addr},</if>
            <if test="status != null">#{status},</if>
            <if test="handleResult != null">#{handleResult},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateSeHeartPromise" parameterType="SeHeartPromise">
        update se_heart_promise
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="feedbackIssue != null">feedback_issue = #{feedbackIssue},</if>
            <if test="communicateTime != null">communicate_time = #{communicateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="addrCounty != null">addr_county = #{addrCounty},</if>
            <if test="addrCountry != null">addr_country = #{addrCountry},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handleResult != null">handle_result = #{handleResult},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeHeartPromiseById" parameterType="Long">
        delete from se_heart_promise where id = #{id}
    </delete>

    <delete id="deleteSeHeartPromiseByIds" parameterType="String">
        delete from se_heart_promise where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>