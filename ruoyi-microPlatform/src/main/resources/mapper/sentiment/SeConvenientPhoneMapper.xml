<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeConvenientPhoneMapper">

    <resultMap type="SeConvenientPhone" id="SeConvenientPhoneResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="type" column="type"/>
        <result property="icon" column="icon"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="phone" column="phone"/>
        <result property="image" column="image"/>
        <result property="status" column="status"/>
        <result property="auditUser" column="audit_user"/>
        <result property="auditTime" column="audit_time"/>
        <result property="addType" column="add_type"/>
        <result property="createId" column="create_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="auditResult"    column="audit_result"    />
        <result property="auditReason"    column="audit_reason"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSeConvenientPhoneVo">
        select id, dept_id, county, country, town, grid_name, name,type, icon, lat, lng, phone, image, status, audit_user, audit_time, add_type, create_id, create_by, create_time, update_by, update_time, audit_result, audit_reason,remark from se_convenient_phone
    </sql>

    <select id="selectSeConvenientPhoneList" parameterType="SeConvenientPhone" resultMap="SeConvenientPhoneResult">
        <include refid="selectSeConvenientPhoneVo"/>
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_convenient_phone.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''">and type like concat('%', #{type}, '%')</if>
            <if test="icon != null  and icon != ''">and icon = #{icon}</if>
            <if test="lat != null  and lat != ''">and lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and lng = #{lng}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="image != null  and image != ''">and image = #{image}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="auditUser != null  and auditUser != ''">and audit_user = #{auditUser}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="addType != null ">and add_type = #{addType}</if>
            <if test="createId != null ">and create_id = #{createId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectSeConvenientPhoneById" parameterType="Long" resultMap="SeConvenientPhoneResult">
        <include refid="selectSeConvenientPhoneVo"/>
        where id = #{id}
    </select>

    <select id="checkSeConvenientPhoneUnique" parameterType="SeConvenientPhone" resultMap="SeConvenientPhoneResult">
        select id from se_convenient_phone where name = #{name}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeConvenientPhone" parameterType="SeConvenientPhone" useGeneratedKeys="true" keyProperty="id">
        insert into se_convenient_phone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="icon != null">icon,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="phone != null">phone,</if>
            <if test="image != null">image,</if>
            <if test="status != null">status,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="addType != null">add_type,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="auditReason != null">audit_reason,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="icon != null">#{icon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="phone != null">#{phone},</if>
            <if test="image != null">#{image},</if>
            <if test="status != null">#{status},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="addType != null">#{addType},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="auditReason != null">#{auditReason},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSeConvenientPhone" parameterType="SeConvenientPhone">
        update se_convenient_phone
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="image != null">image = #{image},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="addType != null">add_type = #{addType},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeConvenientPhoneById" parameterType="Long">
        delete from se_convenient_phone where id = #{id}
    </delete>

    <delete id="deleteSeConvenientPhoneByIds" parameterType="String">
        delete from se_convenient_phone where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectSubmitCount" parameterType="SeConvenientPhone" resultType="map">
        select count(*) as submitCount, max(create_time) as submitTime from se_convenient_phone
        where user_id = #{userId}
    </select>
</mapper>