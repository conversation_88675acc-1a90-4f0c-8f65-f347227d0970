package com.ruoyi.microPlatform.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import com.ruoyi.microPlatform.mapper.TbMerchantAssistantMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbMerchantInfoMapper;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;
import com.ruoyi.microPlatform.service.ITbMerchantInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 门店/商户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbMerchantInfoServiceImpl implements ITbMerchantInfoService {
    @Autowired
    private TbMerchantInfoMapper tbMerchantInfoMapper;
    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TbMerchantAssistantMapper merchantAssistantMapper;

    /**
     * 查询门店/商户信息
     *
     * @param id 门店/商户信息主键
     * @return 门店/商户信息
     */
    @Override
    public TbMerchantInfo selectTbMerchantInfoById(Long id) {
        return tbMerchantInfoMapper.selectTbMerchantInfoById(id);
    }

    /**
     * 校验门店/商户信息是否存在
     *
     * @param tbMerchantInfo
     * @return boolean
     */
    @Override
    public boolean checkTbMerchantInfo(TbMerchantInfo tbMerchantInfo) {
        TbMerchantInfo old = tbMerchantInfoMapper.checkTbMerchantInfoUnique(tbMerchantInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询门店/商户信息列表
     *
     * @param tbMerchantInfo 门店/商户信息
     * @return 门店/商户信息
     */
    @Override
    public List<TbMerchantInfo> selectTbMerchantInfoList(TbMerchantInfo tbMerchantInfo) {
        return tbMerchantInfoMapper.selectTbMerchantInfoList(tbMerchantInfo);
    }

    @Override
    public Integer selectTbMerchantInfoCount(TbMerchantInfo tbMerchantInfo) {
        List<Long> longs = tbMerchantInfoMapper.selectTbMerchantInfoCount(tbMerchantInfo);
        return longs.size();
    }

    /**
     * 导入门店/商户信息
     *
     * @param infos           门店/商户信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Transactional
    public String importTbMerchantInfo(List<TbMerchantInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入门店/商户信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;
        int index = 3;
        StringBuilder memBermsg = new StringBuilder();
        for (TbMerchantInfo info : infos) {
            index++;
            try {
                if (StringUtils.isBlank(info.getMerchantName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据门店名称 不可为空"+ "。数据所在行：" + info.getRowNum());
                } else {
                    if (StringUtils.isBlank(info.getStoreType())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getMerchantName() + " 门店类型 不可为空"+ "。数据所在行：" + info.getRowNum());
                    } else {
                        if (StringUtils.isBlank(info.getLeader())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getMerchantName() + " 门店负责人 不可为空"+ "。数据所在行：" + info.getRowNum());
                        } else {
                            String[] split = userDept.getAncestorsName().split(",");
                            if (StringUtils.isBlank(info.getCounty())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据" + info.getMerchantName() + " 所属县（市、区） 不可为空"+ "。数据所在行：" + info.getRowNum());
                            } else {
                                if (StringUtils.isBlank(info.getCountry())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getMerchantName() + " 所属街道/乡镇 不可为空"+ "。数据所在行：" + info.getRowNum());
                                } else {
                                    if (StringUtils.isBlank(info.getTown())) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getMerchantName() + " 所属村/社区 不可为空"+ "。数据所在行：" + info.getRowNum());
                                    } else {
                                        if (StringUtils.isBlank(info.getGridName())) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 所属网格编码 不可为空"+ "。数据所在行：" + info.getRowNum());
                                        } else {
//                                            if (StringUtils.isBlank(info.getMerchantName())) {
//                                                failureNum++;
//                                                failureMsg.append("<br/>" + failureNum + "、门店数据 店铺名称 不可为空");
//                                            } else {
                                                boolean flag = true;
                                                String msg = "";
                                                if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getMerchantName() + "门店商户"+ "。数据所在行：" + info.getRowNum());
                                                }

                                                if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getMerchantName() + "门店商户"+ "。数据所在行：" + info.getRowNum());
                                                }

                                                if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + info.getGridName() + "的" + info.getMerchantName() + "门店商户"+ "。数据所在行：" + info.getRowNum());
                                                }

                                                if (!flag) {
                                                    failureNum++;
                                                    failureMsg.append(msg);
                                                } else {
                                                    SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                                    if (ObjectUtil.isEmpty(dept)) {
                                                        failureNum++;
                                                        failureMsg.append("<br/>" + failureNum + "、门店数据 " + info.getGridName() + " 门店商户所属村/社区未找到！"+ "数据所在行：" + info.getRowNum());
                                                    } else {
                                                        info.setDeptId(dept.getDeptId());
                                                        // 查询部门下的网格，赋值gridId
                                                        TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                                            setDeptId(info.getDeptId());
                                                            setGridName(info.getGridName());
                                                        }});
                                                        if (ObjectUtil.isEmpty(gridInfo)) {
                                                            failureNum++;
                                                            failureMsg.append("<br/>" + failureNum + "、门店数据 " + info.getMerchantName() + " 门店商户所属网格未找到！"+ "数据所在行：" + info.getRowNum());
                                                        } else {
                                                            info.setGridId(gridInfo.getId());
                                                            info.setSort(gridInfo.getGridSort());
                                                            flag = true;
                                                            // 验证是否存在这个数据
                                                            TbMerchantInfo old = tbMerchantInfoMapper.checkTbMerchantInfoUnique(info);
                                                            if (ObjectUtil.isEmpty(old)) {
                                                                info.setCreateTime(new Date());
                                                                info.setCreateBy(operName);
                                                                tbMerchantInfoMapper.insertTbMerchantInfo(info);
                                                                msg = "";
                                                                if (ObjectUtil.isNotEmpty(info.getList()) && info.getList().size() > 0) {
                                                                    List<TbMerchantAssistant> aaa = new ArrayList<>();
                                                                    for (int i = 0; i < info.getList().size(); i++) {
                                                                        if (i != 0){
                                                                            index++;
                                                                        }
                                                                        TbMerchantAssistant item = info.getList().get(i);
                                                                        if (StringUtils.isNotBlank(item.getName())) {
                                                                            if (StringUtils.isNotBlank( item.getIdCard())){
                                                                                String s = item.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                                                                                item.setIdCard(s);
                                                                                Date idCard = DateUtils.getIdCard(s);
                                                                                if (ObjectUtil.isNotEmpty(idCard)) {
                                                                                    item.setBirthday(idCard);
                                                                                }
                                                                            }
                                                                            aaa.add(item);
                                                                        } else {
                                                                            memBermsg.append("失败");
                                                                            msg += "<br/>  (" + (i +1) + ")、 " + info.getMerchantName() + " 的店员名称不可为空。数据所在行：" + (info.getRowNum() + 1);
                                                                        }
                                                                    }
                                                                    if (aaa.size() > 0) {
                                                                        merchantAssistantMapper.batchTbMerchantAssistant(info.getList(), info);
                                                                    }
                                                                }
                                                                successNum++;
                                                                successMsg.append("<br/>" + successNum + "、门店数据 " + info.getMerchantName() + " 无问题" + (StringUtils.isBlank(msg) ? "" : "，其以下店员数据有问题：" + msg));
                                                            }
//                                                else if (isUpdateSupport) {
//                                                    info.setUpdateBy(operName);
//                                                    info.setUpdateTime(new Date());
//                                                    info.setId(old.getId());
//                                                    tbMerchantInfoMapper.updateTbMerchantInfo(info);
//                                                    successNum++;
//                                                    successMsg.append("<br/>" + successNum + "、数据 " + info.getMerchantName() + " 更新成功");
//                                                }
                                                            else {
                                                                failureNum++;
                                                                failureMsg.append("<br/>" + failureNum + "、门店数据 " + info.getMerchantName() + " 已存在。数据所在行：" + info.getRowNum());
                                                                flag = false;
                                                            }

//                                                if (ObjectUtil.isEmpty(info.getList()) && flag) {
//                                                    // 店员信息不为空，更新或者新增店员信息
//                                                    for (int i = 0; i < info.getList().size(); i++) {
//                                                        TbMerchantAssistant merchantAssistant = info.getList().get(i);
//                                                        merchantAssistant.setMerchantId(info.getId());
//                                                        TbMerchantAssistant oldAssistant = merchantAssistantMapper.checkTbMerchantAssistantUnique(merchantAssistant);
//                                                        if (ObjectUtil.isNotEmpty(oldAssistant)) {
//                                                            merchantAssistant.setId(oldAssistant.getId());
//                                                            // 更新店员
//                                                            merchantAssistantMapper.updateTbMerchantAssistant(merchantAssistant);
//                                                        } else {
//                                                            merchantAssistantMapper.insertTbMerchantAssistant(merchantAssistant);
//                                                        }
//                                                    }
//                                                }
                                                        }


                                                    }

                                                }
//                                            }


                                        }
                                    }
                                }
                            }
                        }
                    }
                }


            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、门店 " + info.getMerchantName() +  "的数据导入失败：";
                failureMsg.append(msg + e.getMessage() + "。所属门店所在行：" + info.getRowNum());
                e.printStackTrace();
            }
        }
        if (failureNum > 0 || StringUtils.isNotBlank(memBermsg.toString())) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条门店数据，其中" + successNum + "条门店数据无问题， " + (StringUtils.isNotBlank(memBermsg) ? "但成员数据有问题！" : "")+ failureNum + " 条门店数据格式不正确，具体信息如下：");
            throw new ServiceException(failureMsg.toString() + successMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增门店/商户信息
     *
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    @Override
    public int insertTbMerchantInfo(TbMerchantInfo tbMerchantInfo) {
        tbMerchantInfo.setCreateTime(DateUtils.getNowDate());
        return tbMerchantInfoMapper.insertTbMerchantInfo(tbMerchantInfo);
    }

    /**
     * 修改门店/商户信息
     *
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    @Override
    public int updateTbMerchantInfo(TbMerchantInfo tbMerchantInfo) {
        tbMerchantInfo.setUpdateTime(DateUtils.getNowDate());
        return tbMerchantInfoMapper.updateTbMerchantInfo(tbMerchantInfo);
    }

    /**
     * 批量删除门店/商户信息
     *
     * @param ids 需要删除的门店/商户信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTbMerchantInfoByIds(Long[] ids) {
        int i = tbMerchantInfoMapper.deleteTbMerchantInfoByIds(ids);
        merchantAssistantMapper.deleteTbMerchantAssistantByMainIds(ids);
        return i;
    }

    /**
     * 删除门店/商户信息信息
     *
     * @param id 门店/商户信息主键
     * @return 结果
     */
    @Override
    public int deleteTbMerchantInfoById(Long id) {
        return tbMerchantInfoMapper.deleteTbMerchantInfoById(id);
    }
}
