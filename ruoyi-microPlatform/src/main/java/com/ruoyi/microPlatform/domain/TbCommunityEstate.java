package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小区物业员对象 tb_community_estate
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public class TbCommunityEstate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    @Excel(name = "所属网格")
    private String gridName;

    /** 所属网格 */
    @Excel(name = "所属网格")
    private Long gridId;

    /** 所属小区 */
    @Excel(name = "所属小区")
    private Long communityId;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private Long deptId;

    /** 关联用户 */
    @Excel(name = "关联用户")
    private Long userId;

    /** 物业姓名 */
    @Excel(name = "物业姓名")
    private String name;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String phone;

    /** 证件照 */
    @Excel(name = "证件照")
    private String idPhoto;

    /** 简介 */
    @Excel(name = "简介")
    private String postDesc;

    /** 人员状态（0正常 1停用） */
    @Excel(name = "人员状态", readConverterExp = "0=正常,1=停用",dictType = "sys_normal_disable", comboReadDict = true)
    private String status;

    /** 职务 */
    @Excel(name = "职务")
    private String postDetail;

    /** 角色(管理员，普通员工) */
    @Excel(name = "角色", readConverterExp = "1=管理员,2=普通员工",dictType = "estate_post_str", comboReadDict = true)
    private Integer postStr;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setCommunityId(Long communityId) 
    {
        this.communityId = communityId;
    }

    public Long getCommunityId() 
    {
        return communityId;
    }
    public void setCommunityName(String communityName) 
    {
        this.communityName = communityName;
    }

    public String getCommunityName() 
    {
        return communityName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setIdPhoto(String idPhoto) 
    {
        this.idPhoto = idPhoto;
    }

    public String getIdPhoto() 
    {
        return idPhoto;
    }
    public void setPostDesc(String postDesc) 
    {
        this.postDesc = postDesc;
    }

    public String getPostDesc() 
    {
        return postDesc;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setPostDetail(String postDetail) 
    {
        this.postDetail = postDetail;
    }

    public String getPostDetail() 
    {
        return postDetail;
    }
    public void setPostStr(Integer postStr)
    {
        this.postStr = postStr;
    }

    public Integer getPostStr()
    {
        return postStr;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("gridId", getGridId())
            .append("communityId", getCommunityId())
            .append("communityName", getCommunityName())
            .append("deptId", getDeptId())
            .append("userId", getUserId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("idPhoto", getIdPhoto())
            .append("postDesc", getPostDesc())
            .append("status", getStatus())
            .append("postDetail", getPostDetail())
            .append("postStr", getPostStr())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
