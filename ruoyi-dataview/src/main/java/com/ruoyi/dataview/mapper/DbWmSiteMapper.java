package com.ruoyi.dataview.mapper;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbWmSite;

/**
 * 水利局涵洞监测站点Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface DbWmSiteMapper 
{
    /**
     * 查询水利局涵洞监测站点
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 水利局涵洞监测站点
     */
    public DbWmSite selectDbWmSiteById(Long id);

    /**
     * 校验水利局涵洞监测站点是否唯一
     *
     * @param dbWmSite
     * @return 水利局涵洞监测站点
     */
    public DbWmSite checkDbWmSiteUnique(DbWmSite dbWmSite);

    /**
     * 查询水利局涵洞监测站点列表
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 水利局涵洞监测站点集合
     */
    public List<DbWmSite> selectDbWmSiteList(DbWmSite dbWmSite);

    /**
     * 新增水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    public int insertDbWmSite(DbWmSite dbWmSite);

    /**
     * 修改水利局涵洞监测站点
     * 
     * @param dbWmSite 水利局涵洞监测站点
     * @return 结果
     */
    public int updateDbWmSite(DbWmSite dbWmSite);

    /**
     * 删除水利局涵洞监测站点
     * 
     * @param id 水利局涵洞监测站点主键
     * @return 结果
     */
    public int deleteDbWmSiteById(Long id);

    /**
     * 批量删除水利局涵洞监测站点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbWmSiteByIds(Long[] ids);

    List<CommonBaseCount> wdDataStatusCount();
}
