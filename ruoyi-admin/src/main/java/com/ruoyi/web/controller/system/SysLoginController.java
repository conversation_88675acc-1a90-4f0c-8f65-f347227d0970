package com.ruoyi.web.controller.system;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.WxAppConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.domain.model.WxLoginBody;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.service.ITbGridInfoService;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.InvalidKeySpecException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController extends BaseController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private WxAppConfig wxAppConfig;
    @Autowired
    private ITbGridInfoService gridInfoService;

    @Autowired
    private ISysConfigService configService;

    private final RestTemplate restTemplate = new RestTemplate();

    @PostMapping("/wxLogin")
    public AjaxResult wxLogin(@RequestBody WxLoginBody wxLoginBody) throws IOException, InvalidKeySpecException, NoSuchAlgorithmException {
        String code = wxLoginBody.getCode();
        //秘钥
        String encryptedIv = wxLoginBody.getEncryptedIv();
        //加密数据
        String encryptedData = wxLoginBody.getEncryptedData();

        String jobUnit = wxLoginBody.getJobUnit();

        String drillNo = wxLoginBody.getDrillNo();

        String userType = wxLoginBody.getUserType();

        //向微信服务器发送请求获取用户信息
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + wxAppConfig.getAppId() + "&secret=" + wxAppConfig.getAppSecret() + "&js_code=" + code + "&grant_type=authorization_code";
        String res = HttpUtil.get(url);
        JSONObject jsonObject = JSONObject.parseObject(res);

        //获取session_key和openid
        String sessionKey = jsonObject.getString("session_key");
        String openid = jsonObject.getString("openid");

        //解密
        String decryptResult = "";
        // 授权登录，需要获取手机号：用户初次注册（应该是）
        if (StringUtils.isNotBlank(encryptedData)) {
            try {
                //如果没有绑定微信开放平台，解析结果是没有unionid的。
                decryptResult = decrypt(sessionKey, encryptedIv, encryptedData);
            } catch (Exception e) {
                e.printStackTrace();
                return error("微信登录获取手机号失败！");
            }
        }
        //如果解析成功,获取token
        Map<String, Object> loginMap = loginService.wxLogin(decryptResult, wxLoginBody.getNickName(), openid, userType, wxLoginBody.getDeptDataId(), drillNo, jobUnit);

        if (ObjectUtil.isNotEmpty(loginMap)) {
            return buildSuccessResult(sessionKey, loginMap, openid);
        } else {
            return buildNeedAuthResult();
        }
    }

    private AjaxResult buildNeedAuthResult() {
        AjaxResult ajax = AjaxResult.success("需要授权登录！");
        ajax.put("code", 400);
        return ajax;
    }

    private AjaxResult buildSuccessResult(String sessionKey, Map<String, Object> loginMap, String openid) {
        String token = (String) loginMap.get("userToken");
        String userTokenId = (String) loginMap.get("userTokenId");

        AjaxResult ajax = AjaxResult.success(sessionKey);
        ajax.put(Constants.TOKEN, token);
        ajax.put("openid", openid);
        ajax.put("userTokenId", userTokenId);
        System.err.println("登陆成功了------");
        return ajax;
    }


    /**
     * AES解密
     */
    private String decrypt(String sessionKey, String encryptedIv, String encryptedData) throws Exception {
        // 转化为字节数组
        byte[] key = Base64.decode(sessionKey);
        byte[] iv = Base64.decode(encryptedIv);
        byte[] encData = Base64.decode(encryptedData);
        // 如果密钥不足16位，那么就补足
        int base = 16;
        if (key.length % base != 0) {
            int groups = key.length / base + (key.length % base != 0 ? 1 : 0);
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(key, 0, temp, 0, key.length);
            key = temp;
        }
        // 如果初始向量不足16位，也补足
        if (iv.length % base != 0) {
            int groups = iv.length / base + (iv.length % base != 0 ? 1 : 0);
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(iv, 0, temp, 0, iv.length);
            iv = temp;
        }

        AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
        String resultStr = null;

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            resultStr = new String(cipher.doFinal(encData), "UTF-8");
        } catch (Exception e) {
            logger.info("解析错误");
            e.printStackTrace();
            throw new ServiceException("解析手机号出现错误！");
        }

        // 解析加密后的字符串
        return resultStr;
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) throws IOException {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("isUpdatePass", false);
        // 生成令牌
        Map loginMap = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid(), loginBody.isFlagCode());
        String token = (String) loginMap.get("userToken");
        String userTokenId = (String) loginMap.get("userTokenId");
        ajax.put(Constants.TOKEN, token);
        ajax.put("userTokenId", userTokenId);
        if (!loginService.checkUpdatePass(loginBody.getUsername())) {
            ajax.put("isUpdatePass", true);
            ajax.put("msg", "请修改密码！");
        }
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        if (!loginUser.getPermissions().equals(permissions)) {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }

        // 岗位ID
        Set<String> posts = postService.getPosts(user.getUserId());
        // 岗位集合
        Set<SysPost> postList = postService.getPostList(user.getUserId());

        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        if (user.getDept().getDeptLevel().intValue() == 8 && StringUtils.isNotBlank(user.getDrillNo())){
            List<TbGridInfo> workGrid = gridInfoService.getWorkGrid(new TbGridInfo() {{
                setGridArr(user.getDrillNo());
            }});
            String collect = workGrid.stream().map(TbGridInfo::getGridName).collect(Collectors.joining(","));
            ajax.put("gridManage", collect);
        }
        ajax.put("roles", roles);
        ajax.put("userType", user.getUserType());  //00 基层管理用户 01需要用签到功能用户
        ajax.put("permissions", permissions);
        ajax.put("gridId", ObjectUtil.isNotEmpty(user.getDept()) ? user.getDept().getGridId() : null);
        ajax.put("isUpdatePass", user.getIsUpdate() == 1 ? false : true);
        ajax.put("updateStatus", false);
        ajax.put("postList", postList);
        ajax.put("posts", posts);
        String dataDeptName = getDataDeptName();
        if (StringUtils.isNotBlank(dataDeptName)) {
            String[] split = dataDeptName.split("\\+");
            ajax.put("deptDataName", split[0]);
            ajax.put("deptDataPhone", split.length > 1 ? split[1] : "");
            ajax.put("deptDataId", split.length > 2 ? split[2] : "");
        } else {
            ajax.put("deptDataName", "");
            ajax.put("deptDataPhone", "");
            ajax.put("deptDataId", "");
        }
        ajax.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
        ajax.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));

        SysDept dept = getDept(getDataDeptId());
        if (dept != null && StringUtils.isNotEmpty(dept.getAncestorsName())){
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2) {
                ajax.put("county", split[1]);
                if (split.length >= 3) {
                    ajax.put("country", split[2]);
                    if (split.length >= 4) {
                        ajax.put("town", split[3]);
                    }
                }
            }

        }

        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    // 检查初始密码是否提醒修改
    public boolean initPasswordIsModify(Date pwdUpdateDate)
    {
        Integer initPasswordModify = Convert.toInt(configService.selectConfigByKey("sys.account.initPasswordModify"));
        return initPasswordModify != null && initPasswordModify == 1 && pwdUpdateDate == null;
    }

    // 检查密码是否过期
    public boolean passwordIsExpiration(Date pwdUpdateDate)
    {
        Integer passwordValidateDays = Convert.toInt(configService.selectConfigByKey("sys.account.passwordValidateDays"));
        if (passwordValidateDays != null && passwordValidateDays > 0)
        {
            if (StringUtils.isNull(pwdUpdateDate))
            {
                // 如果从未修改过初始密码，直接提醒过期
                return true;
            }
            Date nowDate = DateUtils.getNowDate();
            return DateUtils.differentDaysByMillisecond(nowDate, pwdUpdateDate) > passwordValidateDays;
        }
        return false;
    }
}
