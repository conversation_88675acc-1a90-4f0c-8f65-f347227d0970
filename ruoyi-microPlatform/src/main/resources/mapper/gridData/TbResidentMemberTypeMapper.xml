<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbResidentMemberTypeMapper">

    <resultMap type="TbResidentMemberType" id="TbResidentMemberTypeResult">
        <result property="memberId"    column="member_id"    />
        <result property="typeId"    column="type_id"    />
    </resultMap>

    <sql id="selectTbResidentMemberTypeVo">
        select member_id, type_id from tb_resident_member_type
    </sql>

    <select id="selectTbResidentMemberTypeList" parameterType="TbResidentMemberType" resultMap="TbResidentMemberTypeResult">
        <include refid="selectTbResidentMemberTypeVo"/>
        <where>
            <if test="memberId != null "> and member_id = #{memberId}</if>
        </where>
    </select>

    <select id="selectTbResidentMemberTypeByMemberId" parameterType="Long" resultMap="TbResidentMemberTypeResult">
        <include refid="selectTbResidentMemberTypeVo"/>
        where member_id = #{memberId}
    </select>

    <select id="checkTbResidentMemberTypeUnique" parameterType="TbResidentMemberType" resultMap="TbResidentMemberTypeResult">
        select id from tb_resident_member_type where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbResidentMemberType" parameterType="TbResidentMemberType">
        insert into tb_resident_member_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="typeId != null">type_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="typeId != null">#{typeId},</if>
        </trim>
    </insert>

    <update id="updateTbResidentMemberType" parameterType="TbResidentMemberType">
        update tb_resident_member_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeId != null">type_id = #{typeId},</if>
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteTbResidentMemberTypeByMemberId" parameterType="Long">
        delete from tb_resident_member_type where member_id = #{memberId}
    </delete>

    <delete id="deleteTbResidentMemberTypeByMemberIds" parameterType="String">
        delete from tb_resident_member_type where member_id in
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>
    <delete id="deleteTbResidentMemberTypeById" parameterType="Long">
        delete from tb_resident_member_type where type_id = #{id}
    </delete>

    <delete id="deleteTbResidentMemberTypeByIds" parameterType="String">
        delete from tb_resident_member_type where type_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertTbResidentMemberTypeByValues">
        INSERT INTO tb_resident_member_type
        (member_id, type_id)
        VALUES
        <foreach collection="array" item="item" separator=",">
            (#{memberId}, #{item})
        </foreach>
    </insert>
</mapper>
