package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.SeConvenientPhone;

/**
 * 便民电话Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeConvenientPhoneMapper 
{
    /**
     * 查询便民电话
     * 
     * @param id 便民电话主键
     * @return 便民电话
     */
    public SeConvenientPhone selectSeConvenientPhoneById(Long id);

    /**
     * 校验便民电话是否唯一
     *
     * @param seConvenientPhone
     * @return 便民电话
     */
    public SeConvenientPhone checkSeConvenientPhoneUnique(SeConvenientPhone seConvenientPhone);

    /**
     * 查询便民电话列表
     * 
     * @param seConvenientPhone 便民电话
     * @return 便民电话集合
     */
    public List<SeConvenientPhone> selectSeConvenientPhoneList(SeConvenientPhone seConvenientPhone);

    /**
     * 新增便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    public int insertSeConvenientPhone(SeConvenientPhone seConvenientPhone);

    /**
     * 修改便民电话
     * 
     * @param seConvenientPhone 便民电话
     * @return 结果
     */
    public int updateSeConvenientPhone(SeConvenientPhone seConvenientPhone);

    /**
     * 删除便民电话
     * 
     * @param id 便民电话主键
     * @return 结果
     */
    public int deleteSeConvenientPhoneById(Long id);

    /**
     * 批量删除便民电话
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeConvenientPhoneByIds(Long[] ids);


    public Map<String,Object> selectSubmitCount(SeConvenientPhone seConvenientPhone);
}
