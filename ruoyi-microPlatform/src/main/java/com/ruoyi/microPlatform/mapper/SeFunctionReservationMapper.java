package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.HomePage;
import com.ruoyi.microPlatform.domain.SeFunctionReservation;

/**
 * 社区功能室服务预约Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeFunctionReservationMapper 
{
    /**
     * 查询社区功能室服务预约
     * 
     * @param id 社区功能室服务预约主键
     * @return 社区功能室服务预约
     */
    public SeFunctionReservation selectSeFunctionReservationById(Long id);

    /**
     * 校验社区功能室服务预约是否唯一
     *
     * @param seFunctionReservation
     * @return 社区功能室服务预约
     */
    public SeFunctionReservation checkSeFunctionReservationUnique(SeFunctionReservation seFunctionReservation);

    /**
     * 查询社区功能室服务预约列表
     * 
     * @param seFunctionReservation 社区功能室服务预约
     * @return 社区功能室服务预约集合
     */
    public List<SeFunctionReservation> selectSeFunctionReservationList(SeFunctionReservation seFunctionReservation);

    /**
     * 新增社区功能室服务预约
     * 
     * @param seFunctionReservation 社区功能室服务预约
     * @return 结果
     */
    public int insertSeFunctionReservation(SeFunctionReservation seFunctionReservation);

    /**
     * 修改社区功能室服务预约
     * 
     * @param seFunctionReservation 社区功能室服务预约
     * @return 结果
     */
    public int updateSeFunctionReservation(SeFunctionReservation seFunctionReservation);

    /**
     * 删除社区功能室服务预约
     * 
     * @param id 社区功能室服务预约主键
     * @return 结果
     */
    public int deleteSeFunctionReservationById(Long id);

    /**
     * 批量删除社区功能室服务预约
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeFunctionReservationByIds(Long[] ids);
    
    public Map<String,Object> selectSubmitCount(SeFunctionReservation seFunctionReservation);

    int selectSubmitCountByHome(HomePage homePage);

}
