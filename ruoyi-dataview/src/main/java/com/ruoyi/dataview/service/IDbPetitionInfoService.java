package com.ruoyi.dataview.service;

import com.ruoyi.dataview.domain.po.DbPetitionInfo;
import com.ruoyi.dataview.domain.vo.PetitionInfoStatisticsVO;

import java.util.List;

/**
 * 市信访局-上访信息数据Service接口
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface IDbPetitionInfoService {

    /**
     * 查询市信访局-上访信息数据
     *
     * @param id 市信访局-上访信息数据主键
     * @return 市信访局-上访信息数据
     */
    DbPetitionInfo selectDbPetitionInfoById(Long id);

    /**
     * 校验市信访局-上访信息数据是否存在
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 市信访局-上访信息数据
     */
    boolean checkDbPetitionInfo(DbPetitionInfo dbPetitionInfo);

    /**
     * 查询市信访局-上访信息数据列表
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 市信访局-上访信息数据集合
     */
    List<DbPetitionInfo> selectDbPetitionInfoList(DbPetitionInfo dbPetitionInfo);

    /**
     * 导入市信访局-上访信息数据
     *
     * @param infos           市信访局-上访信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    String importDbPetitionInfo(List<DbPetitionInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    int insertDbPetitionInfo(DbPetitionInfo dbPetitionInfo);

    /**
     * 修改市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    int updateDbPetitionInfo(DbPetitionInfo dbPetitionInfo);

    /**
     * 批量删除市信访局-上访信息数据
     *
     * @param ids 需要删除的市信访局-上访信息数据主键集合
     * @return 结果
     */
    int deleteDbPetitionInfoByIds(Long[] ids);

    /**
     * 删除市信访局-上访信息数据信息
     *
     * @param id 市信访局-上访信息数据主键
     * @return 结果
     */
    int deleteDbPetitionInfoById(Long id);

    PetitionInfoStatisticsVO getStatistics();
}