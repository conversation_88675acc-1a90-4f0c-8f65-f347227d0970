<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDeptMapper">

    <resultMap type="SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="ancestorsName" column="ancestor_names"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="nickName" column="nick_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="deptLevel" column="dept_level"/>
        <result property="deptType" column="dept_type"/>
        <result property="code" column="code"/>
        <result property="url" column="url"/>
        <result property="county" column="county"/>
        <result property="hasChildren" column="has_children"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deptIntroduction" column="dept_introduction"/>
        <result property="accessory" column="accessory"/>
        <result property="deptLocation" column="dept_location"/>
        <result property="gridId" column="grid_id"/>

    </resultMap>
    <resultMap type="com.ruoyi.common.core.domain.entity.SysDept" id="DeptUserResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="deptLevel" column="dept_level"/>
        <result property="deptType" column="dept_type"/>
        <result property="code" column="code"/>
        <result property="url" column="url"/>
        <result property="county" column="county"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="hasChildren" column="has_children"/>
        <result property="deptIntroduction" column="dept_introduction"/>
        <result property="accessory" column="accessory"/>
        <result property="deptLocation" column="dept_location"/>
        <result property="gridId" column="grid_id"/>
        <collection property="users" javaType="java.util.List" resultMap="userResult"/>
    </resultMap>
    <resultMap id="userResult" type="com.ruoyi.common.core.domain.entity.SysUser">
        <id property="userId" column="user_id"/>
        <id property="deptId" column="deptId"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
    </resultMap>

    <sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.dept_level, d.code, d.url,d.county,
        d.dept_type, d.status, d.del_flag, d.create_by, d.create_time,d.dept_introduction,d.accessory,d.dept_location,d.grid_id,
        EXISTS (
            SELECT 1 FROM sys_dept d2
            WHERE d2.parent_id = d.dept_id AND d2.del_flag = '0'
        ) AS has_children
        from sys_dept d
    </sql>

    <select id="selectDeptList" parameterType="SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '0'
        <if test="deptId != null and deptId != 0">
            AND d.dept_id = #{deptId}
        </if>
        <if test="parentId != null and parentId != 0">
            AND d.parent_id = #{parentId}
        </if>
        <if test="deptName != null and deptName != ''">
            AND d.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND d.status = #{status}
        </if>
        <if test="ancestors != null and ancestors != ''">
            AND d.ancestors = #{ancestors}
        </if>
        <if test="deptLevel != null and deptLevel == 4">
            AND d.dept_level &lt;= '5'
        </if>
        <if test="deptLevel != null and deptLevel != 4">
            AND d.dept_level = #{deptLevel}
        </if>
        <if test="code != null and code != ''">
            AND d.code = #{code}
        </if>
        <if test="notTable != null">
            AND d.dept_level != 8
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by d.parent_id, d.order_num
    </select>

    <select id="selectTownList" parameterType="SysDept" resultMap="SysDeptResult">
        SELECT
        d.dept_id,
        CONCAT(IFNULL(p.dept_name, ''),'-', d.dept_name ) AS dept_name
        FROM
        sys_dept d
        LEFT JOIN
        sys_dept p ON d.parent_id = p.dept_id
        WHERE d.dept_level = 7
        <if test="county != null and county != ''">
            and d.county = #{county}
        </if>
    </select>

    <select id="selectDeptListByRoleId" resultType="Long">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status,d.dept_level, d.code, d.url,d.county,
        d.dept_type,d.dept_introduction,d.accessory,d.dept_location,d.grid_id,
			(select dept_name from sys_dept where dept_id = d.parent_id) parent_name
		from sys_dept d
		where d.dept_id = #{deptId}
	</select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>

    <select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '0' and find_in_set(#{deptId}, ancestors)
	</select>

    <select id="checkDeptNameUnique" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.dept_name=#{deptName} and d.parent_id = #{parentId} and d.del_flag = '0' limit 1
    </select>

    <insert id="insertDept" parameterType="SysDept" useGeneratedKeys="true" keyProperty="deptId">
        insert into sys_dept(
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="parentId != null and parentId != 0">parent_id,</if>
        <if test="deptName != null and deptName != ''">dept_name,</if>
        <if test="ancestors != null and ancestors != ''">ancestors,</if>
        <if test="orderNum != null">order_num,</if>
        <if test="leader != null and leader != ''">leader,</if>
        <if test="phone != null and phone != ''">phone,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="status != null">status,</if>
        <if test="deptLevel != null">dept_level,</if>
        <if test="deptType != null">dept_type,</if>
        <if test="code != null">code,</if>
        <if test="url != null">url,</if>
        <if test="county != null">county,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="deptIntroduction != null and deptIntroduction != ''">dept_introduction,</if>
        <if test="accessory != null and accessory != ''">accessory,</if>
        <if test="deptLocation != null and deptLocation != ''">dept_location,</if>
        <if test="gridId != null ">grid_id,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="parentId != null and parentId != 0">#{parentId},</if>
        <if test="deptName != null and deptName != ''">#{deptName},</if>
        <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        <if test="orderNum != null">#{orderNum},</if>
        <if test="leader != null and leader != ''">#{leader},</if>
        <if test="phone != null and phone != ''">#{phone},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="status != null">#{status},</if>
        <if test="deptLevel != null">#{deptLevel},</if>
        <if test="deptType != null">#{deptType},</if>
        <if test="code != null">#{code},</if>
        <if test="url != null">#{url},</if>
        <if test="county != null">#{county},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="deptIntroduction != null and deptIntroduction != ''">#{deptIntroduction},</if>
        <if test="accessory != null and accessory != ''">#{accessory},</if>
        <if test="deptLocation != null and deptLocation != ''">#{deptLocation},</if>
        <if test="gridId != null">#{gridId},</if>
        sysdate()
        )
    </insert>

    <update id="updateDept" parameterType="SysDept">
        update sys_dept
        <set>
            <if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="deptLevel != null">dept_level = #{deptLevel},</if>
            <if test="county != null">county = #{county},</if>
            <if test="deptType != null and deptType != ''">dept_type = #{deptType},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="url != null and url != ''">url = #{url},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="deptIntroduction != null and deptIntroduction != ''">dept_introduction = #{deptIntroduction},</if>
            <if test="accessory != null and accessory != ''">accessory = #{accessory},</if>
            <if test="deptLocation != null and deptLocation != ''">dept_location = #{deptLocation},</if>

            update_time = sysdate()
        </set>
        where dept_id = #{deptId}
    </update>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update sys_dept set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case dept_id" close="end">
            when #{item.deptId} then #{item.ancestors}
        </foreach>
        where dept_id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal" parameterType="Long">
        update sys_dept set status = '0' where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <delete id="deleteDeptById" parameterType="Long">
		delete from sys_dept where dept_id = #{deptId}
	</delete>

    <select id="selectDeptByAncestors" parameterType="String" resultMap="SysDeptResult">
		select dept_name, dept_id
        from sys_dept
		where dept_id in (#{ancestors})
	</select>

    <update id="updateDeptStatus" parameterType="com.ruoyi.common.core.domain.entity.SysDept">
        update sys_dept
        <set>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where dept_id in (${ancestors})
    </update>

    <select id="selectByDeptName" resultMap="SysDeptResult">
	select dept_id,ancestors
        from sys_dept where dept_name = #{deptName} limit 1
	</select>

    <select id="selectNameById" parameterType="long" resultType="string">
		select dept_name from sys_dept where dept_id = #{id}
	</select>

    <select id="getDeptUser" resultMap="DeptUserResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name,u.user_id,u.user_name,u.nick_name,d.dept_level,d.code, d.url, d.county,
        d.dept_type, u.dept_id as deptId, d.order_num, d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,
               d.dept_introduction,d.accessory,d.dept_location
        from sys_dept d left join sys_user u
        on d.dept_id = u.dept_id where d.del_flag = '0' order by d.parent_id, d.order_num
	</select>

    <select id="selectList" parameterType="string" resultMap="SysDeptResult">
		select cn_name as phone, code as email from prevention_fever_national
		where parent_code = #{parentCode}
	</select>

    <select id="selectDeptAncestorsCache" resultMap="SysDeptResult">
		WITH RECURSIVE dept_tree AS (
			-- 基础情况：选择所有根部门，并将它们的 ancestors 列初始化为它们自己的 dept_id 和 dept_name
			SELECT
				dept_id,
				parent_id,
				dept_name,
				dept_level,
				phone,
				CAST(dept_id AS CHAR(200)) AS ancestors,
				CAST(dept_name AS CHAR(200)) AS ancestor_names
			FROM
				sys_dept
			WHERE
				parent_id = 0 AND dept_level &lt; 8
			UNION ALL
			-- 递归情况：将当前部门表与之前的结果进行连接，以找到每个部门的父部门，并更新 ancestors 列和 ancestor_names 列
			SELECT
				d.dept_id,
				d.parent_id,
				d.dept_name,
				d.dept_level,
				d.phone,
				CONCAT(t.ancestors, ',', d.dept_id) AS ancestors,
				CONCAT(t.ancestor_names, ',', d.dept_name) AS ancestor_names
			FROM
				sys_dept d
			INNER JOIN
				dept_tree t ON d.parent_id = t.dept_id
			WHERE
				d.dept_level &lt; 8
		)
		-- 最终选择：从递归结果中选择 dept_id、dept_name、ancestors 和 ancestor_names 列
		SELECT
			dept_id,
			dept_name,
			ancestors,
			phone,
			ancestor_names
		FROM
			dept_tree;

	</select>

    <select id="selectDeptAncestorsByDeptId" parameterType="Long" resultMap="SysDeptResult">
		WITH RECURSIVE dept_tree AS (
			-- 基础情况：选择所有根部门，并将它们的 ancestors 列初始化为它们自己的 dept_id 和 dept_name
			SELECT
				dept_id,
				parent_id,
				dept_name,
				dept_level,
				phone,
				CAST(dept_id AS CHAR(200)) AS ancestors,
				CAST(dept_name AS CHAR(200)) AS ancestor_names
			FROM
				sys_dept
			WHERE
				dept_id = #{deptId}
			UNION ALL
			-- 递归情况：将当前部门表与之前的结果进行连接，以找到每个部门的父部门，并更新 ancestors 列和 ancestor_names 列
			SELECT
				d.dept_id,
				d.parent_id,
				d.dept_name,
				d.dept_level,
				d.phone,
				CONCAT(t.ancestors, ',', d.dept_id) AS ancestors,
				CONCAT(t.ancestor_names, ',', d.dept_name) AS ancestor_names
			FROM
				sys_dept d
			INNER JOIN
				dept_tree t ON d.parent_id = t.dept_id
			WHERE
				d.dept_level &lt; 8
		)
		-- 最终选择：从递归结果中选择 dept_id、dept_name、ancestors 和 ancestor_names 列
		SELECT
			dept_id,
			dept_name,
			ancestors,
			ancestor_names,
			phone
		FROM
			dept_tree;

	</select>


    <select id="selectChildrenDeptByParentId" parameterType="Long" resultMap="SysDeptResult">
        select dept_id, dept_name, parent_id
        from sys_dept
        where parent_id = #{deptId}
    </select>

    <select id="selectByDeptName2" resultMap="SysDeptResult">
        select dept_id, dept_name, parent_id
        from sys_dept
        where parent_id = #{parentId} and dept_name = #{deptName} limit 1
    </select>

    <select id="selectByGridId" parameterType="Long" resultMap="SysDeptResult">
        select dept_id, dept_name, dept_level, phone, parent_id
        from sys_dept
        where grid_id = #{gridId} limit 1
    </select>

    <delete id="deleteDeptByGridIds" parameterType="String">
        delete from sys_dept where grid_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectChildDeptIds" resultType="java.lang.Long">
        SELECT dept_id
        FROM sys_dept
        WHERE FIND_IN_SET(#{deptId}, ancestors)
           OR dept_id = #{deptId}
            AND del_flag = '0'
    </select>
</mapper> 