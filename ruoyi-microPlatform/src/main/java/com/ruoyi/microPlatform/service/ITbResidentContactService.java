package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbResidentContact;

import java.util.List;
import java.util.Map;

/**
 * 居民住户联络沟通信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbResidentContactService 
{
    /**
     * 查询居民住户联络沟通信息
     * 
     * @param id 居民住户联络沟通信息主键
     * @return 居民住户联络沟通信息
     */
    public TbResidentContact selectTbResidentContactById(Long id);

    /**
     * 校验居民住户联络沟通信息是否存在
     *
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 居民住户联络沟通信息
     */
    public boolean checkTbResidentContact(TbResidentContact tbResidentContact);

    /**
     * 查询居民住户联络沟通信息列表
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 居民住户联络沟通信息集合
     */
    public List<TbResidentContact> selectTbResidentContactList(TbResidentContact tbResidentContact);

    /**
     * 导入居民住户联络沟通信息
     *
     * @param infos       居民住户联络沟通信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentContact(List<TbResidentContact> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增居民住户联络沟通信息
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 结果
     */
    public Long insertTbResidentContact(TbResidentContact tbResidentContact);

    /**
     * 修改居民住户联络沟通信息
     * 
     * @param tbResidentContact 居民住户联络沟通信息
     * @return 结果
     */
    public int updateTbResidentContact(TbResidentContact tbResidentContact);

    /**
     * 批量删除居民住户联络沟通信息
     * 
     * @param ids 需要删除的居民住户联络沟通信息主键集合
     * @return 结果
     */
    public int deleteTbResidentContactByIds(Long[] ids);

    /**
     * 删除居民住户联络沟通信息信息
     * 
     * @param id 居民住户联络沟通信息主键
     * @return 结果
     */
    public int deleteTbResidentContactById(Long id);

    /**
     * 查询居民住户联络沟通信息 根据resident_id 查询总数据量 和本月总数据量
     */
    Map<String, Integer> selectTbResidentContactListCount(TbResidentContact tbResidentContact);

    /**
     * 查询居民住户联络沟通信息 根据创建者id 查询提交的总次数  和 最新提交的数据日期
     */
    Map<String, Object> selectSubmitCount(TbResidentContact tbResidentContact);


    List<Map<String, Object>> residentContactGroupMonth(LargeScreenInfo largeScreenInfo);


    int residentContactCount(LargeScreenInfo largeScreenInfo);

}
