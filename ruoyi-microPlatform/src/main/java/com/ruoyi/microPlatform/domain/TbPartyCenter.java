package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 党群服务中心报到情况对象 tb_party_center
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class TbPartyCenter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 统计开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 统计结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "统计结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 报到人数 */
    @Excel(name = "报到人数")
    private Integer reportPeoples;

    /** 参与社区活动人次 */
    @Excel(name = "参与社区活动人次")
    private Integer commActCount;

    /** 参与社区党组织组织生活人次 */
    @Excel(name = "参与社区党组织组织生活人次")
    private Integer commParCount;

    /** 助力社区治理次数 */
    @Excel(name = "助力社区治理次数")
    private Integer commGoverCount;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setReportPeoples(Integer reportPeoples) 
    {
        this.reportPeoples = reportPeoples;
    }

    public Integer getReportPeoples() 
    {
        return reportPeoples;
    }
    public void setCommActCount(Integer commActCount) 
    {
        this.commActCount = commActCount;
    }

    public Integer getCommActCount() 
    {
        return commActCount;
    }
    public void setCommParCount(Integer commParCount) 
    {
        this.commParCount = commParCount;
    }

    public Integer getCommParCount() 
    {
        return commParCount;
    }
    public void setCommGoverCount(Integer commGoverCount) 
    {
        this.commGoverCount = commGoverCount;
    }

    public Integer getCommGoverCount() 
    {
        return commGoverCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("reportPeoples", getReportPeoples())
            .append("commActCount", getCommActCount())
            .append("commParCount", getCommParCount())
            .append("commGoverCount", getCommGoverCount())
            .toString();
    }
}
