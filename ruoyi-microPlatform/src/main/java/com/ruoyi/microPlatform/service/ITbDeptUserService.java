package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.TbDeptUser;

/**
 * 社区干部信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbDeptUserService 
{
    /**
     * 查询社区干部信息
     * 
     * @param id 社区干部信息主键
     * @return 社区干部信息
     */
    public TbDeptUser selectTbDeptUserById(Long id);

    /**
     * 校验社区干部信息是否存在
     *
     * @param tbDeptUser 社区干部信息
     * @return 社区干部信息
     */
    public boolean checkTbDeptUser(TbDeptUser tbDeptUser);

    /**
     * 查询社区干部信息列表
     * 
     * @param tbDeptUser 社区干部信息
     * @return 社区干部信息集合
     */
    public List<TbDeptUser> selectTbDeptUserList(TbDeptUser tbDeptUser);

    public Integer selectExportDeptUserCount(TbDeptUser tbDeptUser);

    /**
     * 导入社区干部信息
     *
     * @param infos       社区干部信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @param userDept 操作用户所在部门
     * @return 结果
     */
    public String importTbDeptUser(List<TbDeptUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增社区干部信息
     * 
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    public int insertTbDeptUser(TbDeptUser tbDeptUser);

    /**
     * 修改社区干部信息
     * 
     * @param tbDeptUser 社区干部信息
     * @return 结果
     */
    public int updateTbDeptUser(TbDeptUser tbDeptUser);

    /**
     * 批量删除社区干部信息
     * 
     * @param ids 需要删除的社区干部信息主键集合
     * @return 结果
     */
    public int deleteTbDeptUserByIds(Long[] ids);

    /**
     * 删除社区干部信息信息
     * 
     * @param id 社区干部信息主键
     * @return 结果
     */
    public int deleteTbDeptUserById(Long id);


    Integer partyMemberCount();


    List<CommonBaseCount> partyMemberGroupSexCount();

    // Secretary team statistics
    List<CommonBaseCount> secretaryGroupSexCount();

    List<CommonBaseCount> secretaryGroupPartyCount();

    List<CommonBaseCount> secretaryGroupAgeCount();

    List<CommonBaseCount> secretaryGroupEducationCount();
}
