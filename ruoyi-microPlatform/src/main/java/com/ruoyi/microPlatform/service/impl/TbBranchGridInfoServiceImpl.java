package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbBranchGridInfoMapper;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;
import com.ruoyi.microPlatform.service.ITbBranchGridInfoService;

/**
 * 支部关联网格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class TbBranchGridInfoServiceImpl implements ITbBranchGridInfoService 
{
    @Autowired
    private TbBranchGridInfoMapper tbBranchGridInfoMapper;

    /**
     * 查询支部关联网格
     * 
     * @param id 支部关联网格主键
     * @return 支部关联网格
     */
    @Override
    public TbBranchGridInfo selectTbBranchGridInfoById(Long id)
    {
        return tbBranchGridInfoMapper.selectTbBranchGridInfoById(id);
    }

    /**
     * 校验支部关联网格是否存在
     *
     * @param tbBranchGridInfo
     * @return boolean
     */
    @Override
    public boolean checkTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo){
        TbBranchGridInfo old = tbBranchGridInfoMapper.checkTbBranchGridInfoUnique(tbBranchGridInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询支部关联网格列表
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 支部关联网格
     */
    @Override
    public List<TbBranchGridInfo> selectTbBranchGridInfoList(TbBranchGridInfo tbBranchGridInfo)
    {
        return tbBranchGridInfoMapper.selectTbBranchGridInfoList(tbBranchGridInfo);
    }

    /**
     * 导入支部关联网格
     *
     * @param infos       支部关联网格列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbBranchGridInfo(List<TbBranchGridInfo> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入支部关联网格数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbBranchGridInfo info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbBranchGridInfo old = tbBranchGridInfoMapper.checkTbBranchGridInfoUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbBranchGridInfoMapper.insertTbBranchGridInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbBranchGridInfoMapper.updateTbBranchGridInfo(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    @Override
    public int insertTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo)
    {
        tbBranchGridInfo.setCreateTime(DateUtils.getNowDate());
        return tbBranchGridInfoMapper.insertTbBranchGridInfo(tbBranchGridInfo);
    }

    /**
     * 修改支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    @Override
    public int updateTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo)
    {
        tbBranchGridInfo.setUpdateTime(DateUtils.getNowDate());
        return tbBranchGridInfoMapper.updateTbBranchGridInfo(tbBranchGridInfo);
    }

    /**
     * 批量删除支部关联网格
     * 
     * @param ids 需要删除的支部关联网格主键
     * @return 结果
     */
    @Override
    public int deleteTbBranchGridInfoByIds(Long[] ids)
    {
        return tbBranchGridInfoMapper.deleteTbBranchGridInfoByIds(ids);
    }

    /**
     * 删除支部关联网格信息
     * 
     * @param id 支部关联网格主键
     * @return 结果
     */
    @Override
    public int deleteTbBranchGridInfoById(Long id)
    {
        return tbBranchGridInfoMapper.deleteTbBranchGridInfoById(id);
    }
}
