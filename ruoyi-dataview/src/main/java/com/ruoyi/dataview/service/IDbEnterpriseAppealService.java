package com.ruoyi.dataview.service;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbEnterpriseAppeal;

/**
 * 万人助企诉求Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface IDbEnterpriseAppealService 
{
    /**
     * 查询万人助企诉求
     * 
     * @param id 万人助企诉求主键
     * @return 万人助企诉求
     */
    public DbEnterpriseAppeal selectDbEnterpriseAppealById(Long id);

    /**
     * 校验万人助企诉求是否存在
     *
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 万人助企诉求
     */
    public boolean checkDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 查询万人助企诉求列表
     * 
     * @param dbEnterpriseAppeal 万人助企诉求query参数类
     * @return 万人助企诉求集合
     */
    public List<DbEnterpriseAppeal>  selectDbEnterpriseAppealList(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 导入万人助企诉求
     *
     * @param infos       万人助企诉求列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbEnterpriseAppeal(List<DbEnterpriseAppeal> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    public int insertDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 修改万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    public int updateDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 批量删除万人助企诉求
     * 
     * @param ids 需要删除的万人助企诉求主键集合
     * @return 结果
     */
    public int deleteDbEnterpriseAppealByIds(Long[] ids);

    /**
     * 删除万人助企诉求信息
     * 
     * @param id 万人助企诉求主键
     * @return 结果
     */
    public int deleteDbEnterpriseAppealById(Long id);

    List<CommonBaseCount> enterpriseAppealGroup();
}
