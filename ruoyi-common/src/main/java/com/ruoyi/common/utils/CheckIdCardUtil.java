package com.ruoyi.common.utils;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CheckIdCardUtil {

    public static boolean checkIdCard(int cardType, String idCard) {
        if (ObjectUtil.isEmpty(cardType) || StringUtils.isBlank(idCard)) {
            return false;
        }
        //1：身份证号；2：护照号；3：外国人永久居住证；4：港澳居民来往内地通行证；5：台湾居民来往大陆通行证；6：其他证件类型
        if (cardType == 1) {
            return IdcardUtil.isValidCard(idCard);
        }
        if (cardType == 2) {
            return isPassPortCard(idCard);
        }
        if (cardType == 3) {
            return isJLCard(idCard);
        }
        if (cardType == 4) {
            return isPassCard(idCard);
        }
        if (cardType == 5) {
            return isTWCard(idCard);
        }
        if (cardType == 6) {
            return true;
        }
        return false;
    }

    /**
     * 校验护照
     * @param passPortCard
     * @return
     */
    public static boolean isPassPortCard(String passPortCard) {
        Pattern pattern = Pattern
                .compile("(^[EeKkGgDdSsPpHh]\\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\\d{7}$)");
        Matcher match = pattern.matcher(passPortCard);
        if (!match.matches()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 校验港澳居民来往通行证
     * (通行证证件号码共11位。第1位为字母，“H”字头签发给香港居民，“M”字头签发给澳门居民，第2位至第11位为数字)
     * @param passCard
     * @return
     */
    public static boolean isPassCard(String passCard) {
        Pattern pattern = Pattern
                .compile("^([H|M]\\d{10})$");
        Matcher match = pattern.matcher(passCard);
        if (!match.matches()) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 台湾居民来往大陆通行证
     * 规则： 新版8位或18位数字 或 旧版9位数字 + 英文字母 或 8位数字 + 英文字母
     * 样本： 12345678
     */
    public static Boolean isTWCard(String card) {
        String reg = "^\\d{8}|^[a-zA-Z0-9]{10}|^[a-zA-Z0-9]{9}|^\\d{18}$";
        if (card.matches(reg) == false) {
            //台湾居民来往大陆通行证号码不合格
            return false;
        } else {
            //校验通过
            return true;
        }
    }

    /**
     * 外国人永久居留证
     * @param card
     * @return
     */
    public static Boolean isJLCard(String card){
        String reg = "/^[A-Z]{3}\\d{6}(?:0[1-9]|1[021])(?:0[1-9]|[21]\\d|3[10])\\d{2}$/\n";
        if (card.matches(reg) == false) {
            //外国人永久居留证号码不合格
            return false;
        } else {
            //校验通过
            return true;
        }
    }

}
