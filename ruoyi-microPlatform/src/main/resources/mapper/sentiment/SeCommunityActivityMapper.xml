<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeCommunityActivityMapper">

    <resultMap type="SeCommunityActivity" id="SeCommunityActivityResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="activityName" column="activity_name"/>
        <result property="taskStart" column="task_start"/>
        <result property="taskEnd" column="task_end"/>
        <result property="taskArea" column="task_area"/>
        <result property="taskAddr" column="task_addr"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="taskContent" column="task_content"/>
        <result property="signEnd" column="sign_end"/>
        <result property="taskStatus" column="task_status"/>
        <result property="isReview" column="is_review"/>
        <result property="reviewContent" column="review_content"/>
        <result property="needNum" column="need_num"/>
        <result property="signNum" column="sign_num"/>
        <result property="taskType" column="task_type"/>
        <result property="createId" column="create_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="accessory" column="accessory"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="SeCommunityActivitySeActivityUserResult" type="SeCommunityActivity"
               extends="SeCommunityActivityResult">
        <collection property="seActivityUserList" notNullColumn="sub_id" javaType="java.util.List"
                    resultMap="SeActivityUserResult"/>
    </resultMap>

    <resultMap type="SeActivityUser" id="SeActivityUserResult">
        <result property="id" column="sub_id"/>
        <result property="activityId" column="sub_activity_id"/>
        <result property="userId" column="sub_user_id"/>
        <result property="nickName" column="sub_nick_name"/>
        <result property="phone" column="sub_phone"/>
        <result property="userAvatar" column="user_avatar"/>
    </resultMap>

    <sql id="selectSeCommunityActivityVo">
        select id, dept_id, county, country, town, activity_name, task_start, task_end, task_area, task_addr, lat, lng, task_content, sign_end, task_status, is_review, review_content, need_num, sign_num, task_type, create_id, create_by, create_time, accessory, update_by, update_time from se_community_activity
    </sql>

    <select id="selectSeCommunityActivityList" parameterType="SeCommunityActivity"
            resultMap="SeCommunityActivityResult">
        <include refid="selectSeCommunityActivityVo"/>
        <where>
            <if test="deptId != null">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_community_activity.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="activityName != null  and activityName != ''">and activity_name like concat('%', #{activityName},
                '%')
            </if>
            <if test="taskArea != null  and taskArea != ''">and task_area = #{taskArea}</if>
            <if test="taskAddr != null  and taskAddr != ''">and task_addr = #{taskAddr}</if>
            <if test="lat != null ">and lat = #{lat}</if>
            <if test="lng != null ">and lng = #{lng}</if>
            <if test="taskContent != null  and taskContent != ''">and task_content = #{taskContent}</if>
            <if test="signEnd != null ">and sign_end = #{signEnd}</if>
            <if test="taskStatus != null ">and task_status = #{taskStatus}</if>
            <if test="isReview != null ">and is_review = #{isReview}</if>
            <if test="reviewContent != null  and reviewContent != ''">and review_content = #{reviewContent}</if>
            <if test="needNum != null ">and need_num = #{needNum}</if>
            <if test="signNum != null ">and sign_num = #{signNum}</if>
            <if test="taskType != null  and taskType != ''">and task_type = #{taskType}</if>
            <if test="createId != null ">and create_id = #{createId}</if>
            <if test="accessory != null  and accessory != ''">and accessory = #{accessory}</if>
            <if test="taskStart != null">and task_start = #{taskStart}</if>
            <if test="taskEnd != null">and task_end = #{taskEnd}</if>
            <choose>
                <when test="activityStatusInt != null and activityStatusInt == 0">
                    AND NOW() <![CDATA[ < ]]> task_start
                </when>
                <when test="activityStatusInt != null and activityStatusInt == 1">
                    AND NOW() BETWEEN task_start AND task_end
                </when>
                <when test="activityStatusInt != null and activityStatusInt == 2">
                    AND NOW() <![CDATA[ > ]]> task_end
                </when>
                <when test="activityStatusInt != null and activityStatusInt == 3">
                    AND NOW() <![CDATA[ < ]]> task_end
                </when>
                <when test="activityStatusInt != null and activityStatusInt == 4">
                    AND NOW() <![CDATA[ > ]]> task_end and review_content is not null
                </when>
            </choose>
            <if test="params.beginTaskStart != null and params.beginTaskStart != '' and params.endTaskStart != null and params.endTaskStart != ''">
                and task_start between #{params.beginTaskStart} and #{params.endTaskStart}
            </if>
        </where>
        order by task_end desc
    </select>

    <select id="selectSeCommunityActivityById" parameterType="Long" resultMap="SeCommunityActivitySeActivityUserResult">
            select a.id, a.dept_id, a.county, a.country, a.town, a.activity_name, a.task_start, a.task_end, a.task_area, a.task_addr, a.lat, a.lng, a.task_content, a.sign_end, a.task_status, a.is_review, a.review_content, a.need_num, a.sign_num, a.task_type, a.create_id, a.create_by, a.create_time, a.accessory, a.update_by, a.update_time,
            b.id as sub_id, b.activity_id as sub_activity_id, b.user_id as sub_user_id, b.nick_name as sub_nick_name, b.phone as sub_phone,b.user_avatar
            from se_community_activity a
            left join se_activity_user b on b.activity_id = a.id
            where a.id = #{id}
    </select>

    <select id="checkSeCommunityActivityUnique" parameterType="SeCommunityActivity"
            resultMap="SeCommunityActivityResult">
        select id from se_community_activity where activity_name = #{activityName} and dept_id = #{deptId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeCommunityActivity" parameterType="SeCommunityActivity" useGeneratedKeys="true" keyProperty="id">
        insert into se_community_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="activityName != null">activity_name,</if>
            <if test="taskStart != null">task_start,</if>
            <if test="taskEnd != null">task_end,</if>
            <if test="taskArea != null">task_area,</if>
            <if test="taskAddr != null">task_addr,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="taskContent != null">task_content,</if>
            <if test="signEnd != null">sign_end,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="isReview != null">is_review,</if>
            <if test="reviewContent != null">review_content,</if>
            <if test="needNum != null">need_num,</if>
            <if test="signNum != null">sign_num,</if>
            <if test="taskType != null">task_type,</if>
            <if test="createId != null">create_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="accessory != null">accessory,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="activityName != null">#{activityName},</if>
            <if test="taskStart != null">#{taskStart},</if>
            <if test="taskEnd != null">#{taskEnd},</if>
            <if test="taskArea != null">#{taskArea},</if>
            <if test="taskAddr != null">#{taskAddr},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="taskContent != null">#{taskContent},</if>
            <if test="signEnd != null">#{signEnd},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="isReview != null">#{isReview},</if>
            <if test="reviewContent != null">#{reviewContent},</if>
            <if test="needNum != null">#{needNum},</if>
            <if test="signNum != null">#{signNum},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateSeCommunityActivity" parameterType="SeCommunityActivity">
        update se_community_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="activityName != null">activity_name = #{activityName},</if>
            <if test="taskStart != null">task_start = #{taskStart},</if>
            <if test="taskEnd != null">task_end = #{taskEnd},</if>
            <if test="taskArea != null">task_area = #{taskArea},</if>
            <if test="taskAddr != null">task_addr = #{taskAddr},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="taskContent != null">task_content = #{taskContent},</if>
            <if test="signEnd != null">sign_end = #{signEnd},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="isReview != null">is_review = #{isReview},</if>
            <if test="reviewContent != null">review_content = #{reviewContent},</if>
            <if test="needNum != null">need_num = #{needNum},</if>
            <if test="signNum != null">sign_num = #{signNum},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="insetUserCount != null and insetUserCount == true">sign_num = sign_num + 1</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeCommunityActivityById" parameterType="Long">
        delete from se_community_activity where id = #{id}
    </delete>

    <delete id="deleteSeCommunityActivityByIds" parameterType="String">
        delete from se_community_activity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSeActivityUserByActivityIds" parameterType="String">
        delete from se_activity_user where activity_id in
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>

    <delete id="deleteSeActivityUserByActivityId" parameterType="Long">
        delete from se_activity_user where activity_id = #{activityId}
    </delete>

    <insert id="batchSeActivityUser">
        insert into se_activity_user( id, activity_id, user_id, nick_name, phone,user_avatar) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.activityId}, #{item.userId}, #{item.nickName}, #{item.phone},#{item.userAvatar})
        </foreach>
    </insert>

    <select id="selectSeCommunityActivityListByUserId" resultMap="SeCommunityActivityResult">
        select a.id, a.dept_id, a.county, a.country, a.town, a.activity_name, a.task_start, a.task_end, a.task_area,
        a.task_addr,
        a.lat, a.lng, a.task_content, a.sign_end, a.task_status, a.is_review, a.review_content, a.need_num, a.sign_num,
        a.task_type, a.create_id, a.create_by,
        a.create_time, a.accessory, a.update_by, a.update_time
        from se_community_activity a
        left join se_activity_user b on b.activity_id = a.id
        where b.user_id = #{userId}
        <if test="deptId != null ">and (a.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = a.dept_id))</if>

        <if test="county != null  and county != ''">and a.county = #{county}</if>
        <if test="country != null  and country != ''">and a.country = #{country}</if>
        <if test="town != null  and town != ''">and a.town = #{town}</if>
        <if test="activityName != null  and activityName != ''">and a.activity_name like concat('%', #{activityName},
            '%')
        </if>
        <if test="taskStart != null  and taskStart != ''">and a.task_start = #{taskStart}</if>
        <if test="taskEnd != null  and taskEnd != ''">and a.task_end = #{taskEnd}</if>
        <if test="taskArea != null  and taskArea != ''">and a.task_area = #{taskArea}</if>
        <if test="taskAddr != null  and taskAddr != ''">and a.task_addr = #{taskAddr}</if>
        <if test="lat != null ">and a.lat = #{lat}</if>
        <if test="lng != null ">and a.lng = #{lng}</if>
        <if test="taskContent != null  and taskContent != ''">and a.task_content = #{taskContent}</if>
        <if test="signEnd != null ">and a.sign_end = #{signEnd}</if>
        <if test="taskStatus != null ">and a.task_status = #{taskStatus}</if>
        <if test="isReview != null ">and a.is_review = #{isReview}</if>
        <if test="reviewContent != null  and reviewContent != ''">and a.review_content = #{reviewContent}</if>
        <if test="needNum != null ">and a.need_num = #{needNum}</if>
        <if test="signNum != null ">and a.sign_num = #{signNum}</if>
        <if test="taskType != null  and taskType != ''">and a.task_type = #{taskType}</if>
        <if test="createId != null ">and a.create_id = #{createId}</if>
        <if test="accessory != null  and accessory != ''">and a.accessory = #{accessory}</if>
    </select>

    <select id="selectSeCommunityActivityListSize" resultType="java.util.Map">
        select count(*) AS total_count,
        SUM(sign_num) AS total_sign_num
        from se_community_activity
        <where>
            <if test="deptId != null">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_community_activity.dept_id))</if>
        </where>
    </select>

    <select id="getActivityStatistics" resultType="java.util.Map">
        <choose>
            <when test="deptLevel == 4">
                SELECT county AS name, COUNT(*) AS count
                FROM se_community_activity
                <where>
                    <if test="taskStatusType != null">
                        <choose>
                            <when test="taskStatusType == 0">
                                AND NOW() &lt; task_start
                            </when>
                            <when test="taskStatusType == 1">
                                AND NOW() BETWEEN task_start AND task_end
                            </when>
                            <when test="taskStatusType == 2">
                                AND NOW() &gt; task_end
                            </when>
                        </choose>
                    </if>
                </where>
                GROUP BY county
            </when>
            <when test="deptLevel == 5">
                SELECT country AS name, COUNT(*) AS count
                FROM se_community_activity
                <where>
                    county = #{county}
                    <if test="taskStatusType != null">
                        <choose>
                            <when test="taskStatusType == 0">
                                AND NOW() &lt; task_start
                            </when>
                            <when test="taskStatusType == 1">
                                AND NOW() BETWEEN task_start AND task_end
                            </when>
                            <when test="taskStatusType == 2">
                                AND NOW() &gt; task_end
                            </when>
                        </choose>
                    </if>
                </where>
                GROUP BY country
            </when>
            <when test="deptLevel == 6">
                SELECT town AS name, COUNT(*) AS count
                FROM se_community_activity
                <where>
                    country = #{country}
                    <if test="taskStatusType != null">
                        <choose>
                            <when test="taskStatusType == 0">
                                AND NOW() &lt; task_start
                            </when>
                            <when test="taskStatusType == 1">
                                AND NOW() BETWEEN task_start AND task_end
                            </when>
                            <when test="taskStatusType == 2">
                                AND NOW() &gt; task_end
                            </when>
                        </choose>
                    </if>
                </where>
                GROUP BY town
            </when>

            <otherwise>
                SELECT '未知层级' AS name, 0 AS count
            </otherwise>
        </choose>

    </select>

    <select id="getActivityTotal" resultType="java.lang.Integer">
            <choose>
                <when test="deptLevel == 4">
                    SELECT COUNT(*) FROM se_community_activity
                    <where>
                        <if test="taskStatusType != null">
                            <choose>
                                <when test="taskStatusType == 0">
                                    AND NOW() &lt; task_start
                                </when>
                                <when test="taskStatusType == 1">
                                    AND NOW() BETWEEN task_start AND task_end
                                </when>
                                <when test="taskStatusType == 2">
                                    AND NOW() &gt; task_end
                                </when>
                            </choose>
                        </if>
                    </where>
                </when>

                <when test="deptLevel == 5">
                    SELECT COUNT(*) FROM se_community_activity
                    <where>
                        county = #{county}
                        <if test="taskStatusType != null">
                            <choose>
                                <when test="taskStatusType == 0">
                                    AND NOW() &lt; task_start
                                </when>
                                <when test="taskStatusType == 1">
                                    AND NOW() BETWEEN task_start AND task_end
                                </when>
                                <when test="taskStatusType == 2">
                                    AND NOW() &gt; task_end
                                </when>
                            </choose>
                        </if>
                    </where>
                </when>
                <when test="deptLevel == 6">
                    SELECT COUNT(*) FROM se_community_activity
                    <where>
                        country = #{country}
                        <if test="taskStatusType != null">
                            <choose>
                                <when test="taskStatusType == 0">
                                    AND NOW() &lt; task_start
                                </when>
                                <when test="taskStatusType == 1">
                                    AND NOW() BETWEEN task_start AND task_end
                                </when>
                                <when test="taskStatusType == 2">
                                    AND NOW() &gt; task_end
                                </when>
                            </choose>
                        </if>
                    </where>
                </when>
                <otherwise>
                    SELECT '未知层级' AS name, 0 AS count
                </otherwise>
            </choose>
    </select>
</mapper>