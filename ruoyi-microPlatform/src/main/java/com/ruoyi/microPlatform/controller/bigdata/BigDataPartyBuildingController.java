package com.ruoyi.microPlatform.controller.bigdata;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.microPlatform.service.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 党建 大数据相关api
 */
@RestController
@RequestMapping("/bigData/partyBuilding")
public class BigDataPartyBuildingController extends BaseController {

    /**
     * 党员
     */
    @Resource
    private IPbPartyMemberService pbPartyMemberService;

    @Resource
    private ITbDeptUserService tbDeptUserService;


    /**
     * 党员数量
     */
    @GetMapping("/tb/partyMemberCount")
    public AjaxResult partyMemberCount() {
        return success(pbPartyMemberService.partyMemberCount());
    }

    /**
     * 党员数量 男女分组
     */
    @GetMapping("/tb/partyMemberGroupSexCount")
    public AjaxResult partyMemberGroupSexCount() {
        return success(pbPartyMemberService.partyMemberGroupSexCount());
    }

    /**
     * 党员数量 学历分组
     */
    @GetMapping("/tb/partyMemberGroupEducationCount")
    public AjaxResult partyMemberGroupEducationCount() {
        return success(pbPartyMemberService.partyMemberGroupEducationCount());
    }

    /**
     * 党员数量 政治面貌分组
     */
    @GetMapping("/tb/partyMemberGroupPoliticalStatusCount")
    public AjaxResult partyMemberGroupPoliticalStatusCount() {
        return success(pbPartyMemberService.partyMemberGroupPoliticalStatusCount());
    }

    /**
     * 党员数量 年龄分组
     */
    @GetMapping("/tb/partyMemberGroupAgeCount")
    public AjaxResult partyMemberGroupAgeCount() {
        return success(pbPartyMemberService.partyMemberGroupAgeCount());
    }

    /**
     * 人员分布
     */
    @GetMapping("/tb/personnelDistribution")
    public AjaxResult personnelDistribution() {
        return success(pbPartyMemberService.personnelDistribution());
    }


    @Resource
    private IPbPartyOrgService pbPartyOrgService;

    /**
     * 党委、党总支、党总支部
     */
    @GetMapping("/tb/pbPartyOrgGroupOrgLevel")
    public AjaxResult pbPartyOrgGroupOrgLevel() {
        return success(pbPartyOrgService.pbPartyOrgGroupOrgLevel());
    }

    /**
     * 机关事业单位\国有企业\非公企业\网格党组织\
     */
    @GetMapping("/tb/pbPartyOrgGroupOrgType")
    public AjaxResult pbPartyOrgGroupOrgType() {
        return success(pbPartyOrgService.pbPartyOrgGroupOrgType());
    }

    @Resource
    private IPbPartyReportRelationService pbPartyReportRelationService;


    /**
     * 双报道
     */
    @GetMapping("/tb/doubleReporting")
    public AjaxResult doubleReporting() {
        return success(pbPartyReportRelationService.doubleReporting());
    }

    @Resource
    private IPbPartyEvaluationResultService pbPartyEvaluationResultService;


    /**
     * 星级村庄
     */
    @GetMapping("/pb/starVillage")
    public AjaxResult starVillage() {
        return success(pbPartyEvaluationResultService.starVillage());
    }

    /**
     * 驻村干部
     */
    @GetMapping("/pb/cadresStationedInVillages")
    public AjaxResult cadresStationedInVillages() {
        Map<String, String> res = new HashMap<>();
        // 第一书记 \ 省派 \ 市派 \ 县区派
        // 工作人员 \ 省派 \ 市派 \ 县区派
        List<SysDictData> marketOverview = DictUtils.getDictCache("cadresStationedInVillages");
        if (marketOverview == null) {
            marketOverview = new ArrayList<>();
        }
        Map<String, String> collect = marketOverview.stream().collect(Collectors
                .toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (k1, k2) -> k1));
        res.put("firstSecretary", collect.getOrDefault("firstSecretary", "1229"));// 第一书记
        res.put("provincialDispatched", collect.getOrDefault("provincialDispatched", "234"));// 省派
        res.put("cityDispatched", collect.getOrDefault("ruralCommunity", "234"));// 市派
        res.put("countyDispatched", collect.getOrDefault("ruralCommunity", "234"));// 县区派
        res.put("worker", collect.getOrDefault("cityCommunity", "1229")); // 工作人员
        res.put("workerProvincialDispatched", collect.getOrDefault("ruralCommunity", "234")); // 省派
        res.put("workerCityDispatched", collect.getOrDefault("ruralCommunity", "234"));// 市派
        res.put("workerCountyDispatched", collect.getOrDefault("ruralCommunity", "234"));// 县区派
        return success(res);
    }


    /**
     * 市情概况
     */
    @GetMapping("/pb/marketOverview")
    public AjaxResult marketOverview() {
        Map<String, String> res = new HashMap<>();
        // 城市社区
        // 农村社区
        List<SysDictData> marketOverview = DictUtils.getDictCache("marketOverview");
        if (marketOverview == null) {
            marketOverview = new ArrayList<>();
        }
        Map<String, String> collect = marketOverview.stream().collect(Collectors
                .toMap(SysDictData::getDictLabel, SysDictData::getDictValue, (k1, k2) -> k1));
        res.put("cityCommunity", collect.getOrDefault("cityCommunity", "1229"));
        res.put("ruralCommunity", collect.getOrDefault("ruralCommunity", "234"));
        return success(res);
    }


    /**
     * 村社区书记队伍\ 男女分析
     */
    @GetMapping("/tb/secretaryGroupSexCount")
    public AjaxResult secretaryGroupSexCount() {
        return success(tbDeptUserService.secretaryGroupSexCount());
    }


    /**
     * 村社区书记队伍\ 党员、非党员
     */
    @GetMapping("/tb/secretaryGroupPartyCount")
    public AjaxResult secretaryGroupPartyCount() {
        return success(tbDeptUserService.secretaryGroupPartyCount());
    }


    /**
     * 村社区书记队伍\ 年龄 （34以下 \ 35-60 \ 60 以上）
     */
    @GetMapping("/tb/secretaryGroupAgeCount")
    public AjaxResult secretaryGroupAgeCount() {
        return success(tbDeptUserService.secretaryGroupAgeCount());
    }

    /**
     * 村社区书记队伍\ 学历分析
     */
    @GetMapping("/tb/secretaryGroupEducationCount")
    public AjaxResult secretaryGroupEducationCount() {
        return success(tbDeptUserService.secretaryGroupEducationCount());
    }


    /**
     * 星级社区
     */
    @GetMapping("/pb/starCommunity")
    public AjaxResult starCommunity() {
        List<SysDictData> townEvaluationBatch = DictUtils.getDictCache("town_evaluation_batch");
        String evaluation_batch = DateUtil.thisYear() + "";
        if (townEvaluationBatch != null) {
            // townEvaluationBatch 中sort 最大的值为当前批次
            townEvaluationBatch.sort(Comparator.comparingLong(SysDictData::getDictSort));
            evaluation_batch = townEvaluationBatch.get(townEvaluationBatch.size() - 1).getDictValue();
        }
        return success(pbPartyEvaluationResultService.starCommunity(evaluation_batch));
    }

}
