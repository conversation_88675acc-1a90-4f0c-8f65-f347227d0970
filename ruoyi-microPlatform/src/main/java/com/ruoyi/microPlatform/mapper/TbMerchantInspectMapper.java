package com.ruoyi.microPlatform.mapper;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbMerchantInspect;

import java.util.List;
import java.util.Map;

/**
 * 门店巡查Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbMerchantInspectMapper 
{
    /**
     * 查询门店巡查
     * 
     * @param id 门店巡查主键
     * @return 门店巡查
     */
    public TbMerchantInspect selectTbMerchantInspectById(Long id);

    /**
     * 校验门店巡查是否唯一
     *
     * @param tbMerchantInspect
     * @return 门店巡查
     */
    public TbMerchantInspect checkTbMerchantInspectUnique(TbMerchantInspect tbMerchantInspect);

    /**
     * 查询门店巡查列表
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 门店巡查集合
     */
    public List<TbMerchantInspect> selectTbMerchantInspectList(TbMerchantInspect tbMerchantInspect);

    /**
     * 新增门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    public int insertTbMerchantInspect(TbMerchantInspect tbMerchantInspect);

    /**
     * 修改门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    public int updateTbMerchantInspect(TbMerchantInspect tbMerchantInspect);

    /**
     * 删除门店巡查
     * 
     * @param id 门店巡查主键
     * @return 结果
     */
    public int deleteTbMerchantInspectById(Long id);

    /**
     * 批量删除门店巡查
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMerchantInspectByIds(Long[] ids);

    int selectTbMerchantInspectListCount(TbMerchantInspect tbMerchantInspect);

    int selectTbMerchantInspectListCountTime(TbMerchantInspect tbMerchantInspect);

    Map<String, Object> selectSubmitCount(TbMerchantInspect tbMerchantInspect);

    List<Map<String, Object>> selectMonthlyStats(LargeScreenInfo largeScreenInfo);

    int selectCount(LargeScreenInfo largeScreenInfo);
}
