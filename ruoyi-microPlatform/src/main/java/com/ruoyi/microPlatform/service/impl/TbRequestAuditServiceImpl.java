package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbRequestAuditMapper;
import com.ruoyi.microPlatform.domain.TbRequestAudit;
import com.ruoyi.microPlatform.service.ITbRequestAuditService;

/**
 * 导出请求审核过程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
@Service
public class TbRequestAuditServiceImpl implements ITbRequestAuditService 
{
    @Autowired
    private TbRequestAuditMapper tbRequestAuditMapper;

    /**
     * 查询导出请求审核过程
     * 
     * @param id 导出请求审核过程主键
     * @return 导出请求审核过程
     */
    @Override
    public TbRequestAudit selectTbRequestAuditById(Long id)
    {
        return tbRequestAuditMapper.selectTbRequestAuditById(id);
    }

    /**
     * 校验导出请求审核过程是否存在
     *
     * @param tbRequestAudit
     * @return boolean
     */
    @Override
    public boolean checkTbRequestAudit(TbRequestAudit tbRequestAudit){
        TbRequestAudit old = tbRequestAuditMapper.checkTbRequestAuditUnique(tbRequestAudit);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询导出请求审核过程列表
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 导出请求审核过程
     */
    @Override
    public List<TbRequestAudit> selectTbRequestAuditList(TbRequestAudit tbRequestAudit)
    {
        return tbRequestAuditMapper.selectTbRequestAuditList(tbRequestAudit);
    }

    /**
     * 导入导出请求审核过程
     *
     * @param infos       导出请求审核过程列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbRequestAudit(List<TbRequestAudit> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入导出请求审核过程数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbRequestAudit info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbRequestAudit old = tbRequestAuditMapper.checkTbRequestAuditUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbRequestAuditMapper.insertTbRequestAudit(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbRequestAuditMapper.updateTbRequestAudit(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    @Override
    public int insertTbRequestAudit(TbRequestAudit tbRequestAudit)
    {
        return tbRequestAuditMapper.insertTbRequestAudit(tbRequestAudit);
    }

    /**
     * 修改导出请求审核过程
     * 
     * @param tbRequestAudit 导出请求审核过程
     * @return 结果
     */
    @Override
    public int updateTbRequestAudit(TbRequestAudit tbRequestAudit)
    {
        return tbRequestAuditMapper.updateTbRequestAudit(tbRequestAudit);
    }

    /**
     * 批量删除导出请求审核过程
     * 
     * @param ids 需要删除的导出请求审核过程主键
     * @return 结果
     */
    @Override
    public int deleteTbRequestAuditByIds(Long[] ids)
    {
        return tbRequestAuditMapper.deleteTbRequestAuditByIds(ids);
    }

    /**
     * 删除导出请求审核过程信息
     * 
     * @param id 导出请求审核过程主键
     * @return 结果
     */
    @Override
    public int deleteTbRequestAuditById(Long id)
    {
        return tbRequestAuditMapper.deleteTbRequestAuditById(id);
    }
}
