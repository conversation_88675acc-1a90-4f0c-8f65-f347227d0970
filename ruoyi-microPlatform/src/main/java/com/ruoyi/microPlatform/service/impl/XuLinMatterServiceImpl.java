package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.domain.vo.XuLinMatterStatsVO;
import com.ruoyi.microPlatform.mapper.XuLinMatterMapper;
import com.ruoyi.microPlatform.service.IXuLinMatterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 许邻e家事项统计Service实现类
 */
@Service
@Slf4j
public class XuLinMatterServiceImpl implements IXuLinMatterService {

    @Autowired
    private XuLinMatterMapper xuLinMatterMapper;

    @Override
    public List<XuLinMatterStatsVO> getXuLinMatterStats(BigdataParam bigdataParam) {


        List<XuLinMatterStatsVO> result = new ArrayList<>();
        
        // 1. 居民吹哨
        XuLinMatterStatsVO residentWhistle = createMatterStats(
                "居民吹哨", 
                xuLinMatterMapper.countResidentWhistleStats(bigdataParam)
        );
        
        // 2. 网格巡查问题
        XuLinMatterStatsVO gridInspection = createMatterStats(
                "网格巡查问题", 
                xuLinMatterMapper.countGridInspectionStats(bigdataParam)
        );
        
        // 3. 住户走访问题
        XuLinMatterStatsVO householdVisit = createMatterStats(
                "住户走访问题", 
                xuLinMatterMapper.countHouseholdVisitStats(bigdataParam)
        );
        
        // 4. 门店巡查问题
        XuLinMatterStatsVO storeInspection = createMatterStats(
                "门店巡查问题", 
                xuLinMatterMapper.countStoreInspectionStats(bigdataParam)
        );
        
        // 添加所有统计项到结果列表
        result.add(residentWhistle);
        result.add(gridInspection);
        result.add(householdVisit);
        result.add(storeInspection);
        
        return result;
    }
    
    /**
     * 创建事项统计VO对象
     * @param type 事项类型
     * @param stats 统计数据
     * @return 统计VO对象
     */
    private XuLinMatterStatsVO createMatterStats(String type, Map<String, Object> stats) {
        XuLinMatterStatsVO vo = new XuLinMatterStatsVO();
        vo.setType(type);
        
        // 处理可能为null的统计结果
        if (stats != null) {
            Object processed = stats.get("processed");
            Object pending = stats.get("pending");
            
            vo.setProcessedCount(processed != null ? ((Number) processed).longValue() : 0L);
            vo.setPendingCount(pending != null ? ((Number) pending).longValue() : 0L);
        } else {
            vo.setProcessedCount(0L);
            vo.setPendingCount(0L);
        }
        
        return vo;
    }
}
