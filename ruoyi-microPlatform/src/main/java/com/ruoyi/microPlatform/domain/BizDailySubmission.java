package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 每日要情实体，对应表 biz_daily_submission
 *
 * 根据项目现有实体风格创建，继承 BaseEntity 以复用 createTime、updateTime 等通用字段。
 */
@Data
public class BizDailySubmission extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    private Long id;

    /** 报送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportDate;

    /** 报送单位ID */
    private Long orgId;

    /** 报送单位名称 */
    private String orgName;

    /** 今日动态 (重点工作进展、关键数据成果) */
    private String bizDynamics;

    /** 重大事项 (需报市委(县委)领导知悉的事项) */
    private String riskHiddenDangers;

    /** 事项等级: 高, 中, 低 */
    private String riskLevel;

    /** 佐证附件列表（以逗号分隔的URL或文件标识） */
    private String accessory;

    /** 报送人ID */
    private Long submitUserId;

    /** 报送人姓名 */
    private String submitUserName;

    /** 报送人联系方式 */
    private String submitUserPhone;

    /** 报送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    // createTime、updateTime 继承自 BaseEntity，对应表字段 create_time、update_time
}
