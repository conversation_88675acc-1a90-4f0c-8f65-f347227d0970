package com.ruoyi.common.core.domain.model;

/**
 * 微信用户登录对象
 *
 * <AUTHOR>
 */
public class WxLoginBody
{
    /**
     * 临时登陆凭证 code 只能使用一次
     */
    private String code;

    /**
     * 偏移量
     */
    private String encryptedIv;

    /**
     * 加密数据
     */
    private String encryptedData;
    /**
     * 昵称
     */
    private String nickName;

    private Long deptDataId;
    /**
     * 普通账户
     */
    private String userType = "00";

    private String drillNo;

    private String jobUnit;

    public String getDrillNo() {
        return drillNo;
    }

    public void setDrillNo(String drillNo) {
        this.drillNo = drillNo;
    }

    public String getJobUnit() {
        return jobUnit;
    }

    public void setJobUnit(String jobUnit) {
        this.jobUnit = jobUnit;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public Long getDeptDataId() {
        return deptDataId;
    }

    public void setDeptDataId(Long deptDataId) {
        this.deptDataId = deptDataId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEncryptedIv() {
        return encryptedIv;
    }

    public void setEncryptedIv(String encryptedIv) {
        this.encryptedIv = encryptedIv;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
}
