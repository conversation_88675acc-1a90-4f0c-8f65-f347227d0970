package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 门店/商户信息对象 tb_merchant_info
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbMerchantInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", needMerge = true, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", needMerge = true, width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", needMerge = true, width = 18)
    private String town;

    /** 所属网格 */
    private Long gridId;

    /** 所属网格 */
    @Excel(name = "所属网格编码", needMerge = true)
    private String gridName;

    /** 所属小区 */

    private Long communityId;

//    @Excel(name = "所属小区", needMerge = true)
    private String communityName;

    /** 门店名称 */
    @Excel(name = "门店名称", needMerge = true)
    private String merchantName;

    /** 门店类型 */
    @Excel(name = "门店类型", needMerge = true, comboReadDict = true, dictType = "business_type")
    private String storeType;

    /** 营业执照名称 */
    @Excel(name = "营业执照名称", needMerge = true)
    private String licenseName;

    /** 社会信用代码 */
    @Excel(name = "社会信用代码", needMerge = true)
    private String code;

    /** 营业类型 */
//    @Excel(name = "营业类型", needMerge = true)
    private String businessType;

    /** 经纬度 */
    private String lat;

    /**  */
    private String lng;

    /** 地址 */
    @Excel(name = "门店地址", needMerge = true)
    private String addr;

    /** 面积 */
    @Excel(name = "面积(㎡)", needMerge = true)
    private String area;

    /** 层高 */
//    @Excel(name = "层高", needMerge = true)
    private String floor;

    /** 从业人数 */
    @Excel(name = "从业人数", needMerge = true)
    private String engagedNum;

    /** 负责人 */
    @Excel(name = "门店负责人", needMerge = true)
    private String leader;

    /** 身份证号 */
    @Excel(name = "负责人身份证号", needMerge = true)
    private String idCard;

    /** 联系电话 */
    @Excel(name = "负责人联系电话", needMerge = true)
    private String phone;

    /** 房东(产权人) */
    @Excel(name = "房东(产权人)", needMerge = true)
    private String landlordLandlady;

    /** 房东身份证号 */
    @Excel(name = "房东身份证号", needMerge = true)
    private String landlordCard;

    /** 房东联系电话 */
    @Excel(name = "房东联系电话", needMerge = true)
    private String landlordPhone;

    /** 是否签订产权合同 */
    @Excel(name = "是否签订产权合同", needMerge = true)
    private String isSign;

    /** 餐饮门店填写用气类型 */
    @Excel(name = "餐饮门店填写用气类型", needMerge = true)
    private String gasType;


    /** 门店状态（1正常 2异常） */
    @Excel(name = "门店状态", readConverterExp = "1=正常,2=异常", needMerge = true, type = Excel.Type.EXPORT)
    private Integer storeStatus;

    /** 状态描述  更新为异常的时候可能需要加这个描述，暂时没用 */
//    @Excel(name = "状态描述")
    private String statusDetail;

    /** 照片 */
//    @Excel(name = "门店照片", needMerge = true)
    private String accessory;

    @Excel(name = "门店店员信息")
    private List<TbMerchantAssistant> list;

    private Integer isExport;

    private Integer sort;

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsExport() {
        return isExport;
    }

    public void setIsExport(Integer isExport) {
        this.isExport = isExport;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setCommunityId(Long communityId) 
    {
        this.communityId = communityId;
    }

    public Long getCommunityId() 
    {
        return communityId;
    }
    public void setMerchantName(String merchantName) 
    {
        this.merchantName = merchantName;
    }

    public String getMerchantName() 
    {
        return merchantName;
    }
    public void setLicenseName(String licenseName) 
    {
        this.licenseName = licenseName;
    }

    public String getLicenseName() 
    {
        return licenseName;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setBusinessType(String businessType) 
    {
        this.businessType = businessType;
    }

    public String getBusinessType() 
    {
        return businessType;
    }
    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }
    public void setLng(String lng) 
    {
        this.lng = lng;
    }

    public String getLng() 
    {
        return lng;
    }
    public void setAddr(String addr) 
    {
        this.addr = addr;
    }

    public String getAddr() 
    {
        return addr;
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setFloor(String floor) 
    {
        this.floor = floor;
    }

    public String getFloor() 
    {
        return floor;
    }
    public void setEngagedNum(String engagedNum) 
    {
        this.engagedNum = engagedNum;
    }

    public String getEngagedNum() 
    {
        return engagedNum;
    }
    public void setLeader(String leader) 
    {
        this.leader = leader;
    }

    public String getLeader() 
    {
        return leader;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    @Size(min = 18, max = 18, message = "身份证号长度为18个字符")
    public String getIdCard() 
    {
        return idCard;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    @Size(min = 0, max = 11, message = "电话长度不能超过11个字符")
    public String getPhone() 
    {
        return phone;
    }
    public void setLandlordLandlady(String landlordLandlady) 
    {
        this.landlordLandlady = landlordLandlady;
    }

    public String getLandlordLandlady() 
    {
        return landlordLandlady;
    }
    public void setLandlordCard(String landlordCard) 
    {
        this.landlordCard = landlordCard;
    }

    public String getLandlordCard() 
    {
        return landlordCard;
    }
    public void setLandlordPhone(String landlordPhone) 
    {
        this.landlordPhone = landlordPhone;
    }

    public String getLandlordPhone() 
    {
        return landlordPhone;
    }
    public void setIsSign(String isSign) 
    {
        this.isSign = isSign;
    }

    public String getIsSign() 
    {
        return isSign;
    }
    public void setGasType(String gasType) 
    {
        this.gasType = gasType;
    }

    public String getGasType() 
    {
        return gasType;
    }
    public void setStoreType(String storeType)
    {
        this.storeType = storeType;
    }

    public String getStoreType() 
    {
        return storeType;
    }
    public void setStoreStatus(Integer storeStatus)
    {
        this.storeStatus = storeStatus;
    }

    public Integer getStoreStatus()
    {
        return storeStatus;
    }
    public void setStatusDetail(String statusDetail) 
    {
        this.statusDetail = statusDetail;
    }

    public String getStatusDetail() 
    {
        return statusDetail;
    }

    public List<TbMerchantAssistant> getList() {
        return list;
    }

    public void setList(List<TbMerchantAssistant> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridId", getGridId())
            .append("gridName", getGridName())
            .append("communityId", getCommunityId())
            .append("merchantName", getMerchantName())
            .append("licenseName", getLicenseName())
            .append("code", getCode())
            .append("businessType", getBusinessType())
            .append("lat", getLat())
            .append("lng", getLng())
            .append("addr", getAddr())
            .append("area", getArea())
            .append("floor", getFloor())
            .append("engagedNum", getEngagedNum())
            .append("leader", getLeader())
            .append("idCard", getIdCard())
            .append("phone", getPhone())
            .append("landlordLandlady", getLandlordLandlady())
            .append("landlordCard", getLandlordCard())
            .append("landlordPhone", getLandlordPhone())
            .append("isSign", getIsSign())
            .append("gasType", getGasType())
            .append("accessory", getAccessory())
            .append("storeType", getStoreType())
            .append("storeStatus", getStoreStatus())
            .append("statusDetail", getStatusDetail())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
