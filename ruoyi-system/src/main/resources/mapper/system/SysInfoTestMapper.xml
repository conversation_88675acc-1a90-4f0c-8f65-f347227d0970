<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysInfoTestMapper">
    
    <resultMap type="SysInfoTest" id="SysInfoTestResult">
        <result property="id"    column="id"    />
        <result property="spare1"    column="spare1"    />
        <result property="spare2"    column="spare2"    />
        <result property="dictLabel"    column="dict_label"    />
        <result property="dictValue"    column="dict_value"    />
    </resultMap>

    <resultMap id="SysInfoTestSysInfoChildResult" type="SysInfoTest" extends="SysInfoTestResult">
        <collection property="sysInfoChildList" notNullColumn="sub_id" javaType="java.util.List" resultMap="SysInfoChildResult" />
    </resultMap>

    <resultMap type="SysInfoChild" id="SysInfoChildResult">
        <result property="id"    column="sub_id"    />
        <result property="infoId"    column="sub_info_id"    />
        <result property="childLabel"    column="sub_child_label"    />
        <result property="childValue"    column="sub_child_value"    />
    </resultMap>

    <sql id="selectSysInfoTestVo">
        select id, spare1, spare2, dict_label, dict_value from sys_info_test
    </sql>

    <select id="selectSysInfoTestList" parameterType="SysInfoTest" resultMap="SysInfoTestSysInfoChildResult">
        select a.id, a.spare1, a.spare2, a.dict_label, a.dict_value,
        b.id as sub_id, b.info_id as sub_info_id, b.child_label as sub_child_label, b.child_value as sub_child_value
        from sys_info_test a
        left join sys_info_child b on b.info_id = a.id
        <where>  
            <if test="spare1 != null  and spare1 != ''"> and spare1 = #{spare1}</if>
            <if test="spare2 != null "> and spare2 = #{spare2}</if>
            <if test="dictLabel != null  and dictLabel != ''"> and dict_label = #{dictLabel}</if>
            <if test="dictValue != null  and dictValue != ''"> and dict_value = #{dictValue}</if>
        </where>
    </select>
    
    <select id="selectSysInfoTestById" parameterType="Integer" resultMap="SysInfoTestSysInfoChildResult">
        select a.id, a.spare1, a.spare2, a.dict_label, a.dict_value,
 b.id as sub_id, b.info_id as sub_info_id, b.child_label as sub_child_label, b.child_value as sub_child_value
        from sys_info_test a
        left join sys_info_child b on b.info_id = a.id
        where a.id = #{id}
    </select>
        
    <insert id="insertSysInfoTest" parameterType="SysInfoTest" useGeneratedKeys="true" keyProperty="id">
        insert into sys_info_test
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spare1 != null">spare1,</if>
            <if test="spare2 != null">spare2,</if>
            <if test="dictLabel != null and dictLabel != ''">dict_label,</if>
            <if test="dictValue != null">dict_value,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spare1 != null">#{spare1},</if>
            <if test="spare2 != null">#{spare2},</if>
            <if test="dictLabel != null and dictLabel != ''">#{dictLabel},</if>
            <if test="dictValue != null">#{dictValue},</if>
         </trim>
    </insert>

    <update id="updateSysInfoTest" parameterType="SysInfoTest">
        update sys_info_test
        <trim prefix="SET" suffixOverrides=",">
            <if test="spare1 != null">spare1 = #{spare1},</if>
            <if test="spare2 != null">spare2 = #{spare2},</if>
            <if test="dictLabel != null and dictLabel != ''">dict_label = #{dictLabel},</if>
            <if test="dictValue != null">dict_value = #{dictValue},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysInfoTestById" parameterType="Integer">
        delete from sys_info_test where id = #{id}
    </delete>

    <delete id="deleteSysInfoTestByIds" parameterType="String">
        delete from sys_info_test where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteSysInfoChildByInfoIds" parameterType="String">
        delete from sys_info_child where info_id in 
        <foreach item="infoId" collection="array" open="(" separator="," close=")">
            #{infoId}
        </foreach>
    </delete>

    <delete id="deleteSysInfoChildByInfoId" parameterType="Integer">
        delete from sys_info_child where info_id = #{infoId}
    </delete>

    <insert id="batchSysInfoChild">
        insert into sys_info_child( id, info_id, child_label, child_value) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.infoId}, #{item.childLabel}, #{item.childValue})
        </foreach>
    </insert>
</mapper>