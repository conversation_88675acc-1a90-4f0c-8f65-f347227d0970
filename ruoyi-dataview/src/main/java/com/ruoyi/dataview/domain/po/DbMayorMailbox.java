package com.ruoyi.dataview.domain.po;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 市长信箱主对象 db_mayor_mailbox
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbMayorMailbox extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 信件全局唯一标识(ROWGUID) */
    @Excel(name = "信件全局唯一标识(ROWGUID)")
    private String mailGuid;

    /** 信件编号 */
    @Excel(name = "信件编号")
    private String mailNo;

    /** 信件标题 */
    @Excel(name = "信件标题")
    private String mailTitle;

    /** 信件内容 */
    @Excel(name = "信件内容")
    private String mailContent;

    /** 类别 */
    @Excel(name = "类别")
    private String category;

    /** 信件类别 */
    @Excel(name = "信件类别")
    private String categoryType;

    /** 信件状态 */
    @Excel(name = "信件状态")
    private String status;

    /** 是否热点信件(0否,1是) */
    @Excel(name = "是否热点信件",readConverterExp = "0=否,1=是")
    private Integer isHot;

    /** 是否公开(0否,1是) */
    @Excel(name = "是否公开",readConverterExp = "0=否,1=是")
    private Integer isPublic;

    /** 是否允许发布(0否,1是) */
    @Excel(name = "是否允许发布",readConverterExp = "0=否,1=是")
    private Integer allowPublish;

    /** 写信人 */
    @Excel(name = "写信人")
    private String postUserName;

    /** 写信人性别(0未知,1男,2女) */
    @Excel(name = "写信人性别",readConverterExp = "0=未知,1=男,2=女")
    private Integer postUserGender;

    /** 写信日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "写信日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date postDate;

    /** 写信人电话 */
    @Excel(name = "写信人电话")
    private String postTel;

    /** 写信人邮箱 */
    @Excel(name = "写信人邮箱")
    private String postEmail;

    /** 写信人IP */
    @Excel(name = "写信人IP")
    private String postIp;

    /** 事件发生地/地址 */
    @Excel(name = "事件发生地/地址")
    private String postAddress;

    /** 查询密码/咨询密码 */
    @Excel(name = "查询密码/咨询密码")
    private String consultPwd;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 时限 */
    @Excel(name = "时限")
    private Integer timeLimit;

    /** 处理时限 */
    @Excel(name = "处理时限")
    private Integer handleTimeLimit;

    /** 承办单位 */
    @Excel(name = "承办单位")
    private String undertakeUnit;

    /** 承办单位结果 */
    @Excel(name = "承办单位结果")
    private String undertakeResult;

    /** 回复内容 */
    @Excel(name = "回复内容")
    private String replyContent;

    /** 回复日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回复日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date replyDate;

    /** 最新操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operateDate;

    /** 处理日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleDate;

    /** 是否办结(0否,1是) */
    @Excel(name = "是否办结(0否,1是)",readConverterExp = "0=否,1=是")
    private Integer completeStatus;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishDate;

    /** 是否推送(0否,1是) */
    @Excel(name = "是否推送(0否,1是)",readConverterExp = "0=否,1=是")
    private Integer pushFlag;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pushDate;

}
