<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMerchantInfoMapper">

    <resultMap type="TbMerchantInfo" id="TbMerchantInfoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="communityId" column="community_id"/>
        <result property="communityName" column="community_name"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="licenseName" column="license_name"/>
        <result property="code" column="code"/>
        <result property="businessType" column="business_type"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="addr" column="addr"/>
        <result property="area" column="area"/>
        <result property="floor" column="floor"/>
        <result property="engagedNum" column="engaged_num"/>
        <result property="leader" column="leader"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="landlordLandlady" column="landlord_landlady"/>
        <result property="landlordCard" column="landlord_card"/>
        <result property="landlordPhone" column="landlord_phone"/>
        <result property="isSign" column="is_sign"/>
        <result property="gasType" column="gas_type"/>
        <result property="accessory" column="accessory"/>
        <result property="storeType" column="store_type"/>
        <result property="storeStatus" column="store_status"/>
        <result property="statusDetail" column="status_detail"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
        <collection property="list" javaType="java.util.List" resultMap="TbMerchantAssistantResult"/>
    </resultMap>

    <resultMap type="TbMerchantAssistant" id="TbMerchantAssistantResult">
        <result property="id" column="ma_id"/>
        <result property="merchantId" column="ma_merchant_id"/>
        <result property="name" column="ma_name"/>
        <result property="idCard" column="ma_id_card"/>
        <result property="phone" column="ma_phone"/>
        <result property="age" column="ma_age"/>
        <result property="birthday" column="ma_birthday"/>
        <result property="createTime" column="ma_create_time"/>
    </resultMap>

    <sql id="selectTbMerchantInfoVo">
        select id, dept_id, county, country, town, grid_id, grid_name, community_id,community_name, merchant_name, license_name, code,
        business_type, lat, lng, addr, area, floor, engaged_num, leader, id_card, phone, landlord_landlady, landlord_card,sort,
         landlord_phone, is_sign, gas_type, accessory, store_type, store_status, status_detail, remark, create_by, create_time, update_by, update_time from tb_merchant_info m
    </sql>
    <sql id="queryTbMerchantInfoVo">
        <where>
            <if test="deptId != null ">and (m.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = m.dept_id))</if>
            <if test="county != null  and county != ''">and m.county = #{county}</if>
            <if test="country != null  and country != ''">and m.country = #{country}</if>
            <if test="town != null  and town != ''">and m.town = #{town}</if>
            <if test="gridId != null ">and m.grid_id = #{gridId}</if>
            <if test="gridArr != null  and gridArr != ''">and m.grid_id in
                <foreach item="item" collection="gridArr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="gridName != null  and gridName != ''">and m.grid_name like concat('%', #{gridName}, '%')</if>
            <if test="communityId != null ">and m.community_id = #{communityId}</if>
            <if test="merchantName != null  and merchantName != ''">and m.merchant_name like concat('%', #{merchantName},
                '%')
            </if>
            <if test="licenseName != null  and licenseName != ''">and m.license_name like concat('%', #{licenseName},
                '%')
            </if>
            <if test="code != null  and code != ''">and m.code = #{code}</if>
            <if test="businessType != null  and businessType != ''">and m.business_type = #{businessType}</if>
            <if test="addr != null  and addr != ''">and m.addr = #{addr}</if>
            <if test="area != null  and area != ''">and m.area = #{area}</if>
            <if test="floor != null  and floor != ''">and m.floor = #{floor}</if>
            <if test="engagedNum != null  and engagedNum != ''">and m.engaged_num = #{engagedNum}</if>
            <if test="leader != null  and leader != ''">and m.leader = #{leader}</if>
            <if test="idCard != null  and idCard != ''">and m.id_card = #{idCard}</if>
            <if test="phone != null  and phone != ''">and m.phone = #{phone}</if>
            <if test="landlordLandlady != null  and landlordLandlady != ''">and m.landlord_landlady =
                #{landlordLandlady}
            </if>
            <if test="landlordCard != null  and landlordCard != ''">and m.landlord_card = #{landlordCard}</if>
            <if test="landlordPhone != null  and landlordPhone != ''">and m.landlord_phone = #{landlordPhone}</if>
            <if test="isSign != null  and isSign != ''">and m.is_sign = #{isSign}</if>
            <if test="gasType != null  and gasType != ''">and m.gas_type = #{gasType}</if>
            <if test="storeType != null  and storeType != ''">and m.store_type = #{storeType}</if>
            <if test="storeStatus != null ">and m.store_status = #{storeStatus}</if>
            <if test="statusDetail != null  and statusDetail != ''">and m.status_detail = #{statusDetail}</if>
        </where>
    </sql>

    <select id="selectTbMerchantInfoList" parameterType="TbMerchantInfo" resultMap="TbMerchantInfoResult">
        <if test="isExport != null and isExport == 1">
            select m.id, m.dept_id, m.county, m.country, m.town, m.grid_id, m.grid_name, m.community_id,m.community_name, m.merchant_name, m.license_name, m.code,
            m.business_type, m.lat, m.lng, m.addr, m.area, m.floor, m.engaged_num, m.leader, m.id_card, m.phone, m.landlord_landlady, m.landlord_card,
            m.landlord_phone, m.is_sign, m.gas_type, m.accessory, m.store_type, m.store_status, m.status_detail, m.remark, m.create_by, m.create_time, m.update_by, m.update_time,
            ma.id as ma_id, ma.merchant_id as ma_merchant_id, ma.name as ma_name, ma.id_card as ma_id_card, ma.phone as ma_phone,ma.birthday as ma_birthday,
            IFNULL(TIMESTAMPDIFF(YEAR, ma.birthday, CURDATE()), null) AS ma_age
            from tb_merchant_info m left join tb_merchant_assistant ma on ma.merchant_id = m.id
        </if>
        <if test="isExport == null or isExport != 1">
            <include refid="selectTbMerchantInfoVo"/>
        </if>
        <include refid="queryTbMerchantInfoVo"/>
        order by m.sort asc, m.id desc
    </select>

    <select id="selectTbMerchantInfoCount" parameterType="TbMerchantInfo" resultType="long">
        select m.id from tb_merchant_info m
        <include refid="queryTbMerchantInfoVo"/>
    </select>

    <select id="selectTbMerchantInfoById" parameterType="Long" resultMap="TbMerchantInfoResult">
        select m.id, m.dept_id, m.county, m.country, m.town, m.grid_id, m.grid_name, m.community_id,m.community_name, m.merchant_name, m.license_name, m.code,
        m.business_type, m.lat, m.lng, m.addr, m.area, m.floor, m.engaged_num, m.leader, m.id_card, m.phone, m.landlord_landlady, m.landlord_card,
         m.landlord_phone, m.is_sign, m.gas_type, m.accessory, m.store_type, m.store_status, m.status_detail, m.remark, m.create_by, m.create_time, m.update_by, m.update_time,
         ma.id as ma_id, ma.merchant_id as ma_merchant_id, ma.name as ma_name, ma.id_card as ma_id_card, ma.phone as ma_phone,ma.birthday as ma_birthday,
            IFNULL(TIMESTAMPDIFF(YEAR, ma.birthday, CURDATE()), null) AS ma_age
        from tb_merchant_info m left join tb_merchant_assistant ma on ma.merchant_id = m.id
        where m.id = #{id}
    </select>

    <select id="checkTbMerchantInfoUnique" parameterType="TbMerchantInfo" resultMap="TbMerchantInfoResult">
        select id from tb_merchant_info where merchant_name = #{merchantName} and dept_id = #{deptId} and grid_id = #{gridId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMerchantInfo" parameterType="TbMerchantInfo" useGeneratedKeys="true" keyProperty="id">
        insert into tb_merchant_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="communityId != null">community_id,</if>
            <if test="communityName != null">community_name,</if>
            <if test="merchantName != null">merchant_name,</if>
            <if test="licenseName != null">license_name,</if>
            <if test="code != null">code,</if>
            <if test="businessType != null">business_type,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="addr != null">addr,</if>
            <if test="area != null">area,</if>
            <if test="floor != null">floor,</if>
            <if test="engagedNum != null">engaged_num,</if>
            <if test="leader != null">leader,</if>
            <if test="idCard != null">id_card,</if>
            <if test="phone != null">phone,</if>
            <if test="landlordLandlady != null">landlord_landlady,</if>
            <if test="landlordCard != null">landlord_card,</if>
            <if test="landlordPhone != null">landlord_phone,</if>
            <if test="isSign != null">is_sign,</if>
            <if test="gasType != null">gas_type,</if>
            <if test="accessory != null">accessory,</if>
            <if test="storeType != null">store_type,</if>
            <if test="storeStatus != null">store_status,</if>
            <if test="statusDetail != null">status_detail,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="sort != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="merchantName != null">#{merchantName},</if>
            <if test="licenseName != null">#{licenseName},</if>
            <if test="code != null">#{code},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="addr != null">#{addr},</if>
            <if test="area != null">#{area},</if>
            <if test="floor != null">#{floor},</if>
            <if test="engagedNum != null">#{engagedNum},</if>
            <if test="leader != null">#{leader},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="phone != null">#{phone},</if>
            <if test="landlordLandlady != null">#{landlordLandlady},</if>
            <if test="landlordCard != null">#{landlordCard},</if>
            <if test="landlordPhone != null">#{landlordPhone},</if>
            <if test="isSign != null">#{isSign},</if>
            <if test="gasType != null">#{gasType},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="storeType != null">#{storeType},</if>
            <if test="storeStatus != null">#{storeStatus},</if>
            <if test="statusDetail != null">#{statusDetail},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="sort != null">#{sort},</if>
        </trim>
    </insert>

    <update id="updateTbMerchantInfo" parameterType="TbMerchantInfo">
        update tb_merchant_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="merchantName != null">merchant_name = #{merchantName},</if>
            <if test="licenseName != null">license_name = #{licenseName},</if>
            <if test="code != null">code = #{code},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="addr != null">addr = #{addr},</if>
            <if test="area != null">area = #{area},</if>
            <if test="floor != null">floor = #{floor},</if>
            <if test="engagedNum != null">engaged_num = #{engagedNum},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="landlordLandlady != null">landlord_landlady = #{landlordLandlady},</if>
            <if test="landlordCard != null">landlord_card = #{landlordCard},</if>
            <if test="landlordPhone != null">landlord_phone = #{landlordPhone},</if>
            <if test="isSign != null">is_sign = #{isSign},</if>
            <if test="gasType != null">gas_type = #{gasType},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="storeType != null">store_type = #{storeType},</if>
            <if test="storeStatus != null">store_status = #{storeStatus},</if>
            <if test="statusDetail != null">status_detail = #{statusDetail},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="sort != null">sort = #{sort},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbMerchantInfoById" parameterType="Long">
        delete from tb_merchant_info where id = #{id}
    </delete>

    <delete id="deleteTbMerchantInfoByIds" parameterType="String">
        delete from tb_merchant_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMerchantInfoById" parameterType="Long" resultMap="TbMerchantInfoResult">
        select m.id, m.dept_id, m.county, m.country, m.town, m.grid_id, m.grid_name, m.community_id
        from tb_merchant_info m
        where m.id = #{id}
    </select>

    <select id="selectTbMerchantInfoSpaceList" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbMerchantInfoResult">
        <include refid="selectTbMerchantInfoVo"/>
        <where>
            lat IS NOT NULL AND lat != ''
            AND lng IS NOT NULL AND lng != ''
            <if test="deptId != null ">and (m.dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = m.dept_id))</if>
            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
        </where>
    </select>

    <select id="getMerchantInfoByType" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultType="java.util.Map">
        SELECT store_type, COUNT(*) AS count
        FROM tb_merchant_info
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_merchant_info.dept_id))</if>

            <if test="county != null">
                and county = #{county}
            </if>
            <if test="country != null">
                and country = #{country}
            </if>
            <if test="town != null">
                and town = #{town}
            </if>
            <if test="gridId != null">
                and grid_id = #{gridId}
            </if>
            <if test="communityName != null">
                and community_name = #{communityName}
            </if>
            <if test="gridName != null">
                and grid_name = #{gridName}
            </if>
        </where>
        GROUP BY store_type
    </select>
</mapper>