package com.ruoyi.microPlatform.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.*;
import com.ruoyi.microPlatform.mapper.*;
import com.ruoyi.microPlatform.service.ITbResidentMemberService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.service.ITbCommunityResidentService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 小区住户居民信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbCommunityResidentServiceImpl implements ITbCommunityResidentService {
    @Autowired
    private TbCommunityResidentMapper tbCommunityResidentMapper;
    @Autowired
    private TbCommunityInfoMapper communityInfoMapper;
    @Autowired
    private TbResidentMemberMapper memberMapper;
    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ITbResidentMemberService memberService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询小区住户居民信息
     *
     * @param id 小区住户居民信息主键
     * @return 小区住户居民信息
     */
    @Override
    public TbCommunityResident selectTbCommunityResidentById(Long id) {
        TbCommunityResident tbCommunityResident = tbCommunityResidentMapper.selectTbCommunityResidentById(id);
        for (TbResidentMember residentMember : tbCommunityResident.getList()) {
            if (ObjectUtil.isNotEmpty(residentMember.getPersonTypes()) && residentMember.getPersonTypes().size() > 0) {
                residentMember.setPersonTypeIds(residentMember.getPersonTypes().stream().map(TbPersonType::getId).toArray(Integer[]::new));
                residentMember.setTypeValueList(residentMember.getPersonTypes().stream().map(TbPersonType::getLabel).collect(Collectors.toList()));
                String personLabel = residentMember.getPersonTypes().stream().map(TbPersonType::getLabel).collect(Collectors.joining(","));
                residentMember.setPersonLabel(personLabel);
            }
        }
        return tbCommunityResident;
    }

    /**
     * 校验小区住户居民信息是否存在
     *
     * @param tbCommunityResident
     * @return boolean
     */
    @Override
    public boolean checkTbCommunityResident(TbCommunityResident tbCommunityResident) {
        TbCommunityResident old = tbCommunityResidentMapper.checkTbCommunityResidentUnique(tbCommunityResident);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询小区住户居民信息列表
     *
     * @param tbCommunityResident 小区住户居民信息
     * @return 小区住户居民信息
     */
    @Override
    public TableDataInfo selectTbCommunityResidentList(TbCommunityResident tbCommunityResident, Integer pageNum, Integer pageSize) {
        // 1. 查询总数
        int total = tbCommunityResidentMapper.countResidentList(tbCommunityResident);

        List<TbCommunityResident> tbCommunityResidentList = tbCommunityResidentMapper.selectTbCommunityResidentList(tbCommunityResident);

        // 3. 构建分页返回
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(tbCommunityResidentList);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 导入小区住户居民信息
     *
     * @param infos           小区住户居民信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Transactional
    public String importTbCommunityResident(List<TbCommunityResident> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入小区住户居民信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;
        String memBermsg = "";
        int index = 3;
        for (TbCommunityResident info : infos) {
            index++;
            try {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getRoomNum())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据门牌号 不可为空" + "。数据所在行：" + info.getRowNum());
                } else {
                    if (StringUtils.isBlank(info.getBuildingNum())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据门牌号为" + info.getRoomNum() + " 的楼栋号不可为空" + "。数据所在行：" + info.getRowNum());
                    } else {
                        if (StringUtils.isBlank(info.getUnitNum())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据门牌号为" + info.getRoomNum() + " 的单元号不可为空" + "。数据所在行：" + info.getRowNum());
                        } else {
                            if (StringUtils.isBlank(info.getCounty())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据" + info.getRoomNum() + info.getHouseholder() + " 所属县（市、区） 不可为空" + "。数据所在行：" + info.getRowNum());
                            } else {
                                if (StringUtils.isBlank(info.getCountry())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getRoomNum() + info.getHouseholder() + " 所属街道/乡镇 不可为空" + "。数据所在行：" + info.getRowNum());
                                } else {
                                    if (StringUtils.isBlank(info.getTown())) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getRoomNum() + info.getHouseholder() + " 所属村/社区 不可为空" + "。数据所在行：" + info.getRowNum());
                                    } else {
                                        if (StringUtils.isBlank(info.getCommunityName())) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getRoomNum() + info.getHouseholder() + " 所在小区名称 不可为空" + "。数据所在行：" + info.getRowNum());
                                        } else {
                                            if (StringUtils.isBlank(info.getGridName())) {
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据" + info.getRoomNum() + info.getHouseholder() + "所属网格编码 不可为空" + "。数据所在行：" + info.getRowNum());
                                            } else {
                                                boolean flag = true;
                                                String msg = "";
                                                if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getRoomNum() + info.getHouseholder() + "的住户家庭及成员数据" + "。数据所在行：" + info.getRowNum());
                                                }

                                                if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getRoomNum() + info.getHouseholder() + "的住户家庭及成员数据" + "。数据所在行：" + info.getRowNum());
                                                }

                                                if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                                    flag = false;
                                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getRoomNum() + info.getHouseholder() + "的住户家庭及成员数据" + "。数据所在行：" + info.getRowNum());
                                                }

                                                if (!flag) {
                                                    failureNum++;
                                                    failureMsg.append(msg);
                                                } else {
                                                    SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                                    if (ObjectUtil.isEmpty(dept)) {
                                                        failureNum++;
                                                        failureMsg.append("<br/>" + failureNum + "、数据 " + info.getRoomNum() + info.getHouseholder() + "所属辖区未找到！" + "。数据所在行：" + info.getRowNum());
                                                    } else {
                                                        info.setDeptId(dept.getDeptId());
                                                        TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                                            setDeptId(dept.getDeptId());
                                                            setGridName(info.getGridName());
                                                        }});
                                                        if (ObjectUtil.isEmpty(gridInfo) || StringUtils.isBlank(info.getGridName())) {
                                                            failureNum++;
                                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getRoomNum() + info.getHouseholder() + " 所属网格编码未找到，请核查该网格编码！" + info.getGridName() + "。数据所在行：" + info.getRowNum());
                                                        } else {
                                                            info.setGridId(gridInfo.getId());
                                                            info.setSort(gridInfo.getGridSort());
                                                            TbCommunityInfo communityInfo = communityInfoMapper.checkTbCommunityInfoUnique(new TbCommunityInfo() {{
                                                                setGridId(gridInfo.getId());
                                                                setCommunityName(info.getCommunityName());
                                                            }});
                                                            if (ObjectUtil.isEmpty(communityInfo)) {
                                                                failureNum++;
                                                                failureMsg.append("<br/>" + failureNum + "、数据 " + info.getRoomNum() + info.getHouseholder() + " 所在小区名称未找到，请在平台上添加该小区！网格" + info.getGridName() + "下的" + info.getCommunityName() + "。数据所在行：" + info.getRowNum());
                                                            } else {
                                                                info.setCommunityId(communityInfo.getId());
                                                                // 验证是否存在这个数据
                                                                TbCommunityResident resident = tbCommunityResidentMapper.checkTbCommunityResidentUnique(info);
                                                                if (ObjectUtil.isEmpty(resident)) {
                                                                    info.setCreateTime(new Date());
                                                                    info.setCreateBy(operName + "导入");
                                                                    tbCommunityResidentMapper.insertTbCommunityResident(info);
                                                                    msg = "";
                                                                    if (ObjectUtil.isNotEmpty(info.getList()) && info.getList().size() > 0) {
                                                                        for (int i = 0; i < info.getList().size(); i++) {
                                                                            if (i != 0) {
                                                                                index++;
                                                                            }
                                                                            TbResidentMember member = info.getList().get(i);
                                                                            if (StringUtils.isBlank(member.getName())) {
                                                                                msg += "<br/>  (" + (i + 1) + ")、" + info.getRoomNum() + info.getHouseholder() + " 家庭的成员数据姓名不可为空。数据所在行：" + (member.getRowNum() + 1);
                                                                                memBermsg = "成员失败";
                                                                            } else {
//                                                                                if (StringUtils.isBlank(info.getList().get(i).getPhone())) {
//                                                                                    msg += "<br/>" + failureNum + "、数据 " + info.getRoomNum() + info.getHouseholder() + " 下的" + member.getName() + "联系电话不可为空，导入失败！";
//                                                                                } else {
                                                                                if (StringUtils.isBlank(member.getHouseholdRelation())) {
                                                                                    memBermsg = "成员失败";
                                                                                    msg += "<br/>  (" + (i + 1) + ")、" + info.getRoomNum() + info.getHouseholder() + " 家庭的" + member.getName() + "户主关系不可为空。数据所在行：" + (member.getRowNum() + 1);
                                                                                } else {
                                                                                    info.getList().get(i).setOptionType(1);
                                                                                    info.getList().get(i).setResidentId(info.getId());
                                                                                    info.getList().get(i).setGridId(info.getGridId());
                                                                                    info.getList().get(i).setDeptId(info.getDeptId());
                                                                                    info.getList().get(i).setCounty(info.getCounty());
                                                                                    info.getList().get(i).setCountry(info.getCountry());
                                                                                    info.getList().get(i).setTown(info.getTown());
                                                                                    info.getList().get(i).setGridName(info.getGridName());
                                                                                    memberService.insertTbResidentMember(info.getList().get(i));
                                                                                }
//                                                                                }// 联系电话判定
                                                                            }
                                                                        }
                                                                    }
                                                                    successNum++;
                                                                    successMsg.append("<br/>" + successNum + "、家庭数据 " + info.getRoomNum() + " 无问题" + (StringUtils.isBlank(msg) ? "" : "，以下成员数据有问题：" + msg));
                                                                } else {
                                                                    failureNum++;
                                                                    failureMsg.append("<br/>" + failureNum + "、家庭数据 " + info.getHouseholder() + info.getCommunityName() + "，楼栋号：" + info.getBuildingNum() + "，单元号：" + info.getUnitNum() + (StringUtils.isNotBlank(info.getFloorNum()) ? "，楼层号：" + info.getFloorNum() : "") + "，门牌号：" + info.getRoomNum() + " 已存在" + "。数据所在行：" + info.getRowNum());
                                                                }
                                                            }

                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                //先进行判断当前info的小区名称是否重复，在infos中。

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、户主 " + info.getHouseholder() + "家的数据导入失败：";
                failureMsg.append(msg + e.getMessage() + "。家庭住户数据所在行：" + info.getRowNum());
                e.printStackTrace();
            }
        }
        if (failureNum > 0 || StringUtils.isNotBlank(memBermsg)) {

            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "户家庭住户数据，其中" + successNum + "户家庭数据无问题， " + (StringUtils.isNotBlank(memBermsg) ? "但成员数据有问题！" : "") + failureNum + " 户家庭数据格式不正确，具体信息如下：");
            throw new ServiceException(failureMsg.toString() + successMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 户");
            memberService.updatePersonLabel();
        }
        return successMsg.toString();
    }

    /**
     * 新增小区住户居民信息
     *
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    @Override
    public int insertTbCommunityResident(TbCommunityResident tbCommunityResident) {
        tbCommunityResident.setCreateTime(DateUtils.getNowDate());
        return tbCommunityResidentMapper.insertTbCommunityResident(tbCommunityResident);
    }

    /**
     * 修改小区住户居民信息
     *
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    @Override
    public int updateTbCommunityResident(TbCommunityResident tbCommunityResident) {
        tbCommunityResident.setUpdateTime(DateUtils.getNowDate());
        return tbCommunityResidentMapper.updateTbCommunityResident(tbCommunityResident);
    }

    /**
     * 批量删除小区住户居民信息
     *
     * @param ids 需要删除的小区住户居民信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTbCommunityResidentByIds(Long[] ids) {
        int i = tbCommunityResidentMapper.deleteTbCommunityResidentByIds(ids);
        memberMapper.deleteTbResidentMemberByMainIds(ids);
        return i;
    }

    /**
     * 删除小区住户居民信息信息
     *
     * @param id 小区住户居民信息主键
     * @return 结果
     */
    @Override
    public int deleteTbCommunityResidentById(Long id) {
        return tbCommunityResidentMapper.deleteTbCommunityResidentById(id);
    }

    /**
     * 查询小区住户居民信息和对应的居民住户成员信息  1对多
     *
     * @param communityResident
     * @return
     */
    @Override
    public List<TbCommunityResident> selectTbCommunityResidentAndMemberList(TbCommunityResident communityResident) {
        List<TbCommunityResident> tbCommunityResidents = tbCommunityResidentMapper.selectTbCommunityResidentList(communityResident);
        if (!tbCommunityResidents.isEmpty()) {
            List<Long> mainIds = tbCommunityResidents.stream().map(TbCommunityResident::getId)
                    .collect(Collectors.toList());
            TbResidentMember member = new TbResidentMember();
            member.setMainIds(mainIds);

            List<TbResidentMember> members = memberService.selectTbResidentMemberListByMainIds(member);

            //List<TbResidentMember> members = (List<TbResidentMember>) tableDataInfo.getRows();

            Map<Long, List<TbResidentMember>> subMap = members.stream().collect(Collectors.groupingBy(TbResidentMember::getResidentId));

            tbCommunityResidents.stream().forEach(main ->
                    main.setList(subMap.getOrDefault(main.getId(), Collections.emptyList())));
        }
        return tbCommunityResidents;
    }

    @Override
    public Integer selectTbCommunityResidentCount(TbCommunityResident communityResident) {
        List<Long> mainIds = tbCommunityResidentMapper.selectTbCommunityResidentCount(communityResident);
        Integer totalCount = mainIds.size();
        if (totalCount.intValue() == 0) {
            return 0;
        }
        TbResidentMember member = new TbResidentMember();
        member.setMainIds(mainIds);
        Integer integer = memberService.selectTbResidentMemberCount(member);
        int max = Math.max(totalCount, ObjectUtil.isNotEmpty(integer) ? integer : 0);
        return max;
    }

    @Override
    public List<CountyStatics> selectTypeList(Integer deptLevelType, String county, String country, Integer dataType) {
        if (dataType == 1) {
            // 住户家庭
            return tbCommunityResidentMapper.selectTypeList(deptLevelType, county, country);
        } else if (dataType == 2) {
            // 家庭成员
            return tbCommunityResidentMapper.selectTypeList2(deptLevelType, county, country);
        } else if (dataType == 3) {
            // 门店商户
            return tbCommunityResidentMapper.selectTypeList3(deptLevelType, county, country);
        } else if (dataType == 4) {
            // 门店店员
            return tbCommunityResidentMapper.selectTypeList4(deptLevelType, county, country);
        } else if (dataType == 5) {
            // 网格信息
            return tbCommunityResidentMapper.selectTypeList5(deptLevelType, county, country);
        } else if (dataType == 6) {
            // 网格巡查
            return tbCommunityResidentMapper.selectTypeList6(deptLevelType, county, country);
        } else if (dataType == 7) {
            // 门店巡查
            return tbCommunityResidentMapper.selectTypeList7(deptLevelType, county, country);
        } else if (dataType == 8) {
            // 入户走访
            return tbCommunityResidentMapper.selectTypeList8(deptLevelType, county, country);
        } else if (dataType == 10) {
            // 矛盾纠纷
            return tbCommunityResidentMapper.selectTypeList10(deptLevelType, county, country);
        } else if (dataType == 9) {
            // 居民吹哨
            return tbCommunityResidentMapper.selectTypeList9(deptLevelType, county, country);
        } else if (dataType == 11) {
            // 开放预约
            return tbCommunityResidentMapper.selectTypeList11(deptLevelType, county, country);
        } else if (dataType == 12) {
            // 小区信息
            return tbCommunityResidentMapper.selectTypeList12(deptLevelType, county, country);
        } else if (dataType == 13) {
            // 网格员
            return tbCommunityResidentMapper.selectTypeList13(deptLevelType, county, country);
        } else if (dataType == 14) {
            // 兼职网格员
            return tbCommunityResidentMapper.selectTypeList14(deptLevelType, county, country);
        } else if (dataType == 15) {
            // 社区功能室
            return tbCommunityResidentMapper.selectTypeList15(deptLevelType, county, country);
        } else if (dataType == 16) {
            // 便民电话
            return tbCommunityResidentMapper.selectTypeList16(deptLevelType, county, country);
        } else if (dataType == 17) {
            // 志愿报名
            return tbCommunityResidentMapper.selectTypeList17(deptLevelType, county, country);
        } else {
            return null;
        }
    }
}
