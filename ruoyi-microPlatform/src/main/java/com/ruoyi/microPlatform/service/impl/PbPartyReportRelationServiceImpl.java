package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyReportRelation;
import com.ruoyi.microPlatform.mapper.PbPartyReportRelationMapper;
import com.ruoyi.microPlatform.service.IPbPartyReportRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 党组织党员双报到关系Service业务层处理
 */
@Service
public class PbPartyReportRelationServiceImpl implements IPbPartyReportRelationService {

    @Autowired
    private PbPartyReportRelationMapper pbPartyReportRelationMapper;

    @Override
    public PbPartyReportRelation selectPbPartyReportRelationById(Long id) {
        return pbPartyReportRelationMapper.selectPbPartyReportRelationById(id);
    }

    @Override
    public boolean checkPbPartyReportRelation(PbPartyReportRelation relation) {
        PbPartyReportRelation old = pbPartyReportRelationMapper.checkPbPartyReportRelationUnique(relation);
        return old != null;
    }

    @Override
    public List<PbPartyReportRelation> selectPbPartyReportRelationList(PbPartyReportRelation relation) {
        return pbPartyReportRelationMapper.selectPbPartyReportRelationList(relation);
    }

    @Override
    public int insertPbPartyReportRelation(PbPartyReportRelation relation) {
        relation.setCreateTime(DateUtils.getNowDate());
        return pbPartyReportRelationMapper.insertPbPartyReportRelation(relation);
    }

    @Override
    public int updatePbPartyReportRelation(PbPartyReportRelation relation) {
        relation.setUpdateTime(DateUtils.getNowDate());
        return pbPartyReportRelationMapper.updatePbPartyReportRelation(relation);
    }

    @Override
    public int deletePbPartyReportRelationByIds(Long[] ids) {
        return pbPartyReportRelationMapper.deletePbPartyReportRelationByIds(ids);
    }

    @Override
    public int deletePbPartyReportRelationById(Long id) {
        return pbPartyReportRelationMapper.deletePbPartyReportRelationById(id);
    }

    @Override
    public List<CommonBaseCount> doubleReporting() {
        List<CommonBaseCount> dbCounts = pbPartyReportRelationMapper.doubleReporting();
        int org = 0;
        int member = 0;
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                if ("ORG_REPORT".equalsIgnoreCase(c.getLable())) {
                    org = c.getCount() == null ? 0 : c.getCount();
                } else if ("MEMBER_REPORT".equalsIgnoreCase(c.getLable())) {
                    member = c.getCount() == null ? 0 : c.getCount();
                }
            }
        }
        List<CommonBaseCount> res = new ArrayList<>();
        CommonBaseCount orgItem = new CommonBaseCount();
        orgItem.setLable("组织报到");
        orgItem.setCount(org);
        res.add(orgItem);
        CommonBaseCount memberItem = new CommonBaseCount();
        memberItem.setLable("个人报到");
        memberItem.setCount(member);
        res.add(memberItem);
        return res;
    }
}
