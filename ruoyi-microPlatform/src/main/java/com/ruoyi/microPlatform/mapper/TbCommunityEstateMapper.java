package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbCommunityEstate;

/**
 * 小区物业员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface TbCommunityEstateMapper 
{
    /**
     * 查询小区物业员
     * 
     * @param id 小区物业员主键
     * @return 小区物业员
     */
    public TbCommunityEstate selectTbCommunityEstateById(Long id);

    /**
     * 校验小区物业员是否唯一
     *
     * @param tbCommunityEstate
     * @return 小区物业员
     */
    public TbCommunityEstate checkTbCommunityEstateUnique(TbCommunityEstate tbCommunityEstate);

    /**
     * 查询小区物业员列表
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 小区物业员集合
     */
    public List<TbCommunityEstate> selectTbCommunityEstateList(TbCommunityEstate tbCommunityEstate);

    /**
     * 新增小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    public int insertTbCommunityEstate(TbCommunityEstate tbCommunityEstate);

    /**
     * 修改小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    public int updateTbCommunityEstate(TbCommunityEstate tbCommunityEstate);

    /**
     * 删除小区物业员
     * 
     * @param id 小区物业员主键
     * @return 结果
     */
    public int deleteTbCommunityEstateById(Long id);

    /**
     * 批量删除小区物业员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbCommunityEstateByIds(Long[] ids);

    public int deleteTbCommunityEstateByMainIds(Long[] ids);
}
