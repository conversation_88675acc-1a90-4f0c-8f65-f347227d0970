package com.ruoyi.dataview.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.dataview.domain.bigData.param.DbBigdataParam;
import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbHotlineDetail;
import com.ruoyi.dataview.mapper.DbHotlineDetailMapper;
import com.ruoyi.dataview.service.IDbHotlineDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 12345热线中心综合明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbHotlineDetailServiceImpl implements IDbHotlineDetailService {
    @Autowired
    private DbHotlineDetailMapper dbHotlineDetailMapper;

    /**
     * 查询12345热线中心综合明细
     *
     * @param id 12345热线中心综合明细主键
     * @return 12345热线中心综合明细
     */
    @Override
    public DbHotlineDetail selectDbHotlineDetailById(Long id) {
        return dbHotlineDetailMapper.selectDbHotlineDetailById(id);
    }

    /**
     * 校验12345热线中心综合明细是否存在
     *
     * @param dbHotlineDetail
     * @return boolean
     */
    @Override
    public boolean checkDbHotlineDetail(DbHotlineDetail dbHotlineDetail) {
        DbHotlineDetail old = dbHotlineDetailMapper.checkDbHotlineDetailUnique(dbHotlineDetail);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询12345热线中心综合明细列表
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 12345热线中心综合明细
     */
    @Override
    public List<DbHotlineDetail> selectDbHotlineDetailList(DbHotlineDetail dbHotlineDetail) {
        return dbHotlineDetailMapper.selectDbHotlineDetailList(dbHotlineDetail);
    }

    @Override
    public int selectDbHotlineDetailCount(DbHotlineDetail dbHotlineDetail) {
        return dbHotlineDetailMapper.selectDbHotlineDetailCount(dbHotlineDetail);
    }

    /**
     * 导入12345热线中心综合明细
     *
     * @param infos           12345热线中心综合明细列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbHotlineDetail(List<DbHotlineDetail> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入12345热线中心综合明细数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbHotlineDetail info : infos) {
            try {
                // 验证是否存在这个数据
                DbHotlineDetail old = dbHotlineDetailMapper.checkDbHotlineDetailUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbHotlineDetailMapper.insertDbHotlineDetail(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbHotlineDetailMapper.updateDbHotlineDetail(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    @Override
    public int insertDbHotlineDetail(DbHotlineDetail dbHotlineDetail) {
        dbHotlineDetail.setCreateTime(DateUtils.getNowDate());
        return dbHotlineDetailMapper.insertDbHotlineDetail(dbHotlineDetail);
    }

    /**
     * 修改12345热线中心综合明细
     *
     * @param dbHotlineDetail 12345热线中心综合明细
     * @return 结果
     */
    @Override
    public int updateDbHotlineDetail(DbHotlineDetail dbHotlineDetail) {
        dbHotlineDetail.setUpdateTime(DateUtils.getNowDate());
        return dbHotlineDetailMapper.updateDbHotlineDetail(dbHotlineDetail);
    }

    /**
     * 批量删除12345热线中心综合明细
     *
     * @param ids 需要删除的12345热线中心综合明细主键
     * @return 结果
     */
    @Override
    public int deleteDbHotlineDetailByIds(Long[] ids) {
        return dbHotlineDetailMapper.deleteDbHotlineDetailByIds(ids);
    }

    /**
     * 删除12345热线中心综合明细信息
     *
     * @param id 12345热线中心综合明细主键
     * @return 结果
     */
    @Override
    public int deleteDbHotlineDetailById(Long id) {
        return dbHotlineDetailMapper.deleteDbHotlineDetailById(id);
    }

    @Override
    public List<CommonBaseCount> hotlineDetailGroupRec(DbBigdataParam bigdataParam) {
        return dbHotlineDetailMapper.hotlineDetailGroupRec(bigdataParam);
    }
}
