<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbDeptUserMapper">

    <resultMap type="TbDeptUser" id="TbDeptUserResult">
        <result property="id" column="id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="idPhoto" column="id_photo"/>
        <result property="postDesc" column="post_desc"/>
        <result property="status" column="status"/>
        <result property="idCard" column="id_card"/>
        <result property="birthday" column="birthday" jdbcType="DATE"/>
        <result property="hasParty" column="has_party"/>
        <result property="hasCertificate" column="has_certificate"/>
        <result property="certificate" column="certificate"/>
        <result property="age" column="age"/>
        <result property="postStr" column="post_str"/>
    </resultMap>

    <sql id="selectTbDeptUserVo">
        select id, county, country, town, dept_id, user_id, name, phone, id_photo, post_desc, status,id_card,
               birthday,has_party,has_certificate,certificate,TIMESTAMPDIFF(YEAR, IFNULL(birthday, CURDATE()), CURDATE()) AS age, post_str
        from tb_dept_user
    </sql>

    <select id="selectTbDeptUserList" parameterType="TbDeptUser" resultMap="TbDeptUserResult">
        <include refid="selectTbDeptUserVo"/>
        <where>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_dept_user.dept_id))</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="idPhoto != null  and idPhoto != ''">and id_photo = #{idPhoto}</if>
            <if test="postDesc != null  and postDesc != ''">and post_desc = #{postDesc}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="postStr != null  and postStr != ''">and post_str = #{postStr}</if>
            <if test="postStrArr != null and postStrArr.size() > 0">
                and
                <foreach item="post" collection="postStrArr" separator="and">
                    FIND_IN_SET(#{post}, post_str) > 0
                </foreach>
            </if>
            <if test="notGrid != null ">and id not in (select dept_user_id from tb_dept_user_grid where grid_id = #{notGrid})</if>
            <if test="gridId != null ">and id in (select dept_user_id from tb_dept_user_grid where grid_id = #{gridId})</if>
        </where>
        order by id desc
    </select>

    <select id="selectExportDeptUserCount" parameterType="TbDeptUser" resultType="Long">
        select id from tb_dept_user
        <where>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_dept_user.dept_id))</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="idPhoto != null  and idPhoto != ''">and id_photo = #{idPhoto}</if>
            <if test="postDesc != null  and postDesc != ''">and post_desc = #{postDesc}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="idCard != null  and idCard != ''">and id_card = #{idCard}</if>
            <if test="postStr != null  and postStr != ''">and post_str = #{postStr}</if>
            <if test="notGrid != null ">and id not in (select dept_user_id from tb_dept_user_grid where grid_id = #{notGrid})</if>
            <if test="gridId != null ">and id in (select dept_user_id from tb_dept_user_grid where grid_id = #{gridId})</if>
        </where>
        order by id desc
    </select>

    <select id="selectTbDeptUserById" parameterType="Long" resultMap="TbDeptUserResult">
        <include refid="selectTbDeptUserVo"/>
        where id = #{id}
    </select>

    <select id="checkTbDeptUserUnique" parameterType="TbDeptUser" resultMap="TbDeptUserResult">
        select id from tb_dept_user
        <where>
            <if test="userId  != null">
                user_id = #{userId}
            </if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="id != null">
                and id != #{id}
            </if>
        </where>
        limit 1
    </select>

    <insert id="insertTbDeptUser" parameterType="TbDeptUser" useGeneratedKeys="true" keyProperty="id">
        insert into tb_dept_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="idPhoto != null">id_photo,</if>
            <if test="postDesc != null">post_desc,</if>
            <if test="status != null">status,</if>
            <if test="idCard != null">id_card,</if>
            <if test="birthday != null">birthday,</if>
            <if test="hasParty != null">has_party,</if>
            <if test="hasCertificate != null">has_certificate,</if>
            <if test="certificate != null">certificate,</if>
            <if test="postStr != null">post_str,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idPhoto != null">#{idPhoto},</if>
            <if test="postDesc != null">#{postDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="hasParty != null">#{hasParty},</if>
            <if test="hasCertificate != null">#{hasCertificate},</if>
            <if test="certificate != null">#{certificate},</if>
            <if test="postStr != null">#{postStr},</if>
        </trim>
    </insert>

    <update id="updateTbDeptUser" parameterType="TbDeptUser">
        update tb_dept_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="county != null and county != ''">county = #{county},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="town != null and town != ''">town = #{town},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="phone != null and county != ''">phone = #{phone},</if>
            <if test="idPhoto != null and idPhoto != ''">id_photo = #{idPhoto},</if>
            <if test="postDesc != null and postDesc != ''">post_desc = #{postDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="idCard != null and idCard != ''">id_card = #{idCard},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="hasParty != null and hasParty != ''">has_party = #{hasParty},</if>
            <if test="hasCertificate != null and hasCertificate != ''">has_certificate = #{hasCertificate},</if>
            <if test="certificate != null and certificate != ''">certificate = #{certificate},</if>
            <if test="postStr != null and postStr != ''">post_str = #{postStr},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbDeptUserById" parameterType="Long">
        delete from tb_dept_user where id = #{id}
    </delete>

    <delete id="deleteTbDeptUserByIds" parameterType="String">
        delete from tb_dept_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDeptUserPostType" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="TbDeptUserResult">
        SELECT id, post_str
        FROM tb_dept_user
        WHERE status = '0'
        <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = tb_dept_user.dept_id))</if>

        <if test="county != null">
            and county = #{county}
        </if>
        <if test="country != null">
            and country = #{country}
        </if>
        <if test="town != null">
            and town = #{town}
        </if>
    </select>
    <select id="partyMemberCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tb_dept_user
        WHERE status = '0'
        and has_party = 1
    </select>
    <select id="partyMemberGroupSexCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT CAST(sex AS SIGNED) as type, count(1) as count
        FROM tb_dept_user
        WHERE status = '0'
          and has_party = 1
        GROUP BY CAST(sex AS SIGNED)
    </select>
    <select id="secretaryGroupSexCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT CAST(sex AS SIGNED) as type, count(1) as count
        FROM tb_dept_user
        WHERE status = '0'
          and is_secretary = 1
        GROUP BY CAST(sex AS SIGNED)
    </select>
    <select id="secretaryGroupPartyCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        SELECT CAST(has_party AS SIGNED) as type, count(1) as count
        FROM tb_dept_user
        WHERE status = '0'
          and is_secretary = 1
        GROUP BY CAST(has_party AS SIGNED)
    </select>
    <select id="secretaryGroupAgeCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select
            case
                when age <![CDATA[ <= ]]> 34 then 1
                when age between 35 and 60 then 2
                when age > 60 then 3
            end as type,
            count(1) as count
        from tb_dept_user
        where status = '0'
          and is_secretary = 1
          and age is not null
        group by
            case
                when age <![CDATA[ <= ]]> 34 then 1
                when age between 35 and 60 then 2
                when age > 60 then 3
            end
    </select>
    <select id="secretaryGroupEducationCount" resultType="com.ruoyi.microPlatform.bigdata.res.CommonBaseCount">
        select ifnull(education_level, '未知') as lable, count(1) as count
        from tb_dept_user
        where status = '0'
          and is_secretary = 1
        group by ifnull(education_level, '未知')
        order by count desc
    </select>
</mapper>