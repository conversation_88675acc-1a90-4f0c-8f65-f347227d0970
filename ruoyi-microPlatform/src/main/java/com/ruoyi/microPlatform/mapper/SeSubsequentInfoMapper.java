package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.SeSubsequentInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 后续动态信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface SeSubsequentInfoMapper 
{
    /**
     * 查询后续动态信息
     * 
     * @param id 后续动态信息主键
     * @return 后续动态信息
     */
    public SeSubsequentInfo selectSeSubsequentInfoById(Long id);

    /**
     * 校验后续动态信息是否唯一
     *
     * @param seSubsequentInfo
     * @return 后续动态信息
     */
    public SeSubsequentInfo checkSeSubsequentInfoUnique(SeSubsequentInfo seSubsequentInfo);

    /**
     * 查询后续动态信息列表
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 后续动态信息集合
     */
    public List<SeSubsequentInfo> selectSeSubsequentInfoList(SeSubsequentInfo seSubsequentInfo);

    /**
     * 新增后续动态信息
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    public int insertSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo);

    /**
     * 修改后续动态信息
     * 
     * @param seSubsequentInfo 后续动态信息
     * @return 结果
     */
    public int updateSeSubsequentInfo(SeSubsequentInfo seSubsequentInfo);

    /**
     * 删除后续动态信息
     * 
     * @param id 后续动态信息主键
     * @return 结果
     */
    public int deleteSeSubsequentInfoById(Long id);

    /**
     * 批量删除后续动态信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeSubsequentInfoByIds(Long[] ids);

    /**
     * 查询每个主表关联的动态信息数量
     * @param seSubsequentInfo
     * @return
     */
    public Integer selectDataCount(SeSubsequentInfo seSubsequentInfo);

    void deleteSeSubsequentInfoByPubIds(@Param("ids") Long[] ids,@Param("formType") String formType);

}
