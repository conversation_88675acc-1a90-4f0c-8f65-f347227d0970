package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbCommunityEstate;

/**
 * 小区物业员Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-10
 */
public interface ITbCommunityEstateService 
{
    /**
     * 查询小区物业员
     * 
     * @param id 小区物业员主键
     * @return 小区物业员
     */
    public TbCommunityEstate selectTbCommunityEstateById(Long id);

    /**
     * 校验小区物业员是否存在
     *
     * @param tbCommunityEstate 小区物业员
     * @return 小区物业员
     */
    public boolean checkTbCommunityEstate(TbCommunityEstate tbCommunityEstate);

    /**
     * 查询小区物业员列表
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 小区物业员集合
     */
    public List<TbCommunityEstate> selectTbCommunityEstateList(TbCommunityEstate tbCommunityEstate);

    /**
     * 导入小区物业员
     *
     * @param infos       小区物业员列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbCommunityEstate(List<TbCommunityEstate> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    public int insertTbCommunityEstate(TbCommunityEstate tbCommunityEstate);

    /**
     * 修改小区物业员
     * 
     * @param tbCommunityEstate 小区物业员
     * @return 结果
     */
    public int updateTbCommunityEstate(TbCommunityEstate tbCommunityEstate);

    /**
     * 批量删除小区物业员
     * 
     * @param ids 需要删除的小区物业员主键集合
     * @return 结果
     */
    public int deleteTbCommunityEstateByIds(Long[] ids);

    /**
     * 删除小区物业员信息
     * 
     * @param id 小区物业员主键
     * @return 结果
     */
    public int deleteTbCommunityEstateById(Long id);
}
