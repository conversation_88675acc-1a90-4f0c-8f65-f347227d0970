package com.ruoyi.dataview.mapper;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbEnterpriseAppeal;

/**
 * 万人助企诉求Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
public interface DbEnterpriseAppealMapper 
{
    /**
     * 查询万人助企诉求
     * 
     * @param id 万人助企诉求主键
     * @return 万人助企诉求
     */
    public DbEnterpriseAppeal selectDbEnterpriseAppealById(Long id);

    /**
     * 校验万人助企诉求是否唯一
     *
     * @param dbEnterpriseAppeal
     * @return 万人助企诉求
     */
    public DbEnterpriseAppeal checkDbEnterpriseAppealUnique(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 查询万人助企诉求列表
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 万人助企诉求集合
     */
    public List<DbEnterpriseAppeal> selectDbEnterpriseAppealList(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 新增万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    public int insertDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 修改万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    public int updateDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal);

    /**
     * 删除万人助企诉求
     * 
     * @param id 万人助企诉求主键
     * @return 结果
     */
    public int deleteDbEnterpriseAppealById(Long id);

    /**
     * 批量删除万人助企诉求
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDbEnterpriseAppealByIds(Long[] ids);

    List<CommonBaseCount> enterpriseAppealGroup();
}
