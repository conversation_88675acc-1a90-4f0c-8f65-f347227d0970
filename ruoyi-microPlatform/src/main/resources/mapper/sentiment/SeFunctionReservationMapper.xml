<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeFunctionReservationMapper">

    <resultMap type="SeFunctionReservation" id="SeFunctionReservationResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="county"    column="county"    />
        <result property="country"    column="country"    />
        <result property="town"    column="town"    />
        <result property="gridName"    column="grid_name"    />
        <result property="functionId"    column="function_id"    />
        <result property="functionName"    column="function_name"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="phone"    column="phone"    />
        <result property="createTime"    column="create_time"    />
        <result property="registerTime"    column="register_time"    />
        <result property="reserveUseStart"    column="reserve_use_start"    />
        <result property="reserveUseEnd"    column="reserve_use_end"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
        <result property="auditUser"    column="audit_user"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditResult"    column="audit_result"    />
        <result property="auditReason"    column="audit_reason"    />
        <result property="msg"    column="msg"    />
    </resultMap>

    <sql id="selectSeFunctionReservationVo">
        select id, dept_id, county, country, town, grid_name, function_id, function_name, user_id, nick_name, phone,
        create_time,register_time, reserve_use_start, reserve_use_end, remark, status, audit_user, audit_time,
        audit_result, audit_reason, msg from se_function_reservation
    </sql>

    <select id="selectSeFunctionReservationList" parameterType="SeFunctionReservation"
            resultMap="SeFunctionReservationResult">
        <include refid="selectSeFunctionReservationVo"/>
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_function_reservation.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="functionId != null ">and function_id = #{functionId}</if>
            <if test="functionName != null  and functionName != ''">and function_name like concat('%', #{functionName},
                '%')
            </if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>


            <if test="reserveUseEnd != null "> and reserve_use_end = #{reserveUseEnd}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="auditUser != null  and auditUser != ''"> and audit_user = #{auditUser}</if>
            <if test="params.beginAuditTime != null and params.beginAuditTime != '' and params.endAuditTime != null and params.endAuditTime != ''">
                and audit_time between #{params.beginAuditTime} and #{params.endAuditTime}
            </if>
            <if test="auditResult != null "> and audit_result = #{auditResult}</if>
            <if test="msg != null "> and msg = #{msg}</if>

            <!--<if test="params.beginReserveUseStart != null and params.beginReserveUseStart != '' and params.endReserveUseStart != null and params.endReserveUseStart != ''">
                and reserve_use_start between #{params.beginReserveUseStart} and #{params.endReserveUseStart}
            </if>-->

            <if test="startTime != null and endTime != null">
                AND reserve_use_start BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(startTime == null or startTime == null) and time != null">
                <choose>
                    <when test="time == 'day'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="time == 'week'">
                        AND YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="time == 'month'">
                        AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    </when>
                    <otherwise>
                        AND DATE(create_time) = CURDATE()
                    </otherwise>
                </choose>
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectSeFunctionReservationById" parameterType="Long" resultMap="SeFunctionReservationResult">
        <include refid="selectSeFunctionReservationVo"/>
        where id = #{id}
    </select>

    <select id="checkSeFunctionReservationUnique" parameterType="SeFunctionReservation"
            resultMap="SeFunctionReservationResult">
        select id from se_function_reservation where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeFunctionReservation" parameterType="SeFunctionReservation" useGeneratedKeys="true" keyProperty="id">
        insert into se_function_reservation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="functionId != null">function_id,</if>
            <if test="functionName != null and functionName != ''">function_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="createTime != null">create_time,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="reserveUseStart != null">reserve_use_start,</if>
            <if test="reserveUseEnd != null">reserve_use_end,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="auditUser != null">audit_user,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="auditReason != null">audit_reason,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="functionId != null">#{functionId},</if>
            <if test="functionName != null and functionName != ''">#{functionName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="reserveUseStart != null">#{reserveUseStart},</if>
            <if test="reserveUseEnd != null">#{reserveUseEnd},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="auditUser != null">#{auditUser},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="auditReason != null">#{auditReason},</if>
        </trim>
    </insert>

    <update id="updateSeFunctionReservation" parameterType="SeFunctionReservation">
        update se_function_reservation
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="functionId != null">function_id = #{functionId},</if>
            <if test="functionName != null and functionName != ''">function_name = #{functionName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="reserveUseStart != null">reserve_use_start = #{reserveUseStart},</if>
            <if test="reserveUseEnd != null">reserve_use_end = #{reserveUseEnd},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditUser != null">audit_user = #{auditUser},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="msg != null">msg = #{msg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeFunctionReservationById" parameterType="Long">
        delete from se_function_reservation where id = #{id}
    </delete>

    <delete id="deleteSeFunctionReservationByIds" parameterType="String">
        delete from se_function_reservation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectSubmitCount" parameterType="SeFunctionReservation" resultType="map">
        select count(*) as submitCount, max(create_time) as submitTime, sum(IFNULL(msg, 0)) as msgCount from se_function_reservation
        where user_id = #{userId}
    </select>

    <select id="selectSubmitCountByHome" resultType="int">
        SELECT COUNT(*) AS count
        FROM se_function_reservation
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_function_reservation.dept_id))</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="startTime != null and endTime != null">
                AND create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="(startTime == null or startTime == null) and time != null">
                <choose>
                    <when test="time == 'day'">
                        AND DATE(create_time) = CURDATE()
                    </when>
                    <when test="time == 'week'">
                        AND YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)
                    </when>
                    <when test="time == 'month'">
                        AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m')
                    </when>
                    <otherwise>
                        AND DATE(create_time) = CURDATE()
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
</mapper>