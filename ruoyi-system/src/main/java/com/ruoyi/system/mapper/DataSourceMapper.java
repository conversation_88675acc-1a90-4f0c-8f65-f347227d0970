package com.ruoyi.system.mapper;

import com.ruoyi.common.DateBaseTables.Columns;
import com.ruoyi.common.DateBaseTables.DataSourceTable;
import com.ruoyi.common.DateBaseTables.Tables;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface DataSourceMapper {
    /**
     * 获取表结构信息
     */
    @Select("SHOW FULL COLUMNS FROM ${tableName}")
    List<Map<String, Object>> getTableColumns(@Param("tableName") String tableName);

    /**
     * 获取数据库的所有表信息
     */
    @Select("select table_name as tableName,table_comment as tableComment,table_schema as tableSchema,engine,table_rows as tableRows" +
            ",auto_increment as autoIncrement,create_time as createTime," +
            "update_time as updateTime from information_schema.tables where table_schema=#{dataSourceName} order by create_time DESC")
    List<Tables> getDataSourceTables(@Param("dataSourceName") String dataSourceName);

    /**
     * 检查数据库存不存在表名
     */
    @Select("select table_name as tableName  from information_schema.tables where table_schema=#{dataSourceName} and table_name=#{tableName}")
    String  getTableName(@Param("tableName")String tableName,@Param("dataSourceName") String dataSourceName);

    /**
     * 获取当前连接的数据库名称
     */
    @Select("select database() limit 1")
    String getDataSourceName();

    /**
     * 创建表
     */
    void createTable(@Param("table") DataSourceTable table);

    /**
     * 修改表
     */
    void alterTableField(@Param("column")Columns columns, @Param("tableName") String tableName);

    /**
     * 删除表字段
     */
    void dropTableField(@Param("dropFields")List<String> dropFields,@Param("tableName")String tableName);

    /**
     * 删除表
     */
    void dropTable(@Param("tableName")String tableName);

    /**
     * 清空表数据
     */
    void truncateTable(@Param("tableName")String tableName);

    /**
     * 增加表字段
     */
    void addTableField(@Param("table") DataSourceTable table);

    /**
     * 修改表注释
     */
    void alterTableComment(@Param("table") DataSourceTable table);

}
