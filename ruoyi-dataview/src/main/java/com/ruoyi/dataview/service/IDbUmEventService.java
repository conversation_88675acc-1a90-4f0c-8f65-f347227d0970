package com.ruoyi.dataview.service;

import java.util.List;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbUmEvent;

/**
 * 运管服案件案件数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IDbUmEventService 
{
    /**
     * 查询运管服案件案件数据
     * 
     * @param id 运管服案件案件数据主键
     * @return 运管服案件案件数据
     */
    public DbUmEvent selectDbUmEventById(Long id);

    /**
     * 校验运管服案件案件数据是否存在
     *
     * @param dbUmEvent 运管服案件案件数据
     * @return 运管服案件案件数据
     */
    public boolean checkDbUmEvent(DbUmEvent dbUmEvent);

    /**
     * 查询运管服案件案件数据列表
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 运管服案件案件数据集合
     */
    public List<DbUmEvent> selectDbUmEventList(DbUmEvent dbUmEvent);

    /**
     * 导入运管服案件案件数据
     *
     * @param infos       运管服案件案件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbUmEvent(List<DbUmEvent> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增运管服案件案件数据
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    public int insertDbUmEvent(DbUmEvent dbUmEvent);

    /**
     * 修改运管服案件案件数据
     * 
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    public int updateDbUmEvent(DbUmEvent dbUmEvent);

    /**
     * 批量删除运管服案件案件数据
     * 
     * @param ids 需要删除的运管服案件案件数据主键集合
     * @return 结果
     */
    public int deleteDbUmEventByIds(Long[] ids);

    /**
     * 删除运管服案件案件数据信息
     * 
     * @param id 运管服案件案件数据主键
     * @return 结果
     */
    public int deleteDbUmEventById(Long id);

    List<CommonBaseCount> dbUmEventBigCount(String classTypeName);
}
