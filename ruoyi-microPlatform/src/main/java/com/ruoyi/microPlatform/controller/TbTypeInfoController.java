package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbTypeInfo;
import com.ruoyi.microPlatform.service.ITbTypeInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * 矛盾纠纷类别编码Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@RestController
@RequestMapping("/issue/typeInfo")
public class TbTypeInfoController extends BaseController
{
    @Autowired
    private ITbTypeInfoService tbTypeInfoService;

    /**
     * 查询矛盾纠纷类别编码列表
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:list')")
    @GetMapping("/list")
    public AjaxResult list(TbTypeInfo tbTypeInfo)
    {
        List<TbTypeInfo> list = tbTypeInfoService.selectTbTypeInfoList(tbTypeInfo);
        return success(list);
    }

    @GetMapping("/selectList")
    public AjaxResult selectList(TbTypeInfo tbTypeInfo)
    {
        List<TbTypeInfo> list = tbTypeInfoService.selectTbTypeInfoList2(tbTypeInfo);
        return success(list);
    }

    /**
     * 导出矛盾纠纷类别编码列表
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:export')")
    @Log(title = "矛盾纠纷类别编码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbTypeInfo tbTypeInfo)
    {
        List<TbTypeInfo> list = tbTypeInfoService.selectTbTypeInfoList(tbTypeInfo);
        ExcelUtil<TbTypeInfo> util = new ExcelUtil<TbTypeInfo>(TbTypeInfo.class);
        util.exportExcel(response, list, "矛盾纠纷类别编码数据");
    }

    @Log(title = "矛盾纠纷类别编码", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbTypeInfo> util = new ExcelUtil<TbTypeInfo>(TbTypeInfo.class);
        List<TbTypeInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbTypeInfoService.importTbTypeInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbTypeInfo> util = new ExcelUtil<TbTypeInfo>(TbTypeInfo.class);
        util.exportTemplateExcel(response, "矛盾纠纷类别编码数据");
    }

    /**
     * 获取矛盾纠纷类别编码详细信息
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(tbTypeInfoService.selectTbTypeInfoById(id));
    }

    /**
     * 新增矛盾纠纷类别编码
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:add')")
    @Log(title = "矛盾纠纷类别编码", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbTypeInfo tbTypeInfo)
    {
        tbTypeInfo.setCreateBy(getUsername());
        if (tbTypeInfoService.checkTbTypeInfo(tbTypeInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbTypeInfoService.insertTbTypeInfo(tbTypeInfo));
    }

    /**
     * 修改矛盾纠纷类别编码
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:edit')")
    @Log(title = "矛盾纠纷类别编码", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbTypeInfo tbTypeInfo)
    {
        tbTypeInfo.setUpdateBy(getUsername());
        if (tbTypeInfoService.checkTbTypeInfo(tbTypeInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbTypeInfoService.updateTbTypeInfo(tbTypeInfo));
    }

    /**
     * 删除矛盾纠纷类别编码
     */
    @PreAuthorize("@ss.hasPermi('issue:typeInfo:remove')")
    @Log(title = "矛盾纠纷类别编码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(tbTypeInfoService.deleteTbTypeInfoByIds(ids));
    }
}
