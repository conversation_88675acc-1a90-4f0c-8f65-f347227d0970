package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;
import com.ruoyi.microPlatform.domain.TbBranchInfo;
import com.ruoyi.microPlatform.domain.TbGridInfo;

/**
 * 机关企事业单位（支部）Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ITbBranchInfoService 
{
    /**
     * 查询机关企事业单位（支部）
     * 
     * @param id 机关企事业单位（支部）主键
     * @return 机关企事业单位（支部）
     */
    public TbBranchInfo selectTbBranchInfoById(Long id);

    /**
     * 校验机关企事业单位（支部）是否存在
     *
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 机关企事业单位（支部）
     */
    public boolean checkTbBranchInfo(TbBranchInfo tbBranchInfo);

    /**
     * 查询机关企事业单位（支部）列表
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 机关企事业单位（支部）集合
     */
    public List<TbBranchInfo> selectTbBranchInfoList(TbBranchInfo tbBranchInfo);

    /**
     * 导入机关企事业单位（支部）
     *
     * @param infos       机关企事业单位（支部）列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbBranchInfo(List<TbBranchInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    public int insertTbBranchInfo(TbBranchInfo tbBranchInfo);

    /**
     * 修改机关企事业单位（支部）
     * 
     * @param tbBranchInfo 机关企事业单位（支部）
     * @return 结果
     */
    public int updateTbBranchInfo(TbBranchInfo tbBranchInfo);

    /**
     * 批量删除机关企事业单位（支部）
     * 
     * @param ids 需要删除的机关企事业单位（支部）主键集合
     * @return 结果
     */
    public int deleteTbBranchInfoByIds(Long[] ids);

    /**
     * 删除机关企事业单位（支部）信息
     * 
     * @param id 机关企事业单位（支部）主键
     * @return 结果
     */
    public int deleteTbBranchInfoById(TbBranchGridInfo tbBranchGridInfo);

    List<TbGridInfo> selectTbBranchInfoListByGrid(TbBranchInfo tbBranchInfo, List<TbGridInfo> gridInfos);

}
