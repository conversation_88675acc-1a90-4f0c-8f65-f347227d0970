package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.NumberUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbResidentMember;
import com.ruoyi.microPlatform.domain.TbSidelineGridUser;
import com.ruoyi.microPlatform.mapper.TbResidentMemberMapper;
import com.ruoyi.microPlatform.mapper.TbSidelineGridUserMapper;
import com.ruoyi.microPlatform.service.*;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 网格信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbGridInfoServiceImpl implements ITbGridInfoService {
    @Autowired
    private TbGridInfoMapper tbGridInfoMapper;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AsyncUpdateService asyncUpdateService;

    @Autowired
    private TbSidelineGridUserMapper tbSidelineGridUserMapper;
    /**
     * 查询网格信息
     *
     * @param id 网格信息主键
     * @return 网格信息
     */
    @Override
    public TbGridInfo selectTbGridInfoById(Long id) {
        return tbGridInfoMapper.selectTbGridInfoById(id);
    }

    @Override
    public TbGridInfo selectGridInfoById(Long id) {
        TbGridInfo gridInfo = tbGridInfoMapper.selectGridInfoById(id);
        if (ObjectUtil.isNotEmpty(gridInfo)) {
            gridInfo.setDeptUserList(tbGridInfoMapper.selectDeptUserByGridId(id));
            gridInfo.setGridUserList(tbGridInfoMapper.selectGridUserByGridId(id));
            gridInfo.setSidelineGridUserList(tbSidelineGridUserMapper.selectTbSidelineGridUserList(new TbSidelineGridUser(){{
                setGridId(id);
            }}));
        }
        return gridInfo;
    }

    /**
     * 校验网格信息是否存在
     *
     * @param tbGridInfo
     * @return boolean
     */
    @Override
    public boolean checkTbGridInfo(TbGridInfo tbGridInfo) {
        TbGridInfo old = tbGridInfoMapper.checkTbGridInfoUnique(tbGridInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询网格信息列表
     *
     * @param tbGridInfo 网格信息
     * @return 网格信息
     */
    @Override
    public List<TbGridInfo> selectTbGridInfoList(TbGridInfo tbGridInfo) {
        return tbGridInfoMapper.selectTbGridInfoList(tbGridInfo);
    }

    @Override
    public Integer selectExportGridInfoCount(TbGridInfo tbGridInfo) {
        List<Long> longs = tbGridInfoMapper.selectExportGridInfoCount(tbGridInfo);
        return longs.size();
    }

    @Override
    public List<TbGridInfo> getWorkGrid(TbGridInfo tbGridInfo) {
        return tbGridInfoMapper.getWorkGrid(tbGridInfo);
    }

    /**
     * 导入网格信息
     *
     * @param infos           网格信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbGridInfo(List<TbGridInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入网格信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int index = 2;
        int failureNum = 0;

        for (TbGridInfo info : infos) {
            index++;
            try {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getCounty())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getGridName() + " 所属县（市、区） 不可为空" + "。数据条目：" + index);
                } else {
                    if (StringUtils.isBlank(info.getCountry())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getGridName() + " 所属街道/乡镇 不可为空" + "。数据条目：" + index);
                    } else {
                        if (StringUtils.isBlank(info.getTown())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getGridName() + " 所属村/社区 不可为空" + "。数据条目：" + index);
                        } else {
                            if (StringUtils.isBlank(info.getGridName())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据 网格编码 不可为空" + "。数据条目：" + index);
                            } else {
                                boolean flag = true;
                                String msg = "";
                                if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + "的" + info.getGridName() + "网格数据" + "。数据条目：" + index);
                                }

                                if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + "的" + info.getGridName() + "网格数据" + "。数据条目：" + index);
                                }

                                if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                    flag = false;
                                    msg = ("<br/>" + failureNum + "、您无权导入" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getGridName() + "网格数据" + "。数据条目：" + index);
                                }

                                if (!flag) {
                                    failureNum++;
                                    failureMsg.append(msg);
                                } else {
                                    SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                    if (ObjectUtil.isEmpty(dept)) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格数据所属部门未找到！" + "数据条目：" + index);
                                    } else {
                                        info.setDeptId(dept.getDeptId());

                                        // 验证是否存在这个数据
                                        TbGridInfo old = tbGridInfoMapper.checkTbGridInfoUnique(info);
                                        if (ObjectUtil.isEmpty(old)) {
                                            info.setCreateTime(new Date());
                                            info.setCreateBy(operName);
                                            this.insertTbGridInfo(info);
                                            successNum++;
                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getGridName() + " 导入成功");
                                        } else if (isUpdateSupport) {
                                            info.setUpdateBy(operName);
                                            info.setUpdateTime(new Date());
                                            info.setId(old.getId());
                                            this.updateTbGridInfo(info);
                                            successNum++;
                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getGridName() + " 更新成功");
                                        } else {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 已存在" + "。数据条目：" + index);
                                        }
                                    }

                                }

                            }
                        }
                    }
                }


            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage() + "。数据条目：" + index);
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增网格信息
     *
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTbGridInfo(TbGridInfo tbGridInfo) {
        tbGridInfo.setCreateTime(DateUtils.getNowDate());
        if (StringUtils.isNotBlank(tbGridInfo.getGridName()) && NumberUtil.isNumber(tbGridInfo.getGridName())) {
            tbGridInfo.setGridSort(Integer.parseInt(tbGridInfo.getGridName()));
        }
        int i = tbGridInfoMapper.insertTbGridInfo(tbGridInfo);
        if (i > 0) {
            int i1 = deptService.insertDept(new SysDept() {{
                setGridId(tbGridInfo.getId());
                setParentId(tbGridInfo.getDeptId());
                setDeptName(tbGridInfo.getGridName());
                setOrderNum((tbGridInfo.getGridSort()));
                setDeptLevel(8);
                setDeptType("网格");
                setCreateBy(tbGridInfo.getCreateBy());
                setCreateTime(tbGridInfo.getCreateTime());
                setType(2);
            }});
        }
        return i;
    }

    /**
     * 修改网格信息
     *
     * @param tbGridInfo 网格信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTbGridInfo(TbGridInfo tbGridInfo) {
        tbGridInfo.setUpdateTime(DateUtils.getNowDate());
        TbGridInfo old = tbGridInfoMapper.selectGridInfoById(tbGridInfo.getId());
        int i = tbGridInfoMapper.updateTbGridInfo(tbGridInfo);
//        deptService.updateDept()
        if (i > 0 && !old.getGridName().equals(tbGridInfo.getGridName())) {
            //  事务提交后再能继续执行异步
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronization() {
                            @Override
                            public void afterCommit() {
                                // 事务提交后执行逻辑
                                asyncUpdateService.AsyncUpdateByGridInfo(tbGridInfo);
                            }
                            // 其他方法无需覆盖，默认已实现为空
                        }
                );
            }
        }
        return i;
    }

    /**
     * 批量删除网格信息
     *
     * @param ids 需要删除的网格信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTbGridInfoByIds(Long[] ids) {
        int i = tbGridInfoMapper.deleteTbGridInfoByIds(ids);
        // 网格关联部门也需要删除
        deptService.deleteDeptByGridIds(ids);
        return i;
    }

    /**
     * 删除网格信息信息
     *
     * @param id 网格信息主键
     * @return 结果
     */
    @Override
    public int deleteTbGridInfoById(Long id) {
        return tbGridInfoMapper.deleteTbGridInfoById(id);
    }
}
