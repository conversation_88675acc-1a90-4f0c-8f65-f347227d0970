package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 兼职网格员所属网格信息对象 tb_sideline_grid_info
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public class TbSidelineGridInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 关联兼职网格员id */
    @Excel(name = "关联兼职网格员id")
    private Long sideUserId;

    private Long deptId;

    /** 所属县区 */
    @Excel(name = "县（市、区）")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "乡镇（街道）")
    private String country;

    /** 所属村镇/社区 */
    @Excel(name = "社区")
    private String town;

    /** 网格编码 */
    @Excel(name = "网格编码")
    private String gridName;

    /** 所属网格ID */
    @Excel(name = "所属网格ID")
    private Long gridId;

    /** 网格范围 */
    @Excel(name = "网格范围")
    private String gridRange;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSideUserId(Long sideUserId) 
    {
        this.sideUserId = sideUserId;
    }

    public Long getSideUserId() 
    {
        return sideUserId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setGridRange(String gridRange) 
    {
        this.gridRange = gridRange;
    }

    public String getGridRange() 
    {
        return gridRange;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sideUserId", getSideUserId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("gridId", getGridId())
            .append("gridRange", getGridRange())
            .toString();
    }
}
