package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbResidentMemberType;
import org.apache.ibatis.annotations.Param;

/**
 * 成员标签配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface TbResidentMemberTypeMapper 
{
    /**
     * 查询成员标签配置
     * 
     * @param id 成员标签配置主键
     * @return 成员标签配置
     */
    public TbResidentMemberType selectTbResidentMemberTypeById(Long id);

    /**
     * 校验成员标签配置是否唯一
     *
     * @param tbResidentMemberType
     * @return 成员标签配置
     */
    public TbResidentMemberType checkTbResidentMemberTypeUnique(TbResidentMemberType tbResidentMemberType);

    /**
     * 查询成员标签配置列表
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 成员标签配置集合
     */
    public List<TbResidentMemberType> selectTbResidentMemberTypeList(TbResidentMemberType tbResidentMemberType);

    /**
     * 新增成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    public int insertTbResidentMemberType(TbResidentMemberType tbResidentMemberType);

    /**
     * 修改成员标签配置
     * 
     * @param tbResidentMemberType 成员标签配置
     * @return 结果
     */
    public int updateTbResidentMemberType(TbResidentMemberType tbResidentMemberType);

    /**
     * 删除成员标签配置
     * 
     * @param id 成员标签配置主键
     * @return 结果
     */
    public int deleteTbResidentMemberTypeById(Long id);

    /**
     * 批量删除成员标签配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbResidentMemberTypeByIds(Long[] ids);

    //根据成员id进行删除
    int deleteTbResidentMemberTypeByMemberId(@Param("memberId") Long memberId);

    void insertTbResidentMemberTypeByValues(@Param("memberId") Long memberId, @Param("array") Integer[] ids);


}
