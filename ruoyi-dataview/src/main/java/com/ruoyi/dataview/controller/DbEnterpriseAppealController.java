package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbEnterpriseAppealConvert;
import com.ruoyi.dataview.domain.dto.DbEnterpriseAppealFormDTO;
import com.ruoyi.dataview.domain.dto.DbEnterpriseAppealQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbEnterpriseAppeal;
import com.ruoyi.dataview.service.IDbEnterpriseAppealService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 万人助企诉求Controller
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@RestController
@RequestMapping("/fiveLevel/enterpriseAppeal")
@RequiredArgsConstructor
public class DbEnterpriseAppealController extends BaseController
{
    private final IDbEnterpriseAppealService dbEnterpriseAppealService;

    private final DbEnterpriseAppealConvert enterpriseAppealConvert;


    /**
     * 查询万人助企诉求列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbEnterpriseAppealQueryDTO enterpriseAppealDTO)
    {
        //DTO -> VO
        DbEnterpriseAppeal dbEnterpriseAppeal = enterpriseAppealConvert.toDO(enterpriseAppealDTO);
        startPage();
        return getDataTable(enterpriseAppealConvert.toVOList(dbEnterpriseAppealService.selectDbEnterpriseAppealList(dbEnterpriseAppeal)));
    }

    /**
     * 导出万人助企诉求列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:export')")
    @Log(title = "万人助企诉求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbEnterpriseAppealQueryDTO enterpriseAppealDTO)
    {
        //DTO -> VO
        DbEnterpriseAppeal dbEnterpriseAppeal = enterpriseAppealConvert.toDO(enterpriseAppealDTO);
        List<DbEnterpriseAppeal> list = dbEnterpriseAppealService.selectDbEnterpriseAppealList(dbEnterpriseAppeal);
        ExcelUtil<DbEnterpriseAppeal> util = new ExcelUtil<>(DbEnterpriseAppeal.class);
        util.exportExcel(response, list, "万人助企诉求数据");
    }


    @Log(title = "万人助企诉求", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbEnterpriseAppeal> util = new ExcelUtil<DbEnterpriseAppeal>(DbEnterpriseAppeal.class);
        List<DbEnterpriseAppeal> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbEnterpriseAppealService.importDbEnterpriseAppeal(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbEnterpriseAppeal> util = new ExcelUtil<DbEnterpriseAppeal>(DbEnterpriseAppeal.class);
        util.exportTemplateExcel(response, "万人助企诉求数据");
    }

    /**
     * 获取万人助企诉求详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(enterpriseAppealConvert.toVO(dbEnterpriseAppealService.selectDbEnterpriseAppealById(id)));
    }

    /**
     * 新增万人助企诉求
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:add')")
    @Log(title = "万人助企诉求", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbEnterpriseAppealFormDTO enterpriseAppealFormDTO)
    {
        DbEnterpriseAppeal dbEnterpriseAppeal = enterpriseAppealConvert.toDO(enterpriseAppealFormDTO);
        enterpriseAppealFormDTO.setCreateBy(getUsername());
        /*if (dbEnterpriseAppealService.checkDbEnterpriseAppeal(dbEnterpriseAppeal)){
            return error("数据已存在！");
        }*/
        return toAjax(dbEnterpriseAppealService.insertDbEnterpriseAppeal(dbEnterpriseAppeal));
    }

    /**
     * 修改万人助企诉求
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:edit')")
    @Log(title = "万人助企诉求", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbEnterpriseAppealFormDTO enterpriseAppealFormDTO)
    {
        DbEnterpriseAppeal dbEnterpriseAppeal = enterpriseAppealConvert.toDO(enterpriseAppealFormDTO);
        dbEnterpriseAppeal.setUpdateBy(getUsername());
        /*if (dbEnterpriseAppealService.checkDbEnterpriseAppeal(dbEnterpriseAppeal)){
            return error("数据已存在！");
        }*/
        return toAjax(dbEnterpriseAppealService.updateDbEnterpriseAppeal(dbEnterpriseAppeal));
    }

    /**
     * 删除万人助企诉求
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:enterpriseAppeal:remove')")
    @Log(title = "万人助企诉求", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbEnterpriseAppealService.deleteDbEnterpriseAppealByIds(ids));
    }
}
