package com.ruoyi.microPlatform.domain;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *
 * 完整的大屏数据对象。
 */
@Data
public class LargeScreenOverviewDTO {

    /**
     * 人员标签类型及其对应数量
     * key: 标签名称（如“庭院长”、““六失一偏”人员”），
     * value: 对应人数
     */
    private Map<String, Integer> labelRank;

    /**
     * 社区功能室统计数据
     * key: 功能室名称（如“活动室”、“会议室”），
     * value: 功能室数量
     */
    private Map<String, Integer> communityFunction;

    /**
     * 辖区下各类实体数量统计
     * key: 实体类型（如“网格”、“小区”、“商户”、“住户”），
     * value: 对应数量
     */
    private Map<String, Integer> jurisdiction;

    /**
     * 辖区空间区域经纬度信息
     * 每个元素为一点或区域坐标对象
     */
    private List<?> jurisdictionSpace;

    /**
     * 辖区下具体实体列表
     * 通常包含网格、小区、商户、住户及其成员的详细信息列表
     */
    private List<?> jurisdictionList;

    /**
     * 巡查走访情况统计
     * key: （“门店巡查”，“入户走访”），
     * value:{
     *          key:"日期"
     *          value:"对应的数量"
     *      }
     */
    private Map<String, Map<String, Integer>> patrolVisit;

    /**
     * 居民吹哨统计数据
     * key: 居民吹哨，
     * value: 对应事件数量
     */
    private Map<String, Integer> residentWhistle;

    /**
     * 干部岗位类型统计
     * key: 岗位类型（如“监委会成员”、“社区党组织成员”），
     * value: 对应人数
     */
    private Map<String, Integer> postType;

    /**
     * 门店/商户行业类型统计
     * key: 行业类型（如“餐饮业”、“零售业”），
     * value: 对应商户数量
     */
    private Map<String, Integer> merchantType;
}
