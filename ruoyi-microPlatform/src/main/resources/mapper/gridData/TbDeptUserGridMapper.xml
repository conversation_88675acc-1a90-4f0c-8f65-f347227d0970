<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbDeptUserGridMapper">

    <resultMap type="TbDeptUserGrid" id="TbDeptUserGridResult">
        <result property="id" column="id"/>
        <result property="deptUserId" column="dept_user_id"/>
        <result property="gridId" column="grid_id"/>
        <association property="deptUser" column="dept_user_id" javaType="TbDeptUser" resultMap="TbDeptUserResult"/>
    </resultMap>

    <resultMap type="TbDeptUser" id="TbDeptUserResult">
        <result property="id" column="du_id"/>
        <result property="county" column="du_county"/>
        <result property="country" column="du_country"/>
        <result property="town" column="du_town"/>
        <result property="deptId" column="du_dept_id"/>
        <result property="userId" column="du_user_id"/>
        <result property="name" column="du_name"/>
        <result property="phone" column="du_phone"/>
        <result property="idPhoto" column="du_id_photo"/>
        <result property="postDesc" column="du_post_desc"/>
        <result property="status" column="du_status"/>
    </resultMap>

    <sql id="selectTbDeptUserGridVo">
        select dug.id, dug.dept_user_id, dug.grid_id,
        du.id as du_id, du.county as du_county, du.country as du_country, du.town as du_town, du.dept_id as du_dept_id, du.user_id as du_user_id,
        du.name as du_name, du.phone as du_phone, du.id_photo as du_id_photo, du.post_desc as du_post_desc, du.status as du_status
        from tb_dept_user_grid dug
        left join tb_dept_user du on dug.dept_user_id = du.id
    </sql>

    <select id="selectTbDeptUserGridList" parameterType="TbDeptUserGrid" resultMap="TbDeptUserGridResult">
        <include refid="selectTbDeptUserGridVo"/>
        <where>
            <if test="deptUserId != null ">and dug.dept_user_id = #{deptUserId}</if>
            <if test="gridId != null ">and dug.grid_id = #{gridId}</if>
        </where>
    </select>

    <select id="selectTbDeptUserGridById" parameterType="Long" resultMap="TbDeptUserGridResult">
        <include refid="selectTbDeptUserGridVo"/>
        where dug.id = #{id}
    </select>

    <select id="checkTbDeptUserGridUnique" parameterType="TbDeptUserGrid" resultMap="TbDeptUserGridResult">
        select id from tb_dept_user_grid where dept_user_id = #{deptUserId} and grid_id = #{gridId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbDeptUserGrid" parameterType="TbDeptUserGrid" useGeneratedKeys="true" keyProperty="id">
        insert into tb_dept_user_grid
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptUserId != null">dept_user_id,</if>
            <if test="gridId != null">grid_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptUserId != null">#{deptUserId},</if>
            <if test="gridId != null">#{gridId},</if>
        </trim>
    </insert>

    <update id="updateTbDeptUserGrid" parameterType="TbDeptUserGrid">
        update tb_dept_user_grid
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptUserId != null">dept_user_id = #{deptUserId},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbDeptUserGridById" parameterType="Long">
        delete from tb_dept_user_grid where id = #{id}
    </delete>

    <delete id="deleteTbDeptUserGridByIds" parameterType="String">
        delete from tb_dept_user_grid where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>