package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.PbPartyOrg;
import com.ruoyi.microPlatform.mapper.PbPartyOrgMapper;
import com.ruoyi.microPlatform.service.IPbPartyOrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 基层党组织管理Service业务层处理
 */
@Service
public class PbPartyOrgServiceImpl implements IPbPartyOrgService {

    @Autowired
    private PbPartyOrgMapper pbPartyOrgMapper;

    @Override
    public PbPartyOrg selectPbPartyOrgById(Long id) {
        return pbPartyOrgMapper.selectPbPartyOrgById(id);
    }

    @Override
    public boolean checkPbPartyOrg(PbPartyOrg org) {
        PbPartyOrg old = pbPartyOrgMapper.checkPbPartyOrgUnique(org);
        return old != null;
    }

    @Override
    public List<PbPartyOrg> selectPbPartyOrgList(PbPartyOrg org) {
        return pbPartyOrgMapper.selectPbPartyOrgList(org);
    }

    @Override
    public int insertPbPartyOrg(PbPartyOrg org) {
        org.setCreateTime(DateUtils.getNowDate());
        return pbPartyOrgMapper.insertPbPartyOrg(org);
    }

    @Override
    public int updatePbPartyOrg(PbPartyOrg org) {
        org.setUpdateTime(DateUtils.getNowDate());
        return pbPartyOrgMapper.updatePbPartyOrg(org);
    }

    @Override
    public int deletePbPartyOrgByIds(Long[] ids) {
        return pbPartyOrgMapper.deletePbPartyOrgByIds(ids);
    }

    @Override
    public int deletePbPartyOrgById(Long id) {
        return pbPartyOrgMapper.deletePbPartyOrgById(id);
    }

    @Override
    public List<CommonBaseCount> pbPartyOrgGroupOrgLevel() {
        List<CommonBaseCount> dbCounts = pbPartyOrgMapper.pbPartyOrgGroupOrgLevel();
        // （1党委/2党总支/3党支部）补全三类，即使数据库没有某类也返回0
        int count1 = 0; // 党委
        int count2 = 0; // 党总支
        int count3 = 0; // 党支部
        if (dbCounts != null) {
            for (CommonBaseCount c : dbCounts) {
                Integer t = c.getType();
                if (t == null) continue;
                if (t == 1) {
                    count1 = c.getCount() == null ? 0 : c.getCount();
                } else if (t == 2) {
                    count2 = c.getCount() == null ? 0 : c.getCount();
                } else if (t == 3) {
                    count3 = c.getCount() == null ? 0 : c.getCount();
                }
            }
        }
        List<CommonBaseCount> res = new java.util.ArrayList<>();
        CommonBaseCount item1 = new CommonBaseCount();
        item1.setType(1);
        item1.setLable("党委");
        item1.setCount(count1);
        res.add(item1);

        CommonBaseCount item2 = new CommonBaseCount();
        item2.setType(2);
        item2.setLable("党总支");
        item2.setCount(count2);
        res.add(item2);

        CommonBaseCount item3 = new CommonBaseCount();
        item3.setType(3);
        item3.setLable("党支部");
        item3.setCount(count3);
        res.add(item3);

        return res;
    }

    @Override
    public List<CommonBaseCount> pbPartyOrgGroupOrgType() {
        return pbPartyOrgMapper.pbPartyOrgGroupOrgType();
    }
}
