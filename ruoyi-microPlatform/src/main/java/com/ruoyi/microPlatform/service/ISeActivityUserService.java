package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.SeActivityUser;

/**
 * 报名者信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ISeActivityUserService 
{
    /**
     * 查询报名者信息
     * 
     * @param id 报名者信息主键
     * @return 报名者信息
     */
    public SeActivityUser selectSeActivityUserById(Long id);

    /**
     * 校验报名者信息是否存在
     *
     * @param seActivityUser 报名者信息
     * @return 报名者信息
     */
    public boolean checkSeActivityUser(SeActivityUser seActivityUser);

    /**
     * 查询报名者信息列表
     * 
     * @param seActivityUser 报名者信息
     * @return 报名者信息集合
     */
    public List<SeActivityUser> selectSeActivityUserList(SeActivityUser seActivityUser);

    /**
     * 导入报名者信息
     *
     * @param infos       报名者信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeActivityUser(List<SeActivityUser> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增报名者信息
     * 
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    public int insertSeActivityUser(SeActivityUser seActivityUser);

    /**
     * 修改报名者信息
     * 
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    public int updateSeActivityUser(SeActivityUser seActivityUser);

    /**
     * 批量删除报名者信息
     * 
     * @param ids 需要删除的报名者信息主键集合
     * @return 结果
     */
    public int deleteSeActivityUserByIds(Long[] ids);

    /**
     * 删除报名者信息信息
     * 
     * @param id 报名者信息主键
     * @return 结果
     */
    public int deleteSeActivityUserById(Long id);
}
