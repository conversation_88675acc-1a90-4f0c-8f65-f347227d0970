package com.ruoyi.dataview.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.ruoyi.dataview.mapper.DbEnterpriseAppealMapper;
import com.ruoyi.dataview.domain.po.DbEnterpriseAppeal;
import com.ruoyi.dataview.service.IDbEnterpriseAppealService;

/**
 * 万人助企诉求Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.SLAVE)
public class DbEnterpriseAppealServiceImpl implements IDbEnterpriseAppealService 
{
    private final DbEnterpriseAppealMapper dbEnterpriseAppealMapper;


    /**
     * 查询万人助企诉求
     * 
     * @param id 万人助企诉求主键
     * @return 万人助企诉求
     */
    @Override
    public DbEnterpriseAppeal selectDbEnterpriseAppealById(Long id)
    {
        return dbEnterpriseAppealMapper.selectDbEnterpriseAppealById(id);
    }

    /**
     * 校验万人助企诉求是否存在
     *
     * @param dbEnterpriseAppeal
     * @return boolean
     */
    @Override
    public boolean checkDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal){
        DbEnterpriseAppeal old = dbEnterpriseAppealMapper.checkDbEnterpriseAppealUnique(dbEnterpriseAppeal);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询万人助企诉求列表
     * 
     * @param dbEnterpriseAppeal 万人助企诉求query参数类
     * @return 万人助企诉求
     */
    @Override
    public List<DbEnterpriseAppeal> selectDbEnterpriseAppealList(DbEnterpriseAppeal dbEnterpriseAppeal)
    {
        return dbEnterpriseAppealMapper.selectDbEnterpriseAppealList(dbEnterpriseAppeal);
    }

    /**
     * 导入万人助企诉求
     *
     * @param infos       万人助企诉求列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbEnterpriseAppeal(List<DbEnterpriseAppeal> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入万人助企诉求数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbEnterpriseAppeal info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                DbEnterpriseAppeal old = dbEnterpriseAppealMapper.checkDbEnterpriseAppealUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbEnterpriseAppealMapper.insertDbEnterpriseAppeal(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbEnterpriseAppealMapper.updateDbEnterpriseAppeal(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    @Override
    public int insertDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal)
    {
        dbEnterpriseAppeal.setCreateTime(DateUtils.getNowDate());
        return dbEnterpriseAppealMapper.insertDbEnterpriseAppeal(dbEnterpriseAppeal);
    }

    /**
     * 修改万人助企诉求
     * 
     * @param dbEnterpriseAppeal 万人助企诉求
     * @return 结果
     */
    @Override
    public int updateDbEnterpriseAppeal(DbEnterpriseAppeal dbEnterpriseAppeal)
    {
        dbEnterpriseAppeal.setUpdateTime(DateUtils.getNowDate());
        return dbEnterpriseAppealMapper.updateDbEnterpriseAppeal(dbEnterpriseAppeal);
    }

    /**
     * 批量删除万人助企诉求
     * 
     * @param ids 需要删除的万人助企诉求主键
     * @return 结果
     */
    @Override
    public int deleteDbEnterpriseAppealByIds(Long[] ids)
    {
        return dbEnterpriseAppealMapper.deleteDbEnterpriseAppealByIds(ids);
    }

    /**
     * 删除万人助企诉求信息
     * 
     * @param id 万人助企诉求主键
     * @return 结果
     */
    @Override
    public int deleteDbEnterpriseAppealById(Long id)
    {
        return dbEnterpriseAppealMapper.deleteDbEnterpriseAppealById(id);
    }

    @Override
    public List<CommonBaseCount> enterpriseAppealGroup() {
        return Collections.emptyList();
    }
}
