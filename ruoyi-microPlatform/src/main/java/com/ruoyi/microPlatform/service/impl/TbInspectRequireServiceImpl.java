package com.ruoyi.microPlatform.service.impl;

import java.util.*;

import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbInspectRequireMapper;
import com.ruoyi.microPlatform.domain.TbInspectRequire;
import com.ruoyi.microPlatform.service.ITbInspectRequireService;

/**
 * 门店基础检查项目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbInspectRequireServiceImpl implements ITbInspectRequireService 
{
    @Autowired
    private TbInspectRequireMapper tbInspectRequireMapper;

    /**
     * 查询门店基础检查项目
     * 
     * @param id 门店基础检查项目主键
     * @return 门店基础检查项目
     */
    @Override
    public TbInspectRequire selectTbInspectRequireById(Integer id)
    {
        return tbInspectRequireMapper.selectTbInspectRequireById(id);
    }

    /**
     * 校验门店基础检查项目是否存在
     *
     * @param tbInspectRequire
     * @return boolean
     */
    @Override
    public boolean checkTbInspectRequire(TbInspectRequire tbInspectRequire){
        TbInspectRequire old = tbInspectRequireMapper.checkTbInspectRequireUnique(tbInspectRequire);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询门店基础检查项目列表
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 门店基础检查项目
     */
    @Override
    public List<TbInspectRequire> selectTbInspectRequireList(TbInspectRequire tbInspectRequire)
    {
        return tbInspectRequireMapper.selectTbInspectRequireList(tbInspectRequire);
    }

    /**
     * 导入门店基础检查项目
     *
     * @param infos       门店基础检查项目列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbInspectRequire(List<TbInspectRequire> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入门店基础检查项目数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbInspectRequire info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbInspectRequire old = tbInspectRequireMapper.checkTbInspectRequireUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbInspectRequireMapper.insertTbInspectRequire(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbInspectRequireMapper.updateTbInspectRequire(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    @Override
    public int insertTbInspectRequire(TbInspectRequire tbInspectRequire)
    {
        return tbInspectRequireMapper.insertTbInspectRequire(tbInspectRequire);
    }

    /**
     * 修改门店基础检查项目
     * 
     * @param tbInspectRequire 门店基础检查项目
     * @return 结果
     */
    @Override
    public int updateTbInspectRequire(TbInspectRequire tbInspectRequire)
    {
        return tbInspectRequireMapper.updateTbInspectRequire(tbInspectRequire);
    }

    /**
     * 批量删除门店基础检查项目
     * 
     * @param ids 需要删除的门店基础检查项目主键
     * @return 结果
     */
    @Override
    public int deleteTbInspectRequireByIds(Integer[] ids)
    {
        return tbInspectRequireMapper.deleteTbInspectRequireByIds(ids);
    }

    /**
     * 删除门店基础检查项目信息
     * 
     * @param id 门店基础检查项目主键
     * @return 结果
     */
    @Override
    public int deleteTbInspectRequireById(Integer id)
    {
        return tbInspectRequireMapper.deleteTbInspectRequireById(id);
    }


    @Override
    public Map<String,List<TbInspectRequire>> selectTbInspectRequireListByType(TbInspectRequire tbInspectRequire) {
        Map<String,List<TbInspectRequire>> map = new HashMap<>();
        tbInspectRequire.setStatus("0");
        List<TbInspectRequire> list = tbInspectRequireMapper.selectTbInspectRequireListByType(tbInspectRequire);

        // 按 type 分组
        Map<String, List<TbInspectRequire>> resultMap = new HashMap<>();
        for (TbInspectRequire require : list) {
            String type = require.getType();
            resultMap.computeIfAbsent(type, k -> new ArrayList<>()).add(require);
        }


        return resultMap;
    }
}
