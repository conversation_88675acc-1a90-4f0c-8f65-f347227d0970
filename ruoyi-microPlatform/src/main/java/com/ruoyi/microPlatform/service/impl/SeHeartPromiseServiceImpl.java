package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.SeHeartPromiseMapper;
import com.ruoyi.microPlatform.domain.SeHeartPromise;
import com.ruoyi.microPlatform.service.ISeHeartPromiseService;

/**
 * 心灵有约Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class SeHeartPromiseServiceImpl implements ISeHeartPromiseService 
{
    @Autowired
    private SeHeartPromiseMapper seHeartPromiseMapper;

    /**
     * 查询心灵有约
     * 
     * @param id 心灵有约主键
     * @return 心灵有约
     */
    @Override
    public SeHeartPromise selectSeHeartPromiseById(Long id)
    {
        return seHeartPromiseMapper.selectSeHeartPromiseById(id);
    }

    /**
     * 校验心灵有约是否存在
     *
     * @param seHeartPromise
     * @return boolean
     */
    @Override
    public boolean checkSeHeartPromise(SeHeartPromise seHeartPromise){
        SeHeartPromise old = seHeartPromiseMapper.checkSeHeartPromiseUnique(seHeartPromise);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询心灵有约列表
     * 
     * @param seHeartPromise 心灵有约
     * @return 心灵有约
     */
    @Override
    public List<SeHeartPromise> selectSeHeartPromiseList(SeHeartPromise seHeartPromise)
    {
        return seHeartPromiseMapper.selectSeHeartPromiseList(seHeartPromise);
    }

    /**
     * 导入心灵有约
     *
     * @param infos       心灵有约列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeHeartPromise(List<SeHeartPromise> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入心灵有约数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeHeartPromise info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                SeHeartPromise old = seHeartPromiseMapper.checkSeHeartPromiseUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seHeartPromiseMapper.insertSeHeartPromise(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seHeartPromiseMapper.updateSeHeartPromise(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    @Override
    public int insertSeHeartPromise(SeHeartPromise seHeartPromise)
    {
        seHeartPromise.setCreateTime(DateUtils.getNowDate());
        return seHeartPromiseMapper.insertSeHeartPromise(seHeartPromise);
    }

    /**
     * 修改心灵有约
     * 
     * @param seHeartPromise 心灵有约
     * @return 结果
     */
    @Override
    public int updateSeHeartPromise(SeHeartPromise seHeartPromise)
    {
        return seHeartPromiseMapper.updateSeHeartPromise(seHeartPromise);
    }

    /**
     * 批量删除心灵有约
     * 
     * @param ids 需要删除的心灵有约主键
     * @return 结果
     */
    @Override
    public int deleteSeHeartPromiseByIds(Long[] ids)
    {
        return seHeartPromiseMapper.deleteSeHeartPromiseByIds(ids);
    }

    /**
     * 删除心灵有约信息
     * 
     * @param id 心灵有约主键
     * @return 结果
     */
    @Override
    public int deleteSeHeartPromiseById(Long id)
    {
        return seHeartPromiseMapper.deleteSeHeartPromiseById(id);
    }
}
