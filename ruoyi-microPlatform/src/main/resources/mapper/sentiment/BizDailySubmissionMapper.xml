<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.BizDailySubmissionMapper">

    <resultMap type="BizDailySubmission" id="BizDailySubmissionResult">
        <result property="id" column="id"/>
        <result property="reportDate" column="report_date"/>
        <result property="orgId" column="org_id"/>
        <result property="orgName" column="org_name"/>
        <result property="bizDynamics" column="biz_dynamics"/>
        <result property="riskHiddenDangers" column="risk_hidden_dangers"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="accessory" column="accessory"/>
        <result property="submitUserId" column="submit_user_id"/>
        <result property="submitUserName" column="submit_user_name"/>
        <result property="submitUserPhone" column="submit_user_phone"/>
        <result property="submitTime" column="submit_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectBizDailySubmissionVo">
        select id, report_date, org_id, org_name, biz_dynamics, risk_hidden_dangers, risk_level,
               accessory, submit_user_id, submit_user_name, submit_user_phone, submit_time,
               create_time, update_time
        from biz_daily_submission
    </sql>

    <select id="selectBizDailySubmissionList" parameterType="BizDailySubmission"
            resultMap="BizDailySubmissionResult">
        <include refid="selectBizDailySubmissionVo"/>
        <where>
            <if test="reportDate != null">and report_date = #{reportDate}</if>
            <if test="orgId != null">and org_id = #{orgId}</if>
            <if test="orgName != null and orgName != ''">and org_name like concat('%', #{orgName}, '%')</if>
            <if test="bizDynamics != null and bizDynamics != ''">and biz_dynamics like concat('%', #{bizDynamics}, '%')</if>
            <if test="riskHiddenDangers != null and riskHiddenDangers != ''">and risk_hidden_dangers like concat('%', #{riskHiddenDangers}, '%')</if>
            <if test="riskLevel != null and riskLevel != ''">and risk_level = #{riskLevel}</if>
            <if test="accessory != null and accessory != ''">and accessory = #{accessory}</if>
            <if test="submitUserId != null">and submit_user_id = #{submitUserId}</if>
            <if test="submitUserName != null and submitUserName != ''">and submit_user_name like concat('%', #{submitUserName}, '%')</if>
            <if test="submitUserPhone != null and submitUserPhone != ''">and submit_user_phone = #{submitUserPhone}</if>
            <if test="submitTime != null">and submit_time = #{submitTime}</if>
        </where>
        order by report_date desc, id desc
    </select>

    <select id="selectBizDailySubmissionById" parameterType="Long" resultMap="BizDailySubmissionResult">
        <include refid="selectBizDailySubmissionVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizDailySubmission" parameterType="BizDailySubmission" useGeneratedKeys="true" keyProperty="id">
        insert into biz_daily_submission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportDate != null">report_date,</if>
            <if test="orgId != null">org_id,</if>
            <if test="orgName != null">org_name,</if>
            <if test="bizDynamics != null">biz_dynamics,</if>
            <if test="riskHiddenDangers != null">risk_hidden_dangers,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="accessory != null">accessory,</if>
            <if test="submitUserId != null">submit_user_id,</if>
            <if test="submitUserName != null">submit_user_name,</if>
            <if test="submitUserPhone != null">submit_user_phone,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportDate != null">#{reportDate},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="bizDynamics != null">#{bizDynamics},</if>
            <if test="riskHiddenDangers != null">#{riskHiddenDangers},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="submitUserId != null">#{submitUserId},</if>
            <if test="submitUserName != null">#{submitUserName},</if>
            <if test="submitUserPhone != null">#{submitUserPhone},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateBizDailySubmission" parameterType="BizDailySubmission">
        update biz_daily_submission
        <trim prefix="SET" suffixOverrides=",">
            <if test="reportDate != null">report_date = #{reportDate},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="bizDynamics != null">biz_dynamics = #{bizDynamics},</if>
            <if test="riskHiddenDangers != null">risk_hidden_dangers = #{riskHiddenDangers},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="submitUserId != null">submit_user_id = #{submitUserId},</if>
            <if test="submitUserName != null">submit_user_name = #{submitUserName},</if>
            <if test="submitUserPhone != null">submit_user_phone = #{submitUserPhone},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizDailySubmissionById" parameterType="Long">
        delete from biz_daily_submission where id = #{id}
    </delete>

    <delete id="deleteBizDailySubmissionByIds" parameterType="String">
        delete from biz_daily_submission where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
