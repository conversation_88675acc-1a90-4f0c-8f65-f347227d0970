package com.ruoyi.framework.web.service;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.exception.user.*;
import com.ruoyi.common.utils.*;
import com.ruoyi.framework.websocket.WebSocketServer;
import com.ruoyi.system.domain.SysUserOnline;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;
    @Resource
    private AuthenticationManager authenticationManager;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private SysUserRoleMapper userRoleMapper;
    @Autowired
    private UserDetailsServiceImpl impl;


    public boolean checkUpdatePass(String username) {

        SysUser sysUser = userMapper.selectPassUpdateByUserName(username);
        if (!ObjectUtils.isEmpty(sysUser) && !ObjectUtils.isEmpty(sysUser.getIsUpdate()) && sysUser.getIsUpdate() == 1) {
            return true;
        }
        return false;
    }

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public Map login(String username, String password, String code, String uuid, boolean flagCode) throws IOException {
        boolean loginType = true;//是小程序登录
        if (flagCode) {//有验证码
            loginType = false;//PC端登录
            boolean captchaEnabled = configService.selectCaptchaEnabled();
            // 验证码开关
            if (captchaEnabled) {
                validateCaptcha(username, code, uuid);
            }
        }
        // 登录前置校验
        loginPreCheck(username, password);
        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match") +  "-("+ password + ")"));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        /*if (!loginUser.getUser().getUserType().equals("00")) {
            throw new ServiceException("非系统用户，无法登录！");
        }*/
        onlineDelete(loginUser.getUserId(), loginType);

        loginUser.setLoginType(loginType);

        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    public void onlineDelete(Long userId, boolean loginType) throws IOException {
        boolean isSimultaneouslyOnline = new Boolean(configService.selectConfigByKey("sys.user.isSimultaneouslyOnline"));
        if (userId.longValue() != 1 && isSimultaneouslyOnline) {//如果不允许同时在线则 其他用户强制退出
            Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + userId +  "*");
            for (String key : keys) {
                LoginUser user = redisCache.getCacheObject(key);
                System.err.println(user.getUsername() + loginType + user.getLoginType());
                if (userId.longValue() == user.getUserId().longValue() && loginType == user.getLoginType()) {//去除这个人
                    System.out.println("强制退出：" + key);
                    redisCache.deleteObject(key);
//                    WebSocketServer.sendMessage(key.replaceAll(CacheConstants.LOGIN_TOKEN_KEY, ""));

                }
            }
        }
    }

    /**
     * 微信登录
     *
     * @param decryptResult 登录凭证 只能用一次
     * @return
     */
    public Map wxLogin(String decryptResult, String nickName, String openId, String userType, Long deptId, String drillNo, String jobUnit) throws IOException, InvalidKeySpecException, NoSuchAlgorithmException {
        //根据openid判断数据库中是否有该用户
        SysUser wxUser = userMapper.selectWxUserByOpenId(openId);
        //如果查不到，则新增，查到了，则更新
        SysUser user = new SysUser();
        //字符串转json
        if (StringUtils.isNotBlank(decryptResult)) {
            JSONObject jsonObject = JSONObject.parseObject(decryptResult);
            String phoneNumber = jsonObject.getString("phoneNumber");
            if (StringUtils.isBlank(nickName)) {
                nickName = phoneNumber;
            }
            if (wxUser == null) {
                // 新增
                user.setUserName(phoneNumber);// 生成16位随机用户名
                user.setNickName(nickName);
                SysUser old = userMapper.checkPhoneUnique(new SysUser() {{
                    setPhonenumber(phoneNumber);
                }});
                if (ObjectUtil.isNotEmpty(old) && StringUtils.isNotBlank(old.getOpenId())) {
                    throw new ServiceException("该手机号已被用户关联，禁止重复关联！");
                }
                if (ObjectUtil.isNotEmpty(old) && StringUtils.isBlank(old.getOpenId())) {
                    String finalNickName = nickName;
                    userMapper.updateUser(new SysUser() {{
                        setUserId(old.getUserId());
                        setOpenId(openId);
                        if (StringUtils.isNotBlank(finalNickName) && !finalNickName.equals(phoneNumber)){
                            setNickName(finalNickName);
                        }
                    }});
                    user.setUserId(old.getUserId());
                    if (old.getStatus().equals('1')){
                        throw new ServiceException("您的账号已停用！");
                    }
                    System.err.println("更新已存在的用户的openId");
                } else {
                    user.setPhonenumber(phoneNumber);
                    user.setOpenId(openId);
                    user.setCreateTime(DateUtils.getNowDate());
                    user.setDeptId(deptId);
                    user.setUserType("00");
                    if (ObjectUtil.isEmpty(deptId)){
                        Long aLong = userMapper.selectDeptId(phoneNumber);
                        if (ObjectUtil.isNotEmpty(aLong)){
                            user.setDeptId(aLong);
                        } else {
                            user.setDeptId(3709l);
                            // 临时部门
                        }
                    }
//                    if ("00".equals(userType)) {
//                        // 微信用户
//                        user.setUserType("00");
//                        user.setDeptId(125l);
////                    if (ObjectUtil)
////                    user.setDeptId(1l);
//                    } else {
//                        // 签到打卡用户
//                        user.setDrillNo(drillNo);
//                        user.setJobUnit(jobUnit);
//                        user.setUserType("01");
////                    user.setRoleIds(new Long[]{2l});
//
//                        user.setDeptId(2655l);
//                    }
                    String password = configService.selectConfigByKey("sys.user.initPassword");
                    user.setSpare(RSAUtils.publicEncrypt(password, RSAUtils.getPublicKey(RuoYiConfig.getPublicKey())));
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    //新增 用户
                    userMapper.insertUser(user);
                    this.updateUserGrid(user.getUserId(), user.getPhonenumber());
                    // 新增用户与角色管理
                    List<SysUserRole> list = new ArrayList<>();
                    SysUserRole ur = new SysUserRole();
                    ur.setUserId(user.getUserId());
                    ur.setRoleId(2l);
//                ur.setRoleId(3l);
                    list.add(ur);

                    userRoleMapper.batchUserRole(list);
                }
            } else {
                //更新
                user = wxUser;
                if (user.getStatus().equals("1")){
                    throw new ServiceException("您的账号已停用！");
                }
                if (StringUtils.isNotBlank(nickName) && !nickName.equals(phoneNumber)){
                    user.setNickName(nickName);
                }
                if (ObjectUtil.isNotEmpty(user.getRoles()) && user.getRoles().size() > 0){
                    List<Long> collect = user.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList());
                    if (collect.contains(2l)){
                        // 普通用户
                        if (ObjectUtil.isNotEmpty(deptId)) {
                            user.setDeptId(deptId);
                            user.setUpdateBy("普通用户自己变更");
                        }
                    }
                }
                user.setUpdateTime(DateUtils.getNowDate());
                System.err.println("更新用户");
                userMapper.updateUser(user);
            }
        } else {
            if (wxUser == null) {
                // 用户没有注册过
                return null;
            } else {
                user = wxUser;
                if (user.getStatus().equals("1")){
                    throw new ServiceException("您的账号已停用！");
                }
            }
        }

        onlineDelete(user.getUserId(), true);
        UserDetails userDetail = impl.createLoginUser(userMapper.selectUserByUserName(user.getUserName()));
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));

        LoginUser loginUser = BeanUtil.copyProperties(userDetail, LoginUser.class);

        loginUser.setLoginType(true);
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    @Async("threadPoolTaskExecutor")
    public void updateUserGrid(Long userId, String phone) {
        Long gridId = userMapper.selectGridId(phone);
        if (ObjectUtil.isNotEmpty(gridId)) {
            System.err.println("更新用户gridId");
            userMapper.updateUser(new SysUser() {{
                setUserId(userId);
                setJobId(gridId);
            }});
        }
    }

    //生成随机用户名，数字和字母组成,
    public static String getStringRandom(int length) {

        String val = "";
        Random random = new Random();

        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {

            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (random.nextInt(26) + temp);
            } else if ("num".equalsIgnoreCase(charOrNum)) {
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        if (captcha == null) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        redisCache.deleteObject(verifyKey);
        if (!code.equalsIgnoreCase(captcha)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }
    }

    /**
     * 登录前置校验
     *
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotNullException();
        }

        if (ObjectUtil.isEmpty(userMapper.selectUserByUserName(username))){
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.not.exists")));
            throw new UserNotExistsException();
        }

        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")+  "-密码校验("+ password + ")"));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match") +  "-用户名校验("+ password + ")"));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }
}
