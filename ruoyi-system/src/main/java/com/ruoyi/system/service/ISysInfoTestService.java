package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysInfoTest;

/**
 * 测试管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-01-10
 */
public interface ISysInfoTestService 
{
    /**
     * 查询测试管理
     * 
     * @param id 测试管理主键
     * @return 测试管理
     */
    public SysInfoTest selectSysInfoTestById(Integer id);

    /**
     * 查询测试管理列表
     * 
     * @param sysInfoTest 测试管理
     * @return 测试管理集合
     */
    public List<SysInfoTest> selectSysInfoTestList(SysInfoTest sysInfoTest);

    /**
     * 新增测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    public int insertSysInfoTest(SysInfoTest sysInfoTest);

    /**
     * 修改测试管理
     * 
     * @param sysInfoTest 测试管理
     * @return 结果
     */
    public int updateSysInfoTest(SysInfoTest sysInfoTest);

    /**
     * 批量删除测试管理
     * 
     * @param ids 需要删除的测试管理主键集合
     * @return 结果
     */
    public int deleteSysInfoTestByIds(Integer[] ids);

    /**
     * 删除测试管理信息
     * 
     * @param id 测试管理主键
     * @return 结果
     */
    public int deleteSysInfoTestById(Integer id);
}
