package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbGroupSite;

/**
 * 新就业群体站点Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface TbGroupSiteMapper 
{
    /**
     * 查询新就业群体站点
     * 
     * @param id 新就业群体站点主键
     * @return 新就业群体站点
     */
    public TbGroupSite selectTbGroupSiteById(Long id);

    /**
     * 校验新就业群体站点是否唯一
     *
     * @param tbGroupSite
     * @return 新就业群体站点
     */
    public TbGroupSite checkTbGroupSiteUnique(TbGroupSite tbGroupSite);

    /**
     * 查询新就业群体站点列表
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 新就业群体站点集合
     */
    public List<TbGroupSite> selectTbGroupSiteList(TbGroupSite tbGroupSite);

    /**
     * 新增新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    public int insertTbGroupSite(TbGroupSite tbGroupSite);

    /**
     * 修改新就业群体站点
     * 
     * @param tbGroupSite 新就业群体站点
     * @return 结果
     */
    public int updateTbGroupSite(TbGroupSite tbGroupSite);

    /**
     * 删除新就业群体站点
     * 
     * @param id 新就业群体站点主键
     * @return 结果
     */
    public int deleteTbGroupSiteById(Long id);

    /**
     * 批量删除新就业群体站点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbGroupSiteByIds(Long[] ids);
}
