<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbOrgEmployeeMapper">

    <resultMap type="TbOrgEmployee" id="TbOrgEmployeeResult">
            <result property="id"    column="id"    />
            <result property="orgId"    column="org_id"    />
            <result property="memberId"    column="member_id"    />
            <result property="name"    column="name"    />
            <result property="idCard"    column="id_card"    />
            <result property="partyStatus"    column="party_status"    />
            <result property="hasPartyMember"    column="has_party_member"    />
            <result property="partyName"    column="party_name"    />
            <result property="position"    column="position"    />
            <result property="entryDate"    column="entry_date"    />
    </resultMap>

    <sql id="selectTbOrgEmployeeVo">
        select id, org_id, member_id, name, id_card, party_status, has_party_member, party_name, position, entry_date from tb_org_employee
    </sql>

    <select id="selectTbOrgEmployeeList" parameterType="TbOrgEmployee" resultMap="TbOrgEmployeeResult">
        <include refid="selectTbOrgEmployeeVo"/>
        <where>
                        <if test="orgId != null "> and org_id = #{orgId}</if>
                        <if test="memberId != null "> and member_id = #{memberId}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
                        <if test="partyStatus != null  and partyStatus != ''"> and party_status = #{partyStatus}</if>
                        <if test="hasPartyMember != null  and hasPartyMember != ''"> and has_party_member = #{hasPartyMember}</if>
                        <if test="partyName != null  and partyName != ''"> and party_name like concat('%', #{partyName}, '%')</if>
                        <if test="position != null  and position != ''"> and position = #{position}</if>
                        <if test="entryDate != null "> and entry_date = #{entryDate}</if>
        </where>
    </select>

    <select id="selectTbOrgEmployeeById" parameterType="Long" resultMap="TbOrgEmployeeResult">
            <include refid="selectTbOrgEmployeeVo"/>
            where id = #{id}
    </select>

    <select id="checkTbOrgEmployeeUnique" parameterType="TbOrgEmployee" resultMap="TbOrgEmployeeResult">
        select id from tb_org_employee where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbOrgEmployee" parameterType="TbOrgEmployee" useGeneratedKeys="true" keyProperty="id">
        insert into tb_org_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="orgId != null">org_id,</if>
                    <if test="memberId != null">member_id,</if>
                    <if test="name != null">name,</if>
                    <if test="idCard != null">id_card,</if>
                    <if test="partyStatus != null">party_status,</if>
                    <if test="hasPartyMember != null">has_party_member,</if>
                    <if test="partyName != null">party_name,</if>
                    <if test="position != null">position,</if>
                    <if test="entryDate != null">entry_date,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="orgId != null">#{orgId},</if>
                    <if test="memberId != null">#{memberId},</if>
                    <if test="name != null">#{name},</if>
                    <if test="idCard != null">#{idCard},</if>
                    <if test="partyStatus != null">#{partyStatus},</if>
                    <if test="hasPartyMember != null">#{hasPartyMember},</if>
                    <if test="partyName != null">#{partyName},</if>
                    <if test="position != null">#{position},</if>
                    <if test="entryDate != null">#{entryDate},</if>
        </trim>
    </insert>

    <update id="updateTbOrgEmployee" parameterType="TbOrgEmployee">
        update tb_org_employee
        <trim prefix="SET" suffixOverrides=",">
                    <if test="orgId != null">org_id = #{orgId},</if>
                    <if test="memberId != null">member_id = #{memberId},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="idCard != null">id_card = #{idCard},</if>
                    <if test="partyStatus != null">party_status = #{partyStatus},</if>
                    <if test="hasPartyMember != null">has_party_member = #{hasPartyMember},</if>
                    <if test="partyName != null">party_name = #{partyName},</if>
                    <if test="position != null">position = #{position},</if>
                    <if test="entryDate != null">entry_date = #{entryDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbOrgEmployeeById" parameterType="Long">
        delete from tb_org_employee where id = #{id}
    </delete>

    <delete id="deleteTbOrgEmployeeByIds" parameterType="String">
        delete from tb_org_employee where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>