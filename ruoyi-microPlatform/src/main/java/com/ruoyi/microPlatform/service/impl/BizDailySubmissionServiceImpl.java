package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.microPlatform.domain.BizDailySubmission;
import com.ruoyi.microPlatform.mapper.BizDailySubmissionMapper;
import com.ruoyi.microPlatform.service.IBizDailySubmissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 每日要情 Service 实现
 */
@Service
public class BizDailySubmissionServiceImpl implements IBizDailySubmissionService {

    @Autowired
    private BizDailySubmissionMapper bizDailySubmissionMapper;

    @Override
    public BizDailySubmission selectBizDailySubmissionById(Long id) {
        return bizDailySubmissionMapper.selectBizDailySubmissionById(id);
    }

    @Override
    public List<BizDailySubmission> selectBizDailySubmissionList(BizDailySubmission bizDailySubmission) {
        return bizDailySubmissionMapper.selectBizDailySubmissionList(bizDailySubmission);
    }

    @Transactional
    @Override
    public int insertBizDailySubmission(BizDailySubmission bizDailySubmission) {
        bizDailySubmission.setCreateTime(DateUtils.getNowDate());
        return bizDailySubmissionMapper.insertBizDailySubmission(bizDailySubmission);
    }

    @Transactional
    @Override
    public int updateBizDailySubmission(BizDailySubmission bizDailySubmission) {
        bizDailySubmission.setUpdateTime(DateUtils.getNowDate());
        return bizDailySubmissionMapper.updateBizDailySubmission(bizDailySubmission);
    }

    @Transactional
    @Override
    public int deleteBizDailySubmissionByIds(Long[] ids) {
        return bizDailySubmissionMapper.deleteBizDailySubmissionByIds(ids);
    }

    @Transactional
    @Override
    public int deleteBizDailySubmissionById(Long id) {
        return bizDailySubmissionMapper.deleteBizDailySubmissionById(id);
    }
}
