package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbBranchGridInfo;

/**
 * 支部关联网格Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface ITbBranchGridInfoService 
{
    /**
     * 查询支部关联网格
     * 
     * @param id 支部关联网格主键
     * @return 支部关联网格
     */
    public TbBranchGridInfo selectTbBranchGridInfoById(Long id);

    /**
     * 校验支部关联网格是否存在
     *
     * @param tbBranchGridInfo 支部关联网格
     * @return 支部关联网格
     */
    public boolean checkTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 查询支部关联网格列表
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 支部关联网格集合
     */
    public List<TbBranchGridInfo> selectTbBranchGridInfoList(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 导入支部关联网格
     *
     * @param infos       支部关联网格列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbBranchGridInfo(List<TbBranchGridInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    public int insertTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 修改支部关联网格
     * 
     * @param tbBranchGridInfo 支部关联网格
     * @return 结果
     */
    public int updateTbBranchGridInfo(TbBranchGridInfo tbBranchGridInfo);

    /**
     * 批量删除支部关联网格
     * 
     * @param ids 需要删除的支部关联网格主键集合
     * @return 结果
     */
    public int deleteTbBranchGridInfoByIds(Long[] ids);

    /**
     * 删除支部关联网格信息
     * 
     * @param id 支部关联网格主键
     * @return 结果
     */
    public int deleteTbBranchGridInfoById(Long id);
}
