package com.ruoyi.web.controller.system;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.DateBaseTables.Columns;
import com.ruoyi.common.DateBaseTables.DataSourceTable;
import com.ruoyi.common.DateBaseTables.Tables;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.framework.datasource.DynamicDataSourceContextHolder;
import com.ruoyi.system.mapper.DataSourceMapper;
import com.ruoyi.system.service.DataSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/dataSource")
public class DataSourceController extends BaseController {
    @Autowired
    DataSourceService dataSourceService;
    @Resource
    DataSourceMapper dataSourceMapper;
    /**
     * 创建表
     */
    @RepeatSubmit
    @PostMapping("/createTable")
    public AjaxResult createTable(@RequestBody DataSourceTable table) {
        this.changeDateSource(table);
        return dataSourceService.createTable(table);
    }

    /**
     * 修改表字段
     */
    @RepeatSubmit
    @PostMapping("/alterTableField")
    public AjaxResult alterTableField(@RequestBody String str) {
        System.out.println(str);
        DataSourceTable table = JSONObject.parseObject(str, DataSourceTable.class);
        JSONObject jsonObject = JSONObject.parseObject(str);
        List<Columns> columnsList = JSON.parseArray(JSON.toJSONString(jsonObject.get("columnsList"))).toJavaList(Columns.class);
        table.setColumnsList(columnsList);
        this.changeDateSource(table);
        return dataSourceService.alterTableField(table);
    }

    /**
     * 删除表字段
     */
    @GetMapping("/dropTableField")
    public AjaxResult dropTableField(DataSourceTable table) {
        this.changeDateSource(table);
        return dataSourceService.dropTableField(table);
    }

    /**
     * 删除表
     */
    @GetMapping("/dropTable")
    public AjaxResult dropTable(DataSourceTable table) {
        this.changeDateSource(table);
        return dataSourceService.dropTable(table);
    }

    /**
     * 清空表数据
     */
    @GetMapping("/truncateTable")
    public AjaxResult truncateTable(DataSourceTable table) {
        this.changeDateSource(table);
        return dataSourceService.truncateTable(table);
    }

    /**
     * 增加表字段
     */
    @RepeatSubmit
    @PostMapping("/addTableField")
    public AjaxResult addTableField(@RequestBody DataSourceTable table) {
        this.changeDateSource(table);
        return dataSourceService.addTableField(table);
    }

    /**
     * 数据库的表信息
     */
    @GetMapping("/getTables")
    public TableDataInfo getTables(DataSourceTable table) {
        this.changeDateSource(table);
        table.setDataSourceName(dataSourceMapper.getDataSourceName());
        startPage();
        List<Tables> tables = dataSourceService.getTables(table);
        return getDataTable(tables);
    }

    /**
     * 表结构信息
     */
    @GetMapping("/getTableColumns")
    public TableDataInfo getTableColumns(DataSourceTable table) {
        this.changeDateSource(table);
//        startPage();
        List<Columns> tableColumns = dataSourceService.getTableColumns(table);
        return getDataTable(tableColumns);
    }

    @GetMapping("/getFieldTypes")
    public AjaxResult getFieldTypes(){
        return dataSourceService.getFieldTypeList();
    }

    @RepeatSubmit
    @PostMapping("/alterTableComment")
    public AjaxResult alterTableComment(@RequestBody DataSourceTable table){
        return dataSourceService.alterTableComment(table);
    }
    /**
     * 切换数据源
     */
    private void changeDateSource(DataSourceTable table) {
        if (StrUtil.isNotBlank(table.getDataSource()) && !table.getDataSource().equals("MASTER")) {
            DynamicDataSourceContextHolder.setDataSourceType(table.getDataSource());
        }
    }
}
