<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.EventStatisticsSlaveMapper">

    <!-- 运管服事项统计 -->
    <!-- 获取运管服事项当日新增数量 -->
    <select id="getUmEventTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_um_event 
        WHERE DATE(report_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND area_name = #{param.county}
        </if>
    </select>

    <!-- 获取运管服事项待办数量 -->
    <select id="getUmEventPendingCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_um_event 
        WHERE case_status = '0'
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND area_name = #{param.county}
        </if>
    </select>

    <!-- 获取运管服事项累计办理数量 -->
    <select id="getUmEventTotalHandledCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_um_event 
        WHERE case_status IN ('1', '2')
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND area_name = #{param.county}
        </if>
    </select>

    <!-- 获取运管服事项办结数量 -->
    <select id="getUmEventCompletedCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_um_event 
        WHERE case_status = '1'
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND area_name = #{param.county}
        </if>
    </select>

    <!-- 12345热线统计 -->
    <!-- 获取12345热线当日新增数量 -->
    <select id="getHotlineDetailTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_hotline_detail 
        WHERE DATE(handle_start_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND district_name = #{param.county}
        </if>
    </select>

    <!-- 获取12345热线待办数量 -->
    <select id="getHotlineDetailPendingCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_hotline_detail 
        WHERE event_status IN ('待办理', '办理中')
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND district_name = #{param.county}
        </if>
    </select>

    <!-- 获取12345热线累计办理数量 -->
    <select id="getHotlineDetailTotalHandledCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_hotline_detail 
        WHERE event_status IN ('已办结', '办理中')
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND district_name = #{param.county}
        </if>
    </select>

    <!-- 获取12345热线办结数量 -->
    <select id="getHotlineDetailCompletedCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM db_hotline_detail 
        WHERE event_status = '已办结'
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND district_name = #{param.county}
        </if>
    </select>

    <!-- 获取12345热线满意度统计 -->
    <select id="getHotlineDetailSatisfactionStats" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalSatisfactionCount,
            SUM(CASE WHEN return_visit_score IN ('满意', '非常满意') THEN 1 ELSE 0 END) as satisfiedCount,
            SUM(CASE WHEN return_visit_score = '基本满意' THEN 1 ELSE 0 END) as basicallySatisfiedCount,
            SUM(CASE WHEN return_visit_score IN ('不满意', '非常不满意') THEN 1 ELSE 0 END) as unsatisfiedCount,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND((SUM(CASE WHEN return_visit_score IN ('满意', '非常满意', '基本满意') THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2)
                ELSE 0 
            END as satisfactionRate
        FROM db_hotline_detail 
        WHERE is_return_visit = 1 AND return_visit_score IS NOT NULL AND return_visit_score != ''
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND district_name = #{param.county}
        </if>
    </select>

    <!-- 万人助企统计 -->
    <!-- 获取万人助企当日新增数量 -->
    <select id="getEnterpriseAppealTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_enterprise_appeal
        WHERE DATE(collect_date) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取万人助企累计数量 -->
    <select id="getEnterpriseAppealTotalCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_enterprise_appeal
        <where>
            <if test="param.deptId != null">
                AND dept_id = #{param.deptId}
            </if>
            <if test="param.county != null and param.county != ''">
                AND county = #{param.county}
            </if>
        </where>
    </select>

    <!-- 应急事项统计 -->
    <!-- 获取应急事项当日新增数量 -->
    <select id="getEmergencyEventTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_emergency_event
        WHERE DATE(report_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取应急事项待办数量 -->
    <select id="getEmergencyEventPendingCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_emergency_event
        WHERE status IN (0, 1)
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取应急事项累计办理数量 -->
    <select id="getEmergencyEventTotalHandledCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_emergency_event
        WHERE status IN (1, 2, 3)
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取应急事项办结数量 -->
    <select id="getEmergencyEventCompletedCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_emergency_event
        WHERE status IN (2, 3)
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 市长信箱统计 -->
    <!-- 获取市长信箱当日新增数量 -->
    <select id="getMayorMailboxTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_mayor_mailbox
        WHERE DATE(post_date) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND post_address LIKE CONCAT('%', #{param.county}, '%')
        </if>
    </select>

    <!-- 获取市长信箱累计数量 -->
    <select id="getMayorMailboxTotalCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_mayor_mailbox
        <where>
            <if test="param.deptId != null">
                AND dept_id = #{param.deptId}
            </if>
            <if test="param.county != null and param.county != ''">
                AND post_address LIKE CONCAT('%', #{param.county}, '%')
            </if>
        </where>
    </select>

    <!-- 信访统计 -->
    <!-- 获取信访当日新增数量 -->
    <select id="getPetitionInfoTodayCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_petition_info
        WHERE DATE(register_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND problem_location LIKE CONCAT('%', #{param.county}, '%')
        </if>
    </select>

    <!-- 获取信访累计数量 -->
    <select id="getPetitionInfoTotalCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_petition_info
        <where>
            <if test="param.deptId != null">
                AND dept_id = #{param.deptId}
            </if>
            <if test="param.county != null and param.county != ''">
                AND problem_location LIKE CONCAT('%', #{param.county}, '%')
            </if>
        </where>
    </select>

    <!-- 获取信访办结数量 -->
    <select id="getPetitionInfoCompletedCount" parameterType="com.ruoyi.dataview.domain.bigData.param.DbBigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM db_petition_info
        WHERE is_closed = 1
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND problem_location LIKE CONCAT('%', #{param.county}, '%')
        </if>
    </select>

</mapper>
