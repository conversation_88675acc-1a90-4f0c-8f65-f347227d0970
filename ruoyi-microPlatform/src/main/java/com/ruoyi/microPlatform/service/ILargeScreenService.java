package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.CommunityUnderInfoVO;
import com.ruoyi.microPlatform.domain.GridUnderInfoVO;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbPersonType;

import java.util.List;
import java.util.Map;

/**
 * 大屏展示Service
 */
public interface ILargeScreenService {

    /**
     * 人员标签类型排名
     */
    Map<String, Integer> getLableRank(LargeScreenInfo largeScreenInfo);


    /**
     * 社区功能室统计
     */
    Map<String, Integer> getCommunityFunctionCount(LargeScreenInfo largeScreenInfo);

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 数量
     */
    Map<String, Integer> getStatisticsByJurisdiction(LargeScreenInfo largeScreenInfo);


    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 。空间区域经纬度
     */
    List<?> getSpaceByJurisdiction(LargeScreenInfo largeScreenInfo);

    /**
     * 辖区下的 网格、小区、门店商户、住户、住户成员 具体数据
     */
    List<?> getListByJurisdiction(LargeScreenInfo largeScreenInfo);

    /**
     * 巡查走访情况
     */
    Map<String,Map<String,Integer>> getpatrolVisitSituation(LargeScreenInfo largeScreenInfo);

    /**
     * 居民吹哨
     */
    Map<String,Integer> getResidentWhistleCount(LargeScreenInfo largeScreenInfo);

    /**
     * 干部岗位类型
     */
    Map<String, Integer> getDeptUserPostType(LargeScreenInfo largeScreenInfo);

    /**
     * 门店行业类型
     */
    Map<String, Integer> getMerchantInfoType(LargeScreenInfo largeScreenInfo);

    /**
     * 根据网格编码获得 所属村/社区    网格范围   住户家庭数   人员数    门店商户数
     * @param largeScreenInfo
     * @return
     */
    GridUnderInfoVO getGridUnderInfo(LargeScreenInfo largeScreenInfo);

    /**
     *       根据小区id获得 小区名称   所属网格    面积   住户家庭数   住户成员数
     * @param largeScreenInfo
     * @return
     */
    CommunityUnderInfoVO getCommunityUnderInfo(LargeScreenInfo largeScreenInfo);


    /**
     * 矛盾纠纷 已化解数 未化解数 总数
     *              已化解 包含：公安已化解 、综治已化解
     *             未化解 包含：除了 已化解的
     */
    Map<String, Integer> getIssueCount(LargeScreenInfo largeScreenInfo);


}
