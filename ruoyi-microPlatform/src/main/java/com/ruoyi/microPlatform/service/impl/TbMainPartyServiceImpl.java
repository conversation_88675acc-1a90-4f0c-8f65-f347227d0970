package com.ruoyi.microPlatform.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.mapper.TbMainPartyMapper;
import com.ruoyi.microPlatform.domain.TbMainParty;
import com.ruoyi.microPlatform.service.ITbMainPartyService;

/**
 * 矛盾纠纷当事人信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
@Service
public class TbMainPartyServiceImpl implements ITbMainPartyService 
{
    @Autowired
    private TbMainPartyMapper tbMainPartyMapper;

    /**
     * 查询矛盾纠纷当事人信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 矛盾纠纷当事人信息
     */
    @Override
    public TbMainParty selectTbMainPartyById(Long id)
    {
        return tbMainPartyMapper.selectTbMainPartyById(id);
    }

    /**
     * 校验矛盾纠纷当事人信息是否存在
     *
     * @param tbMainParty
     * @return boolean
     */
    @Override
    public boolean checkTbMainParty(TbMainParty tbMainParty){
        TbMainParty old = tbMainPartyMapper.checkTbMainPartyUnique(tbMainParty);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询矛盾纠纷当事人信息列表
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 矛盾纠纷当事人信息
     */
    @Override
    public List<TbMainParty> selectTbMainPartyList(TbMainParty tbMainParty)
    {
        return tbMainPartyMapper.selectTbMainPartyList(tbMainParty);
    }

    /**
     * 导入矛盾纠纷当事人信息
     *
     * @param infos       矛盾纠纷当事人信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMainParty(List<TbMainParty> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入矛盾纠纷当事人信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbMainParty info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbMainParty old = tbMainPartyMapper.checkTbMainPartyUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbMainPartyMapper.insertTbMainParty(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbMainPartyMapper.updateTbMainParty(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    @Override
    public int insertTbMainParty(TbMainParty tbMainParty)
    {
        tbMainParty.setCreateTime(DateUtils.getNowDate());
        return tbMainPartyMapper.insertTbMainParty(tbMainParty);
    }

    /**
     * 修改矛盾纠纷当事人信息
     * 
     * @param tbMainParty 矛盾纠纷当事人信息
     * @return 结果
     */
    @Override
    public int updateTbMainParty(TbMainParty tbMainParty)
    {
        tbMainParty.setUpdateTime(DateUtils.getNowDate());
        return tbMainPartyMapper.updateTbMainParty(tbMainParty);
    }

    /**
     * 批量删除矛盾纠纷当事人信息
     * 
     * @param ids 需要删除的矛盾纠纷当事人信息主键
     * @return 结果
     */
    @Override
    public int deleteTbMainPartyByIds(Long[] ids)
    {
        return tbMainPartyMapper.deleteTbMainPartyByIds(ids);
    }

    /**
     * 删除矛盾纠纷当事人信息信息
     * 
     * @param id 矛盾纠纷当事人信息主键
     * @return 结果
     */
    @Override
    public int deleteTbMainPartyById(Long id)
    {
        return tbMainPartyMapper.deleteTbMainPartyById(id);
    }
}
