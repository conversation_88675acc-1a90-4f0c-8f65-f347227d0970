package com.ruoyi.microPlatform.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 门店巡查基础检查项目对象 tb_merchant_inspect_detail
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbMerchantInspectDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Integer id;

    /** 门店基础检查项目 */
    @Excel(name = "门店基础检查项目")
    private String inspectType;

    /** 门店巡查id */
    @Excel(name = "门店巡查id")
    private Long inspectId;

    /** 检查要求 */
    @Excel(name = "检查要求")
    private String inspectDesc;

    /** 巡查结果（1正常 2异常） */
    @Excel(name = "巡查结果", readConverterExp = "1=正常,2=异常")
    private Integer inspectResult;

    /** 大类 */
    @Excel(name = "大类")
    private String type;

    private String accessory;

    public Long getInspectId() {
        return inspectId;
    }

    public void setInspectId(Long inspectId) {
        this.inspectId = inspectId;
    }

    public void setId(Integer id)
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setInspectType(String inspectType) 
    {
        this.inspectType = inspectType;
    }

    public String getInspectType() 
    {
        return inspectType;
    }
    public void setInspectDesc(String inspectDesc) 
    {
        this.inspectDesc = inspectDesc;
    }

    public String getInspectDesc() 
    {
        return inspectDesc;
    }
    public void setInspectResult(Integer inspectResult) 
    {
        this.inspectResult = inspectResult;
    }

    public Integer getInspectResult() 
    {
        return inspectResult;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("inspectType", getInspectType())
            .append("inspectId", getInspectId())
            .append("inspectDesc", getInspectDesc())
            .append("inspectResult", getInspectResult())
            .append("remark", getRemark())
            .append("type", getType())
            .toString();
    }
}
