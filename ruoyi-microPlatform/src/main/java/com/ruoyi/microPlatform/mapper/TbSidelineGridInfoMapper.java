package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;

/**
 * 兼职网格员所属网格信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface TbSidelineGridInfoMapper 
{
    /**
     * 查询兼职网格员所属网格信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 兼职网格员所属网格信息
     */
    public TbSidelineGridInfo selectTbSidelineGridInfoById(Long id);

    /**
     * 校验兼职网格员所属网格信息是否唯一
     *
     * @param tbSidelineGridInfo
     * @return 兼职网格员所属网格信息
     */
    public TbSidelineGridInfo checkTbSidelineGridInfoUnique(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 查询兼职网格员所属网格信息列表
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 兼职网格员所属网格信息集合
     */
    public List<TbSidelineGridInfo> selectTbSidelineGridInfoList(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 新增兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    public int insertTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 修改兼职网格员所属网格信息
     * 
     * @param tbSidelineGridInfo 兼职网格员所属网格信息
     * @return 结果
     */
    public int updateTbSidelineGridInfo(TbSidelineGridInfo tbSidelineGridInfo);

    /**
     * 删除兼职网格员所属网格信息
     * 
     * @param id 兼职网格员所属网格信息主键
     * @return 结果
     */
    public int deleteTbSidelineGridInfoById(Long id);

    /**
     * 批量删除兼职网格员所属网格信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbSidelineGridInfoByIds(Long[] ids);
}
