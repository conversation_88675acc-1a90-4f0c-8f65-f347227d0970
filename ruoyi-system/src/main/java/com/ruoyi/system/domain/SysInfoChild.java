package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 测试子对象 sys_info_child
 * 
 * <AUTHOR>
 * @date 2023-01-10
 */
public class SysInfoChild extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Integer id;

    /** 主表id */
    private Integer infoId;

    /** 子标签名 */
    @Excel(name = "子标签名")
    private String childLabel;

    /** 子标签值 */
    @Excel(name = "子标签值")
    private String childValue;

    public void setId(Integer id) 
    {
        this.id = id;
    }

    public Integer getId() 
    {
        return id;
    }
    public void setInfoId(Integer infoId) 
    {
        this.infoId = infoId;
    }

    public Integer getInfoId() 
    {
        return infoId;
    }
    public void setChildLabel(String childLabel) 
    {
        this.childLabel = childLabel;
    }

    public String getChildLabel() 
    {
        return childLabel;
    }
    public void setChildValue(String childValue) 
    {
        this.childValue = childValue;
    }

    public String getChildValue() 
    {
        return childValue;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("infoId", getInfoId())
            .append("childLabel", getChildLabel())
            .append("childValue", getChildValue())
            .toString();
    }
}
