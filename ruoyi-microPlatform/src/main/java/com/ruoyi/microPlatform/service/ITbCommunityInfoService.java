package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.domain.TbCommunityInfo;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;

/**
 * 小区信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbCommunityInfoService 
{
    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    public TbCommunityInfo selectTbCommunityInfoById(Long id);

    /**
     * 校验小区信息是否存在
     *
     * @param tbCommunityInfo 小区信息
     * @return 小区信息
     */
    public boolean checkTbCommunityInfo(TbCommunityInfo tbCommunityInfo);

    /**
     * 查询小区信息列表
     * 
     * @param tbCommunityInfo 小区信息
     * @return 小区信息集合
     */
    public List<TbCommunityInfo> selectTbCommunityInfoList(TbCommunityInfo tbCommunityInfo);

    public Integer selectTbCommunityInfoCount(TbCommunityInfo tbCommunityInfo);

    /**
     * 导入小区信息
     *
     * @param infos       小区信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbCommunityInfo(List<TbCommunityInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增小区信息
     * 
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    public int insertTbCommunityInfo(TbCommunityInfo tbCommunityInfo);

    /**
     * 修改小区信息
     * 
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    public int updateTbCommunityInfo(TbCommunityInfo tbCommunityInfo);

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的小区信息主键集合
     * @return 结果
     */
    public int deleteTbCommunityInfoByIds(Long[] ids);

    /**
     * 删除小区信息信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    public int deleteTbCommunityInfoById(Long id);


}
