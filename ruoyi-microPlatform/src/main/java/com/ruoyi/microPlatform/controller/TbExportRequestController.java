package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbExportRequest;
import com.ruoyi.microPlatform.service.ITbExportRequestService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数据导出审核Controller
 *
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
@RestController
@RequestMapping("/gridData/exportRequest")
public class TbExportRequestController extends BaseController {
    @Autowired
    private ITbExportRequestService tbExportRequestService;

    /**
     * 查询数据导出审核列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbExportRequest tbExportRequest) {
        startPage();
        if (ObjectUtil.isNotEmpty(tbExportRequest.getQueryType())){
            if (tbExportRequest.getQueryType() == 1) {
                // 我发起的
                tbExportRequest.setDeptId(getDeptId());
            } else if (tbExportRequest.getQueryType() == 2) {
                // 需要我审核的
                tbExportRequest.setAuditDeptId(getDeptId());
            }else if (tbExportRequest.getQueryType() == 3){
                // 我审核过的
                tbExportRequest.setAuditDoneDeptId(getDeptId());
            }
        }
        List<TbExportRequest> list = tbExportRequestService.selectTbExportRequestList(tbExportRequest);
        return getDataTable(list);
    }

    /**
     * 导出数据导出审核列表
     */
    @Log(title = "数据导出审核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbExportRequest tbExportRequest) {
        List<TbExportRequest> list = tbExportRequestService.selectTbExportRequestList(tbExportRequest);
        ExcelUtil<TbExportRequest> util = new ExcelUtil<TbExportRequest>(TbExportRequest.class);
        util.exportExcel(response, list, "数据导出审核数据");
    }

    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:exportRequest:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbExportRequest> util = new ExcelUtil<TbExportRequest>(TbExportRequest.class);
        List<TbExportRequest> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbExportRequestService.importTbExportRequest(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbExportRequest> util = new ExcelUtil<TbExportRequest>(TbExportRequest.class);
        util.exportTemplateExcel(response, "数据导出审核数据");
    }

    /**
     * 获取数据导出审核详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbExportRequestService.selectTbExportRequestById(id));
    }

    /**
     * 新增数据导出审核
     */
    @Log(title = "数据导出审核", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbExportRequest tbExportRequest) {
        SysUser user = getLoginUser().getUser();
        tbExportRequest.setCreateBy(getUsername());
        tbExportRequest.setUserId(getUserId());
        tbExportRequest.setNickName(getNickName());
        tbExportRequest.setDeptId(getDeptId());
        tbExportRequest.setDeptLevel(user.getDept().getDeptLevel());
        tbExportRequest.setRequestUnit(user.getDept().getDeptName());
        tbExportRequest.setStatus(0);

        // 上级部门审核
        tbExportRequest.setAuditDeptId(user.getDept().getParentId());
        tbExportRequest.setAuditLevel(tbExportRequest.getDeptLevel() + 1);

        // 下级
        return toAjax(tbExportRequestService.insertTbExportRequest(tbExportRequest));
    }

    /**
     * 修改数据导出审核
     */
    @Log(title = "数据导出审核", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbExportRequest tbExportRequest) {
        tbExportRequest.setUpdateBy(getUsername());
        return toAjax(tbExportRequestService.updateTbExportRequest(tbExportRequest, getLoginUser()));
    }

    @PutMapping("dataExport")
    @RepeatSubmit
    public AjaxResult dataExport(@RequestBody TbExportRequest tbExportRequest) {
        tbExportRequest.setUpdateBy(getUsername());
        return toAjax(tbExportRequestService.dataExport(tbExportRequest, getLoginUser()));
    }

    /**
     * 删除数据导出审核
     */
    @Log(title = "数据导出审核", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbExportRequestService.deleteTbExportRequestByIds(ids));
    }
}
