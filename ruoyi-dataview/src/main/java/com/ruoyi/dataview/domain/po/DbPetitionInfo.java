package com.ruoyi.dataview.domain.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 市信访局-上访信息数据对象 db_petition_info
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Data
public class DbPetitionInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 信访日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "信访日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date petitionDate;

    /**
     * 是否复核
     */
    @Excel(name = "是否复核")
    private Integer isReviewed;

    /**
     * 概况信息
     */
    @Excel(name = "概况信息")
    private String overview;

    /**
     * 登记时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "登记时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerTime;

    /**
     * 信访形式
     */
    @Excel(name = "信访形式")
    private String petitionForm;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 住址
     */
    @Excel(name = "住址")
    private String address;

    /**
     * 信访目的
     */
    @Excel(name = "信访目的")
    private String petitionPurpose;

    /**
     * 内容分类
     */
    @Excel(name = "内容分类")
    private String contentCategory;

    /**
     * 问题属地
     */
    @Excel(name = "问题属地")
    private String problemLocation;

    /**
     * 责任单位
     */
    @Excel(name = "责任单位")
    private String responsibleUnit;

    /**
     * 信访件状态
     */
    @Excel(name = "信访件状态")
    private String petitionStatus;

    /**
     * 登记机构
     */
    @Excel(name = "登记机构")
    private String registerOrg;

    /**
     * 投诉方式
     */
    @Excel(name = "投诉方式")
    private String complaintMethod;

    /**
     * 重复信访次数
     */
    @Excel(name = "重复信访次数")
    private Integer repeatCount;

    /**
     * 接访领导
     */
    @Excel(name = "接访领导")
    private String receptionLeader;

    /**
     * 登记部门
     */
    @Excel(name = "登记部门")
    private String registerDepartment;

    /**
     * 办理方式
     */
    @Excel(name = "办理方式")
    private String handleMethod;

    /**
     * 办理意见
     */
    @Excel(name = "办理意见")
    private String handleOpinion;

    /**
     * 纳入满意度评价标志
     */
    @Excel(name = "纳入满意度评价标志")
    private Integer inSatisfactionEval;

    /**
     * 信访件编号
     */
    @Excel(name = "信访件编号")
    private String petitionNo;

    /**
     * 是否终结
     */
    @Excel(name = "是否终结")
    private Integer isFinalized;

    /**
     * 人数
     */
    @Excel(name = "人数")
    private Integer peopleCount;

    /**
     * 办理情形
     */
    @Excel(name = "办理情形")
    private String handlingSituation;

    /**
     * 办理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "办理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date handlingTime;

    /**
     * 户口所在地
     */
    @Excel(name = "户口所在地")
    private String householdReg;

    /**
     * 民族
     */
    @Excel(name = "民族")
    private String ethnicity;

    /**
     * 热点问题
     */
    @Excel(name = "热点问题")
    private String hotIssue;

    /**
     * 剩余天数
     */
    @Excel(name = "剩余天数")
    private Integer remainingDays;

    /**
     * 是否办结
     */
    @Excel(name = "是否办结")
    private Integer isClosed;

    /**
     * 是否积案
     */
    @Excel(name = "是否积案")
    private Integer isAccumulatedCase;

    /**
     * 是否联名
     */
    @Excel(name = "是否联名")
    private Integer isJoint;

    /**
     * 是否匿名
     */
    @Excel(name = "是否匿名")
    private Integer isAnonymous;

    /**
     * 是否三跨三分离
     */
    @Excel(name = "是否三跨三分离")
    private Integer isThreeCrossThreeSplit;

    /**
     * 是否涉澳
     */
    @Excel(name = "是否涉澳")
    private Integer isInvolvingMacau;

    /**
     * 是否涉法涉诉
     */
    @Excel(name = "是否涉法涉诉")
    private Integer isInvolvingLaw;

    /**
     * 是否涉港
     */
    @Excel(name = "是否涉港")
    private Integer isInvolvingHk;

    /**
     * 是否涉侨
     */
    @Excel(name = "是否涉侨")
    private Integer isInvolvingOverseasChinese;

    /**
     * 是否涉台
     */
    @Excel(name = "是否涉台")
    private Integer isInvolvingTaiwan;

    /**
     * 是否扬言
     */
    @Excel(name = "是否扬言")
    private Integer isThreatening;

    /**
     * 是否重复信访
     */
    @Excel(name = "是否重复信访")
    private Integer isRepeatPetition;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 首次信访机构
     */
    @Excel(name = "首次信访机构")
    private String firstPetitionOrg;

    /**
     * 首次信访日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次信访日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstPetitionDate;

    /**
     * 首次投诉方式
     */
    @Excel(name = "首次投诉方式")
    private String firstComplaintMethod;

    /**
     * 投诉内容
     */
    @Excel(name = "投诉内容")
    private String complaintContent;

    /**
     * 信访原因
     */
    @Excel(name = "信访原因")
    private String petitionReason;

    /**
     * 性别
     */
    @Excel(name = "性别")
    private String gender;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String idNumber;

    /**
     * 证件类型
     */
    @Excel(name = "证件类型")
    private String idType;

    /**
     * 职业
     */
    @Excel(name = "职业")
    private String occupation;

    /**
     * 主题词
     */
    @Excel(name = "主题词")
    private String keywords;

    /**
     * 最后信访日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后信访日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastPetitionDate;
}