package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbMerchantAssistant;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 门店店员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbMerchantAssistantMapper 
{
    /**
     * 查询门店店员
     * 
     * @param id 门店店员主键
     * @return 门店店员
     */
    public TbMerchantAssistant selectTbMerchantAssistantById(Long id);

    /**
     * 校验门店店员是否唯一
     *
     * @param tbMerchantAssistant
     * @return 门店店员
     */
    public TbMerchantAssistant checkTbMerchantAssistantUnique(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 查询门店店员列表
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 门店店员集合
     */
    public List<TbMerchantAssistant> selectTbMerchantAssistantList(TbMerchantAssistant tbMerchantAssistant);

    public List<Long> selectTbMerchantAssistantCount(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 新增门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    public int insertTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 修改门店店员
     * 
     * @param tbMerchantAssistant 门店店员
     * @return 结果
     */
    public int updateTbMerchantAssistant(TbMerchantAssistant tbMerchantAssistant);

    /**
     * 删除门店店员
     * 
     * @param id 门店店员主键
     * @return 结果
     */
    public int deleteTbMerchantAssistantById(Long id);

    /**
     * 批量删除门店店员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMerchantAssistantByIds(Long[] ids);

    public int deleteTbMerchantAssistantByMainIds(Long[] ids);


    /**
     * 批量新增门店店员
     *
     * @param tbMerchantAssistantList 门店店员列表
     * @return 结果
     */
    public int batchTbMerchantAssistant(@Param("list") List<TbMerchantAssistant> tbMerchantAssistantList, @Param("main")TbMerchantInfo merchantInfo);
}
