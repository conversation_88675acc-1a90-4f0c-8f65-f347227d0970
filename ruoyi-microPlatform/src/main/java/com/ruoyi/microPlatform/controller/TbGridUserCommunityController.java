package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.exception.ServiceException;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;
import com.ruoyi.microPlatform.service.ITbGridUserCommunityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格员关联小区Controller
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/gridUserCommunity")
public class TbGridUserCommunityController extends BaseController
{
    @Autowired
    private ITbGridUserCommunityService tbGridUserCommunityService;

    /**
     * 查询网格员关联小区列表
     */
    @GetMapping("/list")
    public TableDataInfo list(TbGridUserCommunity tbGridUserCommunity)
    {
        startPage();
        List<TbGridUserCommunity> list = tbGridUserCommunityService.selectTbGridUserCommunityList(tbGridUserCommunity);
        return getDataTable(list);
    }


    /**
     * 导出网格员关联小区列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUserCommunity:export')")
    @Log(title = "网格员关联小区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbGridUserCommunity tbGridUserCommunity)
    {
        List<TbGridUserCommunity> list = tbGridUserCommunityService.selectTbGridUserCommunityList(tbGridUserCommunity);
        ExcelUtil<TbGridUserCommunity> util = new ExcelUtil<TbGridUserCommunity>(TbGridUserCommunity.class);
        util.exportExcel(response, list, "网格员关联小区数据");
    }

    @Log(title = "网格员关联小区", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:gridUserCommunity:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbGridUserCommunity> util = new ExcelUtil<TbGridUserCommunity>(TbGridUserCommunity.class);
        List<TbGridUserCommunity> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbGridUserCommunityService.importTbGridUserCommunity(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbGridUserCommunity> util = new ExcelUtil<TbGridUserCommunity>(TbGridUserCommunity.class);
        util.exportTemplateExcel(response, "网格员关联小区数据");
    }

    /**
     * 获取网格员关联小区详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUserCommunity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbGridUserCommunityService.selectTbGridUserCommunityById(id));
    }



    /**
     * 新增网格员关联小区
     */
    @Log(title = "网格员关联小区", businessType = BusinessType.INSERT)
    @PostMapping("/addList")
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbGridUserCommunity tbGridUserCommunity)
    {
        if (ObjectUtil.isEmpty(tbGridUserCommunity.getIdList()) || tbGridUserCommunity.getIdList().size() == 0){
            return error("不可为空！");
        }

        for (int i = 0; i < tbGridUserCommunity.getIdList().size(); i++) {
            tbGridUserCommunity.setCommunityId(tbGridUserCommunity.getIdList().get(i));
            tbGridUserCommunity.setCreateBy(getUsername());
            if (tbGridUserCommunityService.checkTbGridUserCommunity(tbGridUserCommunity)){
                return error("数据已存在！");
            }
            tbGridUserCommunityService.insertTbGridUserCommunity(tbGridUserCommunity);
        }

        return toAjax(1);
    }



    /**
     * 修改网格员关联小区
     */
    @PreAuthorize("@ss.hasPermi('gridData:gridUserCommunity:edit')")
    @Log(title = "网格员关联小区", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbGridUserCommunity tbGridUserCommunity)
    {
        tbGridUserCommunity.setUpdateBy(getUsername());

        if (tbGridUserCommunityService.checkTbGridUserCommunity(tbGridUserCommunity)){
            return error("数据已存在！");
        }

        return toAjax(tbGridUserCommunityService.updateTbGridUserCommunity(tbGridUserCommunity));
    }


    /**
     * 删除网格员关联小区
     */
    @Log(title = "网格员关联小区", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbGridUserCommunityService.deleteTbGridUserCommunityByIds(ids));
    }
}
