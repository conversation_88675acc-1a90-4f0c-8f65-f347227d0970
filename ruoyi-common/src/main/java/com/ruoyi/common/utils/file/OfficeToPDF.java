package com.ruoyi.common.utils.file;

import com.documents4j.api.DocumentType;
import com.documents4j.api.IConverter;
import com.documents4j.job.LocalConverter;

import java.io.*;

public class OfficeToPDF {

    /**
     * 文档转PDF
     */
    public static void wordToPdf(String sourceFilePath, String desFilePathd ){
        File inputWord = new File(sourceFilePath);
        File outputFile = new File(desFilePathd);
        try  {
            InputStream docxInputStream = new FileInputStream(inputWord);
            OutputStream outputStream = new FileOutputStream(outputFile);
            IConverter converter = LocalConverter.builder().build();
            converter.convert(docxInputStream)
                    .as(DocumentType.DOCX)
                    .to(outputStream)
                    .as(DocumentType.PDF).execute();
            outputStream.close();
        } catch (Exception e) {
            System.out.println("[documents4J] word转pdf失败:{}");
            System.out.println(e.toString());
        }
    }

//    public static boolean docTopdf(String docPath, String pdfPath) {
//
//        File inputWord = new File(docPath);
//        File outputFile = new File(pdfPath);
//        try {
//            InputStream docxInputStream = new FileInputStream(inputWord);
//            OutputStream outputStream = new FileOutputStream(outputFile);
//            IConverter converter = LocalConverter.builder().build();
//            String fileTyle=docPath.substring(docPath.lastIndexOf("."),docPath.length());//获取文件类型
//            if(".docx".equals(fileTyle)){
//                converter.convert(docxInputStream).as(DocumentType.DOCX).to(outputStream).as(DocumentType.PDF).execute();
//            }else if(".doc".equals(fileTyle)){
//                converter.convert(docxInputStream).as(DocumentType.DOC).to(outputStream).as(DocumentType.PDF).execute();
//            }else if(".xls".equals(fileTyle)){
//                converter.convert(docxInputStream).as(DocumentType.XLS).to(outputStream).as(DocumentType.PDF).execute();
//            }else if(".xlsx".equals(fileTyle)){
//                converter.convert(docxInputStream).as(DocumentType.XLSX).to(outputStream).as(DocumentType.PDF).execute();
//            }
//            docxInputStream.close();
//            outputStream.close();
//            inputWord.delete();
//            System.out.println("pdf转换成功");
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//    }

    public static void main(String[] args) {
        String string = "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\22222.docx";
        String string2 = "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\22222.pdf";
//        docTopdf(string,string2);
    }

//    /**
//     * 获取license 去除水印
//     * @return
//     */
//    public static boolean getLicense() {
//        boolean result = false;
//        try {
//            InputStream is = OfficeToPDF.class.getClassLoader().getResourceAsStream("\\license.xml");
//            License aposeLic = new License();
//            aposeLic.setLicense(is);
//            result = true;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return result;
//    }
//
//    /**
//     * excel 转为pdf 输出。
//     *
//     * @param sourceFilePath  excel文件
//     * @param desFilePathd  pad 输出文件目录
//     */
//    public static Boolean excel2pdf(String sourceFilePath, String desFilePathd ){
//        // 验证License 若不验证则转化出的pdf文档会有水印产生
//        if (!getLicense()) {
//            return false;
//        }
//        try {
//            Workbook wb = new Workbook(sourceFilePath);// 原始excel路径
//
//            FileOutputStream fileOS = new FileOutputStream(desFilePathd);
//            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
//            pdfSaveOptions.setOnePagePerSheet(true);
//
//            int[] autoDrawSheets={3};
//            //当excel中对应的sheet页宽度太大时，在PDF中会拆断并分页。此处等比缩放。
////            autoDraw(wb,autoDrawSheets);
//
//            int[] showSheets={0};
//            //隐藏workbook中不需要的sheet页。
//            printSheetPage(wb,showSheets);
//            wb.save(fileOS, pdfSaveOptions);
//            fileOS.flush();
//            fileOS.close();
//            System.out.println("完毕");
//            return true;
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("excel转pdf失败");
//            return false;
//        }
//    }
//
//
//    /**
//     * 设置打印的sheet 自动拉伸比例
//     * @param wb
//     * @param page 自动拉伸的页的sheet数组
//     */
//    public static void autoDraw(Workbook wb,int[] page){
//        if(null!=page&&page.length>0){
//            for (int i = 0; i < page.length; i++) {
//                wb.getWorksheets().get(i).getHorizontalPageBreaks().clear();
//                wb.getWorksheets().get(i).getVerticalPageBreaks().clear();
//            }
//        }
//    }
//
//
//    /**
//     * 隐藏workbook中不需要的sheet页。
//     * @param wb
//     * @param page 显示页的sheet数组
//     */
//    public static void printSheetPage(Workbook wb,int[] page){
//        for (int i= 1; i < wb.getWorksheets().getCount(); i++)  {
//            wb.getWorksheets().get(i).setVisible(false);
//        }
//        if(null==page||page.length==0){
//            wb.getWorksheets().get(0).setVisible(true);
//        }else{
//            for (int i = 0; i < page.length; i++) {
//                wb.getWorksheets().get(i).setVisible(true);
//            }
//        }
//    }

}
