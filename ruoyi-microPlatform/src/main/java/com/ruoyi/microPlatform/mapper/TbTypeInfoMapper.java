package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbTypeInfo;

/**
 * 矛盾纠纷类别编码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface TbTypeInfoMapper 
{
    /**
     * 查询矛盾纠纷类别编码
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 矛盾纠纷类别编码
     */
    public TbTypeInfo selectTbTypeInfoById(Integer id);

    /**
     * 校验矛盾纠纷类别编码是否唯一
     *
     * @param tbTypeInfo
     * @return 矛盾纠纷类别编码
     */
    public TbTypeInfo checkTbTypeInfoUnique(TbTypeInfo tbTypeInfo);

    /**
     * 查询矛盾纠纷类别编码列表
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 矛盾纠纷类别编码集合
     */
    public List<TbTypeInfo> selectTbTypeInfoList(TbTypeInfo tbTypeInfo);

    public List<TbTypeInfo> selectTbTypeInfoList2(TbTypeInfo tbTypeInfo);

    /**
     * 新增矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    public int insertTbTypeInfo(TbTypeInfo tbTypeInfo);

    /**
     * 修改矛盾纠纷类别编码
     * 
     * @param tbTypeInfo 矛盾纠纷类别编码
     * @return 结果
     */
    public int updateTbTypeInfo(TbTypeInfo tbTypeInfo);

    /**
     * 删除矛盾纠纷类别编码
     * 
     * @param id 矛盾纠纷类别编码主键
     * @return 结果
     */
    public int deleteTbTypeInfoById(Integer id);

    /**
     * 批量删除矛盾纠纷类别编码
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbTypeInfoByIds(Integer[] ids);
}
