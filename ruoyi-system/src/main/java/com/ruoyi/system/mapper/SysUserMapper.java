package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 用户表 数据层
 * 
 * <AUTHOR>
 */
public interface SysUserMapper
{
    /**
     * 根据条件分页查询用户列表
     * 
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 根据条件分页查询已配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     * 
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     * 
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     * 
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 新增用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     * 
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 修改用户头像
     * 
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     * 
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password, @Param("spare") String spare);

    /**
     * 通过用户ID删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     * 
     * @param userName 用户名称
     * @return 结果
     */
    public SysUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(SysUser user);

    public SysUser checkPhoneUnique2(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(SysUser user);

    /**
     * 修改签名图片
     * @param userName
     * @param signature
     * @return
     */
    public int updateUserSignature(@Param("userName") String userName, @Param("signature") String signature);

    /**
     * 查询部门下的人员列表
     * @param deptId
     * @return
     */
    List<SysUser> selectUserByDeptId(Long deptId);

    /**
     * 查询用户加密密码
     * @param userName
     * @return
     */
    String selectSpareByUser(String userName);

    /**
     * 湖区用户账号密码修改状态
     * @param username
     * @return
     */
    SysUser selectPassUpdateByUserName(String username);

    /**
     * 通过昵称名称查询所有userId
     * @param nickName
     * @return
     */
    List<Long> findUserIdByNickName(SysUser nickName);

    /**
     * 获取指定岗位人员信息
     * @param post
     * @return
     */
    List<SysUser> selectUserByPost(String post);

    /**
     * 根据openId查询用户信息
     * @param openId
     * @return
     */
    public SysUser selectWxUserByOpenId(String openId);

    public List<SysUser> selectUserListByPhone(SysUser sysUser);

    /**
     * 根据电话号码 查询居民用户所在网格
     * @param phone
     * @return
     */
    public Long selectGridId(String phone);

    public Long selectDeptId(String phone);

    /**
     * 获取用户网格Id
     * @param userId
     * @return
     */
    public SysUser selectUserGrid(Long userId);

    /**
     * 查询用户我的网格Id信息
     * @param userIds
     * @return
     */
    public List<SysUser> selectUserGridList(@Param("idList") Long[] userIds);
}
