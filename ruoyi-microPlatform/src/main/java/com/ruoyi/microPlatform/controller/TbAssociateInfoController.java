package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbAssociateInfo;
import com.ruoyi.microPlatform.service.ITbAssociateInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 行业协会商会信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/gridData/associateInfo")
public class TbAssociateInfoController extends BaseController
{
    @Autowired
    private ITbAssociateInfoService tbAssociateInfoService;

    /**
     * 查询行业协会商会信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbAssociateInfo tbAssociateInfo)
    {
        startPage();
        List<TbAssociateInfo> list = tbAssociateInfoService.selectTbAssociateInfoList(tbAssociateInfo);
        return getDataTable(list);
    }

    /**
     * 导出行业协会商会信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:export')")
    @Log(title = "行业协会商会信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbAssociateInfo tbAssociateInfo)
    {
        List<TbAssociateInfo> list = tbAssociateInfoService.selectTbAssociateInfoList(tbAssociateInfo);
        ExcelUtil<TbAssociateInfo> util = new ExcelUtil<TbAssociateInfo>(TbAssociateInfo.class);
        util.exportExcel(response, list, "行业协会商会信息数据");
    }

    @Log(title = "行业协会商会信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbAssociateInfo> util = new ExcelUtil<TbAssociateInfo>(TbAssociateInfo.class);
        List<TbAssociateInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbAssociateInfoService.importTbAssociateInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbAssociateInfo> util = new ExcelUtil<TbAssociateInfo>(TbAssociateInfo.class);
        util.exportTemplateExcel(response, "行业协会商会信息数据");
    }

    /**
     * 获取行业协会商会信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbAssociateInfoService.selectTbAssociateInfoById(id));
    }

    /**
     * 新增行业协会商会信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:add')")
    @Log(title = "行业协会商会信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbAssociateInfo tbAssociateInfo)
    {
        tbAssociateInfo.setCreateBy(getUsername());
        if (tbAssociateInfoService.checkTbAssociateInfo(tbAssociateInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbAssociateInfoService.insertTbAssociateInfo(tbAssociateInfo));
    }

    /**
     * 修改行业协会商会信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:edit')")
    @Log(title = "行业协会商会信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbAssociateInfo tbAssociateInfo)
    {
        tbAssociateInfo.setUpdateBy(getUsername());
        if (tbAssociateInfoService.checkTbAssociateInfo(tbAssociateInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbAssociateInfoService.updateTbAssociateInfo(tbAssociateInfo));
    }

    /**
     * 删除行业协会商会信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:associateInfo:remove')")
    @Log(title = "行业协会商会信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbAssociateInfoService.deleteTbAssociateInfoByIds(ids));
    }
}
