package com.ruoyi.dataview.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.dataview.domain.dto.SiteParamsQueryDTO;
import com.ruoyi.dataview.domain.po.DbSiteData;
import com.ruoyi.dataview.domain.vo.SiteDetailsDataResponseVO;
import com.ruoyi.dataview.domain.vo.SiteEchartVO;
import com.ruoyi.dataview.domain.vo.SiteGroupsResponseVO;
import com.ruoyi.dataview.domain.vo.SiteStatisticsVO;

/**
 * 水利局涵洞监测数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface IDbSiteDataService 
{
    /**
     * 查询水利局涵洞监测数据
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 水利局涵洞监测数据
     */
    public DbSiteData selectDbSiteDataById(String id);

    /**
     * 校验水利局涵洞监测数据是否存在
     *
     * @param dbSiteData 水利局涵洞监测数据
     * @return 水利局涵洞监测数据
     */
    public boolean checkDbSiteData(DbSiteData dbSiteData);

    /**
     * 查询水利局涵洞监测数据列表
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 水利局涵洞监测数据集合
     */
    public List<DbSiteData> selectDbSiteDataList(DbSiteData dbSiteData);

    /**
     * 导入水利局涵洞监测数据
     *
     * @param infos       水利局涵洞监测数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbSiteData(List<DbSiteData> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    public int insertDbSiteData(DbSiteData dbSiteData);

    /**
     * 修改水利局涵洞监测数据
     * 
     * @param dbSiteData 水利局涵洞监测数据
     * @return 结果
     */
    public int updateDbSiteData(DbSiteData dbSiteData);

    /**
     * 批量删除水利局涵洞监测数据
     * 
     * @param ids 需要删除的水利局涵洞监测数据主键集合
     * @return 结果
     */
    public int deleteDbSiteDataByIds(String[] ids);

    /**
     * 删除水利局涵洞监测数据信息
     * 
     * @param id 水利局涵洞监测数据主键
     * @return 结果
     */
    public int deleteDbSiteDataById(String id);


    /**
     * 防汛视频登陆
     * @param login 账号
     * @param pwd   密码
     * @return cookie：NFSESSIONID
     */
    Map<String,String> siteSysLogin(String login, String pwd);

    /**
     * 代理请求：/xfServe/nfQuery/groups/selectSiteNoByGroupsNo
     * @param NFSESSIONID cookie
     * @return  涵洞监测站点
     */
    List<SiteGroupsResponseVO.DataItem> proxyRequestGetSiteGroups(String NFSESSIONID);

    /**
     * 代理请求：/xfServe/nfQuery/data/selectSiteSiteManBySerial
     * @param stCode
     * @param NFSESSIONID cookie
     * @return  涵洞监测站点监测数据详情
     */
    SiteDetailsDataResponseVO proxyRequestgGetSiteDetailsByCode(String stCode, String NFSESSIONID);


    /**
     * 代理请求：/xfServe/nfQuery/data/selectSiteSiteManHistoryDataMongoBySerial
     * @param NFSESSIONID cookie
     * @param queryDTO 参数
     * @return  涵洞监测站点历史监测数据
     */
    TableDataInfo proxyRequestGetSiteHistoryDateList(SiteParamsQueryDTO queryDTO, String NFSESSIONID);

    /**
     * 代理请求：/xfServe/nfQuery/data/siteDataEchat
     * @param queryDTO 参数
     * @param NFSESSIONID cookie
     * @return 涵洞监测站点历史监测数据图形
     */
    SiteEchartVO proxyRequestGetSiteEchart(SiteParamsQueryDTO queryDTO, String NFSESSIONID);

    /**
     * 代理请求：/xfServe/nfQuery/data/selectStationBaseStatisticsHistoryDataMongodb
     * @param queryDTO 参数
     * @param NFSESSIONID cookie
     * @return 涵洞监测站点历史监测数据统计
     */
    List<SiteStatisticsVO.DataItem> proxyRequestGetSiteStatistics(SiteParamsQueryDTO queryDTO, String NFSESSIONID);

}
