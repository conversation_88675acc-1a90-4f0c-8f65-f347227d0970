package com.ruoyi.microPlatform.domain;

import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 数据导出审核对象 tb_export_request
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
public class TbExportRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 数据类别 */
    @Excel(name = "数据类别")
    private String dataType;

    /** 请求参数json字符串 */
    @Excel(name = "请求参数json字符串")
    private String paramsStr;
    /**
     * 申请原因
     */
    private String reason;

    /** 请求导出数据条目 */
    @Excel(name = "请求导出数据条目")
    private Integer dataCount;
    /**
     * 数据类别
     */
    private String type;

    /** 发起人账号 */
    @Excel(name = "发起人账号")
    private Long userId;

    /** 发起人Id */
    @Excel(name = "发起人Id")
    private String nickName;

    /** 发起人所在单位Id */
    @Excel(name = "发起人所在单位Id")
    private Long deptId;

    /** 发起者所在deptLevel */
    @Excel(name = "发起者所在deptLevel")
    private Integer deptLevel;

    /** 发起人单位 */
    @Excel(name = "发起人单位")
    private String requestUnit;

    /** 0待审核 1已审核 */
    @Excel(name = "0待审核 1已审核")
    private Integer status;

    /** 审核结果（0不通过 1通过） */
    @Excel(name = "审核结果", readConverterExp = "0=不通过,1=通过")
    private Integer auditResult;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String auditReason;

    /** 审核人id */
    @Excel(name = "审核人id")
    private Long auditUserId;

    /** 审核人名称 */
    @Excel(name = "审核人名称")
    private String auditNickName;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 待审核部门 */
    @Excel(name = "待审核部门")
    private Long auditDeptId;

    private Long auditDoneDeptId;

    /** 审核部门级别 */
    @Excel(name = "审核部门级别")
    private Integer auditLevel;

    /** 数据导出状态（0待导出 1已导出） */
    private Integer exportStatus;

    /** 数据导出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exportTime;

    /** 导出请求审核过程信息 */
    private List<TbRequestAudit> tbRequestAuditList;

    public Long getAuditDoneDeptId() {
        return auditDoneDeptId;
    }

    public void setAuditDoneDeptId(Long auditDoneDeptId) {
        this.auditDoneDeptId = auditDoneDeptId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDataType(String dataType) 
    {
        this.dataType = dataType;
    }

    public String getDataType() 
    {
        return dataType;
    }
    public void setParamsStr(String paramsStr) 
    {
        this.paramsStr = paramsStr;
    }

    public String getParamsStr() 
    {
        return paramsStr;
    }
    public void setDataCount(Integer dataCount) 
    {
        this.dataCount = dataCount;
    }

    public Integer getDataCount() 
    {
        return dataCount;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setDeptLevel(Integer deptLevel) 
    {
        this.deptLevel = deptLevel;
    }

    public Integer getDeptLevel() 
    {
        return deptLevel;
    }
    public void setRequestUnit(String requestUnit) 
    {
        this.requestUnit = requestUnit;
    }

    public String getRequestUnit() 
    {
        return requestUnit;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setAuditResult(Integer auditResult) 
    {
        this.auditResult = auditResult;
    }

    public Integer getAuditResult() 
    {
        return auditResult;
    }
    public void setAuditReason(String auditReason) 
    {
        this.auditReason = auditReason;
    }

    public String getAuditReason() 
    {
        return auditReason;
    }
    public void setAuditUserId(Long auditUserId) 
    {
        this.auditUserId = auditUserId;
    }

    public Long getAuditUserId() 
    {
        return auditUserId;
    }
    public void setAuditNickName(String auditNickName) 
    {
        this.auditNickName = auditNickName;
    }

    public String getAuditNickName() 
    {
        return auditNickName;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setAuditDeptId(Long auditDeptId) 
    {
        this.auditDeptId = auditDeptId;
    }

    public Long getAuditDeptId() 
    {
        return auditDeptId;
    }
    public void setAuditLevel(Integer auditLevel) 
    {
        this.auditLevel = auditLevel;
    }

    public Integer getAuditLevel() 
    {
        return auditLevel;
    }
    public void setExportStatus(Integer exportStatus) 
    {
        this.exportStatus = exportStatus;
    }

    public Integer getExportStatus() 
    {
        return exportStatus;
    }
    public void setExportTime(Date exportTime) 
    {
        this.exportTime = exportTime;
    }

    public Date getExportTime() 
    {
        return exportTime;
    }

    public List<TbRequestAudit> getTbRequestAuditList()
    {
        return tbRequestAuditList;
    }

    public void setTbRequestAuditList(List<TbRequestAudit> tbRequestAuditList)
    {
        this.tbRequestAuditList = tbRequestAuditList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataType", getDataType())
            .append("paramsStr", getParamsStr())
            .append("dataCount", getDataCount())
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("deptId", getDeptId())
            .append("deptLevel", getDeptLevel())
            .append("requestUnit", getRequestUnit())
            .append("status", getStatus())
            .append("auditResult", getAuditResult())
            .append("auditReason", getAuditReason())
            .append("auditUserId", getAuditUserId())
            .append("auditNickName", getAuditNickName())
            .append("auditTime", getAuditTime())
            .append("auditDeptId", getAuditDeptId())
            .append("auditLevel", getAuditLevel())
            .append("exportStatus", getExportStatus())
            .append("exportTime", getExportTime())
            .append("tbRequestAuditList", getTbRequestAuditList())
            .toString();
    }
}
