<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbPartyCenterMapper">

    <resultMap type="TbPartyCenter" id="TbPartyCenterResult">
            <result property="id"    column="id"    />
            <result property="startTime"    column="start_time"    />
            <result property="endTime"    column="end_time"    />
            <result property="reportPeoples"    column="report_peoples"    />
            <result property="commActCount"    column="comm_act_count"    />
            <result property="commParCount"    column="comm_par_count"    />
            <result property="commGoverCount"    column="comm_gover_count"    />
    </resultMap>

    <sql id="selectTbPartyCenterVo">
        select id, start_time, end_time, report_peoples, comm_act_count, comm_par_count, comm_gover_count from tb_party_center
    </sql>

    <select id="selectTbPartyCenterList" parameterType="TbPartyCenter" resultMap="TbPartyCenterResult">
        <include refid="selectTbPartyCenterVo"/>
        <where>
                        <if test="startTime != null "> and start_time = #{startTime}</if>
                        <if test="endTime != null "> and end_time = #{endTime}</if>
                        <if test="reportPeoples != null "> and report_peoples = #{reportPeoples}</if>
                        <if test="commActCount != null "> and comm_act_count = #{commActCount}</if>
                        <if test="commParCount != null "> and comm_par_count = #{commParCount}</if>
                        <if test="commGoverCount != null "> and comm_gover_count = #{commGoverCount}</if>
        </where>
    </select>

    <select id="selectTbPartyCenterById" parameterType="Long" resultMap="TbPartyCenterResult">
            <include refid="selectTbPartyCenterVo"/>
            where id = #{id}
    </select>

    <select id="checkTbPartyCenterUnique" parameterType="TbPartyCenter" resultMap="TbPartyCenterResult">
        select id from tb_party_center where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbPartyCenter" parameterType="TbPartyCenter" useGeneratedKeys="true" keyProperty="id">
        insert into tb_party_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="startTime != null">start_time,</if>
                    <if test="endTime != null">end_time,</if>
                    <if test="reportPeoples != null">report_peoples,</if>
                    <if test="commActCount != null">comm_act_count,</if>
                    <if test="commParCount != null">comm_par_count,</if>
                    <if test="commGoverCount != null">comm_gover_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="startTime != null">#{startTime},</if>
                    <if test="endTime != null">#{endTime},</if>
                    <if test="reportPeoples != null">#{reportPeoples},</if>
                    <if test="commActCount != null">#{commActCount},</if>
                    <if test="commParCount != null">#{commParCount},</if>
                    <if test="commGoverCount != null">#{commGoverCount},</if>
        </trim>
    </insert>

    <update id="updateTbPartyCenter" parameterType="TbPartyCenter">
        update tb_party_center
        <trim prefix="SET" suffixOverrides=",">
                    <if test="startTime != null">start_time = #{startTime},</if>
                    <if test="endTime != null">end_time = #{endTime},</if>
                    <if test="reportPeoples != null">report_peoples = #{reportPeoples},</if>
                    <if test="commActCount != null">comm_act_count = #{commActCount},</if>
                    <if test="commParCount != null">comm_par_count = #{commParCount},</if>
                    <if test="commGoverCount != null">comm_gover_count = #{commGoverCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbPartyCenterById" parameterType="Long">
        delete from tb_party_center where id = #{id}
    </delete>

    <delete id="deleteTbPartyCenterByIds" parameterType="String">
        delete from tb_party_center where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>