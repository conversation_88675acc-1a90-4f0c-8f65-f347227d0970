package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.HandleStepVO;
import com.ruoyi.microPlatform.domain.TbIssueInfo;
import com.ruoyi.microPlatform.domain.TransferIssueInfoVO;

/**
 * 矛盾纠纷Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface ITbIssueInfoService 
{
    /**
     * 查询矛盾纠纷
     * 
     * @param id 矛盾纠纷主键
     * @return 矛盾纠纷
     */
    public TbIssueInfo selectTbIssueInfoById(Long id);

    /**
     * 校验矛盾纠纷是否存在
     *
     * @param tbIssueInfo 矛盾纠纷
     * @return 矛盾纠纷
     */
    public boolean checkTbIssueInfo(TbIssueInfo tbIssueInfo);

    /**
     * 查询矛盾纠纷列表
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 矛盾纠纷集合
     */
    public List<TbIssueInfo> selectTbIssueInfoList(TbIssueInfo tbIssueInfo);

    /**
     * 导入矛盾纠纷
     *
     * @param infos       矛盾纠纷列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbIssueInfo(List<TbIssueInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增矛盾纠纷
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    public int insertTbIssueInfo(TbIssueInfo tbIssueInfo);

    /**
     * 修改矛盾纠纷
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    public int updateTbIssueInfo(TbIssueInfo tbIssueInfo);

    /**
     * 批量删除矛盾纠纷
     * 
     * @param ids 需要删除的矛盾纠纷主键集合
     * @return 结果
     */
    public int deleteTbIssueInfoByIds(Long[] ids);

    /**
     * 删除矛盾纠纷信息
     * 
     * @param id 矛盾纠纷主键
     * @return 结果
     */
    public int deleteTbIssueInfoById(Long id);

    /**
     *获得调处过程信息
     * @param formId
     * @return
     */
    List<HandleStepVO> getHandleStepInfo(Long formId);

    /**
     *获得移交综治数据
     * @param issueId
     * @return
     */
    TransferIssueInfoVO getTransferInfo(Long issueId);


    List<CommonBaseCount> statusCount();

}
