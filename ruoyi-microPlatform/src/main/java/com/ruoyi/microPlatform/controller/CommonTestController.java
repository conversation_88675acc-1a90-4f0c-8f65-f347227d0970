package com.ruoyi.microPlatform.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.microPlatform.domain.TbMsgTemplate;
import com.ruoyi.microPlatform.mapper.TbMsgTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 公共方法类
// */
@RestController
@RequestMapping("/api")
public class CommonTestController extends BaseController {

    @Autowired
    private TbMsgTemplateMapper templateMapper;

    @GetMapping("/gaode")
    public AjaxResult gaode(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String key = request.getParameter("key");
        String extensions = request.getParameter("extensions");
        String location = request.getParameter("location");

        String url = "https://restapi.amap.com/v3/geocode/regeo?key=" + key + "&location=" + location;
        String body = HttpUtil.createGet(url).execute().body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        return success(jsonObject);
    }


    // 发送订阅消息
    @GetMapping("/bindMessage")
    public AjaxResult sendSubscribeMessage(String templateCode) {
        Long userId = getUserId();
        TbMsgTemplate tbMsgTemplate = new TbMsgTemplate() {{
            setTemplateCode(templateCode);
            setUserId(userId);
        }};
        TbMsgTemplate old = templateMapper.checkTbMsgTemplateUnique(tbMsgTemplate);
        if (ObjectUtil.isEmpty(old)){
            templateMapper.insertTbMsgTemplate(tbMsgTemplate);
        }
        return AjaxResult.success();
    }
}
