package com.ruoyi.dataview.mapper;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbPetitionInfo;
import com.ruoyi.dataview.domain.vo.PetitionInfoStatisticsVO;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 市信访局-上访信息数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@MapperScan("slave")
public interface DbPetitionInfoMapper {
    /**
     * 查询市信访局-上访信息数据
     *
     * @param id 市信访局-上访信息数据主键
     * @return 市信访局-上访信息数据
     */
    DbPetitionInfo selectDbPetitionInfoById(Long id);

    /**
     * 校验市信访局-上访信息数据是否唯一
     *
     * @param dbPetitionInfo
     * @return 市信访局-上访信息数据
     */
    DbPetitionInfo checkDbPetitionInfoUnique(DbPetitionInfo dbPetitionInfo);
    
    /**
     * 获取信访总数
     * @return 信访总数
     */
    Long selectTotalCount();
    
    /**
     * 获取本月信访数
     * @return 本月信访数
     */
    Long selectCurrentMonthCount();
    
    /**
     * 按内容分类分组统计信访信息
     * @return 分类统计结果
     */
    List<CommonBaseCount> selectCategoryStatistics();

    /**
     * 查询市信访局-上访信息数据列表
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 市信访局-上访信息数据集合
     */
    List<DbPetitionInfo> selectDbPetitionInfoList(DbPetitionInfo dbPetitionInfo);

    /**
     * 新增市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    int insertDbPetitionInfo(DbPetitionInfo dbPetitionInfo);

    /**
     * 修改市信访局-上访信息数据
     *
     * @param dbPetitionInfo 市信访局-上访信息数据
     * @return 结果
     */
    int updateDbPetitionInfo(DbPetitionInfo dbPetitionInfo);

    /**
     * 删除市信访局-上访信息数据
     *
     * @param id 市信访局-上访信息数据主键
     * @return 结果
     */
    int deleteDbPetitionInfoById(Long id);

    /**
     * 批量删除市信访局-上访信息数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDbPetitionInfoByIds(Long[] ids);
}
