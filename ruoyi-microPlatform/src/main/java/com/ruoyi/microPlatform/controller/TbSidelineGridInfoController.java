package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;
import com.ruoyi.microPlatform.service.ITbSidelineGridInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 兼职网格员所属网格信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@RestController
@RequestMapping("/microPlatform/sidelineGridInfo")
public class TbSidelineGridInfoController extends BaseController
{
    @Autowired
    private ITbSidelineGridInfoService tbSidelineGridInfoService;

    /**
     * 查询兼职网格员所属网格信息列表
     */
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbSidelineGridInfo tbSidelineGridInfo)
    {
        startPage();
        List<TbSidelineGridInfo> list = tbSidelineGridInfoService.selectTbSidelineGridInfoList(tbSidelineGridInfo);
        return getDataTable(list);
    }

    /**
     * 导出兼职网格员所属网格信息列表
     */
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:export')")
    @Log(title = "兼职网格员所属网格信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbSidelineGridInfo tbSidelineGridInfo)
    {
        List<TbSidelineGridInfo> list = tbSidelineGridInfoService.selectTbSidelineGridInfoList(tbSidelineGridInfo);
        ExcelUtil<TbSidelineGridInfo> util = new ExcelUtil<TbSidelineGridInfo>(TbSidelineGridInfo.class);
        util.exportExcel(response, list, "兼职网格员所属网格信息数据");
    }

    @Log(title = "兼职网格员所属网格信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbSidelineGridInfo> util = new ExcelUtil<TbSidelineGridInfo>(TbSidelineGridInfo.class);
        List<TbSidelineGridInfo> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbSidelineGridInfoService.importTbSidelineGridInfo(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbSidelineGridInfo> util = new ExcelUtil<TbSidelineGridInfo>(TbSidelineGridInfo.class);
        util.exportTemplateExcel(response, "兼职网格员所属网格信息数据");
    }

    /**
     * 获取兼职网格员所属网格信息详细信息
     */
   // @PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbSidelineGridInfoService.selectTbSidelineGridInfoById(id));
    }

    /**
     * 新增兼职网格员所属网格信息
     */
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:add')")
    @Log(title = "兼职网格员所属网格信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbSidelineGridInfo tbSidelineGridInfo)
    {
        tbSidelineGridInfo.setCreateBy(getUsername());
        if (tbSidelineGridInfoService.checkTbSidelineGridInfo(tbSidelineGridInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbSidelineGridInfoService.insertTbSidelineGridInfo(tbSidelineGridInfo));
    }

    /**
     * 修改兼职网格员所属网格信息
     */
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:edit')")
    @Log(title = "兼职网格员所属网格信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbSidelineGridInfo tbSidelineGridInfo)
    {
        tbSidelineGridInfo.setUpdateBy(getUsername());
        if (tbSidelineGridInfoService.checkTbSidelineGridInfo(tbSidelineGridInfo)){
            return error("数据已存在！");
        }
        return toAjax(tbSidelineGridInfoService.updateTbSidelineGridInfo(tbSidelineGridInfo));
    }

    /**
     * 删除兼职网格员所属网格信息
     */
    //@PreAuthorize("@ss.hasPermi('microPlatform:sidelineGridInfo:remove')")
    @Log(title = "兼职网格员所属网格信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbSidelineGridInfoService.deleteTbSidelineGridInfoByIds(ids));
    }
}
