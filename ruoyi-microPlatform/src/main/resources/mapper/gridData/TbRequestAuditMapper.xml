<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbRequestAuditMapper">

    <resultMap type="TbRequestAudit" id="TbRequestAuditResult">
        <result property="id" column="id"/>
        <result property="requestId" column="request_id"/>
        <result property="auditUserId" column="audit_user_id"/>
        <result property="auditNickName" column="audit_nick_name"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditDeptName" column="audit_dept_name"/>
        <result property="auditResult" column="audit_result"/>
        <result property="auditReason" column="audit_reason"/>
    </resultMap>

    <sql id="selectTbRequestAuditVo">
        select id, request_id, audit_user_id, audit_nick_name, audit_time, audit_dept_name, audit_result, audit_reason from tb_request_audit
    </sql>

    <select id="selectTbRequestAuditList" parameterType="TbRequestAudit" resultMap="TbRequestAuditResult">
        <include refid="selectTbRequestAuditVo"/>
        <where>
            <if test="requestId != null ">and request_id = #{requestId}</if>
            <if test="auditUserId != null ">and audit_user_id = #{auditUserId}</if>
            <if test="auditNickName != null  and auditNickName != ''">and audit_nick_name like concat('%',
                #{auditNickName}, '%')
            </if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="auditDeptName != null  and auditDeptName != ''">and audit_dept_name like concat('%',
                #{auditDeptName}, '%')
            </if>
            <if test="auditResult != null ">and audit_result = #{auditResult}</if>
            <if test="auditReason != null  and auditReason != ''">and audit_reason = #{auditReason}</if>
        </where>
    </select>

    <select id="selectTbRequestAuditById" parameterType="Long" resultMap="TbRequestAuditResult">
        <include refid="selectTbRequestAuditVo"/>
        where id = #{id}
    </select>

    <select id="checkTbRequestAuditUnique" parameterType="TbRequestAudit" resultMap="TbRequestAuditResult">
        select id from tb_request_audit where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbRequestAudit" parameterType="TbRequestAudit">
        insert into tb_request_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="requestId != null">request_id,</if>
            <if test="auditUserId != null">audit_user_id,</if>
            <if test="auditNickName != null">audit_nick_name,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditDeptName != null">audit_dept_name,</if>
            <if test="auditResult != null">audit_result,</if>
            <if test="auditReason != null">audit_reason,</if>
            <if test="auditDeptId != null">audit_dept_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="requestId != null">#{requestId},</if>
            <if test="auditUserId != null">#{auditUserId},</if>
            <if test="auditNickName != null">#{auditNickName},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditDeptName != null">#{auditDeptName},</if>
            <if test="auditResult != null">#{auditResult},</if>
            <if test="auditReason != null">#{auditReason},</if>
            <if test="auditDeptId != null">#{auditDeptId},</if>
        </trim>
    </insert>

    <update id="updateTbRequestAudit" parameterType="TbRequestAudit">
        update tb_request_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="requestId != null">request_id = #{requestId},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditNickName != null">audit_nick_name = #{auditNickName},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditDeptName != null">audit_dept_name = #{auditDeptName},</if>
            <if test="auditResult != null">audit_result = #{auditResult},</if>
            <if test="auditReason != null">audit_reason = #{auditReason},</if>
            <if test="auditDeptId != null">audit_dept_id = #{auditDeptId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbRequestAuditById" parameterType="Long">
        delete from tb_request_audit where id = #{id}
    </delete>

    <delete id="deleteTbRequestAuditByIds" parameterType="String">
        delete from tb_request_audit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>