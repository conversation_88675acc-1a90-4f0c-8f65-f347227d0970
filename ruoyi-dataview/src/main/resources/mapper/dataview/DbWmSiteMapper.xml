<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.dataview.mapper.DbWmSiteMapper">

    <resultMap type="DbWmSite" id="DbWmSiteResult">
            <result property="id"    column="id"    />
            <result property="stCode"    column="st_code"    />
            <result property="stName"    column="st_name"    />
            <result property="status"    column="status"    />
            <result property="lastObsTime"    column="last_obs_time"    />
            <result property="lastStoreTime"    column="last_store_time"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDbWmSiteVo">
        select id, st_code, st_name, status, last_obs_time, last_store_time, create_time, update_time from db_wm_site
    </sql>

    <select id="selectDbWmSiteList" parameterType="DbWmSite" resultMap="DbWmSiteResult">
        <include refid="selectDbWmSiteVo"/>
        <where>
                        <if test="stCode != null  and stCode != ''"> and st_code = #{stCode}</if>
                        <if test="stName != null  and stName != ''"> and st_name like concat('%', #{stName}, '%')</if>
                        <if test="status != null "> and status = #{status}</if>
                        <if test="lastObsTime != null "> and last_obs_time = #{lastObsTime}</if>
                        <if test="lastStoreTime != null "> and last_store_time = #{lastStoreTime}</if>
        </where>
    </select>

    <select id="selectDbWmSiteById" parameterType="Long" resultMap="DbWmSiteResult">
            <include refid="selectDbWmSiteVo"/>
            where id = #{id}
    </select>

    <select id="checkDbWmSiteUnique" parameterType="DbWmSite" resultMap="DbWmSiteResult">
        select id from db_wm_site where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertDbWmSite" parameterType="DbWmSite" useGeneratedKeys="true" keyProperty="id">
        insert into db_wm_site
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="stCode != null and stCode != ''">st_code,</if>
                    <if test="stName != null and stName != ''">st_name,</if>
                    <if test="status != null">status,</if>
                    <if test="lastObsTime != null">last_obs_time,</if>
                    <if test="lastStoreTime != null">last_store_time,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="stCode != null and stCode != ''">#{stCode},</if>
                    <if test="stName != null and stName != ''">#{stName},</if>
                    <if test="status != null">#{status},</if>
                    <if test="lastObsTime != null">#{lastObsTime},</if>
                    <if test="lastStoreTime != null">#{lastStoreTime},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateDbWmSite" parameterType="DbWmSite">
        update db_wm_site
        <trim prefix="SET" suffixOverrides=",">
                    <if test="stCode != null and stCode != ''">st_code = #{stCode},</if>
                    <if test="stName != null and stName != ''">st_name = #{stName},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="lastObsTime != null">last_obs_time = #{lastObsTime},</if>
                    <if test="lastStoreTime != null">last_store_time = #{lastStoreTime},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDbWmSiteById" parameterType="Long">
        delete from db_wm_site where id = #{id}
    </delete>

    <delete id="deleteDbWmSiteByIds" parameterType="String">
        delete from db_wm_site where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="wdDataStatusCount" resultType="com.ruoyi.dataview.domain.bigData.res.CommonBaseCount">
        select status ,count(*) count from db_wm_site
        group by status
    </select>
</mapper>