package com.ruoyi.microPlatform.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import com.ruoyi.microPlatform.domain.TbResidentContact;
import com.ruoyi.microPlatform.mapper.TbCommunityResidentMapper;
import com.ruoyi.microPlatform.mapper.TbResidentContactMapper;
import com.ruoyi.microPlatform.service.ITbResidentContactService;
import com.ruoyi.microPlatform.service.WeChatService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;

/**
 * 居民住户入户走访信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbResidentContactServiceImpl implements ITbResidentContactService 
{
    @Autowired
    private TbResidentContactMapper tbResidentContactMapper;
    @Autowired
    private TbCommunityResidentMapper residentMapper;
    @Autowired
    private WeChatService weChatService;
    @Autowired
    private SysDeptMapper deptMapper;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询居民住户入户走访信息
     * 
     * @param id 居民住户入户走访信息主键
     * @return 居民住户入户走访信息
     */
    @Override
    public TbResidentContact selectTbResidentContactById(Long id)
    {
        return tbResidentContactMapper.selectTbResidentContactById(id);
    }

    /**
     * 校验居民住户入户走访信息是否存在
     *
     * @param tbResidentContact
     * @return boolean
     */
    @Override
    public boolean checkTbResidentContact(TbResidentContact tbResidentContact){
        TbResidentContact old = tbResidentContactMapper.checkTbResidentContactUnique(tbResidentContact);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询居民住户入户走访信息列表
     * 
     * @param tbResidentContact 居民住户入户走访信息
     * @return 居民住户入户走访信息
     */
    @Override
    public List<TbResidentContact> selectTbResidentContactList(TbResidentContact tbResidentContact)
    {

        return tbResidentContactMapper.selectTbResidentContactList(tbResidentContact);
    }

    /**
     * 导入居民住户入户走访信息
     *
     * @param infos       居民住户入户走访信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbResidentContact(List<TbResidentContact> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入居民住户入户走访信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (TbResidentContact info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                TbResidentContact old = tbResidentContactMapper.checkTbResidentContactUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    tbResidentContactMapper.insertTbResidentContact(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    tbResidentContactMapper.updateTbResidentContact(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增居民住户入户走访信息
     * 
     * @param tbResidentContact 居民住户入户走访信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertTbResidentContact(TbResidentContact tbResidentContact)
    {
        tbResidentContact.setCreateTime(DateUtils.getNowDate());
        int i = tbResidentContactMapper.insertTbResidentContact(tbResidentContact);
        if (i > 0){
            //  事务提交后再能继续执行异步
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronization() {
                            @Override
                            public void afterCommit() {
                                // 事务提交后执行逻辑
                                updateMember(tbResidentContact, SecurityUtils.getLoginUser());
                            }
                            // 其他方法无需覆盖，默认已实现为空
                        }
                );
            }
        }
        return tbResidentContact.getId();

    }

    @Async("asyncExecutor")
    public void updateMember(TbResidentContact tbResidentContact, LoginUser loginUse){
        TbResidentContact contact = new TbResidentContact();
        contact.setId(tbResidentContact.getId());
        TbCommunityResident resident = residentMapper.selectTbCommunityResidentById2(tbResidentContact.getResidentId());
        if (ObjectUtil.isNotEmpty(resident)){
            contact.setGridId(resident.getGridId());
            contact.setGridName(resident.getGridName());
            contact.setDeptId(resident.getDeptId());
            contact.setCountry(resident.getCountry());
            contact.setCounty(resident.getCounty());
            contact.setTown(resident.getTown());

            tbResidentContactMapper.updateTbResidentContact(contact);
            residentMapper.updateTbCommunityResident(new TbCommunityResident(){{
                setId(tbResidentContact.getResidentId());
                setStatus(tbResidentContact.getContractSituation());
                setResidentialSituation(tbResidentContact.getResidentialSituation());
            }});

            contact.setContractSituation(tbResidentContact.getContractSituation());
            handleSend(contact,loginUse);
        }
    }
    @Async("threadPoolTaskExecutor")
    public void handleSend(TbResidentContact tbResidentContact, LoginUser loginUser) {
        // 新增
        Long deptId = tbResidentContact.getDeptId();
        //TODO: 发送消息通知
        weChatService.insertMsg(deptId, "入户走访",
                "D_Ypxxv-GFYgeUOFsT7NC2x1QFZ7cid8X027ynchELs", "/subpackage1/house/store/newRecord/detail?ids=" + tbResidentContact.getId(),
                tbResidentContact.getTown(), "提交了入户走访，状态为" + (tbResidentContact.getContractSituation()));
    }


    /**
     * 修改居民住户入户走访信息
     * 
     * @param tbResidentContact 居民住户入户走访信息
     * @return 结果
     */
    @Override
    public int updateTbResidentContact(TbResidentContact tbResidentContact)
    {
        tbResidentContact.setUpdateTime(DateUtils.getNowDate());
        return tbResidentContactMapper.updateTbResidentContact(tbResidentContact);
    }

    /**
     * 批量删除居民住户入户走访信息
     * 
     * @param ids 需要删除的居民住户入户走访信息主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentContactByIds(Long[] ids)
    {
        return tbResidentContactMapper.deleteTbResidentContactByIds(ids);
    }

    /**
     * 删除居民住户入户走访信息信息
     * 
     * @param id 居民住户入户走访信息主键
     * @return 结果
     */
    @Override
    public int deleteTbResidentContactById(Long id)
    {
        return tbResidentContactMapper.deleteTbResidentContactById(id);
    }

    /**
     * 查询居民住户入户走访信息 根据resident_id 查询总数据量 和本月总数据量
     */
    @Override
    public Map<String, Integer> selectTbResidentContactListCount(TbResidentContact tbResidentContact) {
        Map<String, Integer> map = new HashMap<>();
        //查询当前 resident_id 下的所有数据count
        int count = tbResidentContactMapper.selectTbResidentContactCountById(tbResidentContact);
        map.put("巡查累计次数",count);
        //查询本月 create_time  和 resident_id下的所有数据
        int countTime = tbResidentContactMapper.selectTbResidentContactCountByIdTime(tbResidentContact);
        map.put("巡查本月次数",countTime);

        return map;
    }

    /**
     * 查询居民住户入户走访信息 根据创建者id 查询提交的总次数  和 最新提交的数据
     */
    @Override
    public Map<String, Object> selectSubmitCount(TbResidentContact tbResidentContact) {
        return tbResidentContactMapper.selectSubmitCount(tbResidentContact);
    }

    @Override
    public List<Map<String, Object>> residentContactGroupMonth(LargeScreenInfo largeScreenInfo) {

        List<Map<String, Object>> gridData = tbResidentContactMapper.selectMonthlyStats(largeScreenInfo);//网格巡查

        // 结果用 LinkedHashMap 保持有序
        Map<String, Integer> monthCountMap = new HashMap<>();
        for (Map<String, Object> row : gridData) {
            monthCountMap.put((String) row.get("month"), ((Number) row.get("total")).intValue());
        }
        List<Map<String, Object>> finalResult = new ArrayList<>();

        // 当前月
        DateTime end = DateUtil.endOfMonth(new Date());
        // 往前推 11 个月
        DateTime start = DateUtil.beginOfMonth(DateUtil.offsetMonth(end, -11));
        // 按月遍历
        DateUtil.rangeToList(start, end, DateField.MONTH).forEach(date -> {
            String monthKey = DateUtil.format(date, "yyyyMM");
            Map<String, Object> item = new HashMap<>();
            item.put("month", monthKey);
            item.put("total", monthCountMap.getOrDefault(monthKey, 0));
            finalResult.add(item);
        });
        return finalResult;
    }

    @Override
    public int residentContactCount(LargeScreenInfo largeScreenInfo) {
        return tbResidentContactMapper.selectCount(largeScreenInfo);
    }
}
