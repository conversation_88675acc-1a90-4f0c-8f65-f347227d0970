package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbAssociateInfo;

/**
 * 行业协会商会信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ITbAssociateInfoService 
{
    /**
     * 查询行业协会商会信息
     * 
     * @param id 行业协会商会信息主键
     * @return 行业协会商会信息
     */
    public TbAssociateInfo selectTbAssociateInfoById(Long id);

    /**
     * 校验行业协会商会信息是否存在
     *
     * @param tbAssociateInfo 行业协会商会信息
     * @return 行业协会商会信息
     */
    public boolean checkTbAssociateInfo(TbAssociateInfo tbAssociateInfo);

    /**
     * 查询行业协会商会信息列表
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 行业协会商会信息集合
     */
    public List<TbAssociateInfo> selectTbAssociateInfoList(TbAssociateInfo tbAssociateInfo);

    /**
     * 导入行业协会商会信息
     *
     * @param infos       行业协会商会信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbAssociateInfo(List<TbAssociateInfo> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    public int insertTbAssociateInfo(TbAssociateInfo tbAssociateInfo);

    /**
     * 修改行业协会商会信息
     * 
     * @param tbAssociateInfo 行业协会商会信息
     * @return 结果
     */
    public int updateTbAssociateInfo(TbAssociateInfo tbAssociateInfo);

    /**
     * 批量删除行业协会商会信息
     * 
     * @param ids 需要删除的行业协会商会信息主键集合
     * @return 结果
     */
    public int deleteTbAssociateInfoByIds(Long[] ids);

    /**
     * 删除行业协会商会信息信息
     * 
     * @param id 行业协会商会信息主键
     * @return 结果
     */
    public int deleteTbAssociateInfoById(Long id);
}
