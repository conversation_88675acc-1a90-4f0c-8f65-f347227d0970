package com.ruoyi.microPlatform.service.impl;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.*;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.mapper.TbGridInfoMapper;
import com.ruoyi.microPlatform.mapper.TbSidelineGridInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.microPlatform.domain.TbSidelineGridInfo;
import com.ruoyi.microPlatform.mapper.TbSidelineGridUserMapper;
import com.ruoyi.microPlatform.domain.TbSidelineGridUser;
import com.ruoyi.microPlatform.service.ITbSidelineGridUserService;

/**
 * 兼职网格员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-08
 */
@Service
public class TbSidelineGridUserServiceImpl implements ITbSidelineGridUserService 
{
    @Autowired
    private TbSidelineGridUserMapper tbSidelineGridUserMapper;


    @Autowired
    private TbSidelineGridInfoMapper tbSidelineGridInfoMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TbGridInfoMapper gridInfoMapper;

    /**
     * 查询兼职网格员信息
     * 
     * @param id 兼职网格员信息主键
     * @return 兼职网格员信息
     */
    @Override
    public TbSidelineGridUser selectTbSidelineGridUserById(Long id)
    {
        return tbSidelineGridUserMapper.selectTbSidelineGridUserById(id);
    }

    /**
     * 校验兼职网格员信息是否存在
     *
     * @param tbSidelineGridUser
     * @return boolean
     */
    @Override
    public boolean checkTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser){
        TbSidelineGridUser old = tbSidelineGridUserMapper.checkTbSidelineGridUserUnique(tbSidelineGridUser);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询兼职网格员信息列表
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 兼职网格员信息
     */
    @Override
    public List<TbSidelineGridUser> selectTbSidelineGridUserList(TbSidelineGridUser tbSidelineGridUser)
    {
        return tbSidelineGridUserMapper.selectTbSidelineGridUserList(tbSidelineGridUser);
    }

    @Override
    public Integer selectTbSidelineGridUserCount(TbSidelineGridUser tbSidelineGridUser) {
        List<Long> longs = tbSidelineGridUserMapper.selectTbSidelineGridUserCount(tbSidelineGridUser);
        return longs.size();
    }

    /**
     * 导入兼职网格员信息
     *
     * @param infos       兼职网格员信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbSidelineGridUser(List<TbSidelineGridUser> infos, Boolean isUpdateSupport, String operName, SysDept userDept){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入兼职网格员信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;



         // 手机号：1开头，第二位为3-9，总共11位
        // 座机号格式：支持 (区号)号码、区号-号码、纯号码
        Pattern phonePattern = Pattern.compile(
                "(?<!\\d)" +                   // 避免前面有数字干扰
                        "(" +
                        "1[3-9]\\d{9}" +           // 手机号码
                        "|" +
                        "(0\\d{2,3}[-\\s]?)?\\d{7,8}" + // 座机号码（带/不带区号）
                        ")" +
                        "(?!\\d)"                      // 避免尾部数字干扰
        );
        for (TbSidelineGridUser info : infos)
        {
            updateNum++;
            info.setStatus(0);
            try
            {
                String[] split = userDept.getAncestorsName().split(",");
                if (StringUtils.isBlank(info.getCounty())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属县（市、区） 不可为空"+ "。数据条目：" + updateNum);
                } else {
                    if (StringUtils.isBlank(info.getCountry())) {
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属街道/乡镇 不可为空"+ "。数据条目：" + updateNum);
                    } else {
                        if (StringUtils.isBlank(info.getTown())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + " 所属村/社区 不可为空"+ "。数据条目：" + updateNum);
                        } else {
                                if (StringUtils.isBlank(info.getGridName())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getName() + "所属网格 不可为空"+ "。数据条目：" + updateNum);
                                } else {
                                    boolean flag = true;
                                    String msg = "";
                                    if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getTbSidelineGridInfo().getCounty() + "的" + info.getName() + "网格员数据"+ "。数据条目：" + updateNum);
                                    }

                                    if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getTbSidelineGridInfo().getCounty() + info.getTbSidelineGridInfo().getCountry() + "的" + info.getName() + "网格员数据"+ "。数据条目：" + updateNum);
                                    }

                                    if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                        flag = false;
                                        msg = ("<br/>" + failureNum + "、您无权导入" + info.getTbSidelineGridInfo().getCounty() + info.getTbSidelineGridInfo().getCountry() + info.getTbSidelineGridInfo().getTown() + "的" + info.getName() + "网格员数据"+ "。数据条目：" + updateNum);
                                    }

                                    if (!flag) {
                                        failureNum++;
                                        failureMsg.append(msg);
                                    } else {
                                        SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                        if (ObjectUtil.isEmpty(dept)) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格员数据所属部门未找到！"+ "数据条目：" + updateNum);
                                        } else {
                                            info.setDeptId(dept.getDeptId());
                                            TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                                setDeptId(dept.getDeptId());
                                                setGridName(info.getGridName());
                                            }});
                                            if (ObjectUtil.isEmpty(gridInfo)) {
                                                failureNum++;
                                                failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 网格员数据所属网格未找到！"+ "数据条目：" + updateNum);
                                            } else {
                                                info.setGridId(gridInfo.getId());
                                                //进行数据新增


                                                //先获取兼职兼职网格员的类型字典
                                                List<SysDictData> typeDicts = DictUtils.getDictCache("grid_user_type");

                                                for (SysDictData typeDict : typeDicts) {
                                                    String dictValue = typeDict.getDictValue();
                                                    Class<? extends TbSidelineGridUser> clazz = info.getClass();
                                                    for (Field field : clazz.getDeclaredFields()) {
                                                        Excel annotation = field.getAnnotation(Excel.class);
                                                        if (annotation != null && annotation.name().contains(dictValue)) {
                                                            field.setAccessible(true); // 允许访问私有字段
                                                            String gasName = (String) field.get(info);
                                                            if (gasName == null){
                                                                failureNum++;
                                                                failureMsg.append("<br/>" +"第"+updateNum+"条数据的"+dictValue+"类型下的数据格式错误。"+ "数据条目：" + updateNum);
                                                                continue;
                                                            }
                                                            List<Map<String, String>> pipelineGasNames = extractNameAndPhone(gasName,phonePattern);
                                                            for (Map<String, String> pipelineGasName : pipelineGasNames) {
                                                                String name = pipelineGasName.get("name");
                                                                String phone = pipelineGasName.get("phone");
                                                                // 验证是否存在这个数据
                                                                TbSidelineGridUser old = tbSidelineGridUserMapper.checkTbSidelineGridUserUnique(new TbSidelineGridUser(){{
                                                                    setName(name);
                                                                    setPhone(phone);
                                                                    setType(typeDict.getDictValue());
                                                                }});
                                                                if (ObjectUtil.isEmpty(old)) {
                                                                    //新增setInfo兼职网格员信息
                                                                    setInfo(info, operName, name, phone, typeDict.getDictValue());
                                                                    successMsg.append("<br/>" + successNum + "、数据 " +dictValue+ info.getName() + " 导入成功"+ "数据条目：" + updateNum);
                                                                    //新增兼职网格员关联网格信息
                                                                    insertSideGridInfo(info);
                                                                    successMsg.append("<br/>" + successNum + "、数据 " +dictValue+ info.getName() +"关联 网格编码"+info.getGridName()+ " 导入成功"+ "数据条目：" + updateNum);
                                                                    successNum++;
                                                                }else if (isUpdateSupport){
                                                                    //修改兼职网格员信息
                                                                    updateInfo(operName, info, name, phone, old);
                                                                    successMsg.append("<br/>" + successNum + "、数据 "+dictValue+ info.getName() + " 更新成功"+ "数据条目：" + updateNum);
                                                                    //先判断是否存在当前网格编号信息
                                                                    TbSidelineGridInfo oldSidelineGridInfo = tbSidelineGridInfoMapper.checkTbSidelineGridInfoUnique(new TbSidelineGridInfo() {{
                                                                        setGridName(info.getGridName());
                                                                        setSideUserId(info.getId());
                                                                    }});
                                                                    if (ObjectUtil.isEmpty(oldSidelineGridInfo)){
                                                                        //新增兼职网格员信息
                                                                        insertSideGridInfo(info);
                                                                        successMsg.append("<br/>" + successNum + "、数据 "+dictValue + info.getName() +"关联 网格编码"+info.getGridName()+ " 导入成功"+ "数据条目：" + updateNum);
                                                                    }else {
                                                                        //更新兼职网格员关联网格信息
                                                                        updateSideGridInfo(info,oldSidelineGridInfo);
                                                                        successMsg.append("<br/>" + successNum + "、数据 "+dictValue + info.getName() +"关联 网格编码"+info.getGridName()+ " 更新成功"+ "数据条目：" + updateNum);
                                                                    }
                                                                    successNum++;
                                                                }else {
                                                                    //修改兼职网格员信息
                                                                    updateInfo(operName, info, name, phone, old);
                                                                    //先判断是否存在当前网格编号信息
                                                                    TbSidelineGridInfo tbSidelineGridInfo = tbSidelineGridInfoMapper.checkTbSidelineGridInfoUnique(new TbSidelineGridInfo() {{
                                                                        setGridName(info.getGridName());
                                                                        setSideUserId(info.getId());
                                                                    }});
                                                                    if (ObjectUtil.isEmpty(tbSidelineGridInfo)){
                                                                        //新增
                                                                        insertSideGridInfo(info);
                                                                        successMsg.append("<br/>" + successNum + "、数据 "+dictValue + info.getName() +"关联 网格编码"+info.getGridName()+ " 导入成功"+ "数据条目：" + updateNum);
                                                                        successNum++;
                                                                    }else {
                                                                        failureNum++;
                                                                        failureMsg.append("<br/>" +"第"+updateNum+"条数据的"+info.getName()+"关联的" +" 网格编码"+info.getGridName()+ " 已存在"+ "数据条目：" + updateNum);
                                                                    }
                                                                }

                                                            }
                                                            break;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                            }

                        }
                    }
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    private void updateSideGridInfo(TbSidelineGridUser info, TbSidelineGridInfo oldSidelineGridInfo) {
        tbSidelineGridInfoMapper.updateTbSidelineGridInfo(new TbSidelineGridInfo(){{
            setId(oldSidelineGridInfo.getId());
            setSideUserId(info.getId());
            setDeptId(info.getDeptId());
            setCounty(info.getCounty());
            setCountry(info.getCountry());
            setTown(info.getTown());
            setGridName(info.getGridName());
            setGridId(info.getGridId());
            setGridRange(info.getGridRange());
        }});
    }

    private void updateInfo(String operName, TbSidelineGridUser info, String name, String phone, TbSidelineGridUser old) {
        info.setUpdateBy(operName);
        info.setUpdateTime(new Date());
        info.setId(old.getId());
        info.setUserId(old.getUserId());
        info.setName(name);
        info.setPhone(phone);
        this.updateTbSidelineGridUser(new TbSidelineGridUser(){{
            setId(old.getId());
            setName(name);
            setPhone(phone);
        }});
    }

    private void insertSideGridInfo(TbSidelineGridUser info) {
        tbSidelineGridInfoMapper.insertTbSidelineGridInfo(new TbSidelineGridInfo(){{
            setSideUserId(info.getId());
            setDeptId(info.getDeptId());
            setCounty(info.getCounty());
            setCountry(info.getCountry());
            setTown(info.getTown());
            setGridName(info.getGridName());
            setGridId(info.getGridId());
            setGridRange(info.getGridRange());
        }});
    }

    private void setInfo(TbSidelineGridUser info, String operName, String name, String phone, String type) {
        info.setCreateTime(new Date());
        info.setCreateBy(operName);
        info.setName(name);
        info.setPhone(phone);
        info.setType(type);
        this.insertTbSidelineGridUser(info);
    }


    /**
     * 截取字符串，或者兼职网格员姓名和电话
     *
     * @param branchName
     * @param phonePattern
     * @return
     */
    private  List<Map<String, String>> extractNameAndPhone(String branchName, Pattern phonePattern) {
        List<Map<String, String>> result = new ArrayList<>();
        // 优化后的电话号码正则：严格匹配11位且第二位为3-9
        Matcher matcher = phonePattern.matcher(branchName);
        int lastEnd = 0;

        while (matcher.find()) {
            String phone = matcher.group();
            int start = matcher.start();

            // 从上次结束到当前手机号起始位置之间的是"姓名"
            String namePart = branchName.substring(lastEnd, start);
            String name = processName(namePart);

            // 添加结果
            addEntry(result, name, phone);
            lastEnd = matcher.end();
        }

        return result;

    }
    private void addEntry(List<Map<String, String>> result, String name, String phone) {
        Map<String, String> entry = new HashMap<>();
        entry.put("name", name);
        entry.put("phone", phone);
        result.add(entry);
    }
    private String processName(String raw) {
        if (raw == null) return "";


        return raw
                .replaceAll("（[^）]*）", "")               // 清除中文括号内容
                .replaceAll("\\([^)]*\\)", "")             // 清除英文括号内容
                .replaceAll("^[、,，\\s\\t\\n\\r\\f]+", "") // ✅ 去掉前缀无关字符
                .replaceAll("[：:，、\\s\\t\\n\\r\\f]+$", "") // 去掉结尾干扰字符
                .trim();
    }


    /**
     * 新增兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser) {
        tbSidelineGridUser.setCreateTime(DateUtils.getNowDate());
        int rows = tbSidelineGridUserMapper.insertTbSidelineGridUser(tbSidelineGridUser);
        //insertTbSidelineGridInfo(tbSidelineGridUser);
        return rows;
    }

    /**
     * 修改兼职网格员信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTbSidelineGridUser(TbSidelineGridUser tbSidelineGridUser)
    {
        tbSidelineGridUser.setUpdateTime(DateUtils.getNowDate());
        //tbSidelineGridUserMapper.deleteTbSidelineGridInfoBySideUserId(tbSidelineGridUser.getId());
        //        //insertTbSidelineGridInfo(tbSidelineGridUser);
        return tbSidelineGridUserMapper.updateTbSidelineGridUser(tbSidelineGridUser);
    }

    /**
     * 批量删除兼职网格员信息
     * 
     * @param ids 需要删除的兼职网格员信息主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbSidelineGridUserByIds(Long[] ids)
    {
        tbSidelineGridUserMapper.deleteTbSidelineGridInfoBySideUserIds(ids);
        return tbSidelineGridUserMapper.deleteTbSidelineGridUserByIds(ids);
    }

    @Override
    public List<TbGridInfo> selectTbSidelineGridUserlistByGrid(TbSidelineGridUser tbSidelineGridUser, List<TbGridInfo> gridInfos) {
        //先获取兼职兼职网格员的类型字典
        /*List<SysDictData> typeDicts = DictUtils.getDictCache("grid_user_type");
        List<String> dictValueList = Optional.ofNullable(typeDicts)
                .orElse(Collections.emptyList())
                .stream()
                .map(SysDictData::getDictValue)
                .collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(tbSidelineGridUser.getType())){
            dictValueList.clear();
            dictValueList.add(tbSidelineGridUser.getType());
        }*/
        List<String> dictValueList = Optional.ofNullable(tbSidelineGridUser.getType())
                .filter(ObjectUtil::isNotEmpty)
                .map(type -> Collections.singletonList(type))
                .orElseGet(() ->
                        Optional.ofNullable(DictUtils.getDictCache("grid_user_type"))
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(SysDictData::getDictValue)
                                .collect(Collectors.toList())
                );

        for (TbGridInfo gridInfo : gridInfos) {
                Map<String,List<TbSidelineGridUser>> map = new HashMap<>();
                for (String typeDictValue : dictValueList) {
                    //查询当前网格下的 数据
                    List<TbSidelineGridUser> rawList = tbSidelineGridUserMapper.selectTbSidelineGridUserList2(new TbSidelineGridUser(){{
                        setGridId(gridInfo.getId());
                        setType(typeDictValue);
                        setName(tbSidelineGridUser.getName());
                        setIdCard(tbSidelineGridUser.getIdCard());
                        setPhone(tbSidelineGridUser.getPhone());
                    }});
                    map.put(typeDictValue,rawList);
                }
                gridInfo.setSidelineGridUserMap(map);
            }

        return gridInfos;
       /*
       sql:
           SELECT
            g.id AS grid_id,g.county,g.country,g.town,g.grid_name,g.administrative_plan AS grid_range,
            u.type,CONCAT(u.name, u.phone) AS name

            FROM tb_grid_info g
            LEFT JOIN tb_sideline_grid_info i ON g.id = i.grid_id
            LEFT JOIN tb_sideline_grid_user u ON i.side_user_id = u.id
       List<TbSidelineGridUser> rawList = tbSidelineGridUserMapper.selectTbSidelineGridUserList2()
       构建分组缓存（处理空值）
       Map<Long, Map<String, List<String>>> gridTypeMap = rawList.stream()
                .filter(u -> u.getGridId() != null && u.getType() != null) // 关键过滤
                .collect(Collectors.groupingBy(
                        TbSidelineGridUser::getGridId,
                        Collectors.groupingBy(
                                TbSidelineGridUser::getType,
                                Collectors.mapping(
                                        u -> StringUtils.defaultIfBlank(u.getName(), "无联系方式"),
                                        Collectors.toList()
                                )
                        )
                ));

        List<TbSidelineGridUser> uniqueList = new ArrayList<>(
                rawList.stream()
                        .collect(Collectors.toMap(
                                TbSidelineGridUser::getGridId,
                                user -> user,
                                (existing, replacement) -> existing  // 保留首次出现的对象
                        ))
                        .values()
        );
        List<TbSidelineGridUser> collect = uniqueList.stream()
                .filter(u -> u.getGridId() != null)
                .distinct()
                .map(user -> {
                    Map<String, List<String>> typeMap = new LinkedHashMap<>();

                    // 按字典顺序初始化所有类型
                    orderedTypes.forEach(type ->
                            typeMap.put(type,
                                    gridTypeMap.getOrDefault(user.getGridId(), Collections.emptyMap())
                                            .getOrDefault(type, Collections.emptyList())
                            )
                    );
                    // 设置动态映射
                    user.setSidelineGridUserMap(typeMap);

                    user.setPipelineGas(String.join("、", typeMap.get("管道气公司")));
                    user.setLiquefiedGas(String.join("、", typeMap.get("液化气公司")));

                    return user;
                }).collect(Collectors.toList());*/


    }

    /**
     * 删除兼职网格员信息信息
     * 
     * @param tbSidelineGridInfo 兼职网格员信息
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTbSidelineGridUserById(TbSidelineGridInfo tbSidelineGridInfo)
    {
        int i = tbSidelineGridUserMapper.deleteTbSidelineGridInfoBySideUserId(tbSidelineGridInfo);

        List<TbSidelineGridInfo> tbSidelineGridInfos = tbSidelineGridInfoMapper.selectTbSidelineGridInfoList(new TbSidelineGridInfo() {{
            setSideUserId(tbSidelineGridInfo.getSideUserId());
        }});
        if (ObjectUtil.isEmpty(tbSidelineGridInfos)){
                //如果该兼职网格员没有管理的网格，则删除当前简直网格员
             tbSidelineGridUserMapper.deleteTbSidelineGridUserById(tbSidelineGridInfo.getSideUserId());
        }
        return i;
    }

    /**
     * 新增兼职网格员所属网格信息信息
     * 
     * @param tbSidelineGridUser 兼职网格员信息对象
     */
    public void insertTbSidelineGridInfo(TbSidelineGridUser tbSidelineGridUser)
    {
        List<TbSidelineGridInfo> tbSidelineGridInfoList = tbSidelineGridUser.getTbSidelineGridInfoList();
        Long id = tbSidelineGridUser.getId();
        if (ObjectUtil.isNotEmpty(tbSidelineGridInfoList))
        {
            List<TbSidelineGridInfo> list = new ArrayList<TbSidelineGridInfo>();
            for (TbSidelineGridInfo tbSidelineGridInfo : tbSidelineGridInfoList)
            {
                tbSidelineGridInfo.setSideUserId(id);
                list.add(tbSidelineGridInfo);
            }
            if (list.size() > 0)
            {
                tbSidelineGridUserMapper.batchTbSidelineGridInfo(list);
            }
        }
    }

}
