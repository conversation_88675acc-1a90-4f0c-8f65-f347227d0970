package com.ruoyi.common.core.domain.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 视频信息对象 sys_video_info
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
public class SysVideoInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 文本数据id */
    @Excel(name = "文本数据id")
    private Long dataId;

    /** 视频网络地址 */
    @Excel(name = "视频网络地址")
    private String webAddr;

    /** 视频本地地址 */
    @Excel(name = "视频本地地址")
    private String localAddr;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDataId(Long dataId) 
    {
        this.dataId = dataId;
    }

    public Long getDataId() 
    {
        return dataId;
    }
    public void setWebAddr(String webAddr) 
    {
        this.webAddr = webAddr;
    }

    public String getWebAddr() 
    {
        return webAddr;
    }
    public void setLocalAddr(String localAddr) 
    {
        this.localAddr = localAddr;
    }

    public String getLocalAddr() 
    {
        return localAddr;
    }
    public void setDateTime(Date dateTime) 
    {
        this.dateTime = dateTime;
    }

    public Date getDateTime() 
    {
        return dateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dataId", getDataId())
            .append("webAddr", getWebAddr())
            .append("localAddr", getLocalAddr())
            .append("dateTime", getDateTime())
            .append("remark", getRemark())
            .toString();
    }
}
