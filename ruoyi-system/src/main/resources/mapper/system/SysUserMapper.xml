<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="gridDeptId" column="grid_dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
		<result property="signature" column="signature"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
		<result property="isUpdate" column="is_update"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="pwdUpdateDate" column="pwd_update_date" />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="spare" column="spare"/>
        <result property="openId"    column="open_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="userType"    column="user_type"    />
        <result property="jobUnit"    column="job_unit"    />
        <result property="jobId"    column="job_id"    />
        <result property="drillNo"    column="drill_no"    />
        <association property="dept" column="dept_id" javaType="SysDept" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="gridId" column="grid_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="ancestors" column="ancestors"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="phone" column="dept_phone"/>
        <result property="deptLevel" column="dept_level" />
        <result property="deptType" column="dept_type" />
        <result property="code" column="code" />
        <result property="url" column="url" />
    </resultMap>

    <resultMap id="RoleResult" type="SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.signature, u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.is_update, u.login_ip,
        u.login_date, u.pwd_update_date, u.create_by, u.create_time, u.remark, u.spare,u.user_type,u.update_by, u.update_time,
        u.open_id, u.union_id,u.job_unit,u.drill_no,u.job_id,
        d.dept_level, d.dept_type,d.code, d.url,d.phone as dept_phone,
        d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.leader, d.status as dept_status, d.grid_id as grid_id,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as role_status
        from sys_user u
		    left join sys_dept d on u.dept_id = d.dept_id
		    left join sys_user_role ur on u.user_id = ur.user_id
		    left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.signature, u.phonenumber, u.sex, u.status, u.is_update, u.open_id, u.union_id,u.user_type,u.update_by, u.update_time,
        u.del_flag, u.login_ip, u.login_date, u.pwd_update_date, u.create_by, u.create_time,u.job_unit,u.drill_no, u.job_id, u.remark, d.dept_name, d.phone as dept_phone, d.dept_level, d.dept_type, d.leader, d.grid_id
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0'
        <if test="userId != null and userId != 0">
            AND u.user_id = #{userId}
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="nickName != null and nickName != ''">
            AND u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="userType != null and userType != ''">
            AND u.user_type = #{userType}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%Y%m%d') &gt;= date_format(#{params.beginTime},'%Y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%Y%m%d') &lt;= date_format(#{params.endTime},'%Y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <if test="deptLevel != null">
            AND d.dept_level = #{deptLevel}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by u.user_id desc
    </select>

    <select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time, u.signature, d.dept_name
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and r.role_id = #{roleId}
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time, u.signature
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL) and u.user_type = '00'
        and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and
        ur.role_id = #{roleId})
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName} and u.del_flag = '0'
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select user_id, user_name from sys_user where user_name = #{userName} and del_flag = '0' limit 1
	</select>

    <select id="checkPhoneUnique" parameterType="SysUser" resultMap="SysUserResult">
		select user_id, phonenumber, open_id, drill_no, status, dept_id from sys_user where phonenumber = #{phonenumber} and del_flag = '0'
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        <if test="openId != null and openId != ''">
            and open_id != #{openId}
        </if>
        limit 1
	</select>

    <select id="checkPhoneUnique2" parameterType="SysUser" resultMap="SysUserResult">
		select u.user_id, u.phonenumber, u.open_id, u.drill_no, u.status, u.dept_id, d.grid_id, d.dept_name, u.user_type from sys_user u
		left join sys_dept d on u.dept_id = d.dept_id
		where u.phonenumber = #{phonenumber} and u.del_flag = '0'
        <if test="userId != null">
            and u.user_id != #{userId}
        </if>
        <if test="openId != null and openId != ''">
            and u.open_id != #{openId}
        </if>
        limit 1
	</select>

    <select id="checkEmailUnique" parameterType="SysUser" resultMap="SysUserResult">
		select user_id, email from sys_user where email = #{email} and del_flag = '0'
        <if test="userId != null">
            and user_id != #{userId}
        </if>
        limit 1
	</select>

    <insert id="insertUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">user_id,</if>
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="userName != null and userName != ''">user_name,</if>
        <if test="nickName != null and nickName != ''">nick_name,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="avatar != null and avatar != ''">avatar,</if>
		<if test="signature != null and signature != ''">signature,</if>
        <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
        <if test="sex != null and sex != ''">sex,</if>
        <if test="password != null and password != ''">password,</if>
        <if test="status != null and status != ''">status,</if>
		<if test="isUpdate != null">is_update,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        <if test="remark != null and remark != ''">remark,</if>
        <if test="spare != null and spare != ''">spare,</if>
        <if test="openId != null">open_id,</if>
        <if test="unionId != null">union_id,</if>
        <if test="drillNo != null">drill_no,</if>
        <if test="jobUnit != null">job_unit,</if>
        <if test="userType != null">user_type,</if>
        <if test="jobId != null">job_id,</if>
        <if test="pwdUpdateDate != null">pwd_update_date,</if>
        create_time
        )values(
        <if test="userId != null and userId != ''">#{userId},</if>
        <if test="deptId != null and deptId != ''">#{deptId},</if>
        <if test="userName != null and userName != ''">#{userName},</if>
        <if test="nickName != null and nickName != ''">#{nickName},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="avatar != null and avatar != ''">#{avatar},</if>
		<if test="signature != null and signature != ''">#{signature},</if>
        <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
        <if test="sex != null and sex != ''">#{sex},</if>
        <if test="password != null and password != ''">#{password},</if>
        <if test="status != null and status != ''">#{status},</if>
		<if test="isUpdate != null">is_update = #{isUpdate},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        <if test="remark != null and remark != ''">#{remark},</if>
        <if test="spare != null and spare != ''">#{spare},</if>
        <if test="openId != null">#{openId},</if>
        <if test="unionId != null">#{unionId},</if>
        <if test="drillNo != null">#{drillNo},</if>
        <if test="jobUnit != null">#{jobUnit},</if>
        <if test="userType != null">#{userType},</if>
        <if test="jobId != null">#{jobId},</if>
        <if test="pwdUpdateDate != null">#{pwdUpdateDate},</if>
        sysdate()
        )
    </insert>

    <update id="updateUser" parameterType="SysUser">
        update sys_user
        <set>
            <if test="deptId != null and deptId != 0">dept_id = #{deptId},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="email != null ">email = #{email},</if>
            <if test="phonenumber != null ">phonenumber = #{phonenumber},</if>
            <if test="sex != null and sex != ''">sex = #{sex},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="loginIp != null and loginIp != ''">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
			<if test="signature != null">signature = #{signature},</if>
            <if test="spare != null">spare = #{spare},</if>
            <if test="isUpdate != null">is_update = #{isUpdate},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="jobUnit != null">job_unit = #{jobUnit},</if>
            <if test="jobId != null">job_id = #{jobId},</if>
            <if test="drillNo != null">drill_no = #{drillNo},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="userType != null">user_type = #{userType},</if>
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <update id="updateUserStatus" parameterType="SysUser">
 		update sys_user set status = #{status} where user_id = #{userId}
	</update>

    <update id="updateUserAvatar" parameterType="SysUser">
 		update sys_user set avatar = #{avatar} where user_name = #{userName}
	</update>

    <update id="resetUserPwd" parameterType="SysUser">
 		update sys_user set pwd_update_date = sysdate(), password = #{password},
 		<if test="spare != null and spare != ''">
            spare = #{spare},
        </if>
 		is_update = '1' where user_name = #{userName}
	</update>

    <delete id="deleteUserById" parameterType="Long">
 		delete from sys_user where user_id = #{userId}
 	</delete>

    <delete id="deleteUserByIds" parameterType="Long">
        delete from sys_user where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <update id="updateUserSignature" parameterType="com.ruoyi.common.core.domain.entity.SysUser">
 		update sys_user set signature = #{signature} where user_name = #{userName}
	</update>

    <select id="selectUserByDeptId" parameterType="Long" resultMap="SysUserResult">
		select * from sys_user u where u.del_flag = '0' and status = '0' and dept_id = #{deptId}
	</select>

    <select id="selectSpareByUser" resultType="string">
		select spare from sys_user where user_name = #{userName}
	</select>

    <select id="selectPassUpdateByUserName" parameterType="string" resultMap="SysUserResult">
		select is_update from sys_user where user_name = #{username} limit 1
	</select>

    <select id="findUserIdByNickName" parameterType="com.ruoyi.common.core.domain.entity.SysUser" resultType="Long">
        select u.user_id from sys_user u where u.del_flag = '0'
        <if test="nickName != null and nickName != ''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
    </select>

    <select id="selectUserByPost" resultMap="SysUserResult">
		select u.user_id,u.nick_name,u.user_name,u.open_id from sys_user u left join sys_user_post up on u.user_id = up.user_id left join sys_post p on up.post_id = p.post_id where p.post_code = #{post} and u.user_type = '00'
	</select>

    <select id="selectWxUserByOpenId" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo" />
        where u.open_id = #{openId} and u.del_flag = '0'
    </select>

    <select id="selectUserListByPhone" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.user_name, u.nick_name, u.job_id,
        r.role_id, r.role_name, r.role_key from sys_user u
        left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id and r.role_id = 2
        where u.phonenumber = #{phonenumber} and u.del_flag = '0'
    </select>

    <select id="selectUserGrid" parameterType="Long" resultMap="SysUserResult">
        select u.user_id, u.user_name, u.nick_name, u.job_id, d.dept_id as grid_dept_id from sys_user u left join sys_dept d on u.dept_id = d.dept_id
        where u.user_id = #{userId} and u.del_flag = '0'
    </select>

    <select id="selectGridId" parameterType="String" resultType="Long">
        select grid_id from tb_resident_member where phone = #{phone} order by id desc limit 1
    </select>

    <select id="selectDeptId" parameterType="String" resultType="Long">
        select dept_id from tb_resident_member where phone = #{phone} order by id desc limit 1
    </select>

    <select id="selectUserGridList" resultMap="SysUserResult">
        select u.user_id, u.user_name, u.nick_name, u.job_id, d.dept_id as grid_dept_id from sys_user u left join sys_dept d on u.dept_id = d.dept_id
        where u.del_flag = '0' and u.job_id is not null
        <if test="idList != null and idList.length > 0">
            and u.user_id in
            <foreach collection="idList" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>
</mapper>