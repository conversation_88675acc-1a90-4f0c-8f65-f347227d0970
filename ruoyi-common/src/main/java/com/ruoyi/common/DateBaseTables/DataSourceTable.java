package com.ruoyi.common.DateBaseTables;

import java.util.List;

public class DataSourceTable {
    private String tableName;
    private List<Columns> columnsList;
    private boolean enableField = false;
    private String dataSource = "MASTER";
    private List<String> fieldList;
    private String dataSourceName;
    /**
     * 1新增，2修改
     */
    private int type = 2;

    /**
     * 表注释
     */
    private String tableComment;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDataSourceName() {
        return dataSourceName;
    }

    public void setDataSourceName(String dataSourceName) {
        this.dataSourceName = dataSourceName;
    }

    public String getTableComment() {
        return tableComment;
    }

    public void setTableComment(String tableComment) {
        this.tableComment = tableComment;
    }

    public List<String> getFieldList() {
        return fieldList;
    }

    public void setFieldList(List<String> fieldList) {
        this.fieldList = fieldList;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public boolean isEnableField() {
        return enableField;
    }

    public void setEnableField(boolean enableField) {
        this.enableField = enableField;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<Columns> getColumnsList() {
        return columnsList;
    }

    public void setColumnsList(List<Columns> columnsList) {
        this.columnsList = columnsList;
    }
}
