package com.ruoyi.microPlatform.service;

import java.util.List;
import com.ruoyi.microPlatform.domain.TbOrgActivity;

/**
 * 组织日常活动开展情况Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface ITbOrgActivityService 
{
    /**
     * 查询组织日常活动开展情况
     * 
     * @param id 组织日常活动开展情况主键
     * @return 组织日常活动开展情况
     */
    public TbOrgActivity selectTbOrgActivityById(Long id);

    /**
     * 校验组织日常活动开展情况是否存在
     *
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 组织日常活动开展情况
     */
    public boolean checkTbOrgActivity(TbOrgActivity tbOrgActivity);

    /**
     * 查询组织日常活动开展情况列表
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 组织日常活动开展情况集合
     */
    public List<TbOrgActivity> selectTbOrgActivityList(TbOrgActivity tbOrgActivity);

    /**
     * 导入组织日常活动开展情况
     *
     * @param infos       组织日常活动开展情况列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbOrgActivity(List<TbOrgActivity> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    public int insertTbOrgActivity(TbOrgActivity tbOrgActivity);

    /**
     * 修改组织日常活动开展情况
     * 
     * @param tbOrgActivity 组织日常活动开展情况
     * @return 结果
     */
    public int updateTbOrgActivity(TbOrgActivity tbOrgActivity);

    /**
     * 批量删除组织日常活动开展情况
     * 
     * @param ids 需要删除的组织日常活动开展情况主键集合
     * @return 结果
     */
    public int deleteTbOrgActivityByIds(Long[] ids);

    /**
     * 删除组织日常活动开展情况信息
     * 
     * @param id 组织日常活动开展情况主键
     * @return 结果
     */
    public int deleteTbOrgActivityById(Long id);
}
