package com.ruoyi.common.DateBaseTables;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.builder.ToStringBuilder;

public class Columns {
    /**
     * 字段名称
     */
    private String Field;
    /**
     * 字段类型
     */
    private String Type;

    /**
     * 是否允许为空
     */
    private boolean  columnIsNull=true;

    private String Null;

    /**
     * 是否是主键
     */
    private boolean columnIsKey=false;

    private String Key;

    /**
     * 默认值
     */
    private String Default;

    /**
     * 注释
     */
    private String Comment;

    /**
     * 字段长度
     */
    private Integer Length;

    /**
     * 小数点位数
     */
    private Integer decimalPoint=0;


    private String Extra;

    /**
     * 权限
     */
    private String Privileges;

    /**
     * 字符序
     */
    private String Collation;

    /**
     * 旧字段
     */
    @JsonIgnore
    private String Oldfield;

    /**
     * 前端使用，后端未使用
     */
    private String ids;

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getOldfield() {
        return Oldfield;
    }

    public void setOldfield(String Oldfield) {
        this.Oldfield = Oldfield;
    }

    public String getExtra() {
        return Extra;
    }

    public void setExtra(String extra) {
        Extra = extra;
    }

    public String getPrivileges() {
        return Privileges;
    }

    public void setPrivileges(String privileges) {
        Privileges = privileges;
    }

    public String getCollation() {
        return Collation;
    }

    public void setCollation(String collation) {
        Collation = collation;
    }

    public String getField() {
        return Field;
    }

    public void setField(String field) {
        Field = field;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        if (StrUtil.isNotBlank(type)) {
            if (type.contains("(") && type.contains(")") && !type.contains(",")) {
                this.Length = Integer.valueOf(type.substring(type.indexOf("(") + 1, type.indexOf(")")));
            } else if (type.contains("(") && type.contains(")") && type.contains(",")) {
                this.Length = Integer.valueOf(type.substring(type.indexOf("(") + 1, type.indexOf(",")));
                this.decimalPoint = Integer.valueOf(type.substring(type.indexOf(",") + 1, type.indexOf(")")));
            }
            if (type.contains("(")){
                type=type.substring(0,type.indexOf("("));
            }
        }
        Type = type;
    }

    public boolean isColumnIsNull() {
        return columnIsNull;
    }

    public void setColumnIsNull(boolean columnIsNull) {
        this.columnIsNull = columnIsNull;
    }

    public String getNull() {
        return Null;
    }

    public void setNull(String aNull) {
        if (StrUtil.isNotBlank(aNull)) {
            if (aNull.equalsIgnoreCase("NO")) {
                this.columnIsNull = false;
            } else if (aNull.equalsIgnoreCase("YES")) {
                this.columnIsNull = true;
            } else {
                this.columnIsNull = true;
            }
        } else {
            this.columnIsNull = true;
        }
        Null = aNull;
    }

    public boolean isColumnIsKey() {
        return columnIsKey;
    }

    public void setColumnIsKey(boolean columnIsKey) {
        this.columnIsKey = columnIsKey;
    }

    public String getKey() {
        return Key;
    }

    public void setKey(String key) {
        if (StrUtil.isNotBlank(key)) {
            this.columnIsKey = key.equalsIgnoreCase("PRI");
        } else {
            this.columnIsKey = false;
        }
        Key = key;
    }

    public String getDefault() {
        return Default;
    }

    public void setDefault(String aDefault) {
        Default = aDefault;
    }

    public String getComment() {
        return Comment;
    }

    public void setComment(String comment) {
        Comment = comment;
    }

    public Integer getLength() {
        return Length;
    }

    public void setLength(Integer length) {
        Length = length;
    }

    public Integer getDecimalPoint() {
        return decimalPoint;
    }

    public void setDecimalPoint(Integer decimalPoint) {
        this.decimalPoint = decimalPoint;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("Field" , Field)
                .append("Type" , Type)
                .append("columnIsNull" , columnIsNull)
                .append("Null" , Null)
                .append("columnIsKey" , columnIsKey)
                .append("Key" , Key)
                .append("Default" , Default)
                .append("Comment" , Comment)
                .append("Length" , Length)
                .append("decimalPoint" , decimalPoint)
                .append("Extra" , Extra)
                .append("Privileges" , Privileges)
                .append("Collation" , Collation)
                .toString();
    }
}
