package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbResidentMemberType;
import com.ruoyi.microPlatform.service.ITbResidentMemberTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 成员标签配置Controller
 * 
 * <AUTHOR>
 * @date 2025-03-25
 */
@RestController
@RequestMapping("/gridData/memberType")
public class TbResidentMemberTypeController extends BaseController
{
    @Autowired
    private ITbResidentMemberTypeService tbResidentMemberTypeService;

    /**
     * 查询成员标签配置列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbResidentMemberType tbResidentMemberType)
    {
        startPage();
        List<TbResidentMemberType> list = tbResidentMemberTypeService.selectTbResidentMemberTypeList(tbResidentMemberType);
        return getDataTable(list);
    }

    /**
     * 导出成员标签配置列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:export')")
    @Log(title = "成员标签配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbResidentMemberType tbResidentMemberType)
    {
        List<TbResidentMemberType> list = tbResidentMemberTypeService.selectTbResidentMemberTypeList(tbResidentMemberType);
        ExcelUtil<TbResidentMemberType> util = new ExcelUtil<TbResidentMemberType>(TbResidentMemberType.class);
        util.exportExcel(response, list, "成员标签配置数据");
    }

    @Log(title = "成员标签配置", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:memberType:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbResidentMemberType> util = new ExcelUtil<TbResidentMemberType>(TbResidentMemberType.class);
        List<TbResidentMemberType> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbResidentMemberTypeService.importTbResidentMemberType(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbResidentMemberType> util = new ExcelUtil<TbResidentMemberType>(TbResidentMemberType.class);
        util.exportTemplateExcel(response, "成员标签配置数据");
    }

    /**
     * 获取成员标签配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbResidentMemberTypeService.selectTbResidentMemberTypeById(id));
    }

    /**
     * 新增成员标签配置
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:add')")
    @Log(title = "成员标签配置", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbResidentMemberType tbResidentMemberType)
    {
        tbResidentMemberType.setCreateBy(getUsername());
        if (tbResidentMemberTypeService.checkTbResidentMemberType(tbResidentMemberType)){
            return error("数据已存在！");
        }
        return toAjax(tbResidentMemberTypeService.insertTbResidentMemberType(tbResidentMemberType));
    }

    /**
     * 修改成员标签配置
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:edit')")
    @Log(title = "成员标签配置", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbResidentMemberType tbResidentMemberType)
    {
        tbResidentMemberType.setUpdateBy(getUsername());
        if (tbResidentMemberTypeService.checkTbResidentMemberType(tbResidentMemberType)){
            return error("数据已存在！");
        }
        return toAjax(tbResidentMemberTypeService.updateTbResidentMemberType(tbResidentMemberType));
    }

    /**
     * 删除成员标签配置
     */
    @PreAuthorize("@ss.hasPermi('gridData:memberType:remove')")
    @Log(title = "成员标签配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbResidentMemberTypeService.deleteTbResidentMemberTypeByIds(ids));
    }
}
