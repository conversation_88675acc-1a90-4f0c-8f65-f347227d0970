package com.ruoyi.microPlatform.service.impl;

import java.security.SignedObject;
import java.util.*;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.microPlatform.domain.SeActivityUser;
import com.ruoyi.microPlatform.mapper.SeCommunityActivityMapper;
import com.ruoyi.microPlatform.domain.SeCommunityActivity;
import com.ruoyi.microPlatform.service.ISeCommunityActivityService;

/**
 * 社区活动Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class SeCommunityActivityServiceImpl implements ISeCommunityActivityService 
{
    @Autowired
    private SeCommunityActivityMapper seCommunityActivityMapper;

    /**
     * 查询社区活动
     * 
     * @param id 社区活动主键
     * @return 社区活动
     */
    @Override
    public SeCommunityActivity selectSeCommunityActivityById(Long id, SysUser user)
    {
        SeCommunityActivity seCommunityActivity = seCommunityActivityMapper.selectSeCommunityActivityById(id);
        seCommunityActivity.setSigned(false);
        Integer userId = Math.toIntExact(user.getUserId());
        //判断当前用户是否已经报名
        for (SeActivityUser seActivityUser : seCommunityActivity.getSeActivityUserList()) {
            if (userId.longValue() == seActivityUser.getUserId().longValue()){
                //代表当前用户已经报名该活动
                seCommunityActivity.setSigned(true);
            }
        }
        Date now = new Date();
        //判断当前活动是否已经截止
        if (now.after(seCommunityActivity.getSignEnd())){
            //代表已经截止
            seCommunityActivity.setSignLimit(true);
        }

        return seCommunityActivity;
    }

    /**
     * 校验社区活动是否存在
     *
     * @param seCommunityActivity
     * @return boolean
     */
    @Override
    public boolean checkSeCommunityActivity(SeCommunityActivity seCommunityActivity){
        SeCommunityActivity old = seCommunityActivityMapper.checkSeCommunityActivityUnique(seCommunityActivity);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询社区活动列表
     * 
     * @param seCommunityActivity 社区活动
     * @return 社区活动
     */
    @Override
    public List<SeCommunityActivity> selectSeCommunityActivityList(SeCommunityActivity seCommunityActivity)
    {
        if (ObjectUtil.isNotEmpty(seCommunityActivity.getActivityStatus())){
            switch (seCommunityActivity.getActivityStatus()){
                case "未开始":
                    seCommunityActivity.setActivityStatusInt(0);
                    break;
                case "进行中":
                    seCommunityActivity.setActivityStatusInt(1);
                    break;
                case "已结束":
                    seCommunityActivity.setActivityStatusInt(2);
                    break;
                case "正在进行":
                    seCommunityActivity.setActivityStatusInt(3);
                    break;
                case "精彩回顾":
                    seCommunityActivity.setActivityStatusInt(4);
                    break;
                default:
                    break;
            }
        }
        return seCommunityActivityMapper.selectSeCommunityActivityList(seCommunityActivity);
    }


    /**
     * 导入社区活动
     *
     * @param infos       社区活动列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeCommunityActivity(List<SeCommunityActivity> infos, Boolean isUpdateSupport, String operName){
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入社区活动数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (SeCommunityActivity info : infos)
        {
            try
            {
                // 验证是否存在这个数据
                SeCommunityActivity old = seCommunityActivityMapper.checkSeCommunityActivityUnique(info);
                if (ObjectUtil.isEmpty(old))
                {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    seCommunityActivityMapper.insertSeCommunityActivity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    seCommunityActivityMapper.updateSeCommunityActivity(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， "  + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    @Transactional
    @Override
    public int insertSeCommunityActivity(SeCommunityActivity seCommunityActivity)
    {
        seCommunityActivity.setCreateTime(DateUtils.getNowDate());
        return seCommunityActivityMapper.insertSeCommunityActivity(seCommunityActivity);
    }

    /**
     * 修改社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    @Transactional
    @Override
    public int updateSeCommunityActivity(SeCommunityActivity seCommunityActivity)
    {
        seCommunityActivity.setUpdateTime(DateUtils.getNowDate());
        //在进行新增当前活动的报名用户
        return seCommunityActivityMapper.updateSeCommunityActivity(seCommunityActivity);
    }

    /**
     * 批量删除社区活动
     * 
     * @param ids 需要删除的社区活动主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSeCommunityActivityByIds(Long[] ids)
    {
        seCommunityActivityMapper.deleteSeActivityUserByActivityIds(ids);
        return seCommunityActivityMapper.deleteSeCommunityActivityByIds(ids);
    }

    /**
     * 删除社区活动信息
     * 
     * @param id 社区活动主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteSeCommunityActivityById(Long id)
    {
        seCommunityActivityMapper.deleteSeActivityUserByActivityId(id);
        return seCommunityActivityMapper.deleteSeCommunityActivityById(id);
    }



    @Override
    public List<SeCommunityActivity> selectSeCommunityActivityListByUserId(SeCommunityActivity seCommunityActivity) {
        return seCommunityActivityMapper.selectSeCommunityActivityListByUserId(seCommunityActivity);
    }

    @Override
    public Map<String, Integer> selectSeCommunityActivityMap(SeCommunityActivity seCommunityActivity) {
        Map<String, Integer> mapsize = new HashMap<>();
        Map<String, Integer> map = seCommunityActivityMapper.selectSeCommunityActivityListSize(seCommunityActivity);
        mapsize.put("报名数",map.get("total_sign_num"));
        mapsize.put("任务数",map.get("total_count"));
        return mapsize;
    }


    @Override
    public Map<String, Object> selectActCountList(Integer deptLevel, String county, String country,Integer taskStatusType) {
        Map<String, Object> result = new HashMap<>();
        result.put("total", seCommunityActivityMapper.getActivityTotal(deptLevel, county, country,taskStatusType));
        result.put("list", seCommunityActivityMapper.getActivityStatistics(deptLevel, county, country,taskStatusType));
        return result;
    }
}
