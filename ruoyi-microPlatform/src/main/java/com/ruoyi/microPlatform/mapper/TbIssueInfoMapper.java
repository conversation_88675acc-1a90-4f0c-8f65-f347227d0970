package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.bigdata.res.CommonBaseCount;
import com.ruoyi.microPlatform.domain.*;
import org.apache.ibatis.annotations.Param;

/**
 * 矛盾纠纷Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12 11:18:54
 */
public interface TbIssueInfoMapper 
{
    /**
     * 查询矛盾纠纷
     * 
     * @param id 矛盾纠纷主键
     * @return 矛盾纠纷
     */
    public TbIssueInfo selectTbIssueInfoById(Long id);

    /**
     * 校验矛盾纠纷是否唯一
     *
     * @param tbIssueInfo
     * @return 矛盾纠纷
     */
    public TbIssueInfo checkTbIssueInfoUnique(TbIssueInfo tbIssueInfo);

    /**
     * 查询矛盾纠纷列表
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 矛盾纠纷集合
     */
    public List<TbIssueInfo> selectTbIssueInfoList(TbIssueInfo tbIssueInfo);

    /**
     * 新增矛盾纠纷
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    public int insertTbIssueInfo(TbIssueInfo tbIssueInfo);

    /**
     * 修改矛盾纠纷
     * 
     * @param tbIssueInfo 矛盾纠纷
     * @return 结果
     */
    public int updateTbIssueInfo(TbIssueInfo tbIssueInfo);

    /**
     * 删除矛盾纠纷
     * 
     * @param id 矛盾纠纷主键
     * @return 结果
     */
    public int deleteTbIssueInfoById(Long id);

    /**
     * 批量删除矛盾纠纷
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbIssueInfoByIds(Long[] ids);

    /**
     * 批量删除矛盾纠纷当事人信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbMainPartyByIssueIds(Long[] ids);
    
    /**
     * 批量新增矛盾纠纷当事人信息
     * 
     * @param tbMainPartyList 矛盾纠纷当事人信息列表
     * @return 结果
     */
    public int batchTbMainParty(List<TbMainParty> tbMainPartyList);
    

    /**
     * 通过矛盾纠纷主键删除矛盾纠纷当事人信息信息
     * 
     * @param id 矛盾纠纷ID
     * @return 结果
     */
    public int deleteTbMainPartyByIssueId(Long id);

    Map<String, Integer> getIssueCountByMdjfztdm(LargeScreenInfo largeScreenInfo);

    List<HandleStepVO> getHandleStepInfo(@Param("issueId") Long issueId);

    TransferIssueInfoVO getTransferInfo(@Param("issueId") Long issueId);


    List<CommonBaseCount> statusCount();

}
