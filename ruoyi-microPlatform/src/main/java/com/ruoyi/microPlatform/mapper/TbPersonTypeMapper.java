package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbPersonType;
import org.apache.ibatis.annotations.MapKey;

/**
 * 人员标签类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-04
 */
public interface TbPersonTypeMapper 
{
    /**
     * 查询人员标签类型
     * 
     * @param id 人员标签类型主键
     * @return 人员标签类型
     */
    public TbPersonType selectTbPersonTypeById(Integer id);

    /**
     * 校验人员标签类型是否唯一
     *
     * @param tbPersonType
     * @return 人员标签类型
     */
    public TbPersonType checkTbPersonTypeUnique(TbPersonType tbPersonType);

    /**
     * 查询人员标签类型列表
     * 
     * @param tbPersonType 人员标签类型
     * @return 人员标签类型集合
     */
    public List<TbPersonType> selectTbPersonTypeList(TbPersonType tbPersonType);

    /**
     * 新增人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    public int insertTbPersonType(TbPersonType tbPersonType);

    /**
     * 修改人员标签类型
     * 
     * @param tbPersonType 人员标签类型
     * @return 结果
     */
    public int updateTbPersonType(TbPersonType tbPersonType);

    /**
     * 删除人员标签类型
     * 
     * @param id 人员标签类型主键
     * @return 结果
     */
    public int deleteTbPersonTypeById(Integer id);

    /**
     * 批量删除人员标签类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbPersonTypeByIds(Integer[] ids);

    /**
     * 获取标签的数量排名
     * @param largeScreenInfo
     * @return
     */
    List<TbPersonType> selectLableRank(LargeScreenInfo largeScreenInfo);
}
