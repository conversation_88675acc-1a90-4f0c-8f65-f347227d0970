package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 党员队伍信息管理表 pb_party_member
 */
@Data
public class PbPartyMember extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 党员编号 */
    private String memberCode;

    /** 姓名 */
    private String name;

    /** 身份证号码 */
    private String idCard;

    /** 性别（0男 1女 2未知） */
    private Integer gender;

    /** 民族 */
    private String nation;

    /** 出生日期 */
    private Date birthday;

    /** 年龄 */
    private Integer age;

    /** 学历（见字典） */
    private String educationLevel;

    /** 政治面貌（见字典） */
    private String politicalStatus;

    /** 入党时间 */
    private Date joinPartyDate;

    /** 状态（1在职 2离退休 3其他） */
    private Integer status;

    /** 所属支部ID */
    private Long branchId;

    /** 所属支部名称 */
    private String branchName;

    /** 所属部门ID */
    private Long deptId;

    /** 所属县（市、区） */
    private String county;

    /** 所属街道/乡镇 */
    private String country;

    /** 所属村/社区 */
    private String town;

    /** 参与治理活动次数 */
    private Integer activityCount;

    /** 最近参与治理时间 */
    private Date lastActivityTime;

    /** 联系电话 */
    private String phone;

    /** 常住地址 */
    private String address;
}
