<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbMerchantInspectDetailMapper">

    <resultMap type="TbMerchantInspectDetail" id="TbMerchantInspectDetailResult">
        <result property="id" column="id"/>
        <result property="inspectType" column="inspect_type"/>
        <result property="inspectId" column="inspect_id"/>
        <result property="inspectDesc" column="inspect_desc"/>
        <result property="inspectResult" column="inspect_result"/>
        <result property="remark" column="remark"/>
        <result property="type" column="type"/>
        <result property="accessory" column="accessory"/>
    </resultMap>

    <sql id="selectTbMerchantInspectDetailVo">
        select id, inspect_type,inspect_id, inspect_desc, inspect_result, remark, type, accessory from tb_merchant_inspect_detail
    </sql>

    <select id="selectTbMerchantInspectDetailList" parameterType="TbMerchantInspectDetail"
            resultMap="TbMerchantInspectDetailResult">
        <include refid="selectTbMerchantInspectDetailVo"/>
        <where>
            <if test="inspectType != null  and inspectType != ''">and inspect_type = #{inspectType}</if>
            <if test="inspectId != null  and inspectId != ''">and inspect_id = #{inspectId}</if>
            <if test="inspectDesc != null  and inspectDesc != ''">and inspect_desc = #{inspectDesc}</if>
            <if test="inspectResult != null ">and inspect_result = #{inspectResult}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
        </where>
    </select>

    <select id="selectTbMerchantInspectDetailById" parameterType="Integer" resultMap="TbMerchantInspectDetailResult">
        <include refid="selectTbMerchantInspectDetailVo"/>
        where id = #{id}
    </select>

    <select id="checkTbMerchantInspectDetailUnique" parameterType="TbMerchantInspectDetail"
            resultMap="TbMerchantInspectDetailResult">
        select id from tb_merchant_inspect_detail where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbMerchantInspectDetail" parameterType="TbMerchantInspectDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into tb_merchant_inspect_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inspectType != null">inspect_type,</if>
            <if test="inspectId != null">inspect_id,</if>
            <if test="inspectDesc != null">inspect_desc,</if>
            <if test="inspectResult != null">inspect_result,</if>
            <if test="remark != null">remark,</if>
            <if test="type != null">type,</if>
            <if test="accessory != null">accessory,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inspectType != null">#{inspectType},</if>
            <if test="inspectId != null">#{inspectId},</if>
            <if test="inspectDesc != null">#{inspectDesc},</if>
            <if test="inspectResult != null">#{inspectResult},</if>
            <if test="remark != null">#{remark},</if>
            <if test="type != null">#{type},</if>
            <if test="accessory != null">#{accessory},</if>
        </trim>
    </insert>

    <update id="updateTbMerchantInspectDetail" parameterType="TbMerchantInspectDetail">
        update tb_merchant_inspect_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="inspectType != null">inspect_type = #{inspectType},</if>
            <if test="inspectId != null">inspect_id = #{inspectId},</if>
            <if test="inspectDesc != null">inspect_desc = #{inspectDesc},</if>
            <if test="inspectResult != null">inspect_result = #{inspectResult},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="type != null">type = #{type},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbMerchantInspectDetailById" parameterType="Integer">
        delete from tb_merchant_inspect_detail where id = #{id}
    </delete>

    <delete id="deleteTbMerchantInspectDetailByIds" parameterType="String">
        delete from tb_merchant_inspect_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="batchInsertTbMerchantInspectDetail">
        insert into tb_merchant_inspect_detail(inspect_id, inspect_type, inspect_desc, inspect_result, remark, type, accessory) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{inspectId}, #{item.inspectType}, #{item.inspectDesc}, #{item.inspectResult}, #{item.remark}, #{item.type}, #{item.accessory})
        </foreach>
    </delete>


</mapper>