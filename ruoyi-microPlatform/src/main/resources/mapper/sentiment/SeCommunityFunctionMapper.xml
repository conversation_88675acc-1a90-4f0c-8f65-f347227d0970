<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.SeCommunityFunctionMapper">

    <resultMap type="SeCommunityFunction" id="SeCommunityFunctionResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="name" column="name"/>
        <result property="useStatus" column="use_status"/>
        <result property="introduction" column="introduction"/>
        <result property="area" column="area"/>
        <result property="capacity" column="capacity"/>
        <result property="useStatus" column="use_status"/>
        <result property="label" column="label"/>
        <result property="lat" column="lat"/>
        <result property="lng" column="lng"/>
        <result property="phone" column="phone"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="count" column="count"/>
    </resultMap>

    <sql id="selectSeCommunityFunctionVo">
        select id, dept_id, county, country, town, grid_name, name, use_status, introduction,area,capacity,label,
               accessory, lat, lng, phone, status, create_by, create_time, update_by, update_time from se_community_function
    </sql>



    <select id="selectSeCommunityFunctionList" parameterType="SeCommunityFunction"
            resultMap="SeCommunityFunctionResult">
        <include refid="selectSeCommunityFunctionVo"/>
        <where>
            <if test="deptId != null ">and (dept_id = #{deptId} OR EXISTS (SELECT 1 FROM sys_dept t WHERE find_in_set(#{deptId}, t.ancestors) AND t.dept_id = se_community_function.dept_id))</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="useStatus != null  and useStatus != ''">and use_status = #{useStatus}</if>
            <if test="introduction != null  and useStatus != ''">and introduction = #{introduction}</if>
            <if test="area != null  and useStatus != ''">and area = #{area}</if>
            <if test="capacity != null ">and capacity = #{capacity}</if>
            <if test="label != null  and useStatus != ''">and label = #{label}</if>
            <if test="accessory != null  and accessory != ''">and accessory = #{accessory}</if>
            <if test="lat != null  and lat != ''">and lat = #{lat}</if>
            <if test="lng != null  and lng != ''">and lng = #{lng}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="status != null ">and status = #{status}</if>
        </where>
    </select>

    <select id="selectSeCommunityFunctionById" parameterType="Long" resultMap="SeCommunityFunctionResult">
        <include refid="selectSeCommunityFunctionVo"/>
        where id = #{id}
    </select>

    <select id="checkSeCommunityFunctionUnique" parameterType="SeCommunityFunction"
            resultMap="SeCommunityFunctionResult">
        select id from se_community_function where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertSeCommunityFunction" parameterType="SeCommunityFunction" useGeneratedKeys="true" keyProperty="id">
        insert into se_community_function
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="name != null">name,</if>
            <if test="useStatus != null">use_status,</if>
            <if test="introduction != null">introduction,</if>
            <if test="area != null">area,</if>
            <if test="capacity != null">capacity,</if>
            <if test="label != null">label,</if>
            <if test="accessory != null">accessory,</if>
            <if test="lat != null">lat,</if>
            <if test="lng != null">lng,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="name != null">#{name},</if>
            <if test="useStatus != null">#{useStatus},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="area != null">#{area},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="label != null">#{label},</if>
            <if test="accessory != null">#{accessory},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lng != null">#{lng},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateSeCommunityFunction" parameterType="SeCommunityFunction">
        update se_community_function
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="introduction != null">introduction = #{introduction},</if>
            <if test="area != null">area = #{area},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="label != null">label = #{label},</if>
            <if test="accessory != null">accessory = #{accessory},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSeCommunityFunctionById" parameterType="Long">
        delete from se_community_function where id = #{id}
    </delete>

    <delete id="deleteSeCommunityFunctionByIds" parameterType="String">
        delete from se_community_function where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCommunityFunctionCount" parameterType="com.ruoyi.microPlatform.domain.LargeScreenInfo" resultMap="SeCommunityFunctionResult">
        SELECT
        cf.label,
        COUNT(*) AS count
        FROM
            se_community_function cf
            <where>
                <if test="deptId != null">
                    AND (cf.dept_id = #{deptId}
                    OR EXISTS (
                    SELECT 1
                    FROM sys_dept t
                    WHERE find_in_set(#{deptId}, t.ancestors)
                    AND t.dept_id = cf.dept_id))
                </if>
                <if test="county != null">
                    and cf.county = #{county}
                </if>
                <if test="country != null">
                    and cf.country = #{country}
                </if>
                <if test="town != null">
                    and cf.town = #{town}
                </if>
                <if test="gridName != null">
                    and cf.grid_name = #{gridName}
                </if>
            </where>
        GROUP BY
        cf.label
    </select>


    <select id="getCommunityFunctionCountByBigData" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultMap="SeCommunityFunctionResult">
        SELECT
        cf.label,
        COUNT(*) AS count
        FROM
        se_community_function cf
        where
            cf.label is not null and cf.label != ''
            <if test="deptId != null">
                AND (cf.dept_id = #{deptId}
                OR EXISTS (
                SELECT 1
                FROM sys_dept t
                WHERE find_in_set(#{deptId}, t.ancestors)
                AND t.dept_id = cf.dept_id))
            </if>
            <if test="county != null">
                and cf.county = #{county}
            </if>
        GROUP BY
        cf.label
    </select>


</mapper>