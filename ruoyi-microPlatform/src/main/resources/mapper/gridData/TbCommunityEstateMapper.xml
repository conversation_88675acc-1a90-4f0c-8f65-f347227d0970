<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbCommunityEstateMapper">

    <resultMap type="TbCommunityEstate" id="TbCommunityEstateResult">
        <result property="id" column="id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridName" column="grid_name"/>
        <result property="gridId" column="grid_id"/>
        <result property="communityId" column="community_id"/>
        <result property="communityName" column="community_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="idPhoto" column="id_photo"/>
        <result property="postDesc" column="post_desc"/>
        <result property="status" column="status"/>
        <result property="postDetail" column="post_detail"/>
        <result property="postStr" column="post_str"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbCommunityEstateVo">
        select id, county, country, town, grid_name, grid_id, community_id, community_name, dept_id, user_id, name, phone, id_photo, post_desc, status, post_detail, post_str, create_by, create_time, update_by, update_time from tb_community_estate
    </sql>

    <select id="selectTbCommunityEstateList" parameterType="TbCommunityEstate" resultMap="TbCommunityEstateResult">
        <include refid="selectTbCommunityEstateVo"/>
        <where>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="country != null  and country != ''">and country = #{country}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="gridName != null  and gridName != ''">and grid_name like concat('%', #{gridName}, '%')</if>
            <if test="gridId != null ">and grid_id = #{gridId}</if>
            <if test="communityId != null ">and community_id = #{communityId}</if>
            <if test="communityName != null  and communityName != ''">and community_name like concat('%',
                #{communityName}, '%')
            </if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="idPhoto != null  and idPhoto != ''">and id_photo = #{idPhoto}</if>
            <if test="postDesc != null  and postDesc != ''">and post_desc = #{postDesc}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="postDetail != null  and postDetail != ''">and post_detail = #{postDetail}</if>
            <if test="postStr != null">and post_str = #{postStr}</if>
        </where>
    </select>

    <select id="selectTbCommunityEstateById" parameterType="Long" resultMap="TbCommunityEstateResult">
        <include refid="selectTbCommunityEstateVo"/>
        where id = #{id}
    </select>

    <select id="checkTbCommunityEstateUnique" parameterType="TbCommunityEstate" resultMap="TbCommunityEstateResult">
        select id from tb_community_estate where id = #{id}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbCommunityEstate" parameterType="TbCommunityEstate" useGeneratedKeys="true" keyProperty="id">
        insert into tb_community_estate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="county != null">county,</if>
            <if test="country != null">country,</if>
            <if test="town != null">town,</if>
            <if test="gridName != null">grid_name,</if>
            <if test="gridId != null">grid_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="communityName != null">community_name,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="idPhoto != null">id_photo,</if>
            <if test="postDesc != null">post_desc,</if>
            <if test="status != null">status,</if>
            <if test="postDetail != null">post_detail,</if>
            <if test="postStr != null">post_str,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="county != null">#{county},</if>
            <if test="country != null">#{country},</if>
            <if test="town != null">#{town},</if>
            <if test="gridName != null">#{gridName},</if>
            <if test="gridId != null">#{gridId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="communityName != null">#{communityName},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idPhoto != null">#{idPhoto},</if>
            <if test="postDesc != null">#{postDesc},</if>
            <if test="status != null">#{status},</if>
            <if test="postDetail != null">#{postDetail},</if>
            <if test="postStr != null">#{postStr},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTbCommunityEstate" parameterType="TbCommunityEstate">
        update tb_community_estate
        <trim prefix="SET" suffixOverrides=",">
            <if test="county != null">county = #{county},</if>
            <if test="country != null">country = #{country},</if>
            <if test="town != null">town = #{town},</if>
            <if test="gridName != null">grid_name = #{gridName},</if>
            <if test="gridId != null">grid_id = #{gridId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="communityName != null">community_name = #{communityName},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idPhoto != null">id_photo = #{idPhoto},</if>
            <if test="postDesc != null">post_desc = #{postDesc},</if>
            <if test="status != null">status = #{status},</if>
            <if test="postDetail != null">post_detail = #{postDetail},</if>
            <if test="postStr != null">post_str = #{postStr},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        <where>
            <if test="id != null">id = #{id}</if>
            <if test="id == null and gridId != null">grid_id = #{gridId}</if>
        </where>
    </update>

    <delete id="deleteTbCommunityEstateById" parameterType="Long">
        delete from tb_community_estate where id = #{id}
    </delete>

    <delete id="deleteTbCommunityEstateByIds" parameterType="String">
        delete from tb_community_estate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbCommunityEstateByMainIds" parameterType="String">
        delete from tb_community_estate where community_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>