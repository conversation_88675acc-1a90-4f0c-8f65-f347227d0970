package com.ruoyi.dataview.domain.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 应急管理事项数据对象 db_emergency_event
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbEmergencyEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 事件编号（外部系统唯一标识） */
    @Excel(name = "事件编号")
    private String eventCode;

    /** 事件标题 */
    @Excel(name = "事件标题")
    private String eventTitle;

    /** 事件大类：自然灾害, 事故灾难, 公共卫生, 预警 */
    @Excel(name = "事件大类")
    private String eventType;

    /** 事件子类：暴雨, 火灾, 传染病, 红色预警等 */
    @Excel(name = "事件子类")
    private String eventSubtype;

    /** 事件等级：一般, 较大, 重大, 特大 */
    @Excel(name = "事件等级")
    private String eventLevel;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String description;

    /** 事件发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date occurTime;

    /** 事件上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /** 事件结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 事件地点 */
    @Excel(name = "事件地点")
    private String location;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 影响范围 */
    @Excel(name = "影响范围")
    private String affectedArea;

    /** 应对措施 */
    @Excel(name = "应对措施")
    private String responseMeasure;

    /** 伤亡情况 */
    @Excel(name = "伤亡情况")
    private String casualties;

    /** 经济损失 */
    @Excel(name = "经济损失")
    private String economicLoss;

    /** 状态：0未处理, 1处理中, 2已处理, 3已归档 */
    @Excel(name = "状态", readConverterExp = "0=未处理,1=处理中,2=已处理,3=已归档")
    private Integer status;

    /** 事件处置措施 */
    @Excel(name = "事件处置措施")
    private String feedback;

    /** 所属部门ID */
    @Excel(name = "所属部门ID")
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）")
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇")
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区")
    private String town;
}
