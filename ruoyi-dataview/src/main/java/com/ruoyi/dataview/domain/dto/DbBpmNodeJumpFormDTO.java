package com.ruoyi.dataview.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 运管服案件流程流转记录对象 db_bpm_node_jump
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbBpmNodeJumpFormDTO extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 流程定义id */
    private String procDefId;

    /** 流程实例id */
    private Long procInstId;

    /** 案件ID */
    private Long evtId;

    /** 环节名称 */
    private String nodeName;

    /** 环节定义id(环节key) */
    private String actDefId;

    /** 环节实例id(任务id) */
    private Long actInstId;

    /** 上一环节实例ID */
    private Long fromActInstId;

    /** 来件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arrivalTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 环节以用分钟数，当环节办结时由系统赋值。 */
    private Integer extUsedMin;

    /** 环节须去除的分钟数，如环节跨节假日等等情况时使用。 */
    private Integer extExcludeMin;

    /** 环节实际用的时间 */
    private Integer extRealUsedMin;

    /** 环节累计用时 */
    private Integer extCumulativeMin;

    /** 接受对象类型（1人 2角色 3部门） */
    private Integer receiveTypeId;

    /** 任务所属人id(角色id) */
    private Long ownerId;

    /** 任务所属人名称(角色名称) */
    private String ownerName;

    /** 处理人id */
    private Long handlerId;

    /** 处理人名称 */
    private String handlerName;

    /** 处理人部门id */
    private Long deptId;

    /** 处理人部门名称 */
    private String deptName;

    /** 处理时限（分钟） */
    private Integer extLimit;

    /** 即将超时时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date fastExpirationTime;

    /** 应完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shouldFinishedTime;

    /** 状态（是否智能分拨 0否  1是） 默认0 */
    private String checkStatus;

    /** 跳转类型 0:正常 1：回退 2：撤件 3监察召回 */
    private String jumpType;

    /** 创建人id */
    private Long createId;

    /** 更新人id */
    private Long updateId;

    /** 环节是否超时1为超时，0 不超时 */
    private Integer isTimeout;

    /** $column.columnComment */
    private String updateContent;

    /** 是否为挂账(0:否；1:是) */
    private Integer isOnAccount;

    /** 市区同步状态 0未同步 1已同步 */
    private Integer dockingStatus;

    /** 分区ID,目前根据结束状态分区，未结束案件分配到id=10000分区，结束案件进一步按年分区 */
    private Integer partitionId;

}
