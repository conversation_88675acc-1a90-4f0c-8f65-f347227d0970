package com.ruoyi.microPlatform.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.microPlatform.domain.SeActivityUser;
import com.ruoyi.microPlatform.domain.SeCommunityActivity;

/**
 * 社区活动Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface ISeCommunityActivityService 
{
    /**
     * 查询社区活动
     * 
     * @param id 社区活动主键
     * @return 社区活动
     */
    public SeCommunityActivity selectSeCommunityActivityById(Long id, SysUser user);

    /**
     * 校验社区活动是否存在
     *
     * @param seCommunityActivity 社区活动
     * @return 社区活动
     */
    public boolean checkSeCommunityActivity(SeCommunityActivity seCommunityActivity);

    /**
     * 查询社区活动列表
     * 
     * @param seCommunityActivity 社区活动
     * @return 社区活动集合
     */
    public List<SeCommunityActivity> selectSeCommunityActivityList(SeCommunityActivity seCommunityActivity);

    /**
     * 导入社区活动
     *
     * @param infos       社区活动列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importSeCommunityActivity(List<SeCommunityActivity> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    public int insertSeCommunityActivity(SeCommunityActivity seCommunityActivity);

    /**
     * 修改社区活动
     * 
     * @param seCommunityActivity 社区活动
     * @return 结果
     */
    public int updateSeCommunityActivity(SeCommunityActivity seCommunityActivity);

    /**
     * 批量删除社区活动
     * 
     * @param ids 需要删除的社区活动主键集合
     * @return 结果
     */
    public int deleteSeCommunityActivityByIds(Long[] ids);

    /**
     * 删除社区活动信息
     * 
     * @param id 社区活动主键
     * @return 结果
     */
    public int deleteSeCommunityActivityById(Long id);

    List<SeCommunityActivity> selectSeCommunityActivityListByUserId(SeCommunityActivity seCommunityActivity);

    Map<String, Integer> selectSeCommunityActivityMap(SeCommunityActivity seCommunityActivity);


    Map<String, Object> selectActCountList(Integer deptLevel, String county, String country,Integer taskStatusType);
}
