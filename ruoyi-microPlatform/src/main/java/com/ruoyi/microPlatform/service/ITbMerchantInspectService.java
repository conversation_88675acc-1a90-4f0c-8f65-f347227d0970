package com.ruoyi.microPlatform.service;

import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbMerchantInspect;

import java.util.List;
import java.util.Map;

/**
 * 门店巡查Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbMerchantInspectService 
{
    /**
     * 查询门店巡查
     * 
     * @param id 门店巡查主键
     * @return 门店巡查
     */
    public TbMerchantInspect selectTbMerchantInspectById(Long id);

    /**
     * 校验门店巡查是否存在
     *
     * @param tbMerchantInspect 门店巡查
     * @return 门店巡查
     */
    public boolean checkTbMerchantInspect(TbMerchantInspect tbMerchantInspect);

    /**
     * 查询门店巡查列表
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 门店巡查集合
     */
    public List<TbMerchantInspect> selectTbMerchantInspectList(TbMerchantInspect tbMerchantInspect);

    /**
     * 导入门店巡查
     *
     * @param infos       门店巡查列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantInspect(List<TbMerchantInspect> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    public Long insertTbMerchantInspect(TbMerchantInspect tbMerchantInspect);

    /**
     * 修改门店巡查
     * 
     * @param tbMerchantInspect 门店巡查
     * @return 结果
     */
    public int updateTbMerchantInspect(TbMerchantInspect tbMerchantInspect);

    /**
     * 批量删除门店巡查
     * 
     * @param ids 需要删除的门店巡查主键集合
     * @return 结果
     */
    public int deleteTbMerchantInspectByIds(Long[] ids);

    /**
     * 删除门店巡查信息
     * 
     * @param id 门店巡查主键
     * @return 结果
     */
    public int deleteTbMerchantInspectById(Long id);

    /**
     * 获取门店巡查 表累计次数和巡查记录表本月次数
     *
     */
    Map<String, Integer> selectTbMerchantInspectListCount(TbMerchantInspect tbMerchantInspect);

    Map<String, Object> selectSubmitCount(TbMerchantInspect tbMerchantInspect);


    List<Map<String, Object>> merchantInspectGroupMonth(LargeScreenInfo largeScreenInfo);


    int tbMerchantInspectCount(LargeScreenInfo largeScreenInfo);
}
