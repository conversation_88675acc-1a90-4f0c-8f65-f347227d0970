package com.ruoyi.microPlatform.controller;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.SeActivityUser;
import com.ruoyi.microPlatform.service.ISeActivityUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 报名者信息Controller
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@RestController
@RequestMapping("/sentiment/activityUser")
public class SeActivityUserController extends BaseController
{
    @Autowired
    private ISeActivityUserService seActivityUserService;

    /**
     * 查询报名者信息列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(SeActivityUser seActivityUser)
    {
        startPage();
        List<SeActivityUser> list = seActivityUserService.selectSeActivityUserList(seActivityUser);
        return getDataTable(list);
    }

    /**
     * 导出报名者信息列表
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:export')")
    @Log(title = "报名者信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SeActivityUser seActivityUser)
    {
        List<SeActivityUser> list = seActivityUserService.selectSeActivityUserList(seActivityUser);
        ExcelUtil<SeActivityUser> util = new ExcelUtil<SeActivityUser>(SeActivityUser.class);
        util.exportExcel(response, list, "报名者信息数据");
    }

    @Log(title = "报名者信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SeActivityUser> util = new ExcelUtil<SeActivityUser>(SeActivityUser.class);
        List<SeActivityUser> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = seActivityUserService.importSeActivityUser(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SeActivityUser> util = new ExcelUtil<SeActivityUser>(SeActivityUser.class);
        util.exportTemplateExcel(response, "报名者信息数据");
    }

    /**
     * 获取报名者信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(seActivityUserService.selectSeActivityUserById(id));
    }

    /**
     * 新增报名者信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:add')")
    @Log(title = "报名者信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @RepeatSubmit
    public AjaxResult add(@RequestBody SeActivityUser seActivityUser)
    {
        SysUser user = getLoginUser().getUser();
        if (ObjectUtil.isEmpty(seActivityUser.getUserId())){
            seActivityUser.setUserId(Math.toIntExact(getUserId()));
        }
        if (ObjectUtil.isEmpty(seActivityUser.getNickName())){
            seActivityUser.setNickName(user.getNickName());
        }
        if (ObjectUtil.isEmpty(seActivityUser.getPhone())){
            seActivityUser.setPhone(user.getPhonenumber());
        }
        if (ObjectUtil.isEmpty(seActivityUser.getUserAvatar())){
            seActivityUser.setUserAvatar(user.getAvatar());
        }
        if (seActivityUserService.checkSeActivityUser(seActivityUser)){
            return error("您已报名，不可重复报名！");
        }
        return toAjax(seActivityUserService.insertSeActivityUser(seActivityUser));
    }

    /**
     * 修改报名者信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:edit')")
    @Log(title = "报名者信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SeActivityUser seActivityUser)
    {
        seActivityUser.setUpdateBy(getUsername());
        if (seActivityUserService.checkSeActivityUser(seActivityUser)){
            return error("数据已存在！");
        }
        return toAjax(seActivityUserService.updateSeActivityUser(seActivityUser));
    }

    /**
     * 删除报名者信息
     */
    @PreAuthorize("@ss.hasPermi('sentiment:activityUser:remove')")
    @Log(title = "报名者信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(seActivityUserService.deleteSeActivityUserByIds(ids));
    }
}
