package com.ruoyi.dataview.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.dataview.domain.po.DbBpmNodeJump;
import com.ruoyi.dataview.mapper.DbUmEventMapper;
import com.ruoyi.dataview.domain.po.DbUmEvent;
import com.ruoyi.dataview.service.IDbUmEventService;

/**
 * 运管服案件案件数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@DataSource(DataSourceType.SLAVE)
public class DbUmEventServiceImpl implements IDbUmEventService {
    @Autowired
    private DbUmEventMapper dbUmEventMapper;

    /**
     * 查询运管服案件案件数据
     *
     * @param id 运管服案件案件数据主键
     * @return 运管服案件案件数据
     */
    @Override
    public DbUmEvent selectDbUmEventById(Long id) {
        return dbUmEventMapper.selectDbUmEventById(id);
    }

    /**
     * 校验运管服案件案件数据是否存在
     *
     * @param dbUmEvent
     * @return boolean
     */
    @Override
    public boolean checkDbUmEvent(DbUmEvent dbUmEvent) {
        DbUmEvent old = dbUmEventMapper.checkDbUmEventUnique(dbUmEvent);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询运管服案件案件数据列表
     *
     * @param dbUmEvent 运管服案件案件数据
     * @return 运管服案件案件数据
     */
    @Override
    public List<DbUmEvent> selectDbUmEventList(DbUmEvent dbUmEvent) {
        return dbUmEventMapper.selectDbUmEventList(dbUmEvent);
    }

    /**
     * 导入运管服案件案件数据
     *
     * @param infos           运管服案件案件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbUmEvent(List<DbUmEvent> infos, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入运管服案件案件数据数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int updateNum = 0;
        int failureNum = 0;

        for (DbUmEvent info : infos) {
            try {
                // 验证是否存在这个数据
                DbUmEvent old = dbUmEventMapper.checkDbUmEventUnique(info);
                if (ObjectUtil.isEmpty(old)) {
                    info.setCreateTime(new Date());
                    info.setCreateBy(operName);
                    dbUmEventMapper.insertDbUmEvent(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 导入成功");
                } else if (isUpdateSupport) {
                    info.setUpdateBy(operName);
                    info.setUpdateTime(new Date());
                    info.setId(old.getId());
                    dbUmEventMapper.updateDbUmEvent(info);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、数据 " + info.getId() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getId() + " 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage());
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增运管服案件案件数据
     *
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    @Transactional
    @Override
    public int insertDbUmEvent(DbUmEvent dbUmEvent) {
        dbUmEvent.setCreateTime(DateUtils.getNowDate());
        int rows = dbUmEventMapper.insertDbUmEvent(dbUmEvent);
        insertDbBpmNodeJump(dbUmEvent);
        return rows;
    }

    /**
     * 修改运管服案件案件数据
     *
     * @param dbUmEvent 运管服案件案件数据
     * @return 结果
     */
    @Transactional
    @Override
    public int updateDbUmEvent(DbUmEvent dbUmEvent) {
        dbUmEvent.setUpdateTime(DateUtils.getNowDate());
        dbUmEventMapper.deleteDbBpmNodeJumpByProcInstId(dbUmEvent.getId());
        insertDbBpmNodeJump(dbUmEvent);
        return dbUmEventMapper.updateDbUmEvent(dbUmEvent);
    }

    /**
     * 批量删除运管服案件案件数据
     *
     * @param ids 需要删除的运管服案件案件数据主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteDbUmEventByIds(Long[] ids) {
        dbUmEventMapper.deleteDbBpmNodeJumpByProcInstIds(ids);
        return dbUmEventMapper.deleteDbUmEventByIds(ids);
    }

    /**
     * 删除运管服案件案件数据信息
     *
     * @param id 运管服案件案件数据主键
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteDbUmEventById(Long id) {
        dbUmEventMapper.deleteDbBpmNodeJumpByProcInstId(id);
        return dbUmEventMapper.deleteDbUmEventById(id);
    }

    /**
     * 新增运管服案件流程流转记录信息
     *
     * @param dbUmEvent 运管服案件案件数据对象
     */
    public void insertDbBpmNodeJump(DbUmEvent dbUmEvent) {
        List<DbBpmNodeJump> dbBpmNodeJumpList = dbUmEvent.getDbBpmNodeJumpList();
        Long id = dbUmEvent.getId();
        if (ObjectUtil.isNotEmpty(dbBpmNodeJumpList)) {
            List<DbBpmNodeJump> list = new ArrayList<DbBpmNodeJump>();
            for (DbBpmNodeJump dbBpmNodeJump : dbBpmNodeJumpList) {
                dbBpmNodeJump.setProcInstId(id);
                list.add(dbBpmNodeJump);
            }
            if (list.size() > 0) {
                dbUmEventMapper.batchDbBpmNodeJump(list);
            }
        }
    }


    @Override
    public List<CommonBaseCount> dbUmEventBigCount(String classTypeName) {
        return dbUmEventMapper.dbUmEventBigCount(classTypeName);
    }
}
