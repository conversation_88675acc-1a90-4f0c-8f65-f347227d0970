package com.ruoyi.microPlatform.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 小区住户居民信息对象 tb_community_resident
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbCommunityResident extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 小区id
     */
//    @Excel(name = "住户ID", needMerge = true)
    private Long id;

    /**
     * 所属部门
     */
    private Long deptId;

    private List<Long> deptIdList;

    /**
     * 所属小区ID
     */
//    @Excel(name = "所属小区ID", needMerge = true)
    private Long communityId;

    /**
     * 所属县（市、区）
     */
    @Excel(name = "所属县（市、区）", needMerge = true, oneKey = "ONE_DEPT_LIST", childKey = "THREE_DEPT_LIST", linkageLevel = 2, isMultiLinkage = true, width = 18)
    private String county;

    /**
     * 所属街道/乡镇
     */
    @Excel(name = "所属街道/乡镇", needMerge = true, width = 18)
    private String country;

    /**
     * 所属村/社区
     */
    @Excel(name = "所属村/社区", needMerge = true, width = 18)
    private String town;

    /**
     * 所属网格
     */
    @Excel(name = "所属网格编码", needMerge = true)
    private String gridName;

    /**
     * 所属网格ID
     */
//    @Excel(name = "所属网格ID", needMerge = true)
    private Long gridId;

    /**
     * 小区名称
     */
    @Excel(name = "所在小区名称", needMerge = true)
    private String communityName;

    /**
     * 楼栋号
     */
    @Excel(name = "楼栋号", needMerge = true)
    private String buildingNum;

    /**
     * 单元号
     */
    @Excel(name = "单元号", needMerge = true)
    private String unitNum;

    /**
     * 楼层号
     */
    @Excel(name = "楼层号", needMerge = true)
    private String floorNum;

    /**
     * 户号
     */
    @Excel(name = "门牌号", needMerge = true)
    private String roomNum;

    /**
     * 住宅面积
     */
    @Excel(name = "住宅面积(㎡)", needMerge = true)
    private String residenceArea;

    /**
     * 常住人口
     */
    @Excel(name = "常住人口", needMerge = true)
    private Integer populationNum;

    /**
     * 户主
     */
    @Excel(name = "户主", needMerge = true)
    private String householder;

    @Excel(name = "户主联系电话", needMerge = true)
    private String phone;

    /**
     * 居住情况
     */
    @Excel(name = "居住情况", needMerge = true, comboReadDict = true, dictType = "live_type")
    private String residentialSituation;

    /**
     * 家庭情况
     */
//    @Excel(name = "家庭情况",needMerge =true)
    private String familySituation;

    /**
     * 是否困难
     */
    @Excel(name = "是否困难", needMerge = true, combo = {"是", "否"}, readConverterExp = "1=是,0=否")
    private Integer difficulty;

    /**
     * 居民状态（无需跟进处理 需跟进处理 已现场处理 居民建议 入户访问）
     */
//    @Excel(name = "入户访问状态", readConverterExp = "无=需跟进处理,需=跟进处理,已=现场处理,居=民建议,入=户访问", needMerge = true)
    private String status;

    /*居民住户成员*/
    @Excel(name = "居民住户成员信息")
    @ExcelIgnore
    private List<TbResidentMember> list;


    private String name;

    private String phoneUser;

    private String idCard;

    private String registerResidence;

    private Integer sort;


    /**
     * 所属小区的空间区域
     */
    //@Excel(name = "所属小区的空间区域", type = Excel.Type.EXPORT)
    private String geometry;

    public String getRegisterResidence() {
        return registerResidence;
    }

    private Integer offset;

    private Integer pageSize;


    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public void setRegisterResidence(String registerResidence) {
        this.registerResidence = registerResidence;
    }


    public Integer getSort() {
        return sort;
    }


    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public List<Long> getDeptIdList() {
        return deptIdList;
    }

    public void setDeptIdList(List<Long> deptIdList) {
        this.deptIdList = deptIdList;
    }

    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    public String getPhoneUser() {
        return phoneUser;
    }

    public void setPhoneUser(String phoneUser) {
        this.phoneUser = phoneUser;
    }

    public List<TbResidentMember> getList() {
        return list;
    }

    public void setList(List<TbResidentMember> list) {
        this.list = list;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setCommunityId(Long communityId) {
        this.communityId = communityId;
    }

    public Long getCommunityId() {
        return communityId;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCounty() {
        return county;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTown() {
        return town;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridId(Long gridId) {
        this.gridId = gridId;
    }

    public Long getGridId() {
        return gridId;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setBuildingNum(String buildingNum) {
        this.buildingNum = buildingNum;
    }

    public String getBuildingNum() {
        return buildingNum;
    }

    public void setUnitNum(String unitNum) {
        this.unitNum = unitNum;
    }

    public String getUnitNum() {
        return unitNum;
    }

    public void setFloorNum(String floorNum) {
        this.floorNum = floorNum;
    }

    public String getFloorNum() {
        return floorNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setResidenceArea(String residenceArea) {
        this.residenceArea = residenceArea;
    }

    public String getResidenceArea() {
        return residenceArea;
    }

    public void setPopulationNum(Integer populationNum) {
        this.populationNum = populationNum;
    }

    public Integer getPopulationNum() {
        return populationNum;
    }

    public void setHouseholder(String householder) {
        this.householder = householder;
    }

    public String getHouseholder() {
        return householder;
    }

    public void setResidentialSituation(String residentialSituation) {
        this.residentialSituation = residentialSituation;
    }

    public String getResidentialSituation() {
        return residentialSituation;
    }

    public void setFamilySituation(String familySituation) {
        this.familySituation = familySituation;
    }

    public String getFamilySituation() {
        return familySituation;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deptId", getDeptId())
                .append("communityId", getCommunityId())
                .append("county", getCounty())
                .append("country", getCountry())
                .append("town", getTown())
                .append("gridName", getGridName())
                .append("gridId", getGridId())
                .append("communityName", getCommunityName())
                .append("buildingNum", getBuildingNum())
                .append("unitNum", getUnitNum())
                .append("floorNum", getFloorNum())
                .append("roomNum", getRoomNum())
                .append("residenceArea", getResidenceArea())
                .append("populationNum", getPopulationNum())
                .append("householder", getHouseholder())
                .append("residentialSituation", getResidentialSituation())
                .append("familySituation", getFamilySituation())
                .append("difficulty", getDifficulty())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("status", getStatus())
                .toString();
    }
}
