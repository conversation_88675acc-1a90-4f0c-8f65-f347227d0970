<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DataSourceMapper">
    <select id="createTable">
        create table ${table.tableName}(
        <foreach collection="table.columnsList" item="columns" separator=",">
            ${columns.Field} ${columns.Type}
            <if test="columns.Length != null and columns.decimalPoint==0">
                (${columns.Length})
            </if>
            <if test="columns.Length != null and columns.decimalPoint>0">
                (${columns.Length},${columns.decimalPoint})
            </if>
            <if test="columns.Field!=null and columns.Field!='' and columns.Field=='varchar'">
                CHARACTER SET utf8 COLLATE utf8_general_ci
            </if>
            <if test="columns.columnIsNull !=null and columns.columnIsNull==true and columns.Default==null">
                DEFAULT NULL
            </if>
            <if test="columns.columnIsNull !=null and columns.columnIsNull==false">
                NOT NULL
            </if>
            <if test="columns.Default !=null and columns.Default!='' and columns.columnIsKey!=true">
                DEFAULT #{columns.Default}
            </if>
            <if test="columns.columnIsKey !=null and columns.columnIsKey==true and columns.Default==null">
                AUTO_INCREMENT
            </if>
            <if test="columns.Comment!=null and columns.Comment!=''">
                COMMENT #{columns.Comment}
            </if>
        </foreach>
        <foreach collection="table.columnsList" item="columns">
            <if test="columns.columnIsKey !=null and columns.columnIsKey==true">
                ,PRIMARY KEY(${columns.Field})
            </if>
        </foreach>
        )ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC
        <if test="table.tableComment!=null and table.tableComment!=''">
            COMMENT=#{table.tableComment}
        </if>
    </select>
    <update id="alterTableField">
        ALTER TABLE ${tableName}
        <if test="column.oldfield==null || column.oldfield==''">
            CHANGE ${column.Field} ${column.Type}
        </if>
        <if test="column.oldfield!=null and column.oldfield!=''">
            CHANGE ${column.oldfield} ${column.Field} ${column.Type}
        </if>
        <if test="column.Length!=null and column.decimalPoint==0">
            (${column.Length})
        </if>
        <if test="column.Length != null and column.decimalPoint>0">
            (${column.Length},${column.decimalPoint})
        </if>
        <if test="column.Field!=null and column.Field!='' and column=='varchar'">
            CHARACTER SET utf8 COLLATE utf8_general_ci
        </if>
        <if test="column.columnIsNull !=null and column.columnIsNull==true and column.Default==null">
            DEFAULT NULL
        </if>
        <if test="column.columnIsNull !=null and column.columnIsNull==false">
            NOT NULL
        </if>
        <if test="column.Default !=null and column.Default!='' and column.columnIsKey!=true">
            DEFAULT #{column.Default}
        </if>
        <if test="column.columnIsKey !=null and column.columnIsKey==true and column.Default==null">
            AUTO_INCREMENT PRIMARY KEY
        </if>
        <if test="column.Comment!=null and column.Comment!=''">
            COMMENT #{column.Comment}
        </if>
    </update>
    <delete id="dropTableField">
        ALTER TABLE ${tableName}
        <foreach collection="dropFields" item="field" separator=",">
            DROP ${field}
        </foreach>
    </delete>
    <delete id="dropTable">
        DROP table ${tableName}
    </delete>
    <delete id="truncateTable">
        truncate table ${tableName}
    </delete>
    <select id="addTableField">
        ALTER TABLE ${table.tableName} ADD
        <foreach collection="table.columnsList" item="columns" separator="," open="(" close=")">
            ${columns.Field} ${columns.Type}
            <if test="columns.Length != null and columns.decimalPoint==0">
                (${columns.Length})
            </if>
            <if test="columns.Length != null and columns.decimalPoint>0">
                (${columns.Length},${columns.decimalPoint})
            </if>
            <if test="columns.Field!=null and columns.Field!='' and columns=='varchar'">
                CHARACTER SET utf8 COLLATE utf8_general_ci
            </if>
            <if test="columns.columnIsNull !=null and columns.columnIsNull==true and columns.Default==null">
                DEFAULT NULL
            </if>
            <if test="columns.columnIsNull !=null and columns.columnIsNull==false">
                NOT NULL
            </if>
            <if test="columns.Default !=null and columns.Default!='' and columns.columnIsKey!=true">
                DEFAULT #{columns.Default}
            </if>
            <if test="column.columnIsKey !=null and column.columnIsKey==true and column.Default==null">
                AUTO_INCREMENT PRIMARY KEY
            </if>
            <if test="columns.Comment!=null and columns.Comment!=''">
                COMMENT #{columns.Comment}
            </if>
        </foreach>
    </select>
    <select id="alterTableComment">
        ALTER TABLE ${table.tableName}
            COMMENT #{table.tableComment}
    </select>
</mapper>