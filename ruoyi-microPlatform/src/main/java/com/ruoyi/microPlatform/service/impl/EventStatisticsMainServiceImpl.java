package com.ruoyi.microPlatform.service.impl;

import com.ruoyi.microPlatform.bigdata.param.BigdataParam;
import com.ruoyi.microPlatform.mapper.EventStatisticsMainMapper;
import com.ruoyi.microPlatform.service.IEventStatisticsMainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 主库事项统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-12
 */
@Service
public class EventStatisticsMainServiceImpl implements IEventStatisticsMainService {

    @Autowired
    private EventStatisticsMainMapper eventStatisticsMainMapper;

    @Override
    public Map<String, Object> getMainEventStatistics(BigdataParam bigdataParam) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取综合事项统计
        Integer comprehensiveTodayCount = eventStatisticsMainMapper.getComprehensiveEventTodayCount(bigdataParam);
        Integer comprehensivePendingCount = eventStatisticsMainMapper.getComprehensiveEventPendingCount(bigdataParam);
        Integer comprehensiveTotalHandledCount = eventStatisticsMainMapper.getComprehensiveEventTotalHandledCount(bigdataParam);
        Integer comprehensiveCompletedCount = eventStatisticsMainMapper.getComprehensiveEventCompletedCount(bigdataParam);
        Map<String, Object> comprehensiveSatisfactionStats = eventStatisticsMainMapper.getComprehensiveEventSatisfactionStats(bigdataParam);
        
        // 获取舆情事项统计
        Integer majorOpinionTodayCount = eventStatisticsMainMapper.getMajorOpinionEventTodayCount(bigdataParam);
        Integer majorOpinionTotalCount = eventStatisticsMainMapper.getMajorOpinionEventTotalCount(bigdataParam);
        
        // 汇总数据
        result.put("comprehensiveTodayCount", comprehensiveTodayCount != null ? comprehensiveTodayCount : 0);
        result.put("comprehensivePendingCount", comprehensivePendingCount != null ? comprehensivePendingCount : 0);
        result.put("comprehensiveTotalHandledCount", comprehensiveTotalHandledCount != null ? comprehensiveTotalHandledCount : 0);
        result.put("comprehensiveCompletedCount", comprehensiveCompletedCount != null ? comprehensiveCompletedCount : 0);
        result.put("comprehensiveSatisfactionStats", comprehensiveSatisfactionStats != null ? comprehensiveSatisfactionStats : new HashMap<>());
        
        result.put("majorOpinionTodayCount", majorOpinionTodayCount != null ? majorOpinionTodayCount : 0);
        result.put("majorOpinionTotalCount", majorOpinionTotalCount != null ? majorOpinionTotalCount : 0);
        
        return result;
    }
}
