package com.ruoyi.microPlatform.service;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.microPlatform.domain.TbMerchantInfo;

/**
 * 门店/商户信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface ITbMerchantInfoService 
{
    /**
     * 查询门店/商户信息
     * 
     * @param id 门店/商户信息主键
     * @return 门店/商户信息
     */
    public TbMerchantInfo selectTbMerchantInfoById(Long id);

    /**
     * 校验门店/商户信息是否存在
     *
     * @param tbMerchantInfo 门店/商户信息
     * @return 门店/商户信息
     */
    public boolean checkTbMerchantInfo(TbMerchantInfo tbMerchantInfo);

    /**
     * 查询门店/商户信息列表
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 门店/商户信息集合
     */
    public List<TbMerchantInfo> selectTbMerchantInfoList(TbMerchantInfo tbMerchantInfo);

    public Integer selectTbMerchantInfoCount(TbMerchantInfo tbMerchantInfo);

    /**
     * 导入门店/商户信息
     *
     * @param infos       门店/商户信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbMerchantInfo(List<TbMerchantInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept);

    /**
     * 新增门店/商户信息
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    public int insertTbMerchantInfo(TbMerchantInfo tbMerchantInfo);

    /**
     * 修改门店/商户信息
     * 
     * @param tbMerchantInfo 门店/商户信息
     * @return 结果
     */
    public int updateTbMerchantInfo(TbMerchantInfo tbMerchantInfo);

    /**
     * 批量删除门店/商户信息
     * 
     * @param ids 需要删除的门店/商户信息主键集合
     * @return 结果
     */
    public int deleteTbMerchantInfoByIds(Long[] ids);

    /**
     * 删除门店/商户信息信息
     * 
     * @param id 门店/商户信息主键
     * @return 结果
     */
    public int deleteTbMerchantInfoById(Long id);
}
