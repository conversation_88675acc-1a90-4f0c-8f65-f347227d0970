package com.ruoyi.microPlatform.domain;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 基层党组织管理对象 pb_party_org
 */
@Data
public class PbPartyOrg extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 党组织编码 */
    private String orgCode;

    /** 党组织名称 */
    private String orgName;

    /** 党组织类型（机关事业单位、国有企业、非公企业、社会组织、网格） */
    private String orgType;

    /** 组织层级（1党委/2党总支/3党支部） */
    private Integer orgLevel;

    /** 上级党组织ID */
    private Long parentOrgId;

    /** 组织路径（便于层级查询） */
    private String parentPath;

    /** 所属部门ID */
    private Long deptId;

    /** 所属县（市、区） */
    private String county;

    /** 所属街道/乡镇 */
    private String country;

    /** 所属村/社区 */
    private String town;

    /** 成立时间 */
    private Date establishDate;

    /** 批准成立单位 */
    private String approveUnit;

    /** 党员活动中心地址 */
    private String activityCenter;

    /** 党员人数 */
    private Integer memberCount;

    /** 负责人姓名 */
    private String leaderName;

    /** 负责人电话 */
    private String leaderPhone;

    /** 状态（1正常 0撤销） */
    private Integer status;
}
