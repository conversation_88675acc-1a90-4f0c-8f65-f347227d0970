package com.ruoyi.dataview.service;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import com.ruoyi.dataview.domain.po.DbCityComponent;

import java.util.List;

/**
 * 运管服部件数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
public interface IDbCityComponentService 
{
    /**
     * 查询运管服部件数据
     * 
     * @param id 运管服部件数据主键
     * @return 运管服部件数据
     */
    public DbCityComponent selectDbCityComponentById(Long id);

    /**
     * 校验运管服部件数据是否存在
     *
     * @param dbCityComponent 运管服部件数据
     * @return 运管服部件数据
     */
    public boolean checkDbCityComponent(DbCityComponent dbCityComponent);

    /**
     * 查询运管服部件数据列表
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 运管服部件数据集合
     */
    public List<DbCityComponent> selectDbCityComponentList(DbCityComponent dbCityComponent);

    /**
     * 导入运管服部件数据
     *
     * @param infos       运管服部件数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importDbCityComponent(List<DbCityComponent> infos, Boolean isUpdateSupport, String operName);

    /**
     * 新增运管服部件数据
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    public int insertDbCityComponent(DbCityComponent dbCityComponent);

    /**
     * 修改运管服部件数据
     * 
     * @param dbCityComponent 运管服部件数据
     * @return 结果
     */
    public int updateDbCityComponent(DbCityComponent dbCityComponent);

    /**
     * 批量删除运管服部件数据
     * 
     * @param ids 需要删除的运管服部件数据主键集合
     * @return 结果
     */
    public int deleteDbCityComponentByIds(Long[] ids);

    /**
     * 删除运管服部件数据信息
     * 
     * @param id 运管服部件数据主键
     * @return 结果
     */
    public int deleteDbCityComponentById(Long id);

    /**
     *
     */
    Integer selectDbCityComponentCount(DbCityComponent dbCityComponent);


    List<CommonBaseCount> selectCountGroupByMajorCode();
}
