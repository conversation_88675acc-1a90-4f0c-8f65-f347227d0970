package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbRequestAudit;
import com.ruoyi.microPlatform.service.ITbRequestAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 导出请求审核过程Controller
 * 
 * <AUTHOR>
 * @date 2025-04-11 10:02:30
 */
@RestController
@RequestMapping("/gridData/requestAudit")
public class TbRequestAuditController extends BaseController
{
    @Autowired
    private ITbRequestAuditService tbRequestAuditService;

    /**
     * 查询导出请求审核过程列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbRequestAudit tbRequestAudit)
    {
        startPage();
        List<TbRequestAudit> list = tbRequestAuditService.selectTbRequestAuditList(tbRequestAudit);
        return getDataTable(list);
    }

    /**
     * 导出导出请求审核过程列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:export')")
    @Log(title = "导出请求审核过程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbRequestAudit tbRequestAudit)
    {
        List<TbRequestAudit> list = tbRequestAuditService.selectTbRequestAuditList(tbRequestAudit);
        ExcelUtil<TbRequestAudit> util = new ExcelUtil<TbRequestAudit>(TbRequestAudit.class);
        util.exportExcel(response, list, "导出请求审核过程数据");
    }

    @Log(title = "导出请求审核过程", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbRequestAudit> util = new ExcelUtil<TbRequestAudit>(TbRequestAudit.class);
        List<TbRequestAudit> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbRequestAuditService.importTbRequestAudit(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbRequestAudit> util = new ExcelUtil<TbRequestAudit>(TbRequestAudit.class);
        util.exportTemplateExcel(response, "导出请求审核过程数据");
    }

    /**
     * 获取导出请求审核过程详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbRequestAuditService.selectTbRequestAuditById(id));
    }

    /**
     * 新增导出请求审核过程
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:add')")
    @Log(title = "导出请求审核过程", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbRequestAudit tbRequestAudit)
    {
        tbRequestAudit.setCreateBy(getUsername());
        if (tbRequestAuditService.checkTbRequestAudit(tbRequestAudit)){
            return error("数据已存在！");
        }
        return toAjax(tbRequestAuditService.insertTbRequestAudit(tbRequestAudit));
    }

    /**
     * 修改导出请求审核过程
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:edit')")
    @Log(title = "导出请求审核过程", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbRequestAudit tbRequestAudit)
    {
        tbRequestAudit.setUpdateBy(getUsername());
        if (tbRequestAuditService.checkTbRequestAudit(tbRequestAudit)){
            return error("数据已存在！");
        }
        return toAjax(tbRequestAuditService.updateTbRequestAudit(tbRequestAudit));
    }

    /**
     * 删除导出请求审核过程
     */
    @PreAuthorize("@ss.hasPermi('gridData:requestAudit:remove')")
    @Log(title = "导出请求审核过程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbRequestAuditService.deleteTbRequestAuditByIds(ids));
    }
}
