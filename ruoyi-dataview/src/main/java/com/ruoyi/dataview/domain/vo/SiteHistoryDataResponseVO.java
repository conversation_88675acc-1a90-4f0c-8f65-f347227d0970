package com.ruoyi.dataview.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SiteHistoryDataResponseVO {

    private Integer total;

    private List<DataItem> rows;

    @Data
    public static class DataItem {
       private String id;
       private String serial;
       private String signal;
       private String st;
       private String stt;
       @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
       private Date sttTime;
       private String tt;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
       private String ttTime;
       private String ut;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
       private String utTime;
       private String vq;
       private String vt;
       private String wlih1;
       private String z;
       private String zt;
    }
}
