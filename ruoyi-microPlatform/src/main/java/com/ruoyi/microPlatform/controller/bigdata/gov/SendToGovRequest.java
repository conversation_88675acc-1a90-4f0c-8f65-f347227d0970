package com.ruoyi.microPlatform.controller.bigdata.gov;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 市长信箱请求工具类
 */
@Slf4j
@Component
public class SendToGovRequest {

    @Resource
    private RedisCache redisCache;
    @Resource
    private ObjectMapper objectMapper;

    private static final String authRedisKey = "algorithm:system:auth:doubleMai:";

    /**
     * 发送请求到市长信箱接口。
     * 使用Retryable注解，针对UnauthorizedException异常进行重试，最大尝试次数为1，退避策略为延迟100毫秒。
     *
     * @param apiEnum       市长信箱接口枚举，定义了请求的URL路径、HTTP方法和数据格式。
     * @param commonBaseReq 基础请求对象，根据不同的API枚举可能有不同的参数要求。
     * @param bizAuthDto    业务认证DTO，包含访问市长信箱接口所需的认证信息和第三方URL。
     * @return 返回市长信箱接口的响应字符串。
     * @throws Exception 如果发生除UnauthorizedException之外的异常。
     */
    public String sendRequest(GovApiEnum apiEnum, HashMap<String, Object> queryMap,
                              GovBaseReq commonBaseReq, BizAuthDto bizAuthDto) throws Exception {
        String resp = null;
        // 构造请求的完整URL
        String url = (bizAuthDto.getThirdUrl().endsWith("/") ? bizAuthDto.getThirdUrl() : bizAuthDto.getThirdUrl() + "/") + apiEnum.getPath();
        Map<String, Object> specialParam = null;
        try {
            // 设置请求头部，包含Cookie信息（accessToken）
            // 放入token
            Map<String, String> headers = new HashMap<>();
            if (queryMap != null && !queryMap.isEmpty()) {
                StringBuilder queryString = new StringBuilder();
                for (HashMap.Entry<String, Object> entry : queryMap.entrySet()) {
                    if (queryString.length() > 0) {
                        queryString.append("&");
                    }
                    queryString.append(entry.getKey())
                            .append("=")
                            .append(StrUtil.toString(entry.getValue())); // 确保值转为字符串
                }
                url = url + "?" + queryString;
            }
            HttpRequest httpRequest = new HttpRequest(url);
            httpRequest.addHeaders(headers);
            // 根据API枚举确定请求的数据格式，是JSON还是表单
            if (apiEnum.getDataWhere() == ContentType.JSON) {
                httpRequest.body(JSON.toJSONString(commonBaseReq));
            } else {
//                httpRequest.contentType("multipart/form-data");
                httpRequest.contentType("multipart/form-data; boundary=----WebKitFormBoundaryHs3qRROGw6BUdiEV");
                // 处理参数转换
                specialParam = objectMapper.convertValue(commonBaseReq, new TypeReference<Map<String, Object>>() {
                });
                // 先添加普通参数
                httpRequest.form(specialParam);
            }
            // 设置请求方法（GET、POST等）
            httpRequest.setMethod(apiEnum.getHttpMethod());
            // 记录请求日志
            log.info("请求市长信箱接口开始...url={}, header={}, req={}", httpRequest.getUrl(), headers, specialParam);
            // 发起请求，获取响应
            HttpResponse execute = httpRequest.execute();
            resp = execute.body();
            int status = execute.getStatus();
            if (resp.length() > 3000){
                log.info("请求市长信箱接口结束...url={}, header={}, req={},respLen={},status={};", httpRequest.getUrl(), headers, specialParam, resp.length(), status);
            }else {
                log.info("请求市长信箱接口结束...url={}, header={}, req={},resp={},status={};", httpRequest.getUrl(), headers, specialParam, resp.replaceAll("\n", ""), status);
            }
        } catch (Exception e) {
            // 记录其他异常日志，并返回响应字符串
            log.info("请求市长信箱接口出错...url={}, req={}, resp={}, msg={}", url, specialParam, resp, e.getMessage(), e);
            return resp;
        }
        return resp;
    }


}