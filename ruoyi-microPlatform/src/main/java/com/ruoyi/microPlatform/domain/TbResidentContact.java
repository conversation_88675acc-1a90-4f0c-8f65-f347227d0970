package com.ruoyi.microPlatform.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 居民住户联络沟通信息对象 tb_resident_contact
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public class TbResidentContact extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 所属部门 */
    private Long deptId;

    /** 所属县（市、区） */
    @Excel(name = "所属县（市、区）", width = 18)
    private String county;

    /** 所属街道/乡镇 */
    @Excel(name = "所属街道/乡镇", width = 18)
    private String country;

    /** 所属村/社区 */
    @Excel(name = "所属村/社区", width = 18)
    private String town;

    /** 所属网格 */
    @Excel(name = "所属网格")
    private String gridName;

    /** 所属网格 */
    private Long gridId;

    //小区名称
    @Excel(name = "小区名称")
    private String  communityName;


    @Excel(name = "门牌号")
    private String  roomNum;
    //户号
    //户主
    @Excel(name = "户主")
    private String  householder;

    /** 关联住户 */
    private Long residentId;

    /** 联络沟通事宜 */
    @Excel(name = "联络沟通事宜")
    private String contactDesc;

    /** 相关图片 */
    @Excel(name = "相关图片",cellType = Excel.ColumnType.IMAGE)
    private String accessory;

    /** 居住情况（常住户 租户 空户 低保户 独居老人（60-70） ...） */
    @Excel(name = "居住情况")
    private String residentialSituation;

    /** 其他情况（党员 退役军人 少数党派 少数民族） */
    @Excel(name = "其他情况")
    private String otherSituation;

    @Excel(name = "是否为矛盾纠纷", combo = {"是", "否"}, readConverterExp = "1=是,0=否")
    private Integer isIssue;

    /** 联络情况（无需跟进处理 需跟进处理 已现场处理 居民建议 入户访问） */
    @Excel(name = "入户走访状态")
    private String contractSituation;

    @Excel(name = "走访人")
    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入户走访时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;



    private TbCommunityResident tbCommunityResident;


    /** 创建者账号 */
    private Long userId;

    //查询时根据这个判断是否查询本月数据  true 是 false 否
    private boolean isCurrentMonth;

    /**
     * 后续动态数量
     */
    private Integer handleCount;

    //是否为矛盾纠纷（0否，1=是）

    public void setAccessory(String accessory)
    {
        this.accessory = accessory;
        if (accessory != null && !this.accessory.equals("")){
            List<String> stringList = Arrays.asList(accessory.split(","));
            this.setFileList(stringList);
            if (stringList.size() > 0){
                this.setFirstFile(stringList.get(0));
            }
        }
    }

    public TbCommunityResident getTbCommunityResident() {
        return tbCommunityResident;
    }

    public void setTbCommunityResident(TbCommunityResident tbCommunityResident) {
        this.tbCommunityResident = tbCommunityResident;
    }

    public String getAccessory()
    {
        return accessory;
    }

    public String getCreateBy()
    {
        return createBy;
    }

    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setCreateTime(Date createTime)
    {
        this.createTime = createTime;
    }


    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getHouseholder() {
        return householder;
    }

    public void setHouseholder(String householder) {
        this.householder = householder;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public boolean isCurrentMonth() {
        return isCurrentMonth;
    }

    public void setCurrentMonth(boolean currentMonth) {
        isCurrentMonth = currentMonth;
    }

    public Integer getHandleCount() {
        return handleCount;
    }

    public void setHandleCount(Integer handleCount) {
        this.handleCount = handleCount;
    }

    public boolean getIsCurrentMonth() {
        return isCurrentMonth;
    }

    public void setIsCurrentMonth(boolean isCurrentMonth) {
        this.isCurrentMonth = isCurrentMonth;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setGridName(String gridName) 
    {
        this.gridName = gridName;
    }

    public String getGridName() 
    {
        return gridName;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Integer getIsIssue() {
        return isIssue;
    }

    public void setIsIssue(Integer isIssue) {
        this.isIssue = isIssue;
    }

    public Long getGridId()
    {
        return gridId;
    }
    public void setResidentId(Long residentId) 
    {
        this.residentId = residentId;
    }

    public Long getResidentId() 
    {
        return residentId;
    }
    public void setContactDesc(String contactDesc) 
    {
        this.contactDesc = contactDesc;
    }

    public String getContactDesc() 
    {
        return contactDesc;
    }
    public void setResidentialSituation(String residentialSituation)
    {
        this.residentialSituation = residentialSituation;
    }

    public String getResidentialSituation() 
    {
        return residentialSituation;
    }
    public void setOtherSituation(String otherSituation) 
    {
        this.otherSituation = otherSituation;
    }

    public String getOtherSituation() 
    {
        return otherSituation;
    }
    public void setContractSituation(String contractSituation) 
    {
        this.contractSituation = contractSituation;
    }

    public String getContractSituation() 
    {
        return contractSituation;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("county", getCounty())
            .append("country", getCountry())
            .append("town", getTown())
            .append("gridName", getGridName())
            .append("gridId", getGridId())
            .append("residentId", getResidentId())
            .append("contactDesc", getContactDesc())
            .append("accessory", getAccessory())
            .append("residentialSituation", getResidentialSituation())
            .append("otherSituation", getOtherSituation())
            .append("contractSituation", getContractSituation())
            .append("userId", getUserId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
