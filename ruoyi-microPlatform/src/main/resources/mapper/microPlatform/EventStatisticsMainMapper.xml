<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.EventStatisticsMainMapper">

    <!-- 获取综合事项当日新增数量 -->
    <select id="getComprehensiveEventTodayCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM biz_comprehensive_event 
        WHERE DATE(report_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取综合事项待办数量 -->
    <select id="getComprehensiveEventPendingCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM biz_comprehensive_event 
        WHERE event_status = 0
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取综合事项累计办理数量 -->
    <select id="getComprehensiveEventTotalHandledCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM biz_comprehensive_event 
        WHERE event_status IN (1, 2)
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取综合事项办结数量 -->
    <select id="getComprehensiveEventCompletedCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM biz_comprehensive_event 
        WHERE event_status = 2
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取综合事项满意度统计 -->
    <select id="getComprehensiveEventSatisfactionStats" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalSatisfactionCount,
            SUM(CASE WHEN satisfaction = 1 THEN 1 ELSE 0 END) as satisfiedCount,
            SUM(CASE WHEN satisfaction = 2 THEN 1 ELSE 0 END) as basicallySatisfiedCount,
            SUM(CASE WHEN satisfaction = 3 THEN 1 ELSE 0 END) as unsatisfiedCount,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND((SUM(CASE WHEN satisfaction IN (1, 2) THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2)
                ELSE 0 
            END as satisfactionRate
        FROM biz_comprehensive_event 
        WHERE is_satisfaction = 1
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取舆情事项当日新增数量 -->
    <select id="getMajorOpinionEventTodayCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM major_opinion_events 
        WHERE DATE(create_time) = CURDATE()
        <if test="param.deptId != null">
            AND dept_id = #{param.deptId}
        </if>
        <if test="param.county != null and param.county != ''">
            AND county = #{param.county}
        </if>
    </select>

    <!-- 获取舆情事项累计数量 -->
    <select id="getMajorOpinionEventTotalCount" parameterType="com.ruoyi.microPlatform.bigdata.param.BigdataParam" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM major_opinion_events
        <where>
            <if test="param.deptId != null">
                AND dept_id = #{param.deptId}
            </if>
            <if test="param.county != null and param.county != ''">
                AND county = #{param.county}
            </if>
        </where>
    </select>

</mapper>
