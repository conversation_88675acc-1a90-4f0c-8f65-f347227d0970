<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.microPlatform.mapper.TbGridUserCommunityMapper">





    <resultMap type="TbGridUserCommunity" id="TbGridUserCommunityResult">
        <result property="id" column="id"/>
        <result property="gridUserId" column="grid_user_id"/>
        <result property="communityId" column="community_id"/>
        <association property="tbCommunityInfo" column="community_id" javaType="TbCommunityInfo" resultMap="TbCommunityInfoResult"/>
    </resultMap>

    <resultMap type="TbCommunityInfo" id="TbCommunityInfoResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="county" column="county"/>
        <result property="country" column="country"/>
        <result property="town" column="town"/>
        <result property="gridId" column="grid_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="communityName" column="community_name"/>
        <result property="communitySort" column="community_sort"/>
        <result property="geometry" column="geometry"/>
        <result property="area" column="area"/>
        <result property="merchantNum" column="merchant_num"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbGridUserCommunityVo">
        select id, grid_user_id, community_id from tb_grid_user_community
    </sql>


    <select id="selectTbGridUserCommunityList" parameterType="TbGridUserCommunity" resultMap="TbGridUserCommunityResult">
        select
        ci.dept_id, ci.county, ci.country, ci.town, ci.grid_id, ci.grid_name, ci.community_name, ci.community_sort,
        ci.geometry, ci.area, ci.merchant_num, ci.remark, ci.create_by, ci.create_time, ci.update_by, ci.update_time,
        gu.id, gu.grid_user_id, gu.community_id
        from  tb_community_info ci  join tb_grid_user_community gu on
        ci.id = gu.community_id
        <where>
            <if test="gridUserId != null ">and gu.grid_user_id = #{gridUserId} </if>
            <if test="communityId != null ">and gu.community_id = #{communityId}</if>
        </where>
    </select>


    <select id="selectTbGridUserCommunityById" parameterType="Long" resultMap="TbGridUserCommunityResult">
        <include refid="selectTbGridUserCommunityVo"/>
        where id = #{id}
    </select>

    <select id="checkTbGridUserCommunityUnique" parameterType="TbGridUserCommunity" resultMap="TbGridUserCommunityResult">
        select id from tb_grid_user_community where grid_user_id = #{gridUserId} and community_id = #{communityId}
        <if test="id != null">
            and id != #{id}
        </if>
    </select>

    <insert id="insertTbGridUserCommunity" parameterType="TbGridUserCommunity" useGeneratedKeys="true" keyProperty="id">
        insert into tb_grid_user_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="gridUserId != null">grid_user_id,</if>
            <if test="communityId != null">community_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="gridUserId != null">#{gridUserId},</if>
            <if test="communityId != null">#{communityId},</if>
        </trim>
    </insert>

    <update id="updateTbGridUserCommunity" parameterType="TbGridUserCommunity">
        update tb_grid_user_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="gridUserId != null">grid_user_id = #{gridUserId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTbGridUserCommunityById" parameterType="Long">
        delete from tb_grid_user_community where id = #{id}
    </delete>

    <delete id="deleteTbGridUserCommunityByUserId" parameterType="Long">
        delete from tb_grid_user_community where grid_user_id = #{id}
    </delete>

    <delete id="deleteTbGridUserCommunityByIds" parameterType="String">
        delete from tb_grid_user_community where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteTbGridUserCommunityByUserIds" parameterType="String">
        delete from tb_grid_user_community where grid_user_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectPersonCount" parameterType="String" resultType="integer">
        select count(*) from tb_grid_user_community where community_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>