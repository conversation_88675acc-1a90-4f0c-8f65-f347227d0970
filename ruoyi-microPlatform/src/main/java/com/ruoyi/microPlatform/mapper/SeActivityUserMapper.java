package com.ruoyi.microPlatform.mapper;

import java.util.List;
import com.ruoyi.microPlatform.domain.SeActivityUser;

/**
 * 报名者信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
public interface SeActivityUserMapper 
{
    /**
     * 查询报名者信息
     * 
     * @param id 报名者信息主键
     * @return 报名者信息
     */
    public SeActivityUser selectSeActivityUserById(Long id);

    /**
     * 校验报名者信息是否唯一
     *
     * @param seActivityUser
     * @return 报名者信息
     */
    public SeActivityUser checkSeActivityUserUnique(SeActivityUser seActivityUser);

    /**
     * 查询报名者信息列表
     * 
     * @param seActivityUser 报名者信息
     * @return 报名者信息集合
     */
    public List<SeActivityUser> selectSeActivityUserList(SeActivityUser seActivityUser);

    /**
     * 新增报名者信息
     * 
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    public int insertSeActivityUser(SeActivityUser seActivityUser);

    /**
     * 修改报名者信息
     * 
     * @param seActivityUser 报名者信息
     * @return 结果
     */
    public int updateSeActivityUser(SeActivityUser seActivityUser);

    /**
     * 删除报名者信息
     * 
     * @param id 报名者信息主键
     * @return 结果
     */
    public int deleteSeActivityUserById(Long id);

    /**
     * 批量删除报名者信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSeActivityUserByIds(Long[] ids);
}
