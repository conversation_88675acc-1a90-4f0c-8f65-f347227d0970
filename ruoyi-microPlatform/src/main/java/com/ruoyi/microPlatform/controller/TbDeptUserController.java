package com.ruoyi.microPlatform.controller;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.RepeatSubmit;

import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbDeptUser;
import com.ruoyi.microPlatform.service.ITbDeptUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 社区干部信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@RestController
@RequestMapping("/gridData/deptUser")
public class TbDeptUserController extends BaseController {
    @Autowired
    private ITbDeptUserService tbDeptUserService;
    @Autowired
    private ISysConfigService configService;

    /**
     * 查询社区干部信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbDeptUser tbDeptUser) {
        startPage();
        handle(tbDeptUser);
        List<TbDeptUser> list = tbDeptUserService.selectTbDeptUserList(tbDeptUser);
        return getDataTable(list);
    }

    /**
     * 查询社区干部
     *
     * @param tbDeptUser
     */
    public void handle(TbDeptUser tbDeptUser) {
        if (!SecurityUtils.isAdmin(getUserId())) {
            if (ObjectUtil.isEmpty(tbDeptUser.getDeptId())) {
                tbDeptUser.setDeptId(getDataDeptId());
            }
        }
    }


    /**
     * 导出社区干部信息列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:export')")
    @Log(title = "社区干部信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestParam(required = false) Integer pageNum,
                       @RequestParam(required = false) Integer pageSize, @ModelAttribute TbDeptUser tbDeptUser) {
        if (ObjectUtil.isNotEmpty(tbDeptUser.getIsExportAll()) && tbDeptUser.getIsExportAll() == 0) {
            startPage(pageNum, pageSize);
        }
        handle(tbDeptUser);
        List<TbDeptUser> list = tbDeptUserService.selectTbDeptUserList(tbDeptUser);
        ExcelUtil<TbDeptUser> util = new ExcelUtil<TbDeptUser>(TbDeptUser.class);
        util.exportExcel(response, list, "社区干部信息数据");
    }

    @GetMapping("/exportCount")
    public AjaxResult exportCount(TbDeptUser tbDeptUser) {
        if (ObjectUtil.isNotEmpty(tbDeptUser.getIsExportAll()) && tbDeptUser.getIsExportAll() == 0) {
            startPage();
        }
        handle(tbDeptUser);
        Integer integer = tbDeptUserService.selectExportDeptUserCount(tbDeptUser);
        String exportData = configService.selectConfigByKey("sys.user.exportData");
        return exportDataMethod(integer, exportData, tbDeptUser);
    }

    @Log(title = "社区干部信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<TbDeptUser> util = new ExcelUtil<TbDeptUser>(TbDeptUser.class);
        List<TbDeptUser> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        SysDept userDept = getDept(getDataDeptId());
        String message = tbDeptUserService.importTbDeptUser(infos, updateSupport, operName, userDept);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TbDeptUser> util = new ExcelUtil<TbDeptUser>(TbDeptUser.class);
        util.exportTemplateExcel(response, "社区干部信息数据");
    }

    /**
     * 获取社区干部信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(tbDeptUserService.selectTbDeptUserById(id));
    }

    public void getDeptInfo(TbDeptUser tbDeptUser) {
        if (ObjectUtil.isEmpty(tbDeptUser.getDeptId())) {
            tbDeptUser.setDeptId(getDataDeptId());
        }
        SysDept dept = getDept(tbDeptUser.getDeptId());
        if (ObjectUtil.isNotEmpty(dept)) {
            String[] split = dept.getAncestorsName().split(",");
            if (split.length >= 2) {
                tbDeptUser.setCounty(split[1]);
                if (split.length >= 3) {
                    tbDeptUser.setCountry(split[2]);
                    if (split.length >= 4) {
                        tbDeptUser.setTown(split[3]);
                    }
                }
            }
        }
        TbDeptUser newData = new TbDeptUser();
        newData.setName(tbDeptUser.getName());
        newData.setCounty(tbDeptUser.getCounty());
        newData.setCountry(tbDeptUser.getCountry());
        newData.setTown(tbDeptUser.getTown());
        newData.setId(tbDeptUser.getId());
        if (tbDeptUserService.checkTbDeptUser(newData)) {
            throw new ServiceException("数据已存在！");
        }

        if (ObjectUtil.isNotEmpty(tbDeptUser.getUserId())) {
            if (tbDeptUserService.checkTbDeptUser(new TbDeptUser() {{
                setUserId(tbDeptUser.getUserId());
                setId(tbDeptUser.getId());
            }})) {
                throw new ServiceException("该账号已关联其他社区干部，不可重复关联！");
            }
        }

        //根据身份证号获取生日信息
        try {
            //根据身份证号获取生日信息
            if (StringUtils.isNotBlank( tbDeptUser.getIdCard())){
                String s = tbDeptUser.getIdCard().replaceAll(" ", "").replaceAll("'", "").replaceAll("'", "");
                tbDeptUser.setIdCard(s);
                Date idCard = DateUtils.getIdCard(s);
                if (ObjectUtil.isNotEmpty(idCard)) {
                    tbDeptUser.setBirthday(idCard);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 新增社区干部信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:add')")
    @Log(title = "社区干部信息", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbDeptUser tbDeptUser) {
        tbDeptUser.setCreateBy(getUsername());
        getDeptInfo(tbDeptUser);
        return toAjax(tbDeptUserService.insertTbDeptUser(tbDeptUser));
    }

    /**
     * 修改社区干部信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:edit')")
    @Log(title = "社区干部信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbDeptUser tbDeptUser) {
        tbDeptUser.setUpdateBy(getUsername());
        getDeptInfo(tbDeptUser);
        return toAjax(tbDeptUserService.updateTbDeptUser(tbDeptUser));
    }

    /**
     * 删除社区干部信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:deptUser:remove')")
    @Log(title = "社区干部信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tbDeptUserService.deleteTbDeptUserByIds(ids));
    }
}
