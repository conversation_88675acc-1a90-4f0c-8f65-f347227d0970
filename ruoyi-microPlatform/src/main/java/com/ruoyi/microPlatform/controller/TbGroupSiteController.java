package com.ruoyi.microPlatform.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.microPlatform.domain.TbGroupSite;
import com.ruoyi.microPlatform.service.ITbGroupSiteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 新就业群体站点Controller
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@RestController
@RequestMapping("/gridData/groupSite")
public class TbGroupSiteController extends BaseController
{
    @Autowired
    private ITbGroupSiteService tbGroupSiteService;

    /**
     * 查询新就业群体站点列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:list')")
    @GetMapping("/list")
    public TableDataInfo list(TbGroupSite tbGroupSite)
    {
        startPage();
        List<TbGroupSite> list = tbGroupSiteService.selectTbGroupSiteList(tbGroupSite);
        return getDataTable(list);
    }

    /**
     * 导出新就业群体站点列表
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:export')")
    @Log(title = "新就业群体站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TbGroupSite tbGroupSite)
    {
        List<TbGroupSite> list = tbGroupSiteService.selectTbGroupSiteList(tbGroupSite);
        ExcelUtil<TbGroupSite> util = new ExcelUtil<TbGroupSite>(TbGroupSite.class);
        util.exportExcel(response, list, "新就业群体站点数据");
    }

    @Log(title = "新就业群体站点", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<TbGroupSite> util = new ExcelUtil<TbGroupSite>(TbGroupSite.class);
        List<TbGroupSite> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = tbGroupSiteService.importTbGroupSite(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TbGroupSite> util = new ExcelUtil<TbGroupSite>(TbGroupSite.class);
        util.exportTemplateExcel(response, "新就业群体站点数据");
    }

    /**
     * 获取新就业群体站点详细信息
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tbGroupSiteService.selectTbGroupSiteById(id));
    }

    /**
     * 新增新就业群体站点
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:add')")
    @Log(title = "新就业群体站点", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody TbGroupSite tbGroupSite)
    {
        tbGroupSite.setCreateBy(getUsername());
        if (tbGroupSiteService.checkTbGroupSite(tbGroupSite)){
            return error("数据已存在！");
        }
        return toAjax(tbGroupSiteService.insertTbGroupSite(tbGroupSite));
    }

    /**
     * 修改新就业群体站点
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:edit')")
    @Log(title = "新就业群体站点", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody TbGroupSite tbGroupSite)
    {
        tbGroupSite.setUpdateBy(getUsername());
        if (tbGroupSiteService.checkTbGroupSite(tbGroupSite)){
            return error("数据已存在！");
        }
        return toAjax(tbGroupSiteService.updateTbGroupSite(tbGroupSite));
    }

    /**
     * 删除新就业群体站点
     */
    @PreAuthorize("@ss.hasPermi('gridData:groupSite:remove')")
    @Log(title = "新就业群体站点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tbGroupSiteService.deleteTbGroupSiteByIds(ids));
    }
}
