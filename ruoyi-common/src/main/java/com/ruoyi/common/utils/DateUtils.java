package com.ruoyi.common.utils;

import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
            "yyyy年MM月dd日", "yyyy年MM月dd号", "MM月dd日", "MM月dd号", "yyyy/MM/dd HH:mm"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            String s = str.toString();
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            if (s.contains(".")) {
                String[] split = s.split("\\.");
                int length = s.split("\\.").length;
                if (s.split("\\.").length < 3) {
                    s = year + "." + s;
                }
            }
            if (s.contains("月") && !s.contains("年")) {
                s = year + "年" + s;
            }
            return parseDate(s, parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     *
     * @return
     */
    public static double differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((new BigDecimal((date2.getTime() - date1.getTime())).divide(new BigDecimal(1000 * 3600 * 24), 2, BigDecimal.ROUND_HALF_UP)).doubleValue());
    }

    /**
     * 计算时间差
     *
     * @param endTime   最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static Date getStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //设置当前时间
        //同理增加一天的方法：
        calendar.set(Calendar.HOUR_OF_DAY, 00);//时
        calendar.set(Calendar.MINUTE, 00);//分
        calendar.set(Calendar.SECOND, 00);//秒
        return calendar.getTime();
    }

    public static Date parseDateByStr(Object str, String format) {
        if (str == null) {
            return null;
        }
        try {
            SimpleDateFormat ft = new SimpleDateFormat(format);

            return ft.parse((String) str);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取两个日期之间的所有日期 (年月日)
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> getBetweenDate(String startTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 声明保存日期集合
        List<String> list = new ArrayList<String>();
        try {
            // 转化成日期类型
            Date startDate = sdf.parse(startTime);
            Date endDate = sdf.parse(endTime);

            //用Calendar 进行日期比较判断
            Calendar calendar = Calendar.getInstance();
            while (startDate.getTime() <= endDate.getTime()) {
                // 把日期添加到集合
                list.add(sdf.format(startDate));
                // 设置日期
                calendar.setTime(startDate);
                //把日期增加一天
                calendar.add(Calendar.DATE, 1);
                // 获取增加后的日期
                startDate = calendar.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return list;
    }

    public static String parseDate(Date dataTime, String s) {
        return DateFormatUtils.format(dataTime, s);
    }

    /**
     * 获取当前月第一天
     *
     * @param
     * @return
     */
    public static Date getFirstDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
//        System.err.println("当前时间:"+calendar.getTime());
//        System.err.println("当前月份:" + (calendar.get(Calendar.MONTH) + 1));
        // 获取某月最小天数----给定日历字段的最小值
        int firstDay = calendar.getActualMinimum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最小天数
        calendar.set(Calendar.DAY_OF_MONTH, firstDay);//日期
        calendar.set(Calendar.HOUR_OF_DAY, 0);//时
        calendar.set(Calendar.MINUTE, 0);//分
        calendar.set(Calendar.SECOND, 0);//秒
//        System.err.println("更改后时间:"+calendar.getTime());
        return calendar.getTime();
    }

    //    public static Date getLastDayOfMonth(int month) {
    public static Date getLastDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
//        System.err.println("当前时间:"+calendar.getTime());
//        System.err.println("当前月份:" + (calendar.get(Calendar.MONTH) + 1));
        int actualMaximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, actualMaximum);//日期
        calendar.set(Calendar.HOUR_OF_DAY, 23);//时
        calendar.set(Calendar.MINUTE, 59);//分
        calendar.set(Calendar.SECOND, 59);//秒
//        System.err.println("更改后时间:"+calendar.getTime());
        return calendar.getTime();
    }

    public static Date getThreeDay() {
        Calendar calendar = Calendar.getInstance();
        System.err.println("当前时间:" + calendar.getTime());
//        System.err.println("当前月份:" + (calendar.get(Calendar.MONTH) + 1));
        int actualMaximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.add(Calendar.DAY_OF_MONTH, 3);//日期
        calendar.set(Calendar.HOUR_OF_DAY, 23);//时
        calendar.set(Calendar.MINUTE, 59);//分
        calendar.set(Calendar.SECOND, 59);//秒
//        System.err.println("更改后时间:"+calendar.getTime());
        return calendar.getTime();
    }

    //当前年月日的零点
    public static Date getEndDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        System.err.println("当前时间---:" + calendar.getTime());
        calendar.set(Calendar.HOUR_OF_DAY, 0);//时
        calendar.set(Calendar.MINUTE, 0);//分
        calendar.set(Calendar.SECOND, 0);//秒
        calendar.set(Calendar.MILLISECOND, 0);//秒
        return calendar.getTime();
    }

    public static Date getEndDayMinute(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        System.err.println("当前时间---:" + calendar.getTime());
        calendar.set(Calendar.SECOND, 0);//秒
        calendar.set(Calendar.MILLISECOND, 0);//毫秒
        return calendar.getTime();
    }

    //当前年月日的晚上23.59.59
    public static Date getEndDay1(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        System.err.println("当前时间---:" + calendar.getTime());
        calendar.set(Calendar.HOUR_OF_DAY, 23);//时
        calendar.set(Calendar.MINUTE, 59);//分
        calendar.set(Calendar.SECOND, 59);//秒
        return calendar.getTime();
    }

    public static Date getStartDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        System.err.println("当前时间---:" + calendar.getTime());
        calendar.set(Calendar.HOUR_OF_DAY, 0);//时
        calendar.set(Calendar.MINUTE, 0);//分
        calendar.set(Calendar.SECOND, 0);//秒
        calendar.set(Calendar.MILLISECOND, 0);//毫秒
        return calendar.getTime();
    }

    public static Map<String, Date> getMonthOfDay(String time) {
        Map<String, Date> map = new HashMap<>();
        //解析年月
        String[] split = time.split("年");
        Integer year = Integer.parseInt(split[0]);
        Integer month = Integer.parseInt(split[1].substring(0, split[1].length() - 1));
        //设置周期
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getMinimum(Calendar.DATE));
        map.put("day1", calendar.getTime());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DATE));
        map.put("day2", calendar.getTime());
        return map;
    }

    /**
     * date2比date1多的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDays(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if (year1 != year2)   //同一年
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)    //闰年
                {
                    timeDistance += 366;
                } else    //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2 - day1);
        } else    //不同年
        {
            System.out.println("判断day2 - day1 : " + (day2 - day1));
            return day2 - day1;
        }
    }

    public static Date getYesterday(Date date) {
        Calendar cal = Calendar.getInstance();
        //设置当前时间
        cal.setTime(date);
        //同理增加一天的方法：
        cal.add(Calendar.DATE, -1);
        return cal.getTime();
    }


    public static String lastMonth(String month) {
        try {
            String yearMonth = "";
            Date parse = new SimpleDateFormat("yyyy-MM").parse(month); //上传月报时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(parse);
            int calendarYear = calendar.get(Calendar.YEAR);
            int calendarMonth = calendar.get(Calendar.MONTH);
            if (calendarMonth < 10) {
                if (calendarMonth == 0) {
                    yearMonth = (calendarYear - 1) + "-12";
                } else {
                    yearMonth = calendarYear + "-0" + calendarMonth;
                }
            } else {
                yearMonth = calendarYear + "-" + calendarMonth;
            }
            return yearMonth;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String lastMonthDate(Date date) {
        String yearMonth = "";
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int calendarYear = calendar.get(Calendar.YEAR);
        int calendarMonth = calendar.get(Calendar.MONTH);
        if (calendarMonth < 10) {
            if (calendarMonth == 0) {
                yearMonth = (calendarYear - 1) + "-12";
            } else {
                yearMonth = calendarYear + "-0" + calendarMonth;
            }
        } else {
            yearMonth = calendarYear + "-" + calendarMonth;
        }
        return yearMonth;
    }


    public static String nextMonth(String month) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date dt = sdf.parse(month);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        rightNow.add(Calendar.MONTH, 1);
        Date dt1 = rightNow.getTime();
        String reStr = sdf.format(dt1);
        return reStr;
    }

    // 0点班
    public static Map<String, Object> getShiftTypeDate(Date date) {
        Map<String, Object> map = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        if (hour >= 23 || hour < 7) {
            map.put("shiftType", "0点班");
            calendar.set(Calendar.HOUR_OF_DAY, 0);//时
            calendar.set(Calendar.MINUTE, 0);//分
            calendar.set(Calendar.SECOND, 0);//秒
            calendar.set(Calendar.MILLISECOND, 0);//毫秒
            if (hour >= 23) {
                // 下一天
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
            Date first = calendar.getTime();
            map.put("jobTime", first);
        } else if (hour >= 7 && hour < 13) {
            map.put("shiftType", "8点班");
            calendar.set(Calendar.HOUR_OF_DAY, 0);//时
            calendar.set(Calendar.MINUTE, 0);//分
            calendar.set(Calendar.SECOND, 0);//秒
            calendar.set(Calendar.MILLISECOND, 0);//毫秒
            Date second = calendar.getTime();
            map.put("jobTime", second);
        } else if (hour >= 13) {
            map.put("shiftType", "4点班");
            calendar.set(Calendar.HOUR_OF_DAY, 0);//时
            calendar.set(Calendar.MINUTE, 0);//分
            calendar.set(Calendar.SECOND, 0);//秒
            calendar.set(Calendar.MILLISECOND, 0);//毫秒
            Date second = calendar.getTime();
            map.put("jobTime", second);
        }
        return map;
    }

    public static Date getIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return null;
        }
        int length = idCard.length();
        String birthDateStr;
        // 提取出生日期部分
        if (length == 18) {
            birthDateStr = idCard.substring(6, 14);
        } else if (length == 15) {
            birthDateStr = "19" + idCard.substring(6, 12); // 15位身份证补年份前缀
        } else {
            throw new IllegalArgumentException(idCard + "身份证号长度不正确");
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            sdf.setLenient(false);
            return sdf.parse(birthDateStr);
        } catch (ParseException e) {
            throw new IllegalArgumentException(idCard + "身份证号包含无效的出生日期", e);
        }
    }
}
