package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbUmEventConvert;
import com.ruoyi.dataview.domain.dto.DbUmEventFormDTO;
import com.ruoyi.dataview.domain.dto.DbUmEventQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbUmEvent;
import com.ruoyi.dataview.service.IDbUmEventService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 运管服案件案件数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/fiveLevel/umEvent")
@RequiredArgsConstructor
public class DbUmEventController extends BaseController
{
    private final IDbUmEventService dbUmEventService;

    private final DbUmEventConvert umEventConvert;

    /**
     * 查询运管服案件案件数据列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbUmEventQueryDTO umEventQueryDTO)
    {
        DbUmEvent dbUmEvent = umEventConvert.toDO(umEventQueryDTO);
        startPage();
        return getDataTable(umEventConvert.toVOList(dbUmEventService.selectDbUmEventList(dbUmEvent)));
    }

    /**
     * 导出运管服案件案件数据列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:export')")
    @Log(title = "运管服案件案件数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbUmEventQueryDTO umEventQueryDTO)
    {
        DbUmEvent dbUmEvent = umEventConvert.toDO(umEventQueryDTO);
        List<DbUmEvent> list = dbUmEventService.selectDbUmEventList(dbUmEvent);
        ExcelUtil<DbUmEvent> util = new ExcelUtil<DbUmEvent>(DbUmEvent.class);
        util.exportExcel(response, list, "运管服案件案件数据数据");
    }

    @Log(title = "运管服案件案件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbUmEvent> util = new ExcelUtil<DbUmEvent>(DbUmEvent.class);
        List<DbUmEvent> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbUmEventService.importDbUmEvent(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbUmEvent> util = new ExcelUtil<DbUmEvent>(DbUmEvent.class);
        util.exportTemplateExcel(response, "运管服案件案件数据数据");
    }

    /**
     * 获取运管服案件案件数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(umEventConvert.toVO(dbUmEventService.selectDbUmEventById(id)));
    }

    /**
     * 新增运管服案件案件数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:add')")
    @Log(title = "运管服案件案件数据", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbUmEventFormDTO umEventFormDTO)
    {
        DbUmEvent dbUmEvent = umEventConvert.toDO(umEventFormDTO);
        dbUmEvent.setCreateBy(getUsername());
        /*if (dbUmEventService.checkDbUmEvent(dbUmEvent)){
            return error("数据已存在！");
        }*/
        return toAjax(dbUmEventService.insertDbUmEvent(dbUmEvent));
    }

    /**
     * 修改运管服案件案件数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:edit')")
    @Log(title = "运管服案件案件数据", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbUmEventFormDTO umEventFormDTO)
    {
        DbUmEvent dbUmEvent = umEventConvert.toDO(umEventFormDTO);
        dbUmEvent.setUpdateBy(getUsername());
        if (dbUmEventService.checkDbUmEvent(dbUmEvent)){
            return error("数据已存在！");
        }
        return toAjax(dbUmEventService.updateDbUmEvent(dbUmEvent));
    }

    /**
     * 删除运管服案件案件数据
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:umEvent:remove')")
    @Log(title = "运管服案件案件数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbUmEventService.deleteDbUmEventByIds(ids));
    }
}
