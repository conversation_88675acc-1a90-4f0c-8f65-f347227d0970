package com.ruoyi.dataview.domain.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 水利局涵洞监测数据对象 db_site_data
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbSiteData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private String id;

    /** 序列号 */
    @Excel(name = "序列号")
    private Long serial;

    /** 站点编号 */
    @Excel(name = "站点编号")
    private String stCode;

    /** 站点名称 */
    @Excel(name = "站点名称")
    private String stName;

    /** 观测时间(ttTime) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "观测时间(ttTime)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ttTime;

    /** 存储时间(sttTime) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "存储时间(sttTime)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sttTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date utTime;

    /** 水位(米,z) */
    @Excel(name = "水位(米,z)")
    private BigDecimal waterLevel;

    /** 报警信息(zt) */
    @Excel(name = "报警信息(zt)")
    private String alarmCode;

    /** 信号强度 */
    @Excel(name = "信号强度")
    private Integer signal;

    /** 电源电压(V) */
    @Excel(name = "电源电压(V)")
    private BigDecimal voltage;

    /** 视频地址 */
    @Excel(name = "视频地址")
    private String videoPath;
}
