package com.ruoyi.microPlatform.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.microPlatform.domain.CountyStatics;
import com.ruoyi.microPlatform.domain.LargeScreenInfo;
import com.ruoyi.microPlatform.domain.TbCommunityResident;
import org.apache.ibatis.annotations.Param;

/**
 * 小区住户居民信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-03
 */
public interface TbCommunityResidentMapper 
{
    /**
     * 查询小区住户居民信息
     * 
     * @param id 小区住户居民信息主键
     * @return 小区住户居民信息
     */
    public TbCommunityResident selectTbCommunityResidentById(Long id);

    public TbCommunityResident selectTbCommunityResidentById2(Long id);

    /**
     * 校验小区住户居民信息是否唯一
     *
     * @param tbCommunityResident
     * @return 小区住户居民信息
     */
    public TbCommunityResident checkTbCommunityResidentUnique(TbCommunityResident tbCommunityResident);

    /**
     * 查询小区住户居民信息列表
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 小区住户居民信息集合
     */
    public List<TbCommunityResident> selectTbCommunityResidentList(TbCommunityResident tbCommunityResident);

    public List<Long> selectTbCommunityResidentCount(TbCommunityResident tbCommunityResident);

    /**
     * 新增小区住户居民信息
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    public int insertTbCommunityResident(TbCommunityResident tbCommunityResident);

    /**
     * 修改小区住户居民信息
     * 
     * @param tbCommunityResident 小区住户居民信息
     * @return 结果
     */
    public int updateTbCommunityResident(TbCommunityResident tbCommunityResident);

    /**
     * 删除小区住户居民信息
     * 
     * @param id 小区住户居民信息主键
     * @return 结果
     */
    public int deleteTbCommunityResidentById(Long id);

    /**
     * 批量删除小区住户居民信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTbCommunityResidentByIds(Long[] ids);

    public Integer selectPersonCount(Long[] ids);

    /**
     * 查询小区住户居民信息和对应的居民住户成员信息  1对多
     * @param communityResident
     * @return
     */
    List<TbCommunityResident> selectTbCommunityResidentAndMemberList(TbCommunityResident communityResident);

    List<TbCommunityResident> selectTbCommunityResidentSpaceList(LargeScreenInfo largeScreenInfo);

    List<CountyStatics> selectTypeList(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList2(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList3(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList4(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList5(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList6(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList7(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList8(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList9(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList10(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList11(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList12(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList13(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList14(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList15(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList16(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);

    List<CountyStatics> selectTypeList17(@Param("deptLevelType") Integer deptLevelType, @Param("county") String county, @Param("country") String country);
    //查总数
    int countResidentList(TbCommunityResident tbCommunityResident);

}
