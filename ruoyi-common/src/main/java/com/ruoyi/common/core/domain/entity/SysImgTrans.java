package com.ruoyi.common.core.domain.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户签名图片转换对象 sys_img_trans
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
public class SysImgTrans extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 源图片地址 */
    @Excel(name = "源图片地址")
    private String srcImg;

    /** 目标图片地址 */
    @Excel(name = "目标图片地址")
    private String targetImg;

    /** 是否已转换 */
    @Excel(name = "是否已转换")
    private Integer isTrans;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Integer isDelete;

    /** 关联用户id */
    @Excel(name = "关联用户id")
    private Long userId;

    /** 关联用户名 */
    @Excel(name = "关联用户名")
    private String userName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSrcImg(String srcImg) 
    {
        this.srcImg = srcImg;
    }

    public String getSrcImg() 
    {
        return srcImg;
    }
    public void setTargetImg(String targetImg) 
    {
        this.targetImg = targetImg;
    }

    public String getTargetImg() 
    {
        return targetImg;
    }
    public void setIsTrans(Integer isTrans) 
    {
        this.isTrans = isTrans;
    }

    public Integer getIsTrans() 
    {
        return isTrans;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("srcImg", getSrcImg())
            .append("targetImg", getTargetImg())
            .append("isTrans", getIsTrans())
            .append("isDelete", getIsDelete())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .toString();
    }
}
