package com.ruoyi.dataview.domain.vo;

import com.ruoyi.dataview.domain.bigData.res.CommonBaseCount;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PetitionInfoStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 信访总数
     */
    private Long total;

    /**
     * 本月信访数
     */
    private Long monthTotal;

    /**
     *
     */
    private List<CommonBaseCount> typeCount;
}