package com.ruoyi.dataview.controller;

import java.util.List;
import com.ruoyi.common.annotation.RepeatSubmit;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.dataview.convert.DbMayorMailboxConvert;
import com.ruoyi.dataview.domain.dto.DbMayorMailboxFormDTO;
import com.ruoyi.dataview.domain.dto.DbMayorMailboxQueryDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.dataview.domain.po.DbMayorMailbox;
import com.ruoyi.dataview.service.IDbMayorMailboxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 市长信箱主Controller
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@RestController
@RequestMapping("/fiveLevel/mayorMailbox")
@RequiredArgsConstructor
public class DbMayorMailboxController extends BaseController
{
    private final IDbMayorMailboxService dbMayorMailboxService;

    private final DbMayorMailboxConvert mayorMailboxConvert;

    /**
     * 查询市长信箱主列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:list')")
    @GetMapping("/list")
    public TableDataInfo list(DbMayorMailboxQueryDTO mayorMailboxQueryDTO)
    {
        DbMayorMailbox dbMayorMailbox = mayorMailboxConvert.toDO(mayorMailboxQueryDTO);
        startPage();
        return getDataTable(mayorMailboxConvert.toVOList(dbMayorMailboxService.selectDbMayorMailboxList(dbMayorMailbox)));
    }

    /**
     * 导出市长信箱主列表
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:export')")
    @Log(title = "市长信箱主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DbMayorMailboxQueryDTO mayorMailboxQueryDTO)
    {
        DbMayorMailbox dbMayorMailbox = mayorMailboxConvert.toDO(mayorMailboxQueryDTO);
        List<DbMayorMailbox> list = dbMayorMailboxService.selectDbMayorMailboxList(dbMayorMailbox);
        ExcelUtil<DbMayorMailbox> util = new ExcelUtil<DbMayorMailbox>(DbMayorMailbox.class);
        util.exportExcel(response, list, "市长信箱主数据");
    }

    @Log(title = "市长信箱主", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:import')")
    @RepeatSubmit//自定义注解，防止重复提交
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DbMayorMailbox> util = new ExcelUtil<DbMayorMailbox>(DbMayorMailbox.class);
        List<DbMayorMailbox> infos = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = dbMayorMailboxService.importDbMayorMailbox(infos, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DbMayorMailbox> util = new ExcelUtil<DbMayorMailbox>(DbMayorMailbox.class);
        util.exportTemplateExcel(response, "市长信箱主数据");
    }

    /**
     * 获取市长信箱主详细信息
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(mayorMailboxConvert.toVO(dbMayorMailboxService.selectDbMayorMailboxById(id)));
    }

    /**
     * 新增市长信箱主
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:add')")
    @Log(title = "市长信箱主", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DbMayorMailboxFormDTO mayorMailboxFormDTO)
    {
        DbMayorMailbox dbMayorMailbox = mayorMailboxConvert.toDO(mayorMailboxFormDTO);
        dbMayorMailbox.setCreateBy(getUsername());
        if (dbMayorMailboxService.checkDbMayorMailbox(dbMayorMailbox)){
            return error("数据已存在！");
        }
        return toAjax(dbMayorMailboxService.insertDbMayorMailbox(dbMayorMailbox));
    }

    /**
     * 修改市长信箱主
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:edit')")
    @Log(title = "市长信箱主", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody DbMayorMailboxFormDTO mayorMailboxFormDTO)
    {
        DbMayorMailbox dbMayorMailbox = mayorMailboxConvert.toDO(mayorMailboxFormDTO);
        dbMayorMailbox.setUpdateBy(getUsername());
        if (dbMayorMailboxService.checkDbMayorMailbox(dbMayorMailbox)){
            return error("数据已存在！");
        }
        return toAjax(dbMayorMailboxService.updateDbMayorMailbox(dbMayorMailbox));
    }

    /**
     * 删除市长信箱主
     */
    @PreAuthorize("@ss.hasPermi('fiveLevel:mayorMailbox:remove')")
    @Log(title = "市长信箱主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dbMayorMailboxService.deleteDbMayorMailboxByIds(ids));
    }
}
