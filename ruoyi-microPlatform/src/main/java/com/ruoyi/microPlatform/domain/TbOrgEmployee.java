package com.ruoyi.microPlatform.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 组织情况员工信息对象 tb_org_employee
 * 
 * <AUTHOR>
 * @date 2025-04-02
 */
public class TbOrgEmployee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 关联组织id */
    @Excel(name = "关联组织id")
    private Long orgId;

    /** 关联居民id */
    @Excel(name = "关联居民id")
    private Long memberId;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    private String name;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 政治面貌 */
    @Excel(name = "政治面貌")
    private String partyStatus;

    /** 是否中共党员(0否 1是) */
    @Excel(name = "是否中共党员(0否 1是)")
    private String hasPartyMember;

    /** 党组织关系所在支部 */
    @Excel(name = "党组织关系所在支部")
    private String partyName;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date entryDate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrgId(Long orgId) 
    {
        this.orgId = orgId;
    }

    public Long getOrgId() 
    {
        return orgId;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPartyStatus(String partyStatus) 
    {
        this.partyStatus = partyStatus;
    }

    public String getPartyStatus() 
    {
        return partyStatus;
    }
    public void setHasPartyMember(String hasPartyMember) 
    {
        this.hasPartyMember = hasPartyMember;
    }

    public String getHasPartyMember() 
    {
        return hasPartyMember;
    }
    public void setPartyName(String partyName) 
    {
        this.partyName = partyName;
    }

    public String getPartyName() 
    {
        return partyName;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setEntryDate(Date entryDate) 
    {
        this.entryDate = entryDate;
    }

    public Date getEntryDate() 
    {
        return entryDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orgId", getOrgId())
            .append("memberId", getMemberId())
            .append("name", getName())
            .append("idCard", getIdCard())
            .append("partyStatus", getPartyStatus())
            .append("hasPartyMember", getHasPartyMember())
            .append("partyName", getPartyName())
            .append("position", getPosition())
            .append("entryDate", getEntryDate())
            .toString();
    }
}
