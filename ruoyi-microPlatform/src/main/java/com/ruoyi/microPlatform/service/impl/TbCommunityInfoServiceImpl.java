package com.ruoyi.microPlatform.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.exception.ServiceException;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.microPlatform.domain.TbGridInfo;
import com.ruoyi.microPlatform.domain.TbGridUser;
import com.ruoyi.microPlatform.domain.TbGridUserCommunity;
import com.ruoyi.microPlatform.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.microPlatform.domain.TbCommunityInfo;
import com.ruoyi.microPlatform.service.ITbCommunityInfoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 小区信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-03
 */
@Service
public class TbCommunityInfoServiceImpl implements ITbCommunityInfoService {
    @Autowired
    private TbCommunityInfoMapper tbCommunityInfoMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TbGridInfoMapper gridInfoMapper;
    @Autowired
    private TbCommunityResidentMapper communityResidentMapper;
    @Autowired
    private TbCommunityEstateMapper estateMapper;
    @Autowired
    private TbGridUserCommunityMapper gridUserCommunityMapper;

    /**
     * 查询小区信息
     *
     * @param id 小区信息主键
     * @return 小区信息
     */
    @Override
    public TbCommunityInfo selectTbCommunityInfoById(Long id) {
        return tbCommunityInfoMapper.selectTbCommunityInfoById(id);
    }

    /**
     * 校验小区信息是否存在
     *
     * @param tbCommunityInfo
     * @return boolean
     */
    @Override
    public boolean checkTbCommunityInfo(TbCommunityInfo tbCommunityInfo) {
        TbCommunityInfo old = tbCommunityInfoMapper.checkTbCommunityInfoUnique(tbCommunityInfo);
        if (ObjectUtil.isNotEmpty(old)) {
            return true;
        }
        return false;
    }

    /**
     * 查询小区信息列表
     *
     * @param tbCommunityInfo 小区信息
     * @return 小区信息
     */
    @Override
    public List<TbCommunityInfo> selectTbCommunityInfoList(TbCommunityInfo tbCommunityInfo) {
        return tbCommunityInfoMapper.selectTbCommunityInfoList(tbCommunityInfo);
    }

    @Override
    public Integer selectTbCommunityInfoCount(TbCommunityInfo tbCommunityInfo) {
        List<Long> longs = tbCommunityInfoMapper.selectTbCommunityInfoCount(tbCommunityInfo);
        return longs.size();
    }

    /**
     * 构建唯一标识
     *
     * @param info 小区信息对象
     * @return 唯一标识字符串
     */
    private String buildIdentifier(TbCommunityInfo info) {
        return info.getDeptId() + "|" +
                info.getCounty() + "|" +
                info.getCountry() + "|" +
                info.getTown() + "|" +
                info.getGridId() + "|" +
                info.getCommunityName();
    }

    /**
     * 导入小区信息
     *
     * @param infos           小区信息列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    public String importTbCommunityInfo(List<TbCommunityInfo> infos, Boolean isUpdateSupport, String operName, SysDept userDept) {
        if (ObjectUtil.isEmpty(infos) || infos.isEmpty()) {
            throw new ServiceException("导入小区信息数据不能为空，请检查数据表头格式是否同模板一致！");
        }
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int successNum = 0;
        int index = 2;
        int failureNum = 0;


        //用于校验小区信息是否重复 要再 同一个网格下的小区
        Set<String> uniqueIdentifiers = new HashSet<>();

        for (TbCommunityInfo info : infos) {
            index++;
            try {
                String[] split = userDept.getAncestorsName().split(",");
                //先进行判断当前info的小区名称是否重复，在infos中。
                if (StringUtils.isBlank(info.getCommunityName())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、数据的小区名称不可为空" + "。数据条目：" + index);
                } else {
                    // 构建唯一标识
                    String identifier = buildIdentifier(info);
                    if (uniqueIdentifiers.contains(identifier)) {
                        //代表当前数据的小区名称重复
                        failureNum++;
                        failureMsg.append("<br/>" + failureNum + "、数据" + info.getCommunityName() + " 的小区名称不可重复" + "。数据条目：" + index);
                    } else {
                        //存入set
                        uniqueIdentifiers.add(identifier);
                        if (StringUtils.isBlank(info.getCounty())) {
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getCommunityName() + " 所属县（市、区） 不可为空" + "。数据条目：" + index);
                        } else {
                            if (StringUtils.isBlank(info.getCountry())) {
                                failureNum++;
                                failureMsg.append("<br/>" + failureNum + "、数据" + info.getCommunityName() + " 所属街道/乡镇 不可为空" + "。数据条目：" + index);
                            } else {
                                if (StringUtils.isBlank(info.getTown())) {
                                    failureNum++;
                                    failureMsg.append("<br/>" + failureNum + "、数据" + info.getCommunityName() + " 所属村/社区 不可为空" + "。数据条目：" + index);
                                } else {
                                    if (StringUtils.isBlank(info.getCommunityName())) {
                                        failureNum++;
                                        failureMsg.append("<br/>" + failureNum + "、数据 小区名称 不可为空");
                                    } else {
                                        if (StringUtils.isBlank(info.getGridName())) {
                                            failureNum++;
                                            failureMsg.append("<br/>" + failureNum + "、数据" + info.getCommunityName() + "所属网格 不可为空" + "。数据条目：" + index);
                                        } else {

                                            boolean flag = true;
                                            String msg = "";
                                            if (split.length >= 2 && !info.getCounty().equals(split[1])) {
                                                flag = false;
                                                msg = ("<br/>" + failureNum + "、您无权导入县区为‘" + info.getCounty() + "’的" + info.getCommunityName() + "的小区数据" + "。数据条目：" + index);
                                            }

                                            if (split.length >= 3 && !info.getCountry().equals(split[2])) {
                                                flag = false;
                                                msg = ("<br/>" + failureNum + "、您无权导入乡镇/街道为‘" + info.getCounty() + info.getCountry() + "’的" + info.getCommunityName() + "的小区数据");
                                            }

                                            if (split.length >= 4 && !info.getTown().equals(split[3])) {
                                                flag = false;
                                                msg = ("<br/>" + failureNum + "、您无权导入村/社区为‘" + info.getCounty() + info.getCountry() + info.getTown() + "的" + info.getCommunityName() + "的小区数据" + "。数据条目：" + index);
                                            }

                                            if (!flag) {
                                                failureNum++;
                                                failureMsg.append(msg);
                                            } else {
                                                SysDept dept = redisCache.getCacheObject(CacheConstants.DEPT_ANCESTORS_NAME_KEY + "许昌市," + info.getCounty() + "," + info.getCountry() + "," + info.getTown());
                                                if (ObjectUtil.isEmpty(dept)) {
                                                    failureNum++;
                                                    failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + "小区数据所属部门未找到！" + "数据条目：" + index);
                                                } else {
                                                    info.setDeptId(dept.getDeptId());
                                                    TbGridInfo gridInfo = gridInfoMapper.checkTbGridInfoUnique(new TbGridInfo() {{
                                                        setDeptId(dept.getDeptId());
                                                        setGridName(info.getGridName());
                                                    }});
                                                    if (ObjectUtil.isEmpty(gridInfo)) {
                                                        failureNum++;
                                                        failureMsg.append("<br/>" + failureNum + "、数据 " + info.getGridName() + " 小区数据所属网格未找到！" + "。数据条目：" + index);
                                                    } else {
                                                        info.setGridId(gridInfo.getId());
                                                        info.setCommunitySort(gridInfo.getGridSort());
                                                        // 验证是否存在这个数据
                                                        TbCommunityInfo tbCommunityInfo = tbCommunityInfoMapper.checkTbCommunityInfoUnique(info);
                                                        if (ObjectUtil.isEmpty(tbCommunityInfo)) {
                                                            info.setCreateTime(new Date());
                                                            info.setCreateBy(operName);
                                                            tbCommunityInfoMapper.insertTbCommunityInfo(info);
                                                            successNum++;
                                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getCommunityName() + " 导入成功");
                                                        } else if (isUpdateSupport) {
                                                            info.setUpdateBy(operName);
                                                            info.setUpdateTime(new Date());
                                                            info.setId(tbCommunityInfo.getId());
                                                            tbCommunityInfoMapper.updateTbCommunityInfo(info);
                                                            successNum++;
                                                            successMsg.append("<br/>" + successNum + "、数据 " + info.getCommunityName() + " 更新成功");
                                                        } else {
                                                            failureNum++;
                                                            failureMsg.append("<br/>" + failureNum + "、数据 " + info.getCommunityName() + " 已存在" + "。数据条目：" + index);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 导入失败：";
                failureMsg.append(msg + e.getMessage() + "。数据条目：" + index);
                e.printStackTrace();
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共" + infos.size() + "条数据，其中" + successNum + "条数据导入成功， " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 新增小区信息
     *
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    @Override
    public int insertTbCommunityInfo(TbCommunityInfo tbCommunityInfo) {
        tbCommunityInfo.setCreateTime(DateUtils.getNowDate());
        return tbCommunityInfoMapper.insertTbCommunityInfo(tbCommunityInfo);
    }

    /**
     * 修改小区信息
     *
     * @param tbCommunityInfo 小区信息
     * @return 结果
     */
    @Override
    public int updateTbCommunityInfo(TbCommunityInfo tbCommunityInfo) {
        tbCommunityInfo.setUpdateTime(DateUtils.getNowDate());
        return tbCommunityInfoMapper.updateTbCommunityInfo(tbCommunityInfo);
    }

    /**
     * 批量删除小区信息
     *
     * @param ids 需要删除的小区信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTbCommunityInfoByIds(Long[] ids) {
        Integer personCount = communityResidentMapper.selectPersonCount(ids);
        if (ObjectUtil.isNotEmpty(personCount) && personCount.intValue() != 0) {
            throw new ServiceException("删除的小区下还有住户家庭信息，不可删除！");
        }
        Integer integer = gridUserCommunityMapper.selectPersonCount(ids);
        if (ObjectUtil.isNotEmpty(integer) && integer.intValue() != 0) {
            throw new ServiceException("删除的小区中有被网格员关联，不可删除！");
        }

        int i = tbCommunityInfoMapper.deleteTbCommunityInfoByIds(ids);
        estateMapper.deleteTbCommunityEstateByMainIds(ids);
        return i;
    }

    /**
     * 删除小区信息信息
     *
     * @param id 小区信息主键
     * @return 结果
     */
    @Override
    public int deleteTbCommunityInfoById(Long id) {
        return tbCommunityInfoMapper.deleteTbCommunityInfoById(id);
    }


}
