package com.ruoyi.dataview.domain.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 运管服部件数据对象 db_city_component
 * 
 * <AUTHOR>
 * @date 2025-09-02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DbCityComponent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 大类代码 */
    @Excel(name = "大类代码")
    private String majorCode;

    /** 大类名称 */
    @Excel(name = "大类名称")
    private String majorName;

    /** 小类代码 */
    @Excel(name = "小类代码")
    private String minorCode;

    /** 小类名称 */
    @Excel(name = "小类名称")
    private String minorName;

    /** 数量 */
    @Excel(name = "数量")
    private Integer quantity;

    /** 说明 */
    @Excel(name = "说明")
    private String description;
}
