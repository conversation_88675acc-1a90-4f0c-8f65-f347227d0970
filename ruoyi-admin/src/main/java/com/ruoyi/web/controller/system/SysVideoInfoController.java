package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.RepeatSubmit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SysVideoInfo;
import com.ruoyi.system.service.ISysVideoInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 视频管理Controller
 * 
 * <AUTHOR>
 * @date 2022-12-29
 */
@RestController
@RequestMapping("/system/video")
public class SysVideoInfoController extends BaseController
{
    @Autowired
    private ISysVideoInfoService sysVideoInfoService;

    /**
     * 查询视频管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:video:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysVideoInfo sysVideoInfo)
    {
        startPage();
        List<SysVideoInfo> list = sysVideoInfoService.selectSysVideoInfoList(sysVideoInfo);
        return getDataTable(list);
    }

    /**
     * 导出视频管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:video:export')")
    @Log(title = "视频管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysVideoInfo sysVideoInfo)
    {
        List<SysVideoInfo> list = sysVideoInfoService.selectSysVideoInfoList(sysVideoInfo);
        ExcelUtil<SysVideoInfo> util = new ExcelUtil<SysVideoInfo>(SysVideoInfo.class);
        util.exportExcel(response, list, "视频管理数据");
    }

    /**
     * 获取视频管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:video:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sysVideoInfoService.selectSysVideoInfoById(id));
    }

    /**
     * 新增视频管理
     */
    @PreAuthorize("@ss.hasPermi('system:video:add')")
    @Log(title = "视频管理", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody SysVideoInfo sysVideoInfo)
    {
        return toAjax(sysVideoInfoService.insertSysVideoInfo(sysVideoInfo));
    }

    /**
     * 修改视频管理
     */
    @PreAuthorize("@ss.hasPermi('system:video:edit')")
    @Log(title = "视频管理", businessType = BusinessType.UPDATE)
    @PutMapping
    @RepeatSubmit
    public AjaxResult edit(@RequestBody SysVideoInfo sysVideoInfo)
    {
        return toAjax(sysVideoInfoService.updateSysVideoInfo(sysVideoInfo));
    }

    /**
     * 删除视频管理
     */
    @PreAuthorize("@ss.hasPermi('system:video:remove')")
    @Log(title = "视频管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @RepeatSubmit
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysVideoInfoService.deleteSysVideoInfoByIds(ids));
    }
}
